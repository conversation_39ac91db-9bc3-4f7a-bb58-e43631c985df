const _env = process.env.VUE_APP_ENV

const taizhouConf = {
  ICO: 'favicon-wigroup.ico',
  TYPE: 'taizhou',
  HOSPITAL_NAME: '',
  HOSPITAL_ADDRESS: '（总院区）江苏省泰州市医药高新区太湖路366号'
}
const testConf = {
  ICO: 'favicon-wigroup.ico',
  TYPE: 'taizhou',
  HOSPITAL_NAME: '',
  HOSPITAL_ADDRESS: '（总院区）江苏省泰州市医药高新区太湖路366号'
}

const trtcConfig = {
  appId: 1600061466,
  secretKey: '9db6730d0996484104d8a974a9c7e0ec8ddfd36553db2b71101acf3e769f24ab'
}

module.exports = {
  URL_LIST: {
    prod: {
      TITLE: 'CSP_PROD',
      ...taizhouConf,
      trtc: trtcConfig,
      FILE_HTTP_ENV: 'https://csp.starcds.cn/',
      HTTP_ENV: 'https://csp.starcds.cn',
      WSS_ENV: 'wss://csp.starcds.cn/ws/ep/pc/'
    },
    test: {
      TITLE: 'CSP_TEST',
      ...testConf,
      trtc: trtcConfig,
      // FILE_HTTP_ENV: 'http://**************:9700/',
      // HTTP_ENV: 'http://**************:9700',
      // WSS_ENV: 'ws://**************:9700/ws/ep/pc/'

      // FILE_HTTP_ENV: 'http://csp.starcds.cn/',
      // HTTP_ENV: 'http://csp.starcds.cn',
      FILE_HTTP_ENV: 'http://**************:8080/',
      HTTP_ENV: 'http://**************:8080',
      WSS_ENV: 'ws://**************:8080/ws/ep/pc/'
    }
  },
  // 根据配置生成 URL_ENV
  genUrlEnum() {
    return this.URL_LIST[_env]
  },
  // 生成 env
  getEnv() {
    return _env
  }
}
