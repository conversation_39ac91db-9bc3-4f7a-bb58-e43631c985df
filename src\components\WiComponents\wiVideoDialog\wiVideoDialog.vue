<template>
  <el-dialog
    v-if="dialogVisible"
    v-el-drag-dialog
    :title="dialogName"
    :visible="dialogVisible"
    custom-class="applicationCustomDialog"
    width="50vw"
    :before-close="done => onClose(done)"
    :close-on-click-modal="false"
    :append-to-body="true"
    top="5vh"
  >
    <!-- 标题 -->
    <span slot="title" class="customDialogTitle customLeftDialogTitle">
      <span>{{ dialogName }}</span>
    </span>
    <video ref="videoPlayer" class="videwPlayer" controls>
      <source :src="videoSource" type="video/mp4">
    </video>
  </el-dialog>
</template>

<script>
import env from '@/utils/env'

export default {
  name: 'WiVideoDialog',
  props: {
    dialogName: {
      type: String,
      default: '视频回放'
    }
  },
  data() {
    return {
      dialogVisible: false,
      videoSource: 'path/to/your/video.mp4',
      currentTime: 0,
      duration: 0,
      isPlaying: false
    }
  },
  methods: {
    onClose(done) {
      this.closeDialog()
      if (done) {
        done()
      }
    },
    openDialog(url) {
      let videoUrl = ''
      if (env.getEnv() === 'test') {
        videoUrl = url
      } else {
        const temp = url.split('com/')[1]
        videoUrl = `https://**************/trtc/vod/play/${temp}`
      }
      this.videoSource = videoUrl
      this.dialogVisible = true
    },
    closeDialog() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.videwPlayer {
  width: 100%;
}
</style>
