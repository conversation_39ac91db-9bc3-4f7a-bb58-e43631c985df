<!-- 慢病病种-房颤 -->
<template>
  <div class="chronic-disease-fibrillation">
    <flag-component title="心电图" />
    <el-form ref="formRef" :model="form" label-width="120px" :rules="rules" style="margin-top: 16px">
      <CheckboxGroupField v-model="form.ecgResult" :item="checkboxItem" />
      <el-form-item label="检查所见：">
        <el-input v-model="form.ecgFinding" type="textarea" />
      </el-form-item>
      <el-form-item label="上传报告图片：">
        <custom-upload v-model="form.ecgAttachmentUrl" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import FlagComponent from '@/components/flagComponent/index.vue'
import CustomUpload from '@/components/customUpload/index.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'
import { ecgOptions } from '@/views/receptionCenter/patientReception/component/complicationsScreening.js'

export default {
  name: 'ChronicDiseaseFibrillation',
  components: {
    FlagComponent,
    CustomUpload,
    CheckboxGroupField
  },
  data() {
    return {
      form: {
        ecgResult: [],
        ecgFinding: '',
        ecgAttachmentUrl: ''
      },
      checkboxItem: {
        label: '检查结果：',
        type: 'checkbox',
        required: true,
        mutuallyExclusive: true, // 互斥
        prop: 'ecgResult',
        options: [...ecgOptions]
      },
      rules: {
        ecgResult: [{ type: 'array', required: true, message: '检查结果', trigger: 'change' }]
      }
    }
  },
  methods: {
    initData(data) {
      const ecgData = data.find((item) => item.itemCode === 'ECG') || {}

      this.form = {
        ecgResult: ecgData.data && ecgData.data.ecgResult ? ecgData.data.ecgResult.split(',') : [],
        ecgFinding: ecgData.data && ecgData.data.ecgFinding,
        ecgAttachmentUrl: ecgData.data && ecgData.data.ecgAttachmentUrl,
        itemId: ecgData.id,
        itemDetailId: ecgData.data && ecgData.data.id
      }
    },
    async handleSave() {
      const result = {
        name: '房颤-心电图',
        success: false,
        data: {
          ecg: {
            ...this.form,
            ecgResult: this.form.ecgResult.join(','),
            name: '心电图',
            itemCode: 'ECG'
          }
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn('房颤-心电图校验异常', err)
        result.success = false
      }
      return result
    }
  }
}
</script>

<style scoped>
.chronic-disease-fibrillation {
  width: 100%;
  height: 100%;
}
</style>
