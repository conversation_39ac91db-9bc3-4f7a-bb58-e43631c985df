import axios from 'axios'

const pendingRequests = new Map() // Format: { contextKey: Map(requestKey, cancelFunction) }

// eslint-disable-next-line no-unused-vars
let requestCount = 0
// 拼接请求参数保证地址统一
const generateRequestKey = (config) => {
  // eslint-disable-next-line no-unused-vars
  const { url, method, params, data } = config || {}
  if (url) requestCount += 1
  // console.log('请求接口', url, requestCount)
  // 如果 params 存在，将其转换为查询字符串
  let paramsString = ''
  if (params) {
    paramsString = Object.keys(params)
      .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
  }
  // 如果有查询参数，将其拼接到 URL 后
  const fullUrl = paramsString ? `${url}?${paramsString}` : url
  // 生成请求唯一标识，包括 URL、方法、以及 body 数据
  // const dataString = data ? JSON.stringify(data) : ''
  // const oldUrl = [fullUrl, method.toLowerCase(), dataString]
  // console.log('oldUrl', oldUrl)
  return fullUrl
}
// 添加接口监听
const addPendingRequest = (config, contextKey) => {
  const requestKey = generateRequestKey(config)
  // 路由请求标识
  if (!pendingRequests.has(contextKey)) {
    pendingRequests.set(contextKey, new Map())
  }
  const contextRequests = pendingRequests.get(contextKey)
  // 重复请求拦截 目前随访体检都有严重的重复请求问题，需要花时间来处理，目前不能解开使用
  if (contextRequests.has(requestKey)) {
    // const cancelToken = contextRequests.get(requestKey)
    // cancelToken(`${requestKey}重复请求`)
  }
  // 创建取消钩子
  const cancelTokenSource = axios.CancelToken.source()
  config.cancelToken = cancelTokenSource.token
  contextRequests.set(requestKey, cancelTokenSource.cancel)
}
// 请求队列里移除当前的标识方法
const removePendingRequest = (config, contextKey) => {
  const requestKey = generateRequestKey(config)
  if (pendingRequests.has(contextKey)) {
    pendingRequests.get(contextKey).delete(requestKey)
  }
}
// 请求已取消
const cancelRequestsByContext = (contextKey) => {
  // console.log('contextKey', contextKey)
  if (pendingRequests.has(contextKey)) {
    pendingRequests.get(contextKey).forEach((cancel) => cancel('请求已取消'))
    pendingRequests.delete(contextKey)
  }
}

export { addPendingRequest, removePendingRequest, cancelRequestsByContext }
