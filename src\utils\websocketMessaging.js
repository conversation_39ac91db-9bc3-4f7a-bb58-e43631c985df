import { Message, Notification } from 'element-ui'
import { updateParticipantApi } from '@/api/remoteConsultation'
import { localCache } from '@/utils/cache'
import { getUserId } from '@/utils/auth'
import store from '@/store'
import router from '@/router'
import Vue from 'vue'
// 必须实例化
const vm = new Vue()
// 根据消息标识做不同的处理
export const messageHandle = (message) => {
  const msg = JSON.parse(message)

  if (msg.module === 'auth') {
    if (msg.subModule === '被踢下线') {
      // 清除所有消息通知
      Notification.closeAll()
      Message({
        duration: 3500,
        message: '您的账号在另一地点登录，您被迫下线。如非本人操作，请立即进行密码修改。',
        type: 'error'
      })
      store.dispatch('user/logout')
      router.push(`/login`)
    }
  }

  if (msg.module.indexOf('远程') === 0 && msg.subModule === '视频开始') {
    const h = vm.$createElement
    Notification({
      title: `${msg.content.createUsername} 邀请你远程会诊`,
      duration: 20000,
      showClose: false,
      message: h(
        'div',
        {
          style: 'text-align: right;width:100%;display:flex;'
        },
        [
          h('img', {
            attrs: {
              src: require('@/assets/dashboard_images/hangUp.png') // 拒绝
            },
            style: 'width:30px;height:30px; margin-right:50px;margin-left:50px;cursor: pointer;',
            on: {
              click: async() => {
                // 更新参与者状态
                const res = await updateParticipantApi({
                  roomId: msg.content.id,
                  status: 4,
                  platform: 'pc'
                })
                if (res.code === 200) {
                  Notification.closeAll()
                }
              }
            }
          }),
          h('img', {
            attrs: {
              src: require('@/assets/dashboard_images/answer.png') // 接听
            },
            style: 'width:30px;height:30px;cursor: pointer; ',
            on: {
              click: async() => {
                // 获取药品列表
                await store.dispatch('drugManagement/getDrugList')
                // 更新参与者状态
                const res = await updateParticipantApi({
                  roomId: msg.content.id,
                  status: 2,
                  platform: 'pc'
                })
                if (res.code === 200) {
                  const doctorList = JSON.parse(msg.content.participantJson).filter((item) => item.id !== getUserId())
                  doctorList.push({
                    id: msg.content.createUserid,
                    name: msg.content.createUsername
                  })
                  store.commit('app/SET_REMOTE_CONSULTATION_VISIBLE', true)
                  localCache.setCache('roomId', Number(msg.content.id))
                  localCache.setCache('participatingDoctors', doctorList)
                  Notification.closeAll()
                }
              }
            }
          })
        ]
      )
    })
  }

  if (msg.module.indexOf('远程') === 0 && msg.subModule === '诊断') {
    store.dispatch('drugManagement/getConsultationRecordDetail', { roomId: Number(msg.content.roomId) })
  }

  if (msg.module === 'reception' && msg.subModule === '视频结束') {
    store.commit('app/SET_REMOTE_CONSULTATION_VISIBLE', false)
  }
}
