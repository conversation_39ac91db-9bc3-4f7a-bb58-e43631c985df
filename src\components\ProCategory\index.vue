<template>
  <div class="pro-category">
    <div
      v-for="item in options"
      :key="item.code"
      class="category-item fontSize_14 flex_center"
      :class="[{ active: value === item.code }]"
      :style="{ cursor: item.disabled ? 'not-allowed' : 'pointer', width: width }"
      @click="handleItemClick(item)"
    >
      {{ item.value }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProCategory',
  props: {
    value: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default: () => []
    },
    width: {
      type: String,
      default: '4.6rem'
    }
  },

  created() {
    if (this.defaultSelected && !this.value) {
      this.emitChange(this.defaultSelected)
    }
  },

  methods: {
    handleItemClick(item) {
      if (item.disabled) return
      const code = this.value === item.code ? '' : item.code
      console.log(code)
      this.emitChange(code)
    },

    emitChange(value) {
      this.$emit('input', value)
      this.$emit('change', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.pro-category {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .category-item {
    height: 1.6rem;
    border-radius: 0.8rem;
    margin: 0 2.1rem 0.8rem 0;
    color: #555;
    background: #e9e9e9;
    border: 1px solid #e9e9e9;
    cursor: pointer;
    width: 4.6rem;
    padding: 0;
    box-sizing: border-box;
    min-width: auto;
    transition: all 0.3s;

    &.check-item2 {
      width: 5.7rem;
      margin-right: 0.3rem;
    }

    &:nth-child(2n) {
      margin-right: 0;
    }

    &.active {
      background: #0a86c8;
      color: #fff;
      border: 1px solid #0a86c8;
    }

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
