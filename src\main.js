/* eslint-disable no-new */
import Vue from 'vue'
import App from './App'
import store from './store'
import router from './router'

// 导入核心系统文件
import './icons'
import './permission'
import './utils/error-log'

// 导入Element UI
import './plugins/element.js'

// 导入初始化函数
import initializeApp from './plugins'

// 初始化应用
initializeApp()

// 关闭生产环境提示
Vue.config.productionTip = false

// 创建Vue实例
new Vue({
  el: '#app',
  router,
  store,
  render: (h) => h(App)
})
