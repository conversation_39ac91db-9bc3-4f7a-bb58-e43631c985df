<template>
  <div class="dynamic-template">
    <el-select v-model="tempValue" placeholder="请选择模板" clearable @change="getProjectConfigurationDetailFn">
      <el-option v-for="item in tempList" :key="item.id" :label="item.templateName" :value="item.id" />
    </el-select>

    <div class="temp-detail">
      <div class="tip">识别内容仅供参考，请仔细核对信息是否正确</div>
      <el-table :data="tempDetail" border style="width: 100%">
        <el-table-column align="center" prop="name" label="名称" min-width="120">
          <template slot-scope="{ row }">
            <el-input v-model="row.name" />
          </template>
        </el-table-column>
        <el-table-column align="center" prop="value" label="值" min-width="110">
          <template slot-scope="{ row }">
            <custom-input-number v-model="row.value" />
          </template>
        </el-table-column>
        <el-table-column align="center" prop="range" label="范围" width="120">
          <template slot-scope="{ row }">
            <el-input v-model="row.range" />
          </template>
        </el-table-column>
        <el-table-column align="center" prop="unit" label="单位" width="100">
          <template slot-scope="{ row }">
            <el-input v-model="row.unit" />
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getProjectConfigurationListByDepartCode, getProjectConfigurationDetail } from '@/api/projectConfiguration'
import { localCache } from '@/utils/cache'

export default {
  name: 'DynamicTemplate',
  props: {
    type: {
      type: String,
      default: ''
    },
    isSave: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tempValue: '',
      tempList: [],
      tempDetail: []
    }
  },

  mounted() {
    this.getProjectConfigurationListByDepartCodeFn()
  },

  methods: {
    // 获取模板列表
    async getProjectConfigurationListByDepartCodeFn() {
      const res = await getProjectConfigurationListByDepartCode({
        departCode: localCache.getCache('userInfo').departCode,
        itemCode: this.type
      })
      if (res.code === 200 && res.data.length > 0) {
        this.tempList = res.data
        this.tempValue = this.tempList[0].id || ''
        // 延迟执行，给父组件initData一个执行机会
        this.$nextTick(() => {
          setTimeout(() => {
            if (!this.isSave) {
              this.getProjectConfigurationDetailFn(this.tempValue)
            }
          }, 1000)
        })
      }
    },

    // 获取模板详情
    async getProjectConfigurationDetailFn(id) {
      if (!id) {
        this.tempDetail = []
        return
      }
      const res = await getProjectConfigurationDetail({
        id
      })
      if (res.code === 200) {
        this.tempDetail = res.data.itemList || []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dynamic-template {
  width: 100%;
  margin-top: 30px;
  .temp-detail {
    width: 100%;
    margin-top: 20px;
    .tip {
      color: red;
      font-size: 15px;
      margin-bottom: 10px;
    }
  }
}
</style>
