<!-- 检查第二部分: 心电图 尿常规 -->
<template>
  <div class="inspection-second-part">
    <div class="content">
      <div class="title">心电图</div>
      <div class="item">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="检查结果"> {{ ecgData.ecgResult }} </el-descriptions-item>
          <el-descriptions-item label="检查所见"> {{ ecgData.ecgFinding }} </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div class="content">
      <div class="title">尿常规</div>
      <div class="item">
        <el-table :data="urineData" style="width: 100%" border>
          <el-table-column align="center" prop="name" label="名称" />
          <el-table-column align="center" prop="unit" label="单位" />
          <el-table-column align="center" prop="range" label="参考范围" />
          <el-table-column align="center" prop="value" label="值" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { ecgOptions } from '@/views/receptionCenter/patientReception/component/complicationsScreening.js'

export default {
  name: 'InspectionSecondPart',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    // 心电图数据
    ecgData() {
      const options = [...ecgOptions]
      const result =
        this.reportInfo.itemList.find((item) => item.itemCode === 'ECG') &&
        this.reportInfo.itemList.find((item) => item.itemCode === 'ECG').data.ecgResult
      const resultList = result.split(',').map((item) => {
        return options.find((option) => option.value === item) && options.find((option) => option.value === item).label
      })
      return {
        ecgResult: resultList.join('、'),
        ecgFinding:
          this.reportInfo.itemList.find((item) => item.itemCode === 'ECG') &&
          this.reportInfo.itemList.find((item) => item.itemCode === 'ECG').data.ecgFinding
      }
    },

    // 尿常规数据
    urineData() {
      return (
        this.reportInfo.itemList.find((item) => item.itemCode === 'URINE_ROUTINE') &&
        this.reportInfo.itemList.find((item) => item.itemCode === 'URINE_ROUTINE').data.itemList
      )
    }
  }
}
</script>

<style lang="scss" scope>
.inspection-second-part {
  padding: 10px;
  .content {
    margin-top: 8px;
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
    }
  }
}
</style>
