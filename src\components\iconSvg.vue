<template>
  <svg :class="'icon ' + iconClass" aria-hidden="true">
    <use v-if="!isFile" :xlink:href="iconName" />
    <img v-else :src="iconName" alt="" width="100" height="100" />
  </svg>
</template>

<script>
import '@/assets/font/iconfont.js'

export default {
  name: 'IconSvg',
  props: {
    iconClass: {
      type: String,
      default: ''
    },
    iconId: {
      type: String,
      required: true
    },
    isFile: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    iconName() {
      if (this.isFile) {
        return require(`@/assets/nav/${this.iconId}.svg`)
      }
      let id = this.iconId.match(/icon-\S+/g)
      if (!id) {
        id = `icon-${this.iconId}`
      }
      return `#${id || this.iconId}`
    }
  }
}
</script>

<style scoped>
.icon {
  width: 0.75rem;
  height: 0.75rem;
  fill: currentColor;
  overflow: hidden;
  cursor: pointer;
}
</style>
