<template>
  <ProDialog :visible.sync="addDialogVisible" :title="title" width="960px">
    <div v-if="detail.signPackageList && detail.signPackageList.length > 0" v-loading="loading" class="performance">
      <el-radio-group v-model="spId" style="margin-bottom: 10px" @input="handleClick">
        <el-radio-button v-for="item in detail.signPackageList" :key="item.id" :label="item.id">
          {{ item.spName }}
        </el-radio-button>
      </el-radio-group>
      <el-table :data="tableData" border style="width: 100%" height="400" :span-method="mergeColumns">
        <el-table-column prop="siName" label="服务项名称" align="center" />
        <el-table-column prop="siFrequencyDesc" label="服务频次" align="center" />
        <el-table-column prop="planDate" label="计划服务时间" align="center" width="200">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.planDate"
              type="date"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              placeholder="选择日期"
            />
          </template>
        </el-table-column>
        <el-table-column prop="realDate" label="实际服务时间" align="center" width="120" />
        <el-table-column prop="status" label="履约状态" align="center" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 5" type="success">已履约</el-tag>
            <el-tag v-else>待履约</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="120">
          <template slot-scope="scope">
            <el-button v-if="scope.row.status === 1" type="text" @click="handleRenewal(scope.row)">履约</el-button>
            <el-button v-if="scope.row.status === 1" type="text" @click="handleSave(scope.row)">保存</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </ProDialog>
</template>
<script>
import {
  getPerformanceList,
  getIndividualSigningDetail,
  completePerformance,
  updatePerformance
} from '@/api/individualSigning'
import ProDialog from '@/components/ProDialog/index.vue'

export default {
  name: 'Performance',
  components: {
    ProDialog
  },
  data() {
    return {
      addDialogVisible: false,
      title: '履约记录',
      detail: {},
      tableData: [],
      spId: '',
      loading: false
    }
  },
  methods: {
    // 获取签约记录详情
    async getIndividualSigningDetail(id) {
      const res = await getIndividualSigningDetail({ id })
      if (res.code === 200) {
        this.detail = res.data
        this.addDialogVisible = true
        this.title = `${this.detail.patientName}-履约记录`
        if (this.detail.signPackageList.length > 0) {
          this.spId = this.detail.signPackageList[0].id
          this.getPerformanceListFn(this.spId)
        }
      }
    },

    // 获取履约记录
    async getPerformanceListFn(spId) {
      this.loading = true
      try {
        const res = await getPerformanceList({ signId: this.detail.id, spId })
        if (res.code === 200) {
          this.tableData = res.data.sort((a, b) => {
            if (a.siName === b.siName) {
              return new Date(a.planDate || '9999-12-31') - new Date(b.planDate || '9999-12-31')
            }
            return a.siName.localeCompare(b.siName)
          })
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },

    handleClick(val) {
      this.tableData = []
      this.getPerformanceListFn(val)
    },

    handleRenewal(row) {
      completePerformance({ id: row.id }).then((res) => {
        if (res.code === 200) {
          this.$message.success('履约成功')
          this.getPerformanceListFn(this.spId)
        }
      })
    },

    handleSave(row) {
      updatePerformance({ id: row.id, planDate: row.planDate }).then((res) => {
        if (res.code === 200) {
          this.$message.success('保存成功')
          this.getPerformanceListFn(this.spId)
        }
      })
    },

    mergeColumns({ row, column, rowIndex, columnIndex }) {
      // 需要合并的列索引，0 表示 siName，1 表示 siFrequencyDesc
      if (columnIndex === 0 || columnIndex === 1) {
        const currentValue = row.siName
        let rowspan = 1
        const colspan = 1

        // 如果不是第一行，判断是否与上一行相同
        if (rowIndex > 0) {
          const prevRow = this.tableData[rowIndex - 1]
          if (prevRow.siName === currentValue) {
            return [0, 0] // 合并单元格
          }
        }

        // 计算 rowspan（当前行与下面几行相同）
        for (let i = rowIndex + 1; i < this.tableData.length; i++) {
          const nextRow = this.tableData[i]
          if (nextRow.siName === currentValue) {
            rowspan++
          } else {
            break
          }
        }

        return [rowspan, colspan]
      }
    }
  }
}
</script>
