import request from '@/utils/request'
// 获取检验单位数据
export function getVerifyListApi(data) {
  return request({
    url: '/cspapi/backend/inspectionUnit/list',
    method: 'get',
    params: data
  })
}
// 获取送检单位数据
export function getInspectListApi(data) {
  return request({
    url: '/cspapi/backend/submissionRecord/listServiceStation',
    method: 'get',
    params: data
  })
}

// 获取送检表格数据
export function getRegionexamWorkDataApi(data) {
  return request({
    url: '/cspapi/backend/submissionRecord/search',
    method: 'get',
    params: data
  })
}
// 根据患者编号创建送检记录
export function addByCodeApi(code) {
  return request({
    url: `/cspapi/backend/submissionRecord/addByCode/${code}`,
    method: 'post'
  })
}

// 修改记录
export function saveByCodeApi(data) {
  return request({
    url: `/cspapi/backend/submissionRecord/save`,
    method: 'put',
    data
  })
}

// 删除记录
export function delUserDataApi(id) {
  return request({
    url: `/cspapi/backend/submissionRecord/delete/${id}`,
    method: 'delete'
  })
}

// 扫码并更新单挑记录为“已采集”
export function updateDataStatusApi(code) {
  return request({
    url: `/cspapi/backend/submissionRecord/scanCode/${code}`,
    method: 'put'
  })
}
