<!-- 检验检查第八部分: 震动阈值 外周动脉 -->
<template>
  <div class="inspection-eigth-part">
    <div class="content">
      <div class="title">震动阈值</div>
      <div class="item">
        <el-table :data="vibrationThresholdData.table" border style="width: 100%" :span-method="objectSpanMethod">
          <el-table-column label="位置" prop="position" align="center" />
          <el-table-column label="检查项目" prop="item" align="center" />
          <el-table-column label="右足" prop="right" align="center">
            <template slot-scope="scope">
              <template v-if="scope.row.type === 'radio'">
                <span>{{
                  scope.row.right === 1 ? '正常' : scope.row.right === 2 ? '减弱' : scope.row.right === 3 ? '消失' : ''
                }}</span>
              </template>
              <template v-else>
                <span>{{ scope.row.right }}</span>
              </template>
            </template>
          </el-table-column>
          <el-table-column label="左足" prop="left" align="center">
            <template slot-scope="scope">
              <template v-if="scope.row.type === 'radio'">
                <span>{{
                  scope.row.left === 1 ? '正常' : scope.row.left === 2 ? '减弱' : scope.row.left === 3 ? '消失' : ''
                }}</span>
              </template>
              <template v-else>
                <span>{{ scope.row.left }}</span>
              </template>
            </template>
          </el-table-column>
        </el-table>

        <el-descriptions :column="1" border style="margin-top: 10px">
          <el-descriptions-item label="震动阈值描述">
            {{ vibrationThresholdData.form.vtDescription }}
          </el-descriptions-item>
          <el-descriptions-item label="震动阈值结果">
            {{
              vibrationThresholdData.form.vtResult === 1
                ? '正常'
                : vibrationThresholdData.form.vtResult === 2
                  ? '异常'
                  : ''
            }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div class="content">
      <div class="title">外周动脉</div>
      <div class="item">
        <el-table
          :data="peripheralArteryData.table"
          style="width: 100%; margin-bottom: 16px"
          border
          :span-method="mergeCells"
        >
          <el-table-column prop="part" label="位置" align="center" />
          <el-table-column prop="type" label="" align="center" />
          <el-table-column prop="left" label="左" align="center" />
          <el-table-column prop="right" label="右" align="center" />
        </el-table>

        <el-descriptions :column="1" border style="margin-top: 10px">
          <el-descriptions-item label="检查所见">
            {{ peripheralArteryData.form.checkResult }}
          </el-descriptions-item>
          <el-descriptions-item label="医生意见">
            {{ peripheralArteryData.form.diagnosis }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { mapDataToTable } from '@/utils/cspUtils'

export default {
  name: 'InspectionEigthPart',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: [
        {
          position: '深感觉震动觉',
          item: '',
          right: '',
          left: '',
          type: 'input',
          rightDeepValue: 'right',
          leftDeepValue: 'left'
        },

        {
          position: '其他检查项目',
          item: '触压觉',
          right: '',
          left: '',
          type: 'radio',
          rightTouchResult: 'right',
          leftTouchResult: 'left'
        },
        {
          position: '其他检查项目',
          item: '疼痛觉',
          right: '',
          left: '',
          type: 'radio',
          rightPainResult: 'right',
          leftPainResult: 'left'
        },
        {
          position: '其他检查项目',
          item: '凉温觉',
          right: '',
          left: '',
          type: 'radio',
          rightCoolResult: 'right',
          leftCoolResult: 'left'
        },
        {
          position: '其他检查项目',
          item: '跟腱反射',
          right: '',
          left: '',
          type: 'radio',
          rightReflectResult: 'right',
          leftReflectResult: 'left'
        },

        {
          position: '足部皮肤温度检查',
          item: '足背（℃）',
          right: '',
          left: '',
          type: 'input',
          rightInstepTempValue: 'right',
          leftInstepTempValue: 'left'
        },
        {
          position: '足部皮肤温度检查',
          item: '内侧足弓（℃）',
          right: '',
          left: '',
          type: 'input',
          rightArchTempValue: 'right',
          leftArchTempValue: 'left'
        },
        {
          position: '足部皮肤温度检查',
          item: '足后跟（℃）',
          right: '',
          left: '',
          type: 'input',
          rightHeelTempValue: 'right',
          leftHeelTempValue: 'left'
        },
        {
          position: '足部皮肤温度检查',
          item: '前脚掌（℃）',
          right: '',
          left: '',
          type: 'input',
          rightForeTempValue: 'right',
          leftForeTempValue: 'left'
        },

        {
          position: '临床表现',
          item: '',
          right: '',
          left: '',
          type: 'input',
          rightClinicalFeature: 'right',
          leftClinicalFeature: 'left'
        }
      ],
      peripheralArteryTableData: [
        { part: '上臂（肱动脉）', type: 'mmHg', left: '', right: '', sbprb: 'right', sbplb: 'left' },
        { part: '上臂（肱动脉）', type: 'index', left: '', right: '', indexRb: 'right', indexLb: 'left' },
        { part: '脚踝（胫后动脉）', type: 'mmHg', left: '', right: '', sbprapta: 'right', sbplapta: 'left' },
        { part: '脚踝（胫后动脉）', type: 'index', left: '', right: '', indexRb: 'right', indexLapta: 'left' },
        { part: '脚踝（足背动脉）', type: 'mmHg', left: '', right: '', sbprafa: 'right', sbplafa: 'left' },
        { part: '脚踝（足背动脉）', type: 'index', left: '', right: '', indexRafa: 'right', indexLafa: 'left' }
      ]
    }
  },
  computed: {
    vibrationThresholdData() {
      const { data = {} } = this.reportInfo.itemList.find((item) => item.itemCode === 'VIBRATION_THRESHOLD') || {}

      return {
        table: mapDataToTable(data, cloneDeep(this.tableData)),
        form: {
          vtDescription: data.vtDescription,
          vtResult: data.vtResult
        }
      }
    },
    peripheralArteryData() {
      const { data = {} } = this.reportInfo.itemList.find((item) => item.itemCode === 'PAO') || {}
      return {
        table: mapDataToTable(data, cloneDeep(this.peripheralArteryTableData)),
        form: {
          checkResult: data.checkResult,
          diagnosis: data.diagnosis
        }
      }
    }
  },
  methods: {
    objectSpanMethod({ rowIndex, columnIndex }) {
      // 第一行：位置 + 检查项目合并为一个单元格（跨2列）
      if (rowIndex === 0) {
        if (columnIndex === 0) {
          return { rowspan: 1, colspan: 2 } // 合并前两列
        } else if (columnIndex === 1) {
          return { rowspan: 0, colspan: 0 } // 被合并隐藏
        }
        return { rowspan: 1, colspan: 1 } // 右足和左足照常显示
      }

      // 后续"位置"列合并逻辑
      if (columnIndex === 0) {
        if (rowIndex === 1) return { rowspan: 4, colspan: 1 }
        if (rowIndex >= 2 && rowIndex <= 4) return { rowspan: 0, colspan: 0 }
        if (rowIndex === 5) return { rowspan: 4, colspan: 1 }
        if (rowIndex >= 6 && rowIndex <= 8) return { rowspan: 0, colspan: 0 }
        if (rowIndex === 9) return { rowspan: 1, colspan: 1 }
      }
    },

    mergeCells({ rowIndex, columnIndex }) {
      // columnIndex：0=part, 1=type, 2=left, 3=right
      if (columnIndex === 0) {
        if (rowIndex % 2 === 0) {
          return { rowspan: 2, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      }
    }
  }
}
</script>

<style lang="scss">
.inspection-eigth-part {
  padding: 10px;
  .content {
    margin-top: 8px;
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    .el-table td.el-table__cell .cell {
      line-height: 1.6rem !important;
    }
  }
  @media print {
    // 增加选择器权重确保打印样式生效
    .content .el-table td.el-table__cell {
      padding: 0 !important;
    }
    .content .el-table td.el-table__cell .cell {
      line-height: 1.6rem !important;
    }
  }
}
</style>
