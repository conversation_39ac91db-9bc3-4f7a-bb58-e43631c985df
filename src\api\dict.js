import request from '@/utils/request'
// 新增字典目录
export function addDictionaryApi(data) {
  return request({
    url: '/cspapi/backend/sys/dictionary/folder',
    method: 'post',
    data
  })
}
// 字典目录列表(分页)
export function getDictionaryApi(data, token) {
  return request({
    url: '/cspapi/backend/sys/dictionary/folder/page',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}

// 字典目录列表（没有分页）
export function getDictionaryFolderListByModel(data, token) {
  return request({
    url: '/cspapi/backend/sys/dictionary/folder/listByModel',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}

// 删除字典目录
export function deleteDictionaryApi(id) {
  return request({
    url: `/cspapi/backend/sys/dictionary/folder/${id}`,
    method: 'delete'
  })
}

export function getfacePhoto(data) {
  return request({
    url: `/cspapi/backend/active/facePhoto/listByModel`,
    method: 'get',
    params: data
  })
}

// 修改字典目录
export function putDictionaryApi(data) {
  return request({
    url: `/cspapi/backend/sys/dictionary/folder`,
    data,
    method: 'put'
  })
}
// 删除字典目录
export function deleteDictionaryFolderApi(id) {
  return request({
    url: `/cspapi/backend/sys/dictionary/folder/${id}`,
    method: 'delete'
  })
}

// 根据moduleCode查询字典值list
export function getDictionaryValApi(code, token) {
  return request({
    url: `/cspapi/backend/sys/dictionary/listByModuleCode/${code}`,
    method: 'get',
    headers: {
      Authorization: token
    }
  })
}

// 根据moduleCode查询字典值list
export function getDictionaryPage(data, token) {
  return request({
    url: `/cspapi/backend/sys/dictionary/page`,
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}

export function getHistoryApi(data, token) {
  return request({
    url: `/cspapi/backend/visitCheck/questionnaires/getLastVisitQuestionnaireByUseMedicine`,
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}

// 新增字典值
export function addDictionaryValApi(data) {
  return request({
    url: '/cspapi/backend/sys/dictionary',
    method: 'post',
    data
  })
}
// 删除字典值
export function deleteDictionaryValApi(id) {
  return request({
    url: `/cspapi/backend/sys/dictionary/${id}`,
    method: 'delete'
  })
}
// 修改字典值
export function putDictionaryValApi(data) {
  return request({
    url: '/cspapi/backend/sys/dictionary',
    method: 'put',
    data
  })
}
// 获取心电图字典 大项
export function getEcgDictionaryApi(data, token) {
  return request({
    url: '/cspapi/backend/sys/dictionary/folder/page',
    method: 'get',
    params: { usage: 'ecg_result', pageNo: 1, pageSize: 99 },
    headers: {
      Authorization: token
    }
  })
}
// 字典目录(folder)+子级(字典)
export function getEcgDictAndChildApi(params) {
  return request({
    url: '/cspapi/backend/sys/dictionary/folder/list/children',
    method: 'get',
    params
  })
}
// 疾病列表(各种条件)
export function getDiseaseListByTypeApi(name, pageNo) {
  return request({
    url: '/cspapi/backend/sys/disease/page',
    method: 'get',
    params: { type: null, name, pageNo, pageSize: 30 }
  })
}

// 疾病列表(各种条件)
export function dictionaryExaminationItemApi(name, pageNo) {
  return request({
    url: '/cspapi/backend/sys/dictionary/listByModuleCode/examinationItem',
    method: 'get',
    params: { type: null, name, pageNo, pageSize: 30 }
  })
}
