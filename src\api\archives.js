import request from '@/utils/request'

// 分页查询居民档案
export function getUserDbList(data) {
  return request({
    url: '/cspapi/backend/base/user/page',
    method: 'post',
    data
  })
}

// 接诊
export function jzBypatientIdApi(data) {
  return request({
    url: '/cspapi/backend/reception/workbenches/detail/patientId',
    method: 'post',
    data
  })
}

// 根据ID查询患者基本信息
export function getUserDbDetail(data) {
  return request({
    url: '/cspapi/backend/base/user/detail',
    method: 'post',
    data
  })
}

// 根据患者ID查询用药记录
export function getUserDbMedicationRecord(data) {
  return request({
    url: '/cspapi/backend/base/user/medical/list/history',
    method: 'post',
    data
  })
}

// 根据患者ID查询高血压记录
export function getUserDbHypertensionRecord(data) {
  return request({
    url: '/cspapi/backend/base/user/pressure/list/history',
    method: 'post',
    data
  })
}

// 根据患者ID查询血糖记录
export function getUserDbBloodSugarRecord(data) {
  return request({
    url: '/cspapi/backend/base/user/sugar/list/history',
    method: 'post',
    data
  })
}

// 根据患者ID查询签约记录
export function getUserDbSignContractRecord(data) {
  return request({
    url: '/cspapi/backend/service/mind/record/list/history',
    method: 'post',
    data
  })
}

// 删除居民档案信息
export function deleteUserDb(data) {
  return request({
    url: '/cspapi/backend/base/user/remove',
    method: 'post',
    data
  })
}

// 根据ID查询患者测量信息
export function getUserDbMeasureInfo(data) {
  return request({
    url: '/cspapi/backend/base/user/measure/detail',
    method: 'post',
    data
  })
}

// 死亡人员名单
export function getDeathPersonList(data) {
  return request({
    url: '/cspapi/backend/base/user/death/page',
    method: 'post',
    data
  })
}

// 根据患者ID查询报告详情
export function getUserDbReportDetail(data) {
  return request({
    url: '/cspapi/backend/base/user/report/detail',
    method: 'post',
    data
  })
}

// 根据患者ID查询转诊记录
export function getUserDbReferralRecord(data) {
  return request({
    url: '/cspapi/backend/base/user/referral/list/history',
    method: 'post',
    data
  })
}

// 根据患者ID查询BNP记录
export function getUserDbBnpRecord(data) {
  return request({
    url: '/cspapi/backend/base/user/bnp/list/history',
    method: 'post',
    data
  })
}
