<!-- 目标管理 -->
<template>
  <div v-loading="$store.state.managePatient.loading" class="manage-by-objectives">
    <flag-component title="管理目标制定" />
    <div class="manage-by-objectives-content">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="170px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="管理周期：" prop="managePeriod">
              <el-date-picker
                v-model="form.managePeriod"
                type="daterange"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                unlink-panels
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                :disabled="isTargetInspection"
                @change="handleManagePeriodChange"
              />
            </el-form-item>
          </el-col>

          <el-col v-if="!isTargetInspection" :span="8">
            <el-form-item label=" " label-width="20px">
              <el-radio-group v-model="form.monthCount" @change="handleMonthCountChange">
                <el-radio-button :label="1">1个月</el-radio-button>
                <el-radio-button :label="2">2个月</el-radio-button>
                <el-radio-button :label="3">3个月</el-radio-button>
                <el-radio-button :label="6">6个月</el-radio-button>
                <el-radio-button :label="12">1年</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col
            v-if="!isTargetInspection && $store.state.managePatient.managePatientData.disease !== 'fangchan'"
            :span="8"
          >
            <el-form-item label="管理病种：" label-width="120px">
              <el-checkbox-group v-model="form.manageDisease">
                <el-checkbox label="1">糖尿病</el-checkbox>
                <el-checkbox label="2">高血压</el-checkbox>
                <el-checkbox label="3">高血脂</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8" style="text-align: center; font-weight: bold; font-size: 16px"> 基线值(mmol/L) </el-col>

          <el-col
            :span="8"
            :push="1"
            style="text-align: center; font-weight: bold; font-size: 16px; margin-bottom: 16px"
          >
            目标值
          </el-col>
          <el-col
            v-if="isTargetInspection"
            :span="8"
            :push="1"
            style="text-align: center; font-weight: bold; font-size: 16px"
          >
            检测值
          </el-col>
        </el-row>

        <el-row>
          <el-col v-if="form.manageDisease.includes('1')" :span="8">
            <el-form-item label="空腹血糖(mmol/L)：">
              <el-input-number
                v-model="form.baseKfSugar"
                controls-position="right"
                :disabled="isTargetInspection"
                style="width: 80%"
              />
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('1')" :span="10">
            <el-form-item label=" " label-width="20px">
              <div class="item">
                <el-input-number
                  v-model="form.minKfSugar"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
                <span style="width: 12%; text-align: center">≤ X ≤</span>
                <el-input-number
                  v-model="form.maxKfSugar"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
              </div>
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('1') && isTargetInspection" :span="6">
            <el-form-item label=" " label-width="80px">
              <el-input-number v-model="form.kfSugarValue" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('1')" :span="8">
            <el-form-item label="餐后2h血糖(mmol/L)：">
              <el-input-number
                v-model="form.baseChTwoSugar"
                controls-position="right"
                :disabled="isTargetInspection"
                style="width: 80%"
              />
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('1')" :span="10">
            <el-form-item label=" " label-width="20px">
              <div class="item">
                <el-input-number
                  v-model="form.minChTwoSugar"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
                <span style="width: 12%; text-align: center">≤ X ≤</span>
                <el-input-number
                  v-model="form.maxChTwoSugar"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
              </div>
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('1') && isTargetInspection" :span="6">
            <el-form-item label=" " label-width="80px">
              <el-input-number v-model="form.chTwoSugar" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('1')" :span="8">
            <el-form-item label="糖化血红蛋白(%)：">
              <el-input-number
                v-model="form.baseSugarHemoglobin"
                controls-position="right"
                :disabled="isTargetInspection"
                style="width: 80%"
              />
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('1')" :span="10">
            <el-form-item label=" " label-width="20px">
              <div class="item">
                <el-input-number
                  v-model="form.minSugarHemoglobin"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
                <span style="width: 12%; text-align: center">≤ X ≤</span>
                <el-input-number
                  v-model="form.maxSugarHemoglobin"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
              </div>
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('1') && isTargetInspection" :span="6">
            <el-form-item label=" " label-width="80px">
              <el-input-number v-model="form.sugarHemoglobin" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col v-if="$store.state.managePatient.managePatientData.disease.includes('fangchan')" :span="8">
            <el-form-item label="静息状态下心率次/分：">
              <el-input-number
                v-model="form.baseHeartRate"
                controls-position="right"
                :disabled="isTargetInspection"
                style="width: 80%"
              />
            </el-form-item>
          </el-col>

          <el-col v-if="$store.state.managePatient.managePatientData.disease.includes('fangchan')" :span="10">
            <el-form-item label=" " label-width="20px">
              <div class="item">
                <el-input-number
                  v-model="form.minHeartRate"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
                <span style="width: 12%; text-align: center">≤ X ≤</span>
                <el-input-number
                  v-model="form.maxHeartRate"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
              </div>
            </el-form-item>
          </el-col>

          <el-col
            v-if="$store.state.managePatient.managePatientData.disease.includes('fangchan') && isTargetInspection"
            :span="6"
          >
            <el-form-item label=" " label-width="80px">
              <el-input-number v-model="form.heartRate" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('2')" :span="8">
            <el-form-item label="收缩压(mmHg)：">
              <el-input-number
                v-model="form.baseSp"
                controls-position="right"
                :disabled="isTargetInspection"
                style="width: 80%"
              />
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('2')" :span="10">
            <el-form-item label=" " label-width="20px">
              <div class="item">
                <el-input-number
                  v-model="form.minSp"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
                <span style="width: 12%; text-align: center">≤ X ≤</span>
                <el-input-number
                  v-model="form.maxSp"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
              </div>
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('2') && isTargetInspection" :span="6">
            <el-form-item label=" " label-width="80px">
              <el-input-number v-model="form.sp" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('2')" :span="8">
            <el-form-item label="舒张压(mmHg)：">
              <el-input-number
                v-model="form.baseDp"
                controls-position="right"
                :disabled="isTargetInspection"
                style="width: 80%"
              />
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('2')" :span="10">
            <el-form-item label=" " label-width="20px">
              <div class="item">
                <el-input-number
                  v-model="form.minDp"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
                <span style="width: 12%; text-align: center">≤ X ≤</span>
                <el-input-number
                  v-model="form.maxDp"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
              </div>
            </el-form-item>
          </el-col>

          <el-col v-if="form.manageDisease.includes('2') && isTargetInspection" :span="6">
            <el-form-item label=" " label-width="80px">
              <el-input-number v-model="form.dp" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col v-if="$store.state.managePatient.managePatientData.disease !== 'fangchan'" :span="8">
            <el-form-item label="体质指数(kg/㎡)：">
              <el-input-number
                v-model="form.baseBmi"
                controls-position="right"
                :disabled="isTargetInspection"
                style="width: 80%"
              />
            </el-form-item>
          </el-col>

          <el-col v-if="$store.state.managePatient.managePatientData.disease !== 'fangchan'" :span="10">
            <el-form-item label=" " label-width="20px">
              <div class="item">
                <el-input-number
                  v-model="form.minBmi"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
                <span style="width: 12%; text-align: center">≤ X ≤</span>
                <el-input-number
                  v-model="form.maxBmi"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
              </div>
            </el-form-item>
          </el-col>

          <el-col
            v-if="isTargetInspection && $store.state.managePatient.managePatientData.disease !== 'fangchan'"
            :span="6"
          >
            <el-form-item label=" " label-width="80px">
              <el-input-number v-model="form.bmi" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              form.manageDisease.includes('3') && $store.state.managePatient.managePatientData.disease !== 'fangchan'
            "
            :span="8"
          >
            <el-form-item label="总胆固醇(mmol/L)：">
              <el-input-number
                v-model="form.baseTc"
                controls-position="right"
                :disabled="isTargetInspection"
                style="width: 80%"
              />
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              form.manageDisease.includes('3') && $store.state.managePatient.managePatientData.disease !== 'fangchan'
            "
            :span="10"
          >
            <el-form-item label=" " label-width="20px">
              <div class="item">
                <el-input-number
                  v-model="form.minTc"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
                <span style="width: 12%; text-align: center">≤ X ≤</span>
                <el-input-number
                  v-model="form.maxTc"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
              </div>
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              form.manageDisease.includes('3') &&
                isTargetInspection &&
                $store.state.managePatient.managePatientData.disease !== 'fangchan'
            "
            :span="6"
          >
            <el-form-item label=" " label-width="80px">
              <el-input-number v-model="form.tc" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              form.manageDisease.includes('3') && $store.state.managePatient.managePatientData.disease !== 'fangchan'
            "
            :span="8"
          >
            <el-form-item label="甘油三酯(mmol/L)：">
              <el-input-number
                v-model="form.baseTg"
                controls-position="right"
                :disabled="isTargetInspection"
                style="width: 80%"
              />
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              form.manageDisease.includes('3') && $store.state.managePatient.managePatientData.disease !== 'fangchan'
            "
            :span="10"
          >
            <el-form-item label=" " label-width="20px">
              <div class="item">
                <el-input-number
                  v-model="form.minTg"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
                <span style="width: 12%; text-align: center">≤ X ≤</span>
                <el-input-number
                  v-model="form.maxTg"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
              </div>
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              form.manageDisease.includes('3') &&
                isTargetInspection &&
                $store.state.managePatient.managePatientData.disease !== 'fangchan'
            "
            :span="6"
          >
            <el-form-item label=" " label-width="80px">
              <el-input-number v-model="form.tg" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              form.manageDisease.includes('3') && $store.state.managePatient.managePatientData.disease !== 'fangchan'
            "
            :span="8"
          >
            <el-form-item label="高密度脂蛋白胆固醇：">
              <el-input-number
                v-model="form.baseHdlc"
                controls-position="right"
                :disabled="isTargetInspection"
                style="width: 80%"
              />
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              form.manageDisease.includes('3') && $store.state.managePatient.managePatientData.disease !== 'fangchan'
            "
            :span="10"
          >
            <el-form-item label=" " label-width="20px">
              <div class="item">
                <el-input-number
                  v-model="form.minHdlc"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
                <span style="width: 12%; text-align: center">≤ X ≤</span>
                <el-input-number
                  v-model="form.maxHdlc"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
              </div>
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              form.manageDisease.includes('3') &&
                isTargetInspection &&
                $store.state.managePatient.managePatientData.disease !== 'fangchan'
            "
            :span="6"
          >
            <el-form-item label=" " label-width="80px">
              <el-input-number v-model="form.hdlc" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              form.manageDisease.includes('3') && $store.state.managePatient.managePatientData.disease !== 'fangchan'
            "
            :span="8"
          >
            <el-form-item label="低密度脂蛋白胆固醇：">
              <el-input-number
                v-model="form.baseLdlc"
                controls-position="right"
                :disabled="isTargetInspection"
                style="width: 80%"
              />
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              form.manageDisease.includes('3') && $store.state.managePatient.managePatientData.disease !== 'fangchan'
            "
            :span="10"
          >
            <el-form-item label=" " label-width="20px">
              <div class="item">
                <el-input-number
                  v-model="form.minLdlc"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
                <span style="width: 12%; text-align: center">≤ X ≤</span>
                <el-input-number
                  v-model="form.maxLdlc"
                  controls-position="right"
                  :disabled="isTargetInspection"
                  style="width: 60%"
                />
              </div>
            </el-form-item>
          </el-col>

          <el-col
            v-if="
              form.manageDisease.includes('3') &&
                isTargetInspection &&
                $store.state.managePatient.managePatientData.disease !== 'fangchan'
            "
            :span="6"
          >
            <el-form-item label=" " label-width="80px">
              <el-input-number v-model="form.ldlc" controls-position="right" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import flagComponent from '@/components/flagComponent'
import dayjs from 'dayjs'
import { getTargetInspection } from '@/api/standardizedManage'

export default {
  name: 'ManageByObjectives',
  components: {
    flagComponent
  },
  props: {
    isTargetInspection: {
      type: Boolean,
      default: false
    },
    historyId: {
      type: String,
      default: ''
    }
  },
  data() {
    const { manageDate } = this.$store.state.managePatient.managePatientData
    const managePeriod = dayjs(manageDate).add(3, 'month').format('YYYY-MM-DD')

    return {
      indicatorFields: [
        'KfSugar',
        'ChTwoSugar',
        'SugarHemoglobin',
        'Sp',
        'Dp',
        'Bmi',
        'Tc',
        'Tg',
        'Hdlc',
        'Ldlc',
        'HeartRate'
      ],
      form: {
        ...this.getBaseForm(manageDate, managePeriod)
      },
      rules: {
        managePeriod: [{ required: true, message: '请选择管理周期' }]
      }
    }
  },
  async created() {
    this.getManagePatientTargetData()
    this.getTargetInspectionFn()
  },
  methods: {
    async getManagePatientTargetData() {
      const { id } = { id: this.historyId || this.$route.query.id }
      const { patientId } = this.$store.state.managePatient.managePatientData
      const res = await this.$store.dispatch('managePatient/getStandardizedManageTargetDetailFn', {
        smrId: id,
        patientId
      })

      if (res) {
        this.setFormFromResponse(res)
      }
    },

    // 生成默认指标字段（base, min, max）
    generateDefaultIndicators(fields = []) {
      const result = {}
      fields.forEach((key) => {
        result[`base${key}`] = undefined
        result[`min${key}`] = undefined
        result[`max${key}`] = undefined
      })
      return result
    },

    // 默认表单结构
    getBaseForm(startDate, endDate) {
      return {
        managePeriod: [startDate, endDate],
        monthCount: 3,
        manageDisease: ['1', '2', '3'],
        kfSugarValue: undefined,
        chTwoSugar: undefined,
        sugarHemoglobin: undefined,
        heartRate: undefined,
        sp: undefined,
        dp: undefined,
        bmi: undefined,
        tc: undefined,
        tg: undefined,
        hdlc: undefined,
        ldlc: undefined,
        ...this.generateDefaultIndicators(this.indicatorFields)
      }
    },

    // 接口数据填充到表单中
    setFormFromResponse(res) {
      const newForm = {
        ...this.form,
        id: res.id,
        managePeriod:
          res.manageStartDate && res.manageEndDate ? [res.manageStartDate, res.manageEndDate] : this.form.managePeriod,
        monthCount: res.monthCount || 3,
        manageDisease: res.manageDisease ? res.manageDisease.split(',') : ['1', '2', '3']
      }

      this.indicatorFields.forEach((field) => {
        newForm[`base${field}`] =
          res[`base${field}`] === '' || res[`base${field}`] === null ? undefined : res[`base${field}`]
        newForm[`min${field}`] =
          res[`min${field}`] === '' || res[`min${field}`] === null ? undefined : res[`min${field}`]
        newForm[`max${field}`] =
          res[`max${field}`] === '' || res[`max${field}`] === null ? undefined : res[`max${field}`]
      })

      this.form = newForm
    },

    handleMonthCountChange(value) {
      const managePeriod = dayjs(this.form.managePeriod[0]).add(value, 'month').format('YYYY-MM-DD')
      this.form.managePeriod = [this.form.managePeriod[0], managePeriod]
    },

    handleManagePeriodChange(value) {
      this.form.monthCount = ''
    },

    // 保存处理
    async handleSave() {
      const { disease } = this.$store.state.managePatient.managePatientData

      const result = {
        name: '管理目标制定',
        success: false,
        data: {
          ...this.form,
          manageStartDate: this.form.managePeriod ? this.form.managePeriod[0] : '',
          manageEndDate: this.form.managePeriod ? this.form.managePeriod[1] : '',
          manageDisease: disease === 'fangchan' ? '' : this.form.manageDisease.join(','),
          id: this.form.id
        }
      }

      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn('管理目标制定失败', err)
        result.success = false
      }

      return result
    },

    // 目标检验查询
    async getTargetInspectionFn() {
      if (!this.isTargetInspection) return
      const res = await getTargetInspection({
        smrId: this.historyId || this.$route.query.id
      })
      if (res.code === 200 && res.data) {
        this.form.kfSugarValue = res.data.kfSugarValue
        this.form.chTwoSugar = res.data.chTwoSugar
        this.form.sugarHemoglobin = res.data.sugarHemoglobin
        this.form.heartRate = res.data.heartRate
        this.form.sp = res.data.sp
        this.form.dp = res.data.dp
        this.form.bmi = res.data.bmi
        this.form.tc = res.data.tc
        this.form.tg = res.data.tg
        this.form.hdlc = res.data.hdlc
        this.form.ldlc = res.data.ldlc
        this.form.id = res.data.id
      }
    },

    // 目标检验保存
    async handleTargetInspectionSave() {
      const result = {
        name: '目标检验',
        success: false,
        data: {
          kfSugarValue: this.form.kfSugarValue,
          chTwoSugar: this.form.chTwoSugar,
          sugarHemoglobin: this.form.sugarHemoglobin,
          heartRate: this.form.heartRate,
          sp: this.form.sp,
          dp: this.form.dp,
          bmi: this.form.bmi,
          tc: this.form.tc,
          tg: this.form.tg,
          hdlc: this.form.hdlc,
          ldlc: this.form.ldlc,
          id: this.form.id
        }
      }

      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn('目标检验失败', err)
        result.success = false
      }

      return result
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.manage-by-objectives-content {
  margin-top: 16px;
  padding: 0 16px;
  .item {
    display: flex;
    align-items: center;
    ::v-deep .el-input-group__append {
      width: 50px !important;
      text-align: center;
      padding: 0 10px;
    }
  }
}
</style>
