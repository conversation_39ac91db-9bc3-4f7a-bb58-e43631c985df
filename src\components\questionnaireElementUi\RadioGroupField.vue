<template>
  <!-- long-label 是用于处理长标签的样式 -->
  <el-form-item :label="item.label" :prop="item.prop" :class="item.labelClass">
    <div :class="item.class">
      <el-radio-group v-model="localValue">
        <el-radio v-for="opt in item.options" :key="opt.value" :label="opt.value">{{ opt.label }}</el-radio>
      </el-radio-group>
    </div>
  </el-form-item>
</template>

<script>
export default {
  name: 'RadioGroupField',
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'input'
  },
  props: ['item', 'value'],
  computed: {
    localValue: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  }
}
</script>
