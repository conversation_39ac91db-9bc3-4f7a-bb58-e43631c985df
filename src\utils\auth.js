import Cookies from 'js-cookie'

const Token<PERSON>ey = 'Admin-Token'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}
export function getUserId(id) {
  return Cookies.get('userId')
}
export function setUserId(id) {
  return Cookies.set('userId', id)
}
export function removeUserId() {
  return Cookies.remove('userId')
}
/**
 * @description: 删除缓存中的登录日志
 * @author: LiSuwan
 * @Date: 2024-09-24 14:18:40
 */
export function removeLoginLog() {
  return sessionStorage.removeItem('loginLog')
}
