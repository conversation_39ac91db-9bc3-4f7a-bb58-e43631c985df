<template>
  <el-form-item :label="item.label" :prop="item.prop">
    <div :class="item.class">
      <el-input
        v-model="localValue"
        :placeholder="item.placeholder"
        :disabled="item.disabled"
        :readonly="item.readonly"
        :clearable="item.clearable"
        :size="item.size"
        :maxlength="item.maxlength"
        :prefix-icon="item.prefixIcon"
        :suffix-icon="item.suffixIcon"
        :style="{ width: item.width || '100%' }"
        @blur="handleBlur"
        @focus="handleFocus"
        @change="handleChange"
        @input="handleInput"
      >
        <template v-if="item.prepend" slot="prepend">
          {{ item.prepend }}
        </template>
        <template v-if="item.append" slot="append">
          {{ item.append }}
        </template>
      </el-input>
    </div>
  </el-form-item>
</template>

<script>
export default {
  name: 'CustomInputNumber',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: ['item', 'value'],
  computed: {
    localValue: {
      get() {
        return this.value
      },
      set(newValue) {
        this.handleInput(newValue)
      }
    }
  },
  methods: {
    // 处理输入事件
    handleInput(value) {
      // 使用cspUtils中的validateOnlyNumber函数处理输入值
      let processedValue = this.$validateOnlyNumber(value)

      // 根据允许的小数位数处理
      if (this.item.decimalPlaces !== undefined && processedValue.includes('.')) {
        const parts = processedValue.split('.')
        if (parts[1] && parts[1].length > this.item.decimalPlaces) {
          parts[1] = parts[1].substring(0, this.item.decimalPlaces)
          processedValue = parts.join('.')
        }
      }

      // 如果不允许小数，移除小数点及其后面的数字
      if (this.item.allowDecimal === false) {
        processedValue = processedValue.split('.')[0]
      }

      // 如果不允许负数，移除负号
      if (this.item.allowNegative !== true && processedValue.startsWith('-')) {
        processedValue = processedValue.substring(1)
      }

      // 向父组件发送更新后的值
      this.$emit('input', processedValue)
    },

    // 处理失焦事件
    handleBlur(event) {
      this.$emit('blur', event)
    },

    // 处理聚焦事件
    handleFocus(event) {
      this.$emit('focus', event)
    },

    // 处理变更事件
    handleChange(value) {
      this.$emit('change', value)
    }
  }
}
</script>

<style scoped>
.custom-input-number {
  display: inline-block;
  width: 100%;
}
</style>
