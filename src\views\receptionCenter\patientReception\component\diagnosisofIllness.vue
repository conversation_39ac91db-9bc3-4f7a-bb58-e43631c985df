<!-- 病情诊断 -->
<template>
  <div v-loading="$store.state.receptionWorkbench.loading" class="diagnosis-of-illness">
    <questionnaire-tags :questionnaire-list="questionnaireList" @click="handleClick" />

    <general-diagnosis-of-ollness
      v-show="questionnaireId === 'tnb'"
      ref="tnbFormRef"
      :radio-list="tnbRadioList"
      :table-object="tnbTableObject"
      :form="tnbForm"
      :result-field="resultFields[0]"
      :desc-field="descFields[0]"
      desc="糖尿病"
    />

    <general-diagnosis-of-ollness
      v-show="questionnaireId === 'gxy'"
      ref="gxyFormRef"
      :radio-list="gxyRadioList"
      :table-object="gxyTableObject"
      :form="gxyForm"
      :result-field="resultFields[1]"
      :desc-field="descFields[1]"
      desc="高血压"
    />

    <general-diagnosis-of-ollness
      v-show="questionnaireId === 'COPD'"
      ref="copdFormRef"
      :radio-list="copdRadioList"
      :table-object="copdTableObject"
      :form="copdForm"
      :result-field="resultFields[2]"
      :desc-field="descFields[2]"
      desc="慢阻肺"
    />

    <general-diagnosis-of-ollness
      v-show="questionnaireId === 'fangchan'"
      ref="fcFormRef"
      :radio-list="fcRadioList"
      :table-object="fcTableObject"
      :form="fcForm"
      :result-field="resultFields[3]"
      :desc-field="descFields[3]"
      desc="房颤"
    />
  </div>
</template>

<script>
import QuestionnaireTags from '@/components/questionnaireTags/index.vue'
import GeneralDiagnosisOfOllness from '../componentProject/generalDiagnosisOfOllness.vue'
import {
  tnbForm,
  gxyForm,
  copdForm,
  fcForm,
  tnbRadioList,
  gxyRadioList,
  copdRadioList,
  fcRadioList,
  tnbTableObject,
  gxyTableObject,
  copdTableObject,
  fcTableObject
} from './diagnosisofIllness'
import { saveDiagnosisOfIllness } from '@/api/receptionWorkbench'
import { cloneDeep } from 'lodash'

export default {
  name: 'DiagnosisOfIllness',
  components: {
    QuestionnaireTags,
    GeneralDiagnosisOfOllness
  },
  props: {
    historyId: {
      type: String,
      default: ''
    }
  },
  data() {
    const questionnaireList = cloneDeep(this.$store.state.receptionWorkbench.receptionWorkbenchData.diseaseList)
    return {
      questionnaireList,
      questionnaireId: questionnaireList[0].id,
      resultFields: ['tnbResult', 'gxyResult', 'copdResult', 'fcResult'],
      descFields: ['tnbDescription', 'gxyDescription', 'copdDescription', 'fcDescription'],
      tnbForm,
      gxyForm,
      copdForm,
      fcForm,
      tnbRadioList,
      gxyRadioList,
      copdRadioList,
      fcRadioList,
      tnbTableObject,
      gxyTableObject,
      copdTableObject,
      fcTableObject
    }
  },
  async created() {
    const data = await this.$store.dispatch('receptionWorkbench/getDiagnosisOfIllnessData', {
      rrId: this.historyId || this.$route.query.id
    })
    this.$refs.tnbFormRef.initData(data)
    this.$refs.gxyFormRef.initData(data)
    this.$refs.copdFormRef.initData(data)
    this.$refs.fcFormRef.initData(data)
  },
  methods: {
    handleClick(item) {
      this.questionnaireId = item.id
    },
    async handleDiagnosisOfIllnessSave(type) {
      const rrId = this.$route.query.id
      const id = (this.$store.getters.diagnosisOfIllnessData && this.$store.getters.diagnosisOfIllnessData.id) || ''
      let status =
        (this.$store.getters.diagnosisOfIllnessData && this.$store.getters.diagnosisOfIllnessData.status) || 1

      if (type === 'skip') {
        status = 9
        await saveDiagnosisOfIllness({ rrId, id, status })
        this.$store.dispatch('receptionWorkbench/getDiagnosisOfIllnessData', { rrId })
        return status
      }

      if (type === 'edit') {
        status = 1
        await saveDiagnosisOfIllness({ rrId, id, status })
        this.$store.dispatch('receptionWorkbench/getDiagnosisOfIllnessData', { rrId })
        return status
      }

      if (status === 9) {
        return status
      }

      // ==== 1. 表单保存 ====
      const refs = {
        tnb: this.$refs.tnbFormRef,
        gxy: this.$refs.gxyFormRef,
        copd: this.$refs.copdFormRef,
        fc: this.$refs.fcFormRef
      }

      const results = {}
      for (const key in refs) {
        // eslint-disable-next-line
        results[key] = await refs[key].handleSave()
      }

      // ==== 2. 处理未参与的问卷 ====
      const activeQuestionnaires = new Set(
        this.questionnaireList.map((item) => {
          if (item.id === 'COPD') {
            return 'copd'
          }
          if (item.id === 'fangchan') {
            return 'fc'
          }
          return item.id
        })
      )
      for (const key in results) {
        if (!activeQuestionnaires.has(key)) {
          results[key].success = true
          results[key].data = {}
        }
      }

      // ==== 3. 合并参数 ====
      const params = Object.values(results).reduce((acc, cur) => {
        return { ...acc, ...cur.data }
      }, {})

      // ==== 4. 校验必填项 ====
      const warnings = Object.entries(results)
        .filter(([_, result]) => !result.success)
        .map(([_, result]) => `${result.name}存在必填未填项！`)

      if (warnings.length && type === 'next') {
        for (const msg of warnings) {
          this.$message.warning(msg)
          // eslint-disable-next-line
          await new Promise(resolve => setTimeout(resolve, 500))
        }
        status = 1
      } else if (!warnings.length) {
        status = 5
      }

      // console.log('params', params)
      // ==== 5. 提交保存 ====
      const res = await saveDiagnosisOfIllness({ rrId, id, status, ...params })

      if (res.code === 200) {
        if (type === 'save') {
          this.$message.success('保存成功')
        }
        this.$store.dispatch('receptionWorkbench/getDiagnosisOfIllnessData', { rrId })
        return status
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
