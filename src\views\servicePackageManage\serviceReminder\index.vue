<!-- 服务提醒 -->
<template>
  <div class="service-reminder">
    <el-card class="service-reminder-search">
      <el-form :model="queryParams" label-width="120px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="姓名：">
              <el-input v-model="queryParams.keyword" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="服务包名称：">
              <el-select v-model="queryParams.servicePackageId" placeholder="请选择服务包名称">
                <el-option v-for="item in servicePackageList" :key="item.id" :label="item.spName" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="service-reminder-table">
      <el-radio-group v-model="queryParams.type" style="margin-bottom: 10px" @change="handleRadioChange">
        <el-radio-button :label="1">即将随访</el-radio-button>
        <el-radio-button :label="2">已经过期</el-radio-button>
      </el-radio-group>
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        :height="'calc(100% - 100px)'"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #sex="{ row }">
          <span>{{ genderTransform(row.sex) }}</span>
        </template>

        <template #idCard="{ row }">
          <EncryptionStr :cipher-text="row.idCard" :replace="row.idCardReplace" />
        </template>

        <template #phone="{ row }">
          <EncryptionStr :cipher-text="row.phone" :replace="row.phoneReplace" />
        </template>

        <!-- 操作 -->
        <template #operation="{ row }">
          <el-button type="text" @click="handleClick(row)">履约</el-button>
        </template>
      </base-table>
    </el-card>
  </div>
</template>

<script>
import { getServiceReminder } from '@/api/serviceReminder'
import { completePerformance } from '@/api/individualSigning'
import { getServicePackageList } from '@/api/servicePackageManage'
import { localCache } from '@/utils/cache'
import { genderTransform } from '@/utils/cspUtils'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import EncryptionStr from '@/components/encryptionStr/index.vue'

export default {
  name: 'ServiceReminder',
  components: {
    BaseTable,
    EncryptionStr
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        keyword: '',
        servicePackageId: '',
        type: 1
      },
      servicePackageList: [],
      recordInfo: {},
      columns: [
        { prop: 'name', label: '姓名' },
        { prop: 'sex', label: '性别', slot: 'sex' },
        { prop: 'idCard', label: '身份证号', slot: 'idCard' },
        { prop: 'phone', label: '手机号', slot: 'phone' },
        { prop: 'doctorName', label: '家庭医生' },
        { prop: 'packageName', label: '签约服务包' },
        { prop: 'itemName', label: '服务包小项' },
        { prop: 'planDate', label: '计划服务时间' },
        { prop: 'operation', label: '操作', slot: 'operation' }
      ]
    }
  },
  created() {
    this.getServicePackageName()
  },
  methods: {
    genderTransform,
    async getTableList(params) {
      return await getServiceReminder(params)
    },

    // 服务包名称
    getServicePackageName() {
      getServicePackageList({
        pageNo: 1,
        pageSize: 10000,
        departCode: localCache.getCache('userInfo').departCode || ''
      }).then((res) => {
        this.servicePackageList = res.data.list
      })
    },

    handleRadioChange(val) {
      this.queryParams.type = val
      this.handleSearch()
    },

    handleClick(row) {
      this.$confirm('是否确认履约？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          completePerformance({ id: row.id }).then((res) => {
            if (res.code === 200) {
              this.$message.success('履约成功')
              this.handleSearch()
            }
          })
        })
        .catch(() => {
          this.$message.info('已取消')
        })
    },

    handleReset() {
      this.queryParams = {
        keyword: '',
        servicePackageId: '',
        type: 1,
        pageNo: 1,
        pageSize: 20
      }
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.service-reminder {
  display: flex;
  flex-direction: column;
  height: 100%;
  .service-reminder-search {
    margin: 16px;
    height: 77px;
  }
  .service-reminder-table {
    margin: 0 16px;
    flex: 1;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
}
</style>
