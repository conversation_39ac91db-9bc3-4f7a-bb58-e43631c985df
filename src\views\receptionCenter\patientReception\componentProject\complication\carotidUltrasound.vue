<!-- 颈动脉超声检查 -->
<template>
  <div class="carotid-ultrasound">
    <div class="carotid-ultrasound-header">
      <div class="left">
        <div v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail'" class="audit-status">
          <audit-status v-if="auditInfo.status === 5" status="pass" />

          <audit-status v-if="auditInfo.status === 9" status="reject" :reason="auditInfo.auditResult" />

          <audit-status v-if="auditInfo.status === 1" status="pending" />
        </div>
        <div
          v-if="
            $route.path !== '/qualityControl/ultrasonicQualityControl/detail' &&
              (form.attachmentPhotoUrl || form.attachmentVideoUrl.length > 0) &&
              (auditInfo.status === 5 || auditInfo.status === 9)
          "
          class="btn"
        >
          <a style="color: #0a86c8" @click="handleImageAudit"> 影像质控 </a>
        </div>
      </div>

      <div class="right">
        <div v-if="showDeviceCode($route.path)" class="device-barcode">
          <Barcode :code="`${getDeviceCode($route.path, getSourceData())}2`" />
        </div>
      </div>
    </div>

    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="内-中膜厚度：">
        <custom-input-number v-model="form.imt" style="width: 30%">
          <template #append>mm</template>
        </custom-input-number>
      </el-form-item>

      <el-form-item label="检查印象：">
        <el-input v-model="form.cuImpression" type="textarea" :rows="3" style="width: 50%" />
      </el-form-item>

      <el-form-item label="检查所见：">
        <el-input v-model="form.cuFinding" type="textarea" :rows="3" style="width: 50%" />
      </el-form-item>

      <CheckboxGroupField v-model="form.cuResult" :item="checkboxItem" />

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail'" label="上传图片：">
            <custom-upload v-model="form.attachmentPhotoUrl" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail'" label="上传视频：">
            <UploadVideo v-model="form.attachmentVideoUrl" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item
            v-if="auditInfo.status === 5 && $route.path !== '/qualityControl/ultrasonicQualityControl/detail'"
            label="质控结论："
          >
            <el-input v-model="auditInfo.auditResult" type="textarea" :rows="3" :disabled="true" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail' && auditInfo.status !== 1">
      <span style="margin-right: 130px; font-size: 14px">质控人：{{ auditInfo.auditName }}</span>
      <span style="font-size: 14px">质控时间：{{ auditInfo.auditTime }}</span>
    </div>

    <!-- 影像质控 -->
    <image-quality-control ref="imageQualityControl" :detail="imageQualityControl" :disabled="true" />
  </div>
</template>

<script>
import { getDeviceCode, showDeviceCode } from '@/utils/cspUtils'
import UploadVideo from '@/components/uploadVideo/index.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'
import AuditStatus from '@/components/auditStatus/index.vue'
import CustomUpload from '@/components/customUpload/index.vue'
import Barcode from '@/components/barcode/barcode.vue'
import ImageQualityControl from '@/components/imageQualityControl/index.vue'

export default {
  name: 'CarotidUltrasound',
  components: { CheckboxGroupField, UploadVideo, AuditStatus, CustomUpload, Barcode, ImageQualityControl },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      checkboxItem: {
        label: '检查结果：',
        type: 'checkbox',
        required: true,
        mutuallyExclusive: true, // 互斥
        prop: 'cuResult',
        options: [
          { label: '未见明显异常', value: '1' },
          { label: '斑块', value: '2' },
          { label: '狭窄', value: '3' },
          { label: '硬化', value: '4' },
          { label: '毛躁', value: '5' }
        ]
      },
      form: {
        imt: '',
        cuImpression: '',
        cuFinding: '',
        cuResult: [],
        attachmentPhotoUrl: '',
        attachmentVideoUrl: []
      },
      rules: {
        cuResult: [{ required: true, message: '请选择检查结果' }]
      },
      auditInfo: {},
      imageQualityControl: {}
    }
  },
  methods: {
    showDeviceCode,
    getDeviceCode,
    getSourceData() {
      return this.$route.path === '/receptionCenter/patientReception'
        ? this.$store.getters.complicationsScreeningData
        : this.$store.state.patientExamination.patientExaminationData
    },
    initData(data) {
      this.imageQualityControl = {
        item: this.itemTemp && this.itemTemp.value,
        data: {
          recordId: data.recordId,
          patientId: data.patientId
        }
      }
      this.auditInfo = {
        status: data && data.status,
        auditResult: data && data.auditResult,
        auditName: data && data.auditName,
        auditTime: data && data.auditTime
      }
      this.form = {
        imt: data.imt,
        cuImpression: data.cuImpression,
        cuFinding: data.cuFinding,
        cuResult: data.cuResult ? data.cuResult.split(',') : [],
        id: data.id,
        attachmentPhotoUrl: data.attachmentPhotoUrl,
        attachmentVideoUrl: data.attachmentVideoUrl ? data.attachmentVideoUrl.split(',') : []
      }
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate()
      })
    },
    async handleSave() {
      const result = {
        name: `${this.itemTemp.label}`,
        success: false,
        data: {
          ...this.form,
          cuResult: this.form.cuResult.join(','),
          attachmentVideoUrl: this.form.attachmentVideoUrl.join(','),
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    },

    async handleImageAudit() {
      const params = {
        recordId: this.imageQualityControl.data.recordId,
        module: 'cs',
        item: this.imageQualityControl.item,
        patientId: this.imageQualityControl.data.patientId
      }
      const res = await this.$refs.imageQualityControl.getImageFileListFn(params)
      if (res.code === 200) {
        this.$refs.imageQualityControl.visible = true
      }
    }
  }
}
</script>

<style scoped lang="scss">
.carotid-ultrasound {
  .carotid-ultrasound-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      width: 65%;
      display: flex;
      align-items: center;
      .audit-status {
        width: 60%;
      }
    }
    .right {
      width: 35%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }

  .device-barcode {
    margin-left: auto;
  }
}
</style>
