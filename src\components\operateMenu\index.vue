<template>
  <div class="operate-container">
    <!-- 可见按钮 -->
    <div class="visible-actions">
      <template v-for="action in visibleActions">
        <slot :name="action" :row="row" />
      </template>
    </div>

    <!-- 更多下拉菜单 -->
    <el-dropdown v-if="dropdownActions.length > 0" trigger="click">
      <el-button type="text" size="small"> 更多 <i class="el-icon-arrow-down el-icon--right" /> </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="action in dropdownActions" :key="action">
          <slot :name="action" :row="row" />
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  name: 'OperateMenu',
  props: {
    row: {
      type: Object,
      required: true
    },
    visibleCount: {
      type: Number,
      default: 1
    }
  },
  computed: {
    // 动态检测实际渲染的插槽
    slotNames() {
      return Object.keys(this.$scopedSlots)
        .filter((slot) => {
          try {
            const vnodes = this.$scopedSlots[slot]({ row: this.row })
            return vnodes && vnodes.some((vnode) => vnode.tag !== undefined)
          } catch (error) {
            return false
          }
        })
        .filter((slot) => slot !== 'default')
    },
    visibleActions() {
      return this.slotNames.slice(0, this.visibleCount)
    },
    dropdownActions() {
      return this.slotNames.slice(this.visibleCount)
    }
  }
}
</script>

<style scoped>
.operate-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.el-dropdown-menu__item > * {
  display: block;
  width: 100%;
  padding: 0 20px;
  line-height: 32px;
}
</style>
