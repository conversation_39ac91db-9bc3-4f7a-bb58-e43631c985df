<template>
  <div class="drug public_v2 flex_spaceBetween">
    <!-- ====== 左侧字典 start ====== -->
    <div ref="drugLeft" class="drug_left public_boxShadow bgColor_FFF borderRadius6">
      <div ref="drugLeftForm" class="drug_leftForm">
        <div class="drug_leftBtns flex_start">
          <!-- addDictShow = true -->
          <el-button
            class="public_button fontSize_14 bgColor_42C9A300B2DC flex_center v2icon_add"
            @click="showAddDict(1)"
          >
            <svg-icon icon-class="v2icon_add" />
            <span>添加字典</span>
          </el-button>
          <el-button
            class="public_button fontSize_14 bgColor_42C9A300B2DC flex_center v2icon_modify"
            @click="showAddDict(2)"
          >
            <svg-icon icon-class="v2icon_modify" />
            <span>修改</span>
          </el-button>
          <el-button
            class="public_button fontSize_14 bgColor_FFBEA2FE8787 flex_center v2icon_delete"
            @click="deleteDictionaryFolder"
          >
            <svg-icon icon-class="v2icon_delete" />
            <span>删除</span>
          </el-button>
        </div>
        <ProInput
          v-model="qeuryDictKeyword"
          placeholder="请输入关键字搜索"
          class="marginTop16 fontSize_14 public_height32 public_inputHeight32"
          suffix-icon="el-icon-search"
          @debounce="getDictionary(true)"
        />
      </div>

      <!-- ====== 数据字典内容 start ======-->
      <div ref="drugDict" class="fontSize_14 marginTop16 drug_dict">
        <div class="drug_dictTable">
          <ul ref="drugDictTableHeader" class="drug_dictTable_thead bgColor_DCF4E8EBF9F2 flex_center">
            <li>序号</li>
            <li>数据字典</li>
            <li />
          </ul>
          <el-scrollbar class="public_scrollbar drug_dictTable_tbodyScroll" :style="{ height: scrollHeight }">
            <ul
              v-for="(item, index) in tabs"
              :key="index"
              class="drug_dictTable_tbody fontSize_14 flex_center"
              :class="item.classActive"
              @click="selectDict(item)"
            >
              <li>{{ index + 1 }}</li>
              <li>{{ item.name }}</li>
              <li class="color1E1E28 flex_center v2icon_rightArrow">
                <svg-icon v-if="item.isSelected == true" icon-class="v2icon_rightArrow" />
              </li>
            </ul>
          </el-scrollbar>
        </div>
      </div>
      <!-- ====== 数据字典内容 end ======-->
    </div>
    <!-- ====== 左侧字典 end ====== -->

    <!-- ====== 右侧字段 start ====== -->
    <div class="drug_right">
      <div class="drug_rightForm public_boxShadow bgColor_FFF borderRadius6 flex_start">
        <div class="public_form_input">
          <ProInput
            v-model="dictionaryPageParams.search"
            placeholder="请输入关键字搜索"
            class="fontSize_14 height32 public_inputHeight32"
            suffix-icon="el-icon-search"
            @debounce="getDictionary(true)"
          />
        </div>
        <div class="drug_rightForm_btns flex_center">
          <el-button
            class="public_button public_buttonReset fontSize_14 bgColor_FFF flex_center v2icon_search"
            @click="resetParams"
          >
            <svg-icon icon-class="v2icon_reset" />
            <span>重置</span>
          </el-button>
          <el-button
            class="public_button fontSize_14 bgColor_42C9A300B2DC flex_center v2icon_search"
            @click="qeuryList"
          >
            <svg-icon icon-class="v2icon_search" />
            <span>搜索</span>
          </el-button>
        </div>
        <div class="public_shrink flex_center v2icon_shrink">
          <svg-icon icon-class="v2icon_shrink" />
        </div>
      </div>

      <div class="drug_rightContent marginTop24 public_boxShadow bgColor_FFF">
        <div class="flex_spaceBetween public_width100">
          <el-button
            class="public_button fontSize_14 bgColor_42C9A300B2DC flex_center v2icon_add"
            @click="addDictValShow = true"
          >
            <svg-icon icon-class="v2icon_add" />
            <span>新增字段</span>
          </el-button>
          <div class="public_tablbeBtns flex_center" style="display: none">
            <el-button class="v2icon_field" disabled>
              <svg-icon icon-class="v2icon_field" />
            </el-button>
            <el-button class="v2icon_print" disabled>
              <svg-icon icon-class="v2icon_print" />
            </el-button>
            <el-button class="v2icon_batchExport" disabled>
              <svg-icon icon-class="v2icon_batchExport" />
            </el-button>
            <el-button class="v2icon_export" disabled>
              <svg-icon icon-class="v2icon_export" />
            </el-button>
          </div>
        </div>
        <el-table v-loading="tableLoading" :data="tableData" class="marginTop30 public_table" stripe>
          <el-table-column type="selection" width="60" align="center" />

          <el-table-column label="序号" type="index" width="80" align="center" />
          <el-table-column prop="code" label="编码" width="220" align="center" />
          <el-table-column prop="value" label="字典值" />
          <el-table-column prop="createUserName" label="创建人" width="200" align="center" />
          <el-table-column label="操作" fixed="right" width="100">
            <template slot-scope="scope">
              <el-button type="text" @click="putDictValData(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="dictionaryPageParams.pageNo"
          background
          :page-sizes="tablePageSizes"
          :page-size="dictionaryPageParams.pageSize"
          :layout="tablePaginationLayout"
          :total="tableDatatotal"
          style="text-align: right; margin: 1rem 0 0"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <!-- ====== 右侧字段 end ====== -->

    <!-- ====== 修改字典值 start ======-->
    <el-dialog
      v-el-drag-dialog
      title="修改字典值"
      :visible.sync="putDictValShow"
      width="700px"
      class="el-big-dialog"
      :close-on-click-modal="false"
      :before-close="putDictValClose"
      custom-class="public_v2_dialog"
    >
      <span slot="title" class="dialog-title">
        <span>修改字典值</span>
      </span>
      <el-form
        ref="putdictValFrom"
        :model="putDictValForm"
        label-width="120px"
        label-position="left"
        class="marginTop24"
      >
        <el-form-item
          prop="moduleCode"
          label="上级字典编码"
          :rules="[{ required: true, message: '请选择上级字典编码', trigger: 'blur' }]"
        >
          <el-select v-model="putDictValForm.moduleCode" placeholder="请选择" class="public_inputHeight32 fontSize_14">
            <el-option v-for="item in tabs" :key="item.id" :label="item.name" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item
          prop="value"
          label="字典值"
          :rules="[{ required: true, message: '请输入字典值', trigger: 'blur' }]"
        >
          <el-input
            v-model="putDictValForm.value"
            maxlength="16"
            show-word-limit
            class="public_inputHeight32 fontSize_14"
          />
        </el-form-item>
        <el-form-item
          prop="code"
          label="字典值编码"
          :rules="[{ required: true, message: '请输入字典值编码', trigger: 'blur' }]"
        >
          <el-input
            v-model="putDictValForm.code"
            :disabled="isPutCode"
            maxlength="25"
            show-word-limitclass="public_inputHeight32 fontSize_14"
          />
        </el-form-item>
        <el-form-item
          prop="sortNo"
          label="字典序列号"
          :rules="[{ required: true, message: '请输入序列号', trigger: 'blur' }]"
        >
          <el-input-number
            v-model="putDictValForm.sortNo"
            :min="1"
            label="序列号"
            class="public_inputHeight32 fontSize_14"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer flex_end">
        <el-button class="public_dialogButton2 color333 fontSize_14 bgColor_FFF flex_center" @click="putDictValClose"
          >取消
        </el-button>
        <el-button
          class="public_dialogButton colorFFF fontSize_14 bgColor_42C9A300B2DC flex_center"
          @click="SubmitForm('putdictValFrom')"
          >保存</el-button
        >
      </span>
    </el-dialog>
    <!-- ====== 修改字典值 end ======-->

    <!-- ====== 新增字典、修改字典 start ====== -->
    <el-dialog
      v-el-drag-dialog
      title="新增字典"
      :visible.sync="addDictShow"
      width="600px"
      class="el-big-dialog"
      :close-on-click-modal="false"
      :before-close="addDictClose"
      custom-class="public_v2_dialog"
    >
      <span slot="title" class="dialog-title">
        <span>{{ addDictTitle }}</span>
      </span>
      <el-form ref="dictFrom" :model="addDictForm" label-width="100px" label-position="left">
        <el-form-item
          prop="name"
          label="字典名称"
          :rules="[{ required: true, message: '请输入字典名称', trigger: 'blur' }]"
        >
          <el-input
            v-model="addDictForm.name"
            maxlength="16"
            show-word-limit
            class="public_inputHeight32 fontSize_14"
          />
        </el-form-item>
        <el-form-item
          prop="code"
          label="字典编码"
          :rules="[{ required: true, message: '请输入字典编码', trigger: 'blur' }]"
        >
          <el-input
            v-model="addDictForm.code"
            maxlength="16"
            show-word-limit
            class="public_inputHeight32 fontSize_14"
          />
        </el-form-item>
        <el-form-item
          prop="description"
          label="字典描述"
          :rules="[{ required: true, message: '请输入字典描述', trigger: 'blur' }]"
        >
          <el-input
            v-model="addDictForm.description"
            type="textarea"
            resize="none"
            :autosize="{ minRows: 2, maxRows: 4 }"
            placeholder=""
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer flex_end">
        <el-button class="public_dialogButton2 color333 fontSize_14 bgColor_FFF flex_center" @click="addDictClose"
          >取消
        </el-button>
        <el-button
          class="public_dialogButton colorFFF fontSize_14 bgColor_42C9A300B2DC flex_center"
          @click="SubmitForm('dictFrom')"
          >保存
        </el-button>
      </span>
    </el-dialog>
    <!-- ====== 新增字典、修改字典 end ====== -->

    <!-- 新增字典值 -->
    <el-dialog
      v-el-drag-dialog
      title="新增字典"
      :visible.sync="addDictValShow"
      width="700px"
      class="el-big-dialog"
      :close-on-click-modal="false"
      :before-close="addDictValClose"
      custom-class="public_v2_dialog"
    >
      <span slot="title" class="dialog-title">
        <span>新增字典值</span>
      </span>
      <el-form ref="dictValFrom" :model="addDictValForm" label-width="120px" label-position="left">
        <el-form-item
          prop="moduleCode"
          label="上级字典编码"
          :rules="[{ required: true, message: '请输入上级字典编码', trigger: 'blur' }]"
        >
          <el-input
            v-model="addDictValForm.moduleCode"
            disabled
            maxlength="16"
            show-word-limit
            class="public_inputHeight32 fontSize_14"
          />
        </el-form-item>
        <el-form-item
          prop="value"
          label="字典值"
          :rules="[{ required: true, message: '请输入字典值', trigger: 'blur' }]"
        >
          <el-input
            v-model="addDictValForm.value"
            maxlength="99"
            show-word-limit
            class="public_inputHeight32 fontSize_14"
          />
        </el-form-item>
        <el-form-item
          prop="code"
          label="字典值编码"
          :rules="[{ required: true, message: '请输入字典值编码', trigger: 'blur' }]"
        >
          <el-input
            v-model="addDictValForm.code"
            maxlength="99"
            show-word-limit
            class="public_inputHeight32 fontSize_14"
          />
        </el-form-item>
        <el-form-item
          prop="sortNo"
          label="字典序列号"
          :rules="[{ required: true, message: '请输入序列号', trigger: 'blur' }]"
        >
          <el-input-number
            v-model="addDictValForm.sortNo"
            :min="1"
            label="序列号"
            class="public_inputHeight32 fontSize_14"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer flex_end">
        <el-button class="public_dialogButton2 color333 fontSize_14 bgColor_FFF flex_center" @click="addDictValClose"
          >取消
        </el-button>
        <el-button
          class="public_dialogButton colorFFF fontSize_14 bgColor_42C9A300B2DC flex_center"
          @click="SubmitForm('dictValFrom')"
          >保存
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import elDragDialog from '@/directive/el-drag-dialog'
import {
  addDictionaryApi,
  getDictionaryFolderListByModel,
  // getDictionaryValApi,
  getDictionaryPage,
  putDictionaryApi,
  addDictionaryValApi,
  deleteDictionaryValApi,
  deleteDictionaryFolderApi,
  putDictionaryValApi,
  deleteDictionaryApi
} from '@/api/dict'
import ProInput from '@/components/ProInput/index.vue'

export default {
  name: 'Drug',
  components: { ProInput },
  directives: {
    elDragDialog
  },
  data() {
    return {
      addDictTitle: '', // 新增修改字典弹窗的名称
      addDictShow: false, // 是否显示新增字典弹出 true显示，false:隐藏
      addDictValShow: false, // 是否显示新增新增字段弹出 true:显示 false
      putDictValShow: false, // 是否显示修改字典值弹出 true显示，false:隐藏
      addDictForm: {
        name: '',
        code: '',
        description: ''
      },
      addDictValForm: {
        moduleCode: '',
        value: '',
        code: '',
        sortNo: 1
      },
      putDictValForm: {
        id: '',
        moduleCode: '',
        value: '',
        code: '',
        sortNo: 1
      },
      activeName: 'second',
      isPutCode: true,

      tabs: [], // 字典数据
      tableData: [], // 字典值数据
      tableDatatotal: 0, // 字典值数据总条数
      tableLoading: false,
      qeuryDictKeyword: '', // 搜索字典关键字
      selectedDict: {}, // 选择的字典数据
      dictionaryPageParams: {
        // 字典值列表参数
        pageNo: 1,
        pageSize: 10,
        moduleCode: '', // 字典目录code
        search: '' // 搜索关键字
      },

      scrollHeight: 0 // 左侧滚动距离的高度
    }
  },
  created() {
    this.getDictionary(true)
  },
  mounted() {
    this.calculationDom() // 设置左侧字典数据滚动的高度

    window.addEventListener('resize', () => {
      this.calculationDom() // 设置左侧字典数据滚动的高度
    })
  },

  methods: {
    /**
     * @description: 显示新增，修改字典的弹窗
     * @param {*} type:1新增字典 2：修改字典
     * @author: LiSuwan
     * @Date: 2024-08-28 17:07:13
     */
    showAddDict(type) {
      this.addDictTitle = type === 1 ? '添加字典' : '修改字典'
      this.addDictShow = true
      this.addDictForm = Object.assign({}, this.$options.data().addDictForm)

      console.log(this.selectedDict)

      if (type === 2) {
        this.addDictForm = {
          ...this.selectedDict
        }
      }
    },
    /**
     * @description: 设置左侧字典数据滚动的高度
     * @author: LiSuwan
     * @Date: 2024-08-28 09:44:49
     */
    calculationDom() {
      this.$nextTick(() => {
        const drugLeftStyle = window.getComputedStyle(this.$refs.drugLeft) // 左侧DOM样式
        const drugLeftFormHeight = this.$refs.drugLeftForm.clientHeight // form表单的高度
        const drugDictTableHeaderHeight = this.$refs.drugDictTableHeader.clientHeight // 表头的高度
        const drugDictStyle = window.getComputedStyle(this.$refs.drugDict)
        let { marginTop } = drugDictStyle // 字典数据外边距高度
        const pxIndex = marginTop.indexOf('px')
        marginTop = parseInt(marginTop.slice(0, pxIndex) * 1)

        let { paddingTop } = drugLeftStyle // 左侧上内边距
        const pxIndex2 = paddingTop.indexOf('px')
        paddingTop = parseInt(paddingTop.slice(0, pxIndex2) * 1)

        const drugLeftHeight = this.$refs.drugLeft.clientHeight // 左侧DOM高度
        this.scrollHeight = `${
          drugLeftHeight - drugLeftFormHeight - drugDictTableHeaderHeight - marginTop - paddingTop * 2 - 10
        }px`
      })
    },
    handleClick(tab, event) {
      this.getDictionaryVal(this.activeName)
    },
    removeTab(targetName) {
      const { id } = this.tabs.find(i => i.code === targetName)
      this.$confirm('此操作将永久删除该字典, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await deleteDictionaryApi(id)
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
          }
          this.getDictionary(true)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    /**
     * @description: 获取字典列表
     * @author: LiSuwan
     * @Date: 2024-08-27 17:33:00
     */
    async getDictionary(val) {
      const res = await getDictionaryFolderListByModel({ value: this.qeuryDictKeyword })
      if (res.code === 200) {
        this.tabs = res.data // 左侧字典数据

        res.data.forEach((value, index) => {
          if (index === 0) {
            value.isSelected = true
            value.classActive = 'tbodyActive'
          } else {
            value.isSelected = false
            value.classActive = ''
          }
        })

        if (val && res.data.length > 0) {
          // this.activeName = res.data[0].code
          this.selectedDict = {
            // 当前选择的字典数据
            ...this.selectedDict,
            ...res.data[0]
          }
          this.addDictValForm = {
            ...res.data[0],
            moduleCode: res.data[0].code,
            code: ''
          }

          this.dictionaryPageParams = {
            ...this.dictionaryPageParams,
            moduleCode: res.data[0].code
          }

          this.getDictionaryVal()
        }
      }
    },
    /**
     * @description: 点击选择左侧字典数据
     * @param {*} item:当前选择的字典数据
     * @author: LiSuwan
     * @Date: 2024-08-27 20:39:57
     */
    selectDict(item) {
      this.tabs.forEach(val => {
        if (val.code === item.code) {
          val.isSelected = true
          val.classActive = 'tbodyActive'
        } else {
          val.isSelected = false
          val.classActive = ''
        }
      })
      this.$forceUpdate()
      this.selectedDict = {
        ...this.selectedDict,
        ...item,
        moduleCode: item.code
      }
      this.addDictValForm = {
        ...this.selectedDict
      }

      this.resetParams() // 重置参数
    },
    /**
     * @description: 获取字典值数据
     * @author: LiSuwan
     * @Date: 2024-08-28 11:05:35
     */
    async getDictionaryVal() {
      this.tableLoading = true
      const res = await getDictionaryPage(this.dictionaryPageParams)

      this.tableLoading = false

      if (res.code === 200) {
        this.tableData = res.data.list // 字典值数据
        this.tableDatatotal = res.data.total // 字典值数据总条数
      }
    },
    /**
     * @description: 重置参数
     * @author: LiSuwan
     * @Date: 2024-08-28 14:03:45
     */
    resetParams() {
      this.dictionaryPageParams = Object.assign({}, this.$options.data().dictionaryPageParams)

      this.dictionaryPageParams = {
        ...this.dictionaryPageParams,
        moduleCode: this.selectedDict.moduleCode
      }
      this.getDictionaryVal() // 获取字典值数据
    },
    /**
     * @description: 修改每页显示的条数
     * @author: LiSuwan
     * @Date: 2024-08-28 13:59:09
     */
    handleSizeChange(val) {
      this.dictionaryPageParams = {
        ...this.dictionaryPageParams,
        pageSize: val,
        pageNo: 1
      }

      this.getDictionaryVal() // 获取字典值数据
    },
    /**
     * @description: 修改页码
     * @author: LiSuwan
     * @Date: 2024-08-28 13:59:09
     */
    handleCurrentChange(val) {
      this.dictionaryPageParams.pageNo = val
      this.getDictionaryVal()
    },
    /**
     * @description: 删除字典目录
     * @author: LiSuwan
     * @Date: 2024-08-28 17:31:28
     */
    deleteDictionaryFolder() {
      const { id } = this.selectedDict
      console.log(this.selectedDict, 'selectDict')

      this.$confirm('此操作将永久删除该字典, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await deleteDictionaryFolderApi(id)
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
          }
          this.getDictionary(true)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    deleteDictionaryVal() {
      const { id } = this.selectDict

      this.$confirm('此操作将永久删除该字典值, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await deleteDictionaryValApi(id)
          if (res.code === 200) {
            this.$message({
              type: 'success',
              message: '删除成功'
            })
          }
          this.getDictionaryVal(this.activeName)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    addDictClose() {
      this.addDictShow = false
      this.addDictForm.name = ''
      this.$refs.dictFrom.resetFields()
    },
    addDictValClose() {
      this.addDictValShow = false
      this.addDictValForm.name = ''
      this.$refs.dictValFrom.resetFields()
    },
    putDictValClose() {
      this.putDictValShow = false
      this.putDictValForm.name = ''
      this.$refs.putdictValFrom.resetFields()
    },
    SubmitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (formName === 'dictFrom') {
            // 新增修改字典
            this.addDict()
          } else if (formName === 'dictValFrom') {
            this.addDictVal()
          } else if (formName === 'putdictValFrom') {
            this.putDictVal()
          }
        } else {
          return false
        }
      })
    },
    async addDictVal() {
      delete this.addDictValForm.id
      const res = await addDictionaryValApi(this.addDictValForm)
      if (res.code === 200) {
        this.$message({
          type: 'success',
          message: '新增成功'
        })
      } else {
        // TODO
      }
      this.addDictValClose()
      this.getDictionaryVal(this.activeName)
    },
    /**
     * @description: 新增修改字典
     * @author: LiSuwan
     * @Date: 2024-08-28 17:17:00
     */
    async addDict() {
      // if(this.addDictTitle = type == 1 ? '添加字典':'修改字典';)

      if (this.addDictTitle === '添加字典') {
        const res = await addDictionaryApi(this.addDictForm)
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '添加成功'
          })
        }
      } else {
        const params = {
          id: this.addDictForm.id,
          code: this.addDictForm.code,
          name: this.addDictForm.name,
          description: this.addDictForm.description
        }
        const res = await putDictionaryApi(params)
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '修改成功'
          })
        }
      }

      this.addDictClose()
      this.getDictionary()
    },
    async putDictVal() {
      const res = await putDictionaryValApi(this.putDictValForm)
      if (res.code === 200) {
        this.$message({
          type: 'success',
          message: '修改成功'
        })
      } else {
        // TODO
      }
      this.putDictValClose()
      this.getDictionaryVal(this.activeName)
    },
    /**
     * @description: 修改字典值
     * @param {*} row：当前点击的数据
     * @author: LiSuwan
     * @Date: 2024-08-28 14:25:44
     */
    async putDictValData(row) {
      console.log('row.allowDelete', row.allowDelete, row.allowDelete === 0)
      this.isPutCode = row.allowDelete === 0
      this.putDictValForm = {
        id: row.id,
        moduleCode: row.moduleCode,
        code: row.code,
        value: row.value,
        sortNo: row.sortNo
      }
      this.putDictValShow = true
    },
    /**
     * @description: 查询字典值列表
     * @author: LiSuwan
     * @Date: 2024-08-28 17:59:38
     */
    qeuryList() {
      this.handleCurrentChange(1)
    }
  }
}
</script>
<style lang="scss" scoped>
.drug {
  width: 100%;
  padding: 0.8rem;
  box-sizing: border-box;
  height: calc(100vh - 84px);
  background-color: #f9fffb;

  .drug_left,
  .drug_right {
    height: 100%;
    overflow-y: auto;
  }

  .drug_right {
    width: calc(100% - 20.5rem);

    .drug_rightForm_input {
      width: 17.6rem;
      margin-right: 5.4rem;
    }
  }

  .drug_left {
    width: 20rem;

    .drug_leftBtns {
      width: 100%;
      overflow: hidden;
    }

    .drug_dict {
      height: calc(100% - 4rem);
    }

    .drug_dictTable {
      width: 100%;
      height: 100%;
      color: #1e1e28;
      box-sizing: border-box;

      .drug_dictTable_thead {
        height: 2.4rem;
      }

      .drug_dictTable_tbody,
      .drug_dictTable_thead {
        width: 100%;
        padding: 0.7rem 0.8rem;

        li {
          text-align: center;
          cursor: pointer;

          &:nth-child(1) {
            width: 25%;
          }

          &:nth-child(2) {
            width: 60%;
          }

          &:nth-child(3) {
            width: 15%;
          }
        }
      }

      .drug_dictTable_tbody:nth-child(odd) {
        background-color: #fff;
      }

      .drug_dictTable_tbody:nth-child(even) {
        background-color: #f9fffb;
      }

      .drug_dictTable_tbody.tbodyActive {
        background: #ebf9f2;
      }
    }
  }
}
</style>
