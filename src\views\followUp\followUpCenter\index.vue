<template>
  <div class="follow-up">
    <div class="follow-up-tabs">
      <el-card>
        <FollowStatusTabs :tabs="tabsList" :default-active="activeTab" @tab-change="handleTabChange" />
      </el-card>
    </div>
    <div class="follow-up-table">
      <el-card class="follow-up-table-card">
        <div class="follow-up-table-operation">
          <el-button type="primary" @click="handleAddUser">添加人员</el-button>
          <div class="follow-up-table-search">
            <el-input
              v-model="queryParams.keyword"
              placeholder="请输入姓名/身份证"
              clearable
              style="width: 200px"
              @change="handleSearch"
            />
            <el-select
              v-model="queryParams.manageDoctorId"
              placeholder="请选择责任医生"
              style="width: 200px; margin-left: 10px"
              clearable
              filterable
              @change="handleManageDoctorChange"
            >
              <el-option v-for="item in manageDoctorList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </div>
        </div>
        <base-table
          ref="baseTable"
          :table-data="tableData"
          :loading="loading"
          :stripe="true"
          height="calc(100% - 110px)"
          row-key="id"
          :columns="columns"
          :total="total"
          :page-info="queryParams"
          @pagination-change="handlePaginationChange"
        >
          <template #disease="{ row }">
            <ChronicDiseaseType :record="row" />
          </template>

          <template #sex="{ row }">
            <span>{{ genderTransform(row.sex) }}</span>
          </template>

          <template #idCard="{ row }">
            <EncryptionStr :cipher-text="row.idCard" :replace="row.idCardReplace" />
          </template>

          <template #phone="{ row }">
            <EncryptionStr :cipher-text="row.phone" :replace="row.phoneReplace" />
          </template>

          <template #type="{ row }">
            <span>{{
              row.type === 1 ? '电话随访' : row.type === 2 ? '上门随访' : row.type === 3 ? '诊室随访' : ''
            }}</span>
          </template>

          <template #planType="{ row }">
            <span :style="{ color: row.planType === 2 ? 'red' : '' }">{{
              row.planType === 1 ? '计划内' : row.planType === 2 ? '计划外' : ''
            }}</span>
          </template>

          <template #status="{ row }">
            <el-tag v-if="row.status === 1" type="primary">待随访</el-tag>
            <el-tag v-else-if="row.status === 5" type="success">已完成</el-tag>
            <el-tag v-else-if="row.status === 7" type="warning">即将逾期</el-tag>
            <el-tag v-else-if="row.status === 9" type="danger">已逾期</el-tag>
          </template>

          <template #operation="{ row }">
            <el-button
              v-if="row.status === 5"
              type="text"
              size="small"
              @click="handleOperation(row, 'view')"
            >查看</el-button>
            <el-button v-else type="text" size="small" @click="handleOperation(row, 'followUp')">随访</el-button>
            <el-button
              v-if="!row.auditResult && row.status === 5"
              type="text"
              size="small"
              @click="handleOperation(row, 'edit')"
            >编辑</el-button>
          </template>
        </base-table>
      </el-card>
    </div>
    <ProDialog ref="addDialog" title="添加人员" :visible.sync="addDialogVisible" width="960px">
      <PatientList ref="patientList" />
      <span slot="footer">
        <el-button @click="addDialogVisible = false">关闭</el-button>
        <el-button type="primary" :loading="buttonLoading" @click="handleAddConfirm">确定</el-button>
      </span>
    </ProDialog>
  </div>
</template>

<script>
import { getFollowUpList, addFollowUp, getFollowUpStatusCount } from '@/api/followUp'
import { getmanageDoctorApi } from '@/api/system'
import { genderTransform } from '@/utils/cspUtils'
import FollowStatusTabs from './component/FollowStatusTabs.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ProDialog from '@/components/ProDialog/index.vue'
import PatientList from '@/views/receptionCenter/receptionWorkbench/component/patientList.vue'
import ChronicDiseaseType from '@/components/chronicDiseaseType/index.vue'
import EncryptionStr from '@/components/encryptionStr/index.vue'

export default {
  name: 'FollowUpCenter',
  components: {
    FollowStatusTabs,
    BaseTable,
    ProDialog,
    PatientList,
    ChronicDiseaseType,
    EncryptionStr
  },
  mixins: [tableMixin],
  data() {
    return {
      activeTab: 0,
      buttonLoading: false,
      manageDoctorList: [],
      queryParams: {
        status: '',
        manageDoctorId: '',
        keyword: ''
      },
      columns: [
        { label: '姓名', prop: 'patientName', width: 120 },
        { label: '慢病病种', prop: 'disease', slot: 'disease', width: 160 },
        { label: '年龄', prop: 'age' },
        { label: '性别', prop: 'sex', slot: 'sex' },
        { label: '身份证号', prop: 'idCard', slot: 'idCard', width: 180 },
        { label: '手机号码', prop: 'phone', slot: 'phone', width: 140 },
        { label: '机构名称', prop: 'departName', width: 150 },
        { label: '随访方式', prop: 'type', slot: 'type' },
        { label: '计划内/外', prop: 'planType', slot: 'planType', width: 130 },
        { label: '计划下次随访日期', prop: 'planDate', width: 150 },
        { label: '随访日期', prop: 'realDate', width: 130 },
        { label: '随访医生', prop: 'doctorName' },
        { label: '责任医生', prop: 'manageDoctorName' },
        { label: '状态', prop: 'status', slot: 'status' },
        { label: '操作', prop: 'operation', slot: 'operation', fixed: 'right', width: 120 }
      ],
      tabsList: [
        {
          diseaseName: '全部',
          count: '',
          icon: 'el-icon-tickets',
          diseaseCode: 'all',
          status: ''
        },
        {
          diseaseName: '待随访',
          count: '',
          icon: 'el-icon-time',
          diseaseCode: 'waitInterview',
          status: 1
        },
        {
          diseaseName: '已完成',
          count: '',
          icon: 'el-icon-check',
          diseaseCode: 'complete',
          status: 5
        },
        {
          diseaseName: '即将逾期',
          count: '',
          icon: 'el-icon-warning',
          diseaseCode: 'preOverdue',
          status: 7
        },
        {
          diseaseName: '已逾期',
          count: '',
          icon: 'el-icon-error',
          diseaseCode: 'overdue',
          status: 9
        }
      ]
    }
  },
  created() {
    this.getDoctorList()
    this.getFollowUpStatusCountFn()
  },
  methods: {
    genderTransform,
    // 责任医生
    async getDoctorList() {
      const params = {
        pageNo: 1,
        pageSize: 2000,
        departCode: this.$store.getters.departCode
      }
      const res = await getmanageDoctorApi(params)
      if (res.code === 200) {
        this.manageDoctorList = res.data.list || []
      }
    },

    // 获取随访状态数量
    async getFollowUpStatusCountFn() {
      const res = await getFollowUpStatusCount(this.queryParams)
      if (res.code === 200) {
        this.tabsList.forEach((item) => {
          const temp = res.data.find((it) => it.diseaseCode === item.diseaseCode)
          item.count = temp.count
        })
      }
    },

    handleManageDoctorChange(value) {
      this.queryParams.manageDoctorId = value
      this.handleSearch()
    },

    handleTabChange(index, tab) {
      this.activeTab = index
      this.queryParams.status = tab.status
      this.handleSearch()
    },

    async getTableList(params) {
      return await getFollowUpList(params)
    },

    handleAddUser() {
      this.addDialogVisible = true
    },

    async handleAddConfirm() {
      try {
        this.buttonLoading = true
        const id = this.$refs.patientList.selectedRow
        if (!id) {
          this.$message.warning('请选择患者')
          return
        }
        const res = await addFollowUp({
          patientId: id
        })
        if (res.code === 200) {
          this.$message.success('添加成功')
          this.handleReset()
          this.addDialogVisible = false
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.buttonLoading = false
      }
    },

    handleOperation(row, type) {
      this.$router.push({
        path: '/followUp/followUpCenter/detail',
        query: {
          id: row.id,
          type
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.follow-up {
  height: 100%;
  display: flex;
  flex-direction: column;
  .follow-up-tabs {
    height: 110px;
    margin: 16px;
  }
  .follow-up-table {
    flex: 1;
    margin: 0 16px;
    .follow-up-table-card {
      height: 100%;
      ::v-deep .el-card__body {
        height: 100%;
      }
    }
    .follow-up-table-operation {
      display: flex;
      justify-content: space-between;
      margin: 10px 0;
      .follow-up-table-search {
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
</style>
