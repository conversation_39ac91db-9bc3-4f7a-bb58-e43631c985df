<template>
  <div class="remoteVideo">
    <div class="left" :class="{ 'full-width': !showMemberPanel }">
      <div class="end-meeting-btn-container">
        <el-button v-if="type === 'main'" type="primary" round @click="toggleMemberPanel">管理成员</el-button>
        <el-button
          type="danger"
          round
          icon="el-icon-switch-button"
          class="end-meeting-btn"
          :loading="exitingMeeting"
          @click="endMeeting"
        >
          {{ type === 'main' ? '结束会议' : '退出会议' }}
        </el-button>
      </div>
      <div class="video-container">
        <!-- 本地视频 -->
        <div class="video-wrapper local-wrapper">
          <div id="local-video" class="video-box" />
          <div class="video-info">
            <div class="video-label">
              <i class="el-icon-user" />
              <span>我（本人）</span>
              <!-- 可以加一个正在说话的图标 -->
              <i v-if="speakingUserId === userId" class="el-icon-microphone speaking-icon" />
            </div>
          </div>
        </div>

        <!-- 远端视频列表 -->
        <template v-if="remoteUsers.length > 0">
          <div v-for="user in remoteUsers" :key="user.userId" class="video-wrapper remote-wrapper">
            <div :id="`remote-video-${user.userId}`" class="video-box" />
            <div class="video-info">
              <div class="video-label">
                <i class="el-icon-user" />
                <span>{{ user.userName }}</span>
                <!-- 也可以给远端用户加图标 -->
                <i v-if="speakingUserId === user.userId" class="el-icon-microphone speaking-icon" />
              </div>
            </div>
          </div>
        </template>
      </div>

      <div v-if="errorMsg" class="error-tips">
        <div class="error-icon"><i class="el-icon-warning" /></div>
        <div class="error-content">{{ errorMsg }}</div>
        <div v-if="errorMsg.includes('无法找到')" class="error-help">
          请检查摄像头是否已正确连接，或尝试重新插拔摄像头设备
        </div>
        <div v-else-if="errorMsg.includes('权限被拒绝')" class="error-help">
          请在浏览器弹出的权限请求中点击"允许"，或在浏览器设置中重置摄像头权限
        </div>
      </div>
      <div v-if="!hasVideo && !loading && !errorMsg" class="no-video-tips">未检测到摄像头画面，请检查摄像头权限</div>
    </div>
    <div v-if="showMemberPanel" class="right">
      <div class="member-panel-header">
        <span>管理成员</span>
        <i class="el-icon-close close-icon" @click="showMemberPanel = false" />
      </div>
      <div class="member-panel-content">
        <el-tabs v-model="activeTab" class="member-tabs">
          <el-tab-pane label="会议中" name="meeting">
            <div class="meeting-doctors">
              <div v-for="doctor in meetingDoctors" :key="doctor.id" class="doctor-item">
                <span>{{ doctor.name }}</span>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="邀请成员" name="calling">
            <div class="calling-doctors">
              <div v-for="doctor in callingDoctors" :key="doctor.id" class="doctor-item">
                <div class="doctor-info">
                  <span class="doctor-name">{{ doctor.name }}</span>
                  <el-badge is-dot type="success" />
                </div>
                <div class="call-icon" @click="callDoctor(doctor)">
                  <img src="@/assets/dashboard_images/answer.png" alt="呼叫">
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import { getUserId } from '@/utils/auth'
import { leaveRoomApi, getOnlineDoctorsApi, addParticipantApi } from '@/api/remoteConsultation'
import TRTC from 'trtc-js-sdk'
import LibGenerateTestUserSig from '@/utils/lib-generate-test-usersig.min.js'
import envModule from '@/utils/env'

export default {
  name: 'RemoteVideo',
  props: {
    type: {
      type: String,
      default: ''
    },
    roomId: {
      type: Number,
      required: true
    },
    participatingDoctors: {
      // 参会医生
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      onlineDoctors: [],
      SDKAPPIDConfig: envModule.genUrlEnum().trtc.appId,
      SECRETKEYConfig: envModule.genUrlEnum().trtc.secretKey,
      userId: getUserId(),
      client: null,
      localStream: null, // 本地流
      remoteUsers: [], // 远程用户数组
      loading: false,
      errorMsg: '',
      hasVideo: false,
      exitingMeeting: false,
      showMemberPanel: false, // 控制成员面板显示
      activeTab: 'meeting', // 当前激活的标签页
      speakingUserId: null // 用于存储正在说话的用户的ID
    }
  },
  computed: {
    meetingDoctors() {
      const doctorList = this.onlineDoctors.filter((doctor) => doctor.status === 2)
      return doctorList
    },
    callingDoctors() {
      const doctorList = this.onlineDoctors.filter((doctor) => doctor.status === 3)
      return doctorList
    }
  },
  mounted() {
    this.getOnlineDoctorsFn()
    this.startTimer()
    this.createClient(this.roomId)
  },
  beforeDestroy() {
    this.clearTimer()
    this.destroyStream()
  },
  methods: {
    // 呼叫医生
    async callDoctor(doctor) {
      await addParticipantApi({
        roomId: this.roomId,
        doctorList: [doctor]
      })
    },

    startTimer() {
      this.clearTimer()
      this.timer = setInterval(() => {
        this.getOnlineDoctorsFn()
      }, 5000)
    },

    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },

    // 获取在线医生
    async getOnlineDoctorsFn() {
      const res = await getOnlineDoctorsApi({
        roleCode: 'remoteExpert'
      })
      if (res.code === 200) {
        this.onlineDoctors = res.data
      }
    },

    // 切换成员面板显示状态
    toggleMemberPanel() {
      this.showMemberPanel = !this.showMemberPanel
    },

    async createClient(roomId) {
      this.loading = true
      // 获取签名
      const { sdkAppId, userSig } = this.genTestUserSig(this.userId)
      // 创建客户端
      this.client = TRTC.createClient({
        mode: 'rtc',
        sdkAppId,
        userId: this.userId,
        userSig
      })

      // 设置远程流事件监听
      this.setupRemoteStreamEvents()

      this.joinRoom(this.client, roomId)
    },

    async joinRoom(client, roomId) {
      try {
        await client.join({ roomId })

        // 开启音量回调功能，每 1000 毫秒回调一次
        // 数字越小，回调越频繁，响应越快，但性能开销也略高
        client.enableAudioVolumeEvaluation(1000)
      } catch (error) {
        console.log('error', error)
        this.errorMsg = `加入房间失败：${error.message || '未知错误'}`
        this.loading = false
      } finally {
        // 本地流
        if (!this.errorMsg) {
          this.createLocalStream()
        }
      }
    },

    async createLocalStream() {
      try {
        // 检查可用设备
        // console.log('检查可用设备navigator', navigator)
        const devices = await navigator.mediaDevices.enumerateDevices()
        const hasVideoDevice = devices.some((device) => device.kind === 'videoinput')
        const hasAudioDevice = devices.some((device) => device.kind === 'audioinput')
        // console.log('devices', devices)
        // console.log('hasVideoDevice', hasVideoDevice)
        // console.log('hasAudioDevice', hasAudioDevice)

        this.localStream = TRTC.createStream({
          userId: this.userId,
          video: hasVideoDevice,
          audio: hasAudioDevice
        })

        // 设置视频配置
        if (hasVideoDevice) {
          this.localStream.setVideoProfile('480p')
        }

        // 监听流初始化事件
        this.localStream.on('player-state-changed', (event) => {
          console.log('本地视频播放状态变化：', event.type, event.state)
          if (event.type === 'video' && event.state === 'playing') {
            this.hasVideo = true
          }
        })

        await this.localStream.initialize()

        // 确保DOM元素存在
        const localVideoEl = document.getElementById('local-video')
        if (!localVideoEl) {
          throw new Error('本地视频容器元素不存在')
        }

        // 清空容器内容，避免重复播放
        localVideoEl.innerHTML = ''

        // 播放本地流
        this.localStream.play('local-video', {
          objectFit: 'cover',
          mirror: true // 镜像显示，更符合自拍习惯
        })

        // 检查视频轨道是否激活
        const videoTrack = this.localStream.getVideoTrack()
        if (videoTrack && videoTrack.enabled) {
          this.hasVideo = true
        } else {
          console.warn('视频轨道未激活')
        }

        // 发布本地流
        await this.client.publish(this.localStream)
        this.loading = false
      } catch (error) {
        console.error('初始化视频失败', error)
        this.errorMsg = `初始化视频失败：${error.message || '请检查摄像头权限是否开启'}`
        this.loading = false
      }
    },

    // 远程流
    setupRemoteStreamEvents() {
      // 监听远端流加入
      this.client.on('stream-added', (event) => {
        const remoteStream = event.stream
        const remoteUserId = remoteStream.getUserId()
        console.log('发现远端流:', remoteUserId)

        // 订阅远端流
        this.client.subscribe(remoteStream)
      })

      // 监听远端流订阅成功事件
      this.client.on('stream-subscribed', (event) => {
        const remoteStream = event.stream
        const remoteUserId = remoteStream.getUserId()
        console.log('订阅远端流成功:', remoteUserId)

        // 查找用户名
        let userName = ''
        const userInfo = this.participatingDoctors.find((user) => user.id === remoteUserId)
        if (userInfo) {
          userName = userInfo.name || ''
        }

        // 添加到远程用户数组
        this.remoteUsers.push({
          userId: remoteUserId,
          userName,
          stream: remoteStream
        })

        // 在下一个DOM更新周期中播放视频
        this.$nextTick(() => {
          // 确保DOM元素存在
          const remoteVideoEl = document.getElementById(`remote-video-${remoteUserId}`)
          if (!remoteVideoEl) {
            console.error(`远端视频容器元素不存在: remote-video-${remoteUserId}`)
            return
          }

          // 清空容器内容，避免重复播放
          remoteVideoEl.innerHTML = ''

          // 播放远端流
          remoteStream.play(`remote-video-${remoteUserId}`, {
            objectFit: 'cover'
          })
        })
      })

      // 监听远端流移除事件
      this.client.on('stream-removed', (event) => {
        const remoteStream = event.stream
        const remoteUserId = remoteStream.getUserId()
        console.log('远端流已移除:', remoteUserId)

        // 停止播放远端流并从数组中移除
        const userIndex = this.remoteUsers.findIndex((user) => user.userId === remoteUserId)
        if (userIndex !== -1) {
          this.remoteUsers[userIndex].stream.stop()
          this.remoteUsers.splice(userIndex, 1)
        }
      })

      // 监听远端用户断开连接事件
      this.client.on('peer-leave', (event) => {
        const remoteUserId = event.userId
        console.log('远端用户断开连接:', remoteUserId)

        // 从数组中移除用户
        const userIndex = this.remoteUsers.findIndex((user) => user.userId === remoteUserId)
        if (userIndex !== -1) {
          // 停止并移除流
          if (this.remoteUsers[userIndex].stream) {
            this.remoteUsers[userIndex].stream.stop()
          }
          this.remoteUsers.splice(userIndex, 1)
        }
      })

      // 监听音量回调事件
      this.client.on('audio-volume', (event) => {
        // event.result 是一个数组，包含了所有正在说话的用户信息
        // 结构示例: [{ userId: 'user1', audioVolume: 30, stream: remoteStream1 }]
        console.log('event', event.result)
        let highestVolume = 0
        let speakingUser = null

        // 遍历所有有音量的用户，找到音量最大的那个
        event.result.forEach((user) => {
          // 我们通常设置一个阈值，比如音量大于 5 才算有效说话，避免背景噪音干扰
          if (user.audioVolume > 5 && user.audioVolume > highestVolume) {
            highestVolume = user.audioVolume
            speakingUser = user.userId
          }
        })

        // 更新正在说话的用户ID
        // 如果几秒钟没人说话 (speakingUser 为 null), speakingUserId 就会被更新为 null
        this.speakingUserId = speakingUser
      })
    },

    // 销毁流，防止内存泄漏
    destroyStream() {
      if (this.localStream) {
        this.localStream.stop()
        this.localStream.close()
        this.localStream = null
      }

      // 清理所有远程流
      this.remoteUsers.forEach((user) => {
        if (user.stream) {
          user.stream.stop()
        }
      })
      this.remoteUsers = []

      if (this.client) {
        // 移除所有事件监听器，防止内存泄漏
        this.client.off('stream-added')
        this.client.off('stream-subscribed')
        this.client.off('stream-removed')
        this.client.off('peer-leave')
        this.client.off('audio-volume')

        this.client.leave().catch((err) => {
          console.error('退出房间失败', err)
        })
        this.client = null
      }

      // 重置说话状态
      this.speakingUserId = null
    },

    // 结束会议
    async endMeeting() {
      this.exitingMeeting = true
      try {
        const res = await leaveRoomApi({ id: this.roomId })
        if (res.code === 200) {
          // 销毁视频流
          this.destroyStream()
          // 通知父组件会议已结束
          this.$emit('meeting-ended')
        }
      } catch (error) {
        console.error('结束会议时出错', error)
      } finally {
        this.exitingMeeting = false
      }
    },

    genTestUserSig(userID) {
      /**
       * 腾讯云 SDKAppId，需要替换为您自己账号下的 SDKAppId。
       *
       * 进入腾讯云实时音视频[控制台](https://console.cloud.tencent.com/rav ) 创建应用，即可看到 SDKAppId，
       * 它是腾讯云用于区分客户的唯一标识。
       */
      const SDKAPPID = this.SDKAPPIDConfig
      /**
       * 签名过期时间，建议不要设置的过短
       * <p>
       * 时间单位：秒
       * 默认时间：7 x 24 x 60 x 60 = 604800 = 7 天
       */
      const EXPIRETIME = 604800
      /**
       * 计算签名用的加密密钥，获取步骤如下：
       *
       * step1. 进入腾讯云实时音视频[控制台](https://console.cloud.tencent.com/rav )，如果还没有应用就创建一个，
       * step2. 单击"应用配置"进入基础配置页面，并进一步找到"帐号体系集成"部分。
       * step3. 点击"查看密钥"按钮，就可以看到计算 UserSig 使用的加密的密钥了，请将其拷贝并复制到如下的变量中
       *
       * 注意：该方案仅适用于调试Demo，正式上线前请将 UserSig 计算代码和密钥迁移到您的后台服务器上，以避免加密密钥泄露导致的流量盗用。
       * 文档：https://cloud.tencent.com/document/product/647/17275#Server
       */
      const SECRETKEY = this.SECRETKEYConfig
      if (SDKAPPID === '' || SECRETKEY === '') {
        // eslint-disable-next-line no-alert
        alert(
          '请先配置好您的账号信息： SDKAPPID 及 SECRETKEY ' +
            '\r\n\r\nPlease configure your SDKAPPID/SECRETKEY in js/debug/GenerateTestUserSig.js'
        )
      }
      const generator = new LibGenerateTestUserSig(SDKAPPID, SECRETKEY, EXPIRETIME)
      const userSig = generator.genTestUserSig(userID)

      return {
        sdkAppId: SDKAPPID,
        userSig
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.remoteVideo {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
  display: flex;
  gap: 10px;

  .left {
    width: 80%;
    height: 100%;
    transition: width 0.3s ease;

    &.full-width {
      width: 100%;
    }

    .end-meeting-btn-container {
      display: flex;
      justify-content: flex-end;
    }

    .video-container {
      display: flex;
      width: 100%;
      height: 450px;
      flex-wrap: wrap;
      overflow: auto;

      .video-wrapper {
        // position: relative;
        // flex: 0 0 calc(25% - 8px); // 每行最多4个视频
        // min-width: 220px;
        // max-height: 450px;
        // overflow: hidden;
        // border-radius: 6px;
        // margin: 4px;
        // box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        // background-color: #1a1a1a;
        // border: 2px solid transparent; // 默认给一个透明边框，防止添加边框时布局抖动
        // transition: all 0.3s ease;
        position: relative;
        flex: 1;
        min-width: 250px;
        max-height: 450px;
        overflow: hidden;
        border-radius: 6px;
        margin: 4px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        background-color: #1a1a1a;
        border: 2px solid transparent; // 默认给一个透明边框，防止添加边框时布局抖动
        transition: all 0.3s ease;

        &.is-speaking {
          // 说话时的高亮效果
          border: 2px solid #409eff; // 使用 Element UI 的主题蓝色作为高亮色
          box-shadow: 0 0 10px rgba(64, 158, 255, 0.7);
        }

        &:hover {
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        &.empty-wrapper {
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #2c3e50;
        }

        @media (max-width: 768px) {
          min-height: 150px;
        }

        .video-box {
          width: 100%;
          height: 100%;
          min-height: 180px;
          background-color: #000;
          z-index: 1;
        }

        .video-info {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          padding: 8px;
          background: linear-gradient(to top, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0));
          z-index: 2;
        }

        .video-label {
          display: inline-flex;
          align-items: center;
          background-color: rgba(0, 0, 0, 0.6);
          color: white;
          padding: 6px 10px;
          border-radius: 20px;
          font-size: 13px;

          i {
            margin-right: 5px;
            font-size: 14px;
          }

          .speaking-icon {
            margin-left: 8px;
            color: #67c23a; // 绿色，表示活跃
            font-size: 16px;
          }
        }

        .waiting-tips {
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: rgba(0, 0, 0, 0.5);
          color: white;
          padding: 12px 18px;
          border-radius: 8px;
          font-size: 14px;

          i {
            margin-right: 8px;
            font-size: 16px;
          }
        }
      }
    }

    .error-tips,
    .no-video-tips {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      padding: 15px 20px;
      background-color: rgba(0, 0, 0, 0.7);
      color: #fff;
      border-radius: 8px;
      font-size: 14px;
      z-index: 10;
      max-width: 80%;
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;

      i {
        margin-right: 8px;
        font-size: 16px;
      }
    }

    .error-tips {
      background-color: rgba(220, 0, 0, 0.85);
      padding: 20px;
      width: 70%;
      border-radius: 8px;

      .error-icon {
        font-size: 28px;
        margin-bottom: 10px;
      }

      .error-content {
        font-size: 16px;
        margin-bottom: 10px;
      }

      .error-help {
        font-size: 13px;
        opacity: 0.9;
        border-top: 1px solid rgba(255, 255, 255, 0.2);
        padding-top: 10px;
        margin-top: 10px;
      }
    }

    .no-video-tips {
      background-color: rgba(0, 0, 0, 0.7);
      padding: 15px 20px;
      border-radius: 8px;
    }
  }
  .right {
    width: 20%;
    height: 100%;
    background-color: #f5f7fa;
    border-left: 1px solid #e6e6e6;
    border-radius: 8px;
    transition: all 0.3s ease;
    overflow: auto;

    .member-panel-header {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #e6e6e6;
      font-weight: bold;

      .close-icon {
        cursor: pointer;
        font-size: 16px;
        padding: 4px;
        border-radius: 50%;

        &:hover {
          background-color: #e6e6e6;
        }
      }
    }

    .member-panel-content {
      padding: 16px;

      .member-tabs {
        width: 100%;
      }

      .doctor-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 0;
        margin: 5px 0;
        border-bottom: 1px solid #ebeef5;

        &:last-child {
          border-bottom: none;
        }

        .doctor-info {
          display: flex;
          align-items: center;

          .doctor-name {
            font-size: 14px;
          }
        }

        .call-icon {
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;

          img {
            width: 100%;
            height: 100%;
          }
        }
      }
    }
  }
}
</style>
