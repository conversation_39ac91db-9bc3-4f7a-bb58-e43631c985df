/* eslint-disable no-shadow */
import {
  getHighRiskScreeningDetail,
  getDiagnosisOfIllnessDetail,
  getReceptionWorkbenchById,
  getConsultationInfoDetail,
  getReceptionStepList
} from '@/api/receptionWorkbench'
import { cloneDeep } from 'lodash'

const state = {
  loading: false,
  saveBtnLoading: false,
  receptionWorkbenchData: {}, // 接诊记录
  consultationInfoData: {}, // 问诊信息
  highRiskScreeningData: {}, // 高危筛查
  diagnosisOfIllnessData: {}, // 病情诊断
  complicationsScreeningData: {}, // 并发症筛查
  stepList: [] // 步骤列表
}

const mutations = {
  SET_LOADING(state, flag) {
    state.loading = flag
  },
  SET_SAVE_BTN_LOADING(state, flag) {
    state.saveBtnLoading = flag
  },
  SET_RECEPTION_WORKBENCH_DATA(state, data) {
    state.receptionWorkbenchData = data
  },
  SET_CONSULTATION_INFO_DATA(state, data) {
    state.consultationInfoData = data
  },
  SET_HIGH_RISK_SCREENING_DATA(state, data) {
    state.highRiskScreeningData = data
  },
  SET_DIAGNOSIS_OF_ILLNESS_DATA(state, data) {
    state.diagnosisOfIllnessData = data
  },
  SET_COMPLICATIONS_SCREENING_DATA(state, data) {
    state.complicationsScreeningData = data
  },
  SET_STEP_LIST(state, data) {
    state.stepList = data
  }
}

const actions = {
  async getReceptionWorkbenchById({ commit }, params) {
    let res = null
    try {
      res = await getReceptionWorkbenchById(params)
      // 慢病按照这个排序['糖尿病', '高血压', '慢阻肺', '房颤']

      res.data.diseaseList = res.data.diseaseList || []
      const diseaseListTemp = cloneDeep(res.data.diseaseList)
      const diseaseList = [
        {
          name: '糖尿病',
          id: 'tnb'
        },
        {
          name: '高血压',
          id: 'gxy'
        },
        {
          name: '慢阻肺',
          id: 'COPD'
        },
        {
          name: '房颤',
          id: 'fangchan'
        }
      ]
      const diseaseListIndex = diseaseList.filter((item) => diseaseListTemp.includes(item.name))
      res.data.diseaseList = diseaseListIndex
      commit('SET_RECEPTION_WORKBENCH_DATA', res.data)
    } catch (error) {
      console.error(error)
    }
    return res ? res.data : null
  },

  // 问诊信息
  async getConsultationInfoData({ commit }, params) {
    commit('SET_LOADING', true)
    let res = null
    try {
      res = await getConsultationInfoDetail(params)
      commit('SET_CONSULTATION_INFO_DATA', res.data)
    } catch (error) {
      console.error(error)
    } finally {
      commit('SET_LOADING', false)
    }
    return res ? res.data : null
  },

  // 高危筛查
  async getHighRiskScreeningData({ commit }, params) {
    commit('SET_LOADING', true)
    let res = null
    try {
      res = await getHighRiskScreeningDetail(params)
      commit('SET_HIGH_RISK_SCREENING_DATA', res.data)
    } catch (error) {
      console.error(error)
    } finally {
      commit('SET_LOADING', false)
    }
    return res ? res.data : null
  },

  // 病情诊断
  async getDiagnosisOfIllnessData({ commit }, params) {
    commit('SET_LOADING', true)
    let res = null
    try {
      res = await getDiagnosisOfIllnessDetail(params)
      commit('SET_DIAGNOSIS_OF_ILLNESS_DATA', res.data)
    } catch (error) {
      console.error(error)
    } finally {
      commit('SET_LOADING', false)
    }
    return res ? res.data : null
  },

  // 步骤列表
  async getReceptionStepListFn({ commit }, params) {
    const res = await getReceptionStepList(params)
    const stepListTemp = res.data.map((item) => ({
      ...item,
      title: item.stepName
    }))
    const idx = stepListTemp.findIndex((it) => it.stepStatus === 0 || it.stepStatus === 1)
    commit('SET_STEP_LIST', stepListTemp)
    return idx
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
