<!-- 转诊记录 -->
<template>
  <div class="referral-record">
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      row-key="id"
      :columns="columns"
      :show-pagination="showPagination"
    >
      <template #referralStatus="{ row }">
        <span v-if="row.referralStatus === 1">待接诊</span>
        <span v-if="row.referralStatus === 5">已接诊</span>
        <span v-if="row.referralStatus === 7">已拒绝</span>
        <span v-if="row.referralStatus === 9">已撤销</span>
      </template>

      <template #referralType="{ row }">
        <span v-if="row.referralType === 1">上转</span>
        <span v-if="row.referralType === 2">下转</span>
      </template>

      <template #treatSuggest="{ row }">
        <span v-if="row.referralType === 1">门诊</span>
        <span v-if="row.referralType === 2">住院</span>
      </template>

      <template #outReason="{ row }">
        <span v-if="row.referralType === 1">病情康复</span>
        <span v-if="row.referralType === 2">病情稳定</span>
        <span v-if="row.referralType === 3">患者意愿</span>
      </template></base-table>
  </div>
</template>

<script>
import { getUserDbReferralRecord } from '@/api/archives'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'

export default {
  name: 'ReferralRecord',
  components: {
    BaseTable
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        patientId: this.$route.query.id
      },
      columns: [
        { label: '申请时间', prop: 'planDate' },
        { label: '申请类型', prop: 'referralType', slot: 'referralType' },
        { label: '转出单位', prop: 'outDepartName' },
        { label: '转入单位', prop: 'inDepartName' },
        { label: '转诊建议', prop: 'treatSuggest', slot: 'treatSuggest' },
        { label: '转出原因', prop: 'outReason', slot: 'outReason' },
        { label: '转诊状态', prop: 'referralStatus', slot: 'referralStatus' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      return await getUserDbReferralRecord(params)
    }
  }
}
</script>
