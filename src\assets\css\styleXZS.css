.addBtn .iconfont.icon_font_btn {
  font-size: 0.8rem;
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.addBtn .iconfont.search_color {
  color: var(--colorBtn) !important;
}

.cursor {
  cursor: pointer;
}

/* 表格样式 */
.el-table.tabelCenter .el-table__header th {
  background: var(--tableTitleBgColor);
}

.el-table.tabelCenter .el-table__header {
  border-radius: 0;
}

.el-table.tabelCenter .el-table__body-wrapper tr:nth-child(even) {
  background: var(--tableCellBgColor);
}

/* 消息弹框 */
body .el-message-box {
  --el-messagebox-width: 24rem;
  --el-messagebox-height: 9.6rem;
  --el-messagebox-padding-primary: 1.6rem;
  --el-messagebox-padding-bottom: .6rem;
  /* height: 12rem; */
  box-shadow: 0 .6rem 2.4rem .8rem rgba(0, 0, 0, 0.03), 0 .45rem 1.4rem 0 rgba(0, 0, 0, 0.05), 0 .3rem .8rem -0.4rem rgba(0, 0, 0, 0.08);
  border-radius: .1rem;
  /* padding: 2rem 2rem 1.5rem 2rem; */
}

body .el-message-box .el-message-box__header {
  padding-bottom: 0;
  margin-bottom: .6rem;

}

body .el-message-box .el-message-box__header.show-close {
  padding-right: 0;
  padding-left: 2.9rem;
}

body .el-message-box .el-message-box__container {
  position: relative;
}

body .el-message-box .el-message-box__container .el-icon {
  position: absolute;
  top: -1.8rem;
}


body .el-message-box .el-message-box__btns .el-button.el-button--primary {
  background: var(--btnPrimaryBgColor);
}

body .el-button.el-button--primary {
  background: var(--btnPrimaryBgColor);
}

/* 分页 */
#app .el-pagination li {
  background: #fff;
  border: 0.05rem solid #DCDFE6;
}

#app .el-pagination li.is-active,#app .el-pagination li.active {
  background: var(--paginationActiveBgColor);
  color: var(--paginationActiveColor);
}

/* #app .el-pager li:hover {
  color: #000;
} */

#app .el-pagination li:hover {
  border-radius: .15rem;
  border: 0.05rem solid;
  border-image: linear-gradient(133deg, rgba(63, 225, 210, 1), rgba(1, 178, 221, 1)) 1 1;
}

/* 新增视频-关联积分 */
.el-input-number.relevance_points .el-input .el-input__inner {
  text-align: left;
}

/* 题目类型 */
#app .el-select {
  padding: 0;
}

/* 取消按钮样式 */
/* .el-button:hover {
  background-color: var(--colorBtnHover);
  border-color: var(--colorBtnHover);
  color: #fff
} */

/* 科普浏览-文章查看 */
.article-content p img {
  margin-top: 1rem;
}

/* 权重样式 */
#app .el-input.weight_input .el-input__wrapper {
  border-radius: 0.4rem 0 0 0.4rem;
}

/* 输入框显示字体大小 */
#app .el-input__inner::placeholder,
#app input.el-range-input::placeholder {
  font-size: .6563rem;
}

.el-select__selected-item.el-select__placeholder.is-transparent {
  font-size: .6563rem;
}

.el-input.plan_name_input .el-input__inner {
  text-align: center;
}

/* 文章查看 */
#app .article_content p {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: .8438rem;
  color: #666666;
  line-height: 1.4063rem;
}

/* 获取焦点样式 */
body .el-input .el-input__wrapper:focus {
  box-shadow: 0 0 0 0.05rem var(--el-input-focus-border-color) inset
}

body .el-select .el-select__wrapper.is-focused {
  box-shadow: 0 0 0 0.05rem var(--el-color-primary) inset
}

body .el-range-editor.is-active,
body .el-range-editor.is-active:hover {
  box-shadow: 0 0 0 0.05rem var(--el-input-focus-border-color) inset
}

.article_content_video {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: .8438rem;
  color: #666666;
  line-height: 1.4063rem;
}

/* 数字输入 */
body .el-input-number.common_input_number .el-input-number__increase {
  margin-right: 0;
}