import request from '@/utils/request'

// 分页查询协议模板
export const getProtocolTemplatePageList = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/protocolModule/page',
    method: 'post',
    data
  })
}

// 新增协议模板
export const addProtocolTemplate = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/protocolModule/save',
    method: 'post',
    data
  })
}

// 查询协议模板详情
export const getProtocolTemplateDetail = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/protocolModule/detail',
    method: 'post',
    data
  })
}

// 删除协议模板
export const deleteProtocolTemplate = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/protocolModule/remove',
    method: 'post',
    data
  })
}

// 启用/禁用协议模板
export const enableProtocolTemplate = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/protocolModule/enabled',
    method: 'post',
    data
  })
}
