<template>
  <div class="searchItemCheck">
    <div
      v-if="hasAll"
      class="check-item fontSize_14 flex_center"
      :class="[{ active: value === null }, className]"
      @click="checkAllClick"
    >
      全部
    </div>
    <div
      v-for="item in options"
      :key="item.code"
      class="check-item fontSize_14 flex_center"
      :class="[isActive(item.code), className]"
      :style="{ cursor: item.value === 'HIS' ? 'no-drop' : '' }"
      @click="checkItemClick(item)"
    >
      {{ item.value }}
    </div>
  </div>
</template>
<script>
import { deepClone } from '@/utils/index'

export default {
  name: 'SearchItemCheck',
  props: {
    value: {
      type: [String, Number, Boolean, Array, Object],
      default: null
    },
    multiple: {
      type: Boolean,
      default: false
    },
    hasAll: {
      type: Boolean,
      default: false
    },
    options: {
      type: Array,
      default: () => {
        return []
      }
    },
    className: {
      type: String,
      default: ''
    }
  },
  methods: {
    isActive(code) {
      if (this.multiple) {
        if (this.value && this.value.includes(code)) {
          return 'active'
        } else {
          return ''
        }
      } else if (this.value === code) {
        return 'active'
      } else {
        return ''
      }
    },
    checkAllClick() {
      let code = ''
      if (this.value === '' || this.value) {
        code = null
      }

      this.$emit('input', code)
      this.$nextTick(() => {
        this.$emit('change', code)
      })
    },
    checkItemClick(item) {
      if (item.value === 'HIS') return

      if (this.multiple) {
        const val = this.value ? deepClone(this.value) : []
        if (val.includes(item.code)) {
          const newVal = val.filter((e) => e !== item.code)
          this.$emit('input', newVal)
          this.$nextTick(() => {
            this.$emit('change', newVal)
          })
        } else {
          val.push(item.code)
          this.$emit('input', val)
          this.$nextTick(() => {
            this.$emit('change', val)
          })
        }
      } else {
        let { code } = item
        if (this.value === item.code) {
          code = ''
        }
        this.$emit('input', code)
        this.$nextTick(() => {
          this.$emit('change', code)
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.searchItemCheck {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .check-item {
    height: 1.6rem;
    border-radius: 0.8rem;
    margin-bottom: 0.8rem;
    margin-right: 2.1rem;
    margin-left: 0rem;
    color: #555;
    background: #e9e9e9;
    border: 1px solid #e9e9e9;
    cursor: pointer;
    width: 4.6rem;
    padding: 0;
    box-sizing: border-box;
    min-width: auto;
    &.check-item2 {
      width: 5.7rem;
      margin-right: 0.3rem;
    }
    &:nth-child(2n) {
      margin-right: 0rem;
    }
    &.active {
      background: #0a86c8;
      color: #fff;
      border: 1px solid #0a86c8;
    }
  }
  // @media (max-width: 1280px) {
  //   .check-item {
  //     width: 4.4rem;
  //     margin-right: 1.4rem;
  //     &.check-item2 {
  //       width: 5rem;
  //       margin-right: 0.5rem;
  //     }
  //   }
  // }
}
</style>
