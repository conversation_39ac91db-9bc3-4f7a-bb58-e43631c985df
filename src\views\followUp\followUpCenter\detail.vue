<template>
  <div class="follow-up-detail">
    <el-card>
      <h2 style="text-align: center; margin: 16px 0">病例随访表</h2>
      <div class="follow-up-detail-content">
        <el-form ref="formRef" :model="form" :rules="rule">
          <el-row>
            <el-col v-if="$route.query.type === 'evaluation'" :span="6" style="margin-left: 16px">
              <el-form-item label="评估状态：" prop="auditResult">
                <el-radio-group v-model="form.auditResult">
                  <el-radio :label="1">稳定</el-radio>
                  <el-radio :label="2">异常</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="follow-up-detail-content-wrapper">
            <div v-if="$route.query.type === 'evaluation'" class="mask" />
            <div v-for="key in groupKeys" :key="key">
              <el-row>
                <!-- 除随访方式外，其他分组显示标记标题 -->
                <flag-component v-if="key !== 'way'" :title="followUpMap[key]" style="margin-bottom: 10px" />
                <el-col v-for="item in followUp[key]" :key="item.label" :span="item.span" style="margin-left: 16px">
                  <component
                    :is="getComponent(item.type)"
                    v-model="form[item.prop]"
                    :item="{
                      ...item,
                      disabled: typeof item.disabled === 'function' ? item.disabled($route.query.type) : false
                    }"
                    :form-data="form"
                  />
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
    </el-card>
    <div
      v-if="$route.query.type !== 'view'"
      style="
        position: fixed;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        z-index: 888;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        background-color: #fff;
        padding: 10px;
        border-radius: 4px;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
      "
    >
      <el-button type="primary" @click="handleComplete">
        {{ $route.query.type === 'evaluation' ? '保存' : '完成随访' }}
      </el-button>
    </div>
  </div>
</template>

<script>
import { followUp, followUpMap } from './followUp'
import { getFollowUpDetail, completeFollowUp, evaluationFollowUp } from '@/api/followUp'
import TextField from '@/components/questionnaireElementUi/TextField.vue'
import RadioGroupField from '@/components/questionnaireElementUi/RadioGroupField.vue'
import SelectField from '@/components/questionnaireElementUi/SelectField.vue'
import DateField from '@/components/questionnaireElementUi/DateField.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'
import MedicationTable from '@/views/receptionCenter/patientReception/componentDetail/medicationTable.vue'
import UploadField from '@/components/questionnaireElementUi/uploadField.vue'
import FlagComponent from '@/components/flagComponent/index.vue'
import dayjs from 'dayjs'

export default {
  name: 'FollowUpCenterDetail',
  components: {
    TextField,
    RadioGroupField,
    SelectField,
    DateField,
    CheckboxGroupField,
    MedicationTable,
    UploadField,
    FlagComponent
  },
  data() {
    const form = {}
    const rule = {}

    Object.entries(followUp).forEach(([key, value]) => {
      value.forEach((item) => {
        if (item.type === 'MedicationTable' || item.type === 'checkbox') {
          form[item.prop] = []
        } else {
          form[item.prop] = ''
        }

        if (item.required) {
          rule[item.prop] = [
            { required: true, message: item.label.split('：')[0], trigger: item.type === 'radio' ? 'change' : 'blur' }
          ]
        }
      })
    })

    return {
      form,
      rule: {
        auditResult: [{ required: true, message: '请选择评估状态', trigger: 'change' }],
        ...rule
      },
      followUp,
      followUpMap,
      /* 按对象属性顺序渲染 */
      groupKeys: Object.keys(followUp)
    }
  },
  async created() {
    // 获取药品列表
    await this.$store.dispatch('drugManagement/getDrugList')
    await this.getDetail()
  },
  methods: {
    async getDetail() {
      const res = await getFollowUpDetail({ id: this.$route.query.id })
      if (res.code === 200) {
        const cspBadEvent = {}
        Object.entries(res.data.cspBadEvent || {}).forEach(([key, value]) => {
          if (key.includes('badEvent')) {
            cspBadEvent[key] = value
          }
        })
        this.form = {
          ...res.data.interviewRecord,
          ...cspBadEvent,
          medicalList: res.data.medicalList,
          realDate: res.data.interviewRecord.realDate
            ? res.data.interviewRecord.realDate
            : dayjs().format('YYYY-MM-DD'),
          badEvent: cspBadEvent.badEvent ? cspBadEvent.badEvent.split(',') : []
        }
        this.$nextTick(() => {
          this.$refs.formRef.clearValidate()
        })
      }
    },

    getComponent(type) {
      const map = {
        radio: 'RadioGroupField',
        select: 'SelectField',
        date: 'DateField',
        checkbox: 'CheckboxGroupField',
        MedicationTable: 'MedicationTable',
        upload: 'UploadField'
      }
      return map[type] || 'TextField'
    },

    async handleComplete() {
      try {
        const valid = await this.$refs.formRef.validate()
        if (!valid) return

        const cspBadEvent = Object.fromEntries(Object.entries(this.form).filter(([key]) => key.includes('badEvent')))

        const params = {
          ...this.form,
          cspBadEvent: {
            ...cspBadEvent,
            badEvent: (cspBadEvent.badEvent || []).join(',')
          },
          id: this.$route.query.id
        }

        const api =
          this.$route.query.type === 'evaluation'
            ? evaluationFollowUp({
              id: params.id,
              auditResult: this.form.auditResult
            })
            : completeFollowUp(params)

        const res = await api

        if (res.code === 200) {
          this.$message.success(this.$route.query.type === 'evaluation' ? '评估完成' : '随访完成')
          setTimeout(() => this.$router.back(), 500)
        }
      } catch (err) {
        console.error(err)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.follow-up-detail {
  .el-card {
    margin: 16px;
  }

  .follow-up-detail-content-wrapper {
    position: relative;
    .mask {
      position: absolute;
      top: -8px;
      left: -8px;
      width: calc(100% + 16px);
      height: calc(100% + 16px);
      background: rgba(0, 0, 0, 0.05);
      z-index: 10;
      cursor: not-allowed;
    }
  }
}
</style>
