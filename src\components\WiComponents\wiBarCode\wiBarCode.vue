<template>
  <content class="barCodeContainer">
    <barcode :code="code" @click.native="dialogVisible = true" />
    <el-dialog
      v-el-drag-dialog
      custom-class="applicationCustomDialog noPaddingDialog barCodeContainerDialog"
      :visible="dialogVisible"
      width="50vw"
      :close-on-click-modal="false"
      :before-close="
        done => {
          dialogVisible = false
          done()
        }
      "
    >
      <!-- 标题 -->
      <span slot="title" class="customDialogTitle customLeftDialogTitle">
        <span>条形码</span>
      </span>
      <div class="bigBarBox">
        <barcode :code="code" />
      </div>
    </el-dialog>
  </content>
</template>

<script>
export default {
  name: 'WiBarCode',
  components: {
    barcode: () => import('@/components/barcode/barcode.vue')
  },
  props: {
    code: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.barCodeContainer {
  display: block;
  width: 14vw;
  height: 100%;
  & > div {
    width: 100%;
    height: 100%;
  }
  ::v-deep .barcode {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }
}
.barCodeContainerDialog {
  .bigBarBox {
    width: calc(50vw - 64px);
    height: 10vw;
    & > div {
      width: 100%;
      height: 100%;
    }
    ::v-deep .barcode {
      width: 100%;
      height: 100%;
      cursor: default;
    }
  }
}
</style>
