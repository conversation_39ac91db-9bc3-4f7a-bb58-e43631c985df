import request from '@/utils/request'

export function userImApi(query) {
  return request({
    url: '/cspapi/backend/chat2/user/im',
    method: 'get',
    params: query
  })
}
// (风险预警)查询最近4条告警信息
export function getQueryPhrLastAlarmsID(id) {
  return request({
    url: `/cspapi/backend/alarm/hr/queryPhrLastAlarms/${id}`,
    method: 'get'
  })
}

// 基础资料
export function userPatientApi(id) {
  return request({
    url: `/cspapi/backend/user/patient/${id}`,
    method: 'get'
  })
}

// 查看2人之间的聊天记录
export function chat2HistoryApi(id) {
  return request({
    url: `/cspapi/backend/chat2/history/${id}`,
    method: 'get'
  })
}

// 上传 粘贴的图片
export function uploadPasteApi(data) {
  return request({
    url: '/cspapi/backend/cos/uploadFile/private',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
    // headers:{Content-Type:'multipart/form-data'}
  })
}
