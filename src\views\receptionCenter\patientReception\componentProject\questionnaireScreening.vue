<!-- 问卷筛查 -->

<template>
  <div class="questionnaire-screening">
    <p>每小题只选择1个最符合的答案，参考评分标准得分，相加得总分。</p>
    <el-table :data="flatTableData" border :span-method="tableSpanMethod" style="width: 80%; margin-top: 32px">
      <el-table-column prop="question" label="问题" align="center">
        <template slot-scope="scope">
          <div>
            <span style="color: red">*</span>
            <span>{{ scope.row.question }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="optionLabel" label="回答" align="center" />
      <el-table-column prop="score" label="评分标准" align="center" />
      <el-table-column label="选项" align="center">
        <template slot-scope="scope">
          <el-radio
            v-model="tableData[scope.row.qIndex].answer"
            :label="scope.row.optionValue"
            @change="onAnswerChange(scope.row.qIndex, scope.row.optionValue)"
          >{{ '' }}</el-radio>
        </template>
      </el-table-column>
      <el-table-column label="得分" align="center">
        <template slot-scope="scope">
          <span>{{ typeof scope.row.rowScore === 'number' ? scope.row.rowScore : null }}</span>
          <span>{{ typeof scope.row.rowScore === 'number' ? '分' : null }}</span>
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 16px; font-weight: bold">总分：{{ totalScore }}</div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'

export default {
  name: 'QuestionnaireScreening',
  data() {
    return {
      tableData: [
        {
          question: '1.您的年龄?',
          options: [
            { label: '40-49岁', score: 0, value: '1' },
            { label: '50-59岁', score: 3, value: '2' },
            { label: '60-69岁', score: 7, value: '3' },
            { label: '≥70岁', score: 10, value: '4' }
          ],
          rowScore: null,
          answer: ''
        },
        {
          question: '2.您吸烟总量（包年）每天吸烟（）包X吸烟（）年',
          options: [
            { label: '1-14包年', score: 0, value: '1' },
            { label: '15-30包年', score: 3, value: '2' },
            { label: '>30包年', score: 7, value: '3' }
          ],
          rowScore: null,
          answer: ''
        },
        {
          question: '3.您的体重指数(kg/m²)(如果您不会计算，您的体重属于哪一块？很瘦(7) 一般(4) 有胖(1) 很胖(0))',
          options: [
            { label: '<18.5kg/m²', score: 7, value: '1' },
            { label: '18.5-23.9kg/m²', score: 4, value: '2' },
            { label: '24.0-27.9kg/m²', score: 1, value: '3' },
            { label: '≥28.0kg/m²', score: 0, value: '4' }
          ],
          rowScore: null,
          answer: ''
        },
        {
          question: '4.没感冒时您是否经常咳嗽?',
          options: [
            { label: '是', score: 3, value: '1' },
            { label: '否', score: 0, value: '2' }
          ],
          rowScore: null,
          answer: ''
        },
        {
          question: '5.您平时是否感觉到气促?',
          options: [
            { label: '没有气促', score: 0, value: '1' },
            { label: '在平地急行或爬小坡时感觉气促', score: 2, value: '2' },
            { label: '平地正常行走时感到气促', score: 3, value: '3' }
          ],
          rowScore: null,
          answer: ''
        },
        {
          question: '6.您目前使用雾化呼吸机等装置吸取氧吗?',
          options: [
            { label: '是', score: 1, value: '1' },
            { label: '否', score: 0, value: '2' }
          ],
          rowScore: null,
          answer: ''
        },
        {
          question: '7.您父母、兄弟姐妹及子女中，是否有人患有哮喘、慢性支气管炎、肺气肿或慢阻肺?',
          options: [
            { label: '是', score: 2, value: '1' },
            { label: '否', score: 0, value: '2' }
          ],
          rowScore: null,
          answer: ''
        }
      ],
      itemId: '',
      itemDetailId: ''
    }
  },
  computed: {
    flatTableData() {
      // 每个选项一行，问题只在第一个选项行显示
      const rows = []
      this.tableData.forEach((q, qIndex) => {
        q.options.forEach((opt, oIndex) => {
          rows.push({
            qIndex,
            oIndex,
            question: oIndex === 0 ? q.question : '',
            optionLabel: opt.label,
            score: opt.score,
            optionValue: opt.value,
            rowScore: q.rowScore
          })
        })
      })
      return rows
    },

    totalScore() {
      return this.tableData.reduce((sum, q) => {
        const opt = q.options.find((o) => o.value === q.answer)
        return sum + (opt ? opt.score : 0)
      }, 0)
    }
  },
  methods: {
    initData(data) {
      const tableDataTemp = cloneDeep(this.tableData)
      tableDataTemp.forEach((item, idx) => {
        item.answer = data.data && data.data[`copd${idx + 1}`]
        item.rowScore = item.answer ? item.options.find((o) => o.value === item.answer).score : null
      })
      this.tableData = tableDataTemp
      this.itemId = data.id
      this.itemDetailId = data.data && data.data.id
    },
    tableSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        // 问题列
        const { qIndex } = row
        const optLen = this.tableData[qIndex].options.length
        if (row.oIndex === 0) {
          return { rowspan: optLen, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      } else if (columnIndex === 4) {
        // 得分列（第5列，索引为4）
        const { qIndex } = row
        const optLen = this.tableData[qIndex].options.length
        if (row.oIndex === 0) {
          return { rowspan: optLen, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      }
    },
    onAnswerChange(qIndex, value) {
      this.$set(this.tableData[qIndex], 'answer', value)
      this.$set(this.tableData[qIndex], 'rowScore', this.tableData[qIndex].options.find((o) => o.value === value).score)
    },

    handleSave() {
      return {
        itemId: this.itemId,
        itemDetailId: this.itemDetailId,
        ...this.tableData.reduce((acc, cur, index) => {
          acc[`copd${index + 1}`] = cur.answer
          return acc
        }, {})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.questionnaire-screening {
  padding: 16px;
}
</style>
