<template>
  <div class="contract-service-package">
    <div class="servicePack">
      <ServicePack ref="servicePack" @checkIdChange="handleCheckIdChange" @getServicePackList="getServicePackList" />
    </div>
    <div class="serviceItems">
      <ServiceItems ref="serviceItems" @handleAssociateSuccess="handleAssociateSuccess" />
    </div>
  </div>
</template>

<script>
import ServicePack from './component/servicePack.vue'
import ServiceItems from './component/serviceItems.vue'

export default {
  name: 'ContractServicePackage',
  components: {
    ServicePack,
    ServiceItems
  },
  mounted() {
    this.$refs.servicePack.getServicePackListFn()
  },
  methods: {
    handleCheckIdChange(id) {
      this.$refs.serviceItems.queryParams.spId = id
      this.$refs.serviceItems.handleSearch()
    },

    getServicePackList(list) {
      this.$refs.serviceItems.servicePackList = list
    },

    handleAssociateSuccess(id) {
      this.$refs.servicePack.checkId = id
      this.$refs.servicePack.getServicePackListFn()
      this.$refs.serviceItems.queryParams.spId = id
      this.$refs.serviceItems.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';

.contract-service-package {
  width: 100%;
  height: 100%;
  padding: 16px;
  display: flex;
  gap: 16px;
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
    padding: 0 15px;
  }
  .servicePack {
    width: 360px;
  }
  .serviceItems {
    flex: 1;
  }
}
</style>
