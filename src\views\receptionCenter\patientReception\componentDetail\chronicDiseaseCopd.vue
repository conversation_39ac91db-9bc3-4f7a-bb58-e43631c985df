<!-- 慢病病种-慢阻肺 -->
<template>
  <div class="chronic-disease-copd">
    <el-tabs v-model="activeName">
      <el-tab-pane label="问卷筛查" name="1" />
      <el-tab-pane label="肺功能仪检测" name="2" />
      <el-tab-pane label="血氧" name="3" />
      <el-tab-pane label="动脉血气" name="4" />
      <el-tab-pane label="肺部CT" name="5" />
    </el-tabs>
    <div v-show="activeName === '1'">
      <questionnaire-screening ref="questionnaireScreeningRef" />
    </div>
    <div v-show="activeName === '2'">
      <pulmonary-func-monitor ref="pulmonaryFuncMonitorRef" />
    </div>

    <div v-show="activeName === '3'">
      <BloodOxygen ref="bloodOxygenRef" />
    </div>

    <div v-show="activeName === '4'">
      <arterial-blood-gas ref="arterialBloodGasRef" />
    </div>

    <div v-show="activeName === '5'">
      <PulmonaryCT ref="pulmonaryCtRef" />
    </div>
  </div>
</template>

<script>
import QuestionnaireScreening from '../componentProject/questionnaireScreening.vue'
import PulmonaryFuncMonitor from '../componentProject/pulmonaryFuncMonitor.vue'
import ArterialBloodGas from '../componentProject/arterialBloodGas.vue'
import PulmonaryCT from '../componentProject/pulmonaryCT.vue'
import BloodOxygen from '../componentProject/bloodOxygen.vue'

export default {
  name: 'ChronicDiseaseCopd',
  components: {
    QuestionnaireScreening, // 问卷筛查
    PulmonaryFuncMonitor, // 肺功能仪监测
    ArterialBloodGas, // 动脉血气
    PulmonaryCT, // 肺部CT
    BloodOxygen // 血氧
  },
  data() {
    return {
      activeName: '1'
    }
  },
  methods: {
    initData(data) {
      const questionnaireData = data.find((item) => item.itemCode === 'COPD_QUESTION') || {}
      const pulmonaryFuncMonitorData = data.find((item) => item.itemCode === 'COPD_MONITOR') || {}
      const arterialBloodGasData = data.find((item) => item.itemCode === 'ABG') || {}
      const pulmonaryCtData = data.find((item) => item.itemCode === 'COPD_CT') || {}
      const bloodOxygenData = data.find((item) => item.itemCode === 'BLOOD_OXYGEN') || {}

      this.$refs.questionnaireScreeningRef.initData(questionnaireData)
      this.$refs.pulmonaryFuncMonitorRef.initData(pulmonaryFuncMonitorData)
      this.$refs.arterialBloodGasRef.initData(arterialBloodGasData)
      this.$refs.pulmonaryCtRef.initData(pulmonaryCtData)
      this.$refs.bloodOxygenRef.initData(bloodOxygenData)
    },

    async handleSave() {
      const result = this.$refs.questionnaireScreeningRef.handleSave()
      let success = true
      for (let i = 0; i < result.length; i++) {
        if (!result[i].answer) {
          success = false
          break
        }
      }
      return {
        name: '慢阻肺-问卷筛查',
        success,
        data: {
          questionnaireScreening: {
            ...result,
            name: '问卷筛查',
            itemCode: 'COPD_QUESTION'
          },
          pulmonaryFuncMonitor: {
            ...this.$refs.pulmonaryFuncMonitorRef.form,
            name: '肺功能仪监测',
            itemCode: 'COPD_MONITOR'
          },
          arterialBloodGas: {
            ...this.$refs.arterialBloodGasRef.form,
            arteryVigorResult: this.$refs.arterialBloodGasRef.form.arteryVigorResult.join(','),
            name: '动脉血气',
            itemCode: 'ABG'
          },
          pulmonaryCt: {
            ...this.$refs.pulmonaryCtRef.form,
            ctResult: this.$refs.pulmonaryCtRef.form.ctResult.join(','),
            attachmentVideoUrl: this.$refs.pulmonaryCtRef.form.attachmentVideoUrl.join(','),
            name: '肺部CT',
            itemCode: 'COPD_CT'
          },
          bloodOxygen: {
            ...this.$refs.bloodOxygenRef.form,
            name: '血氧',
            itemCode: 'BLOOD_OXYGEN'
          }
        }
      }
    }
  }
}
</script>
