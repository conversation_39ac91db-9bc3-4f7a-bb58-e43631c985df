<template>
  <div class="app-container">
    <div class="organization-container">
      <!-- 左侧树形结构 -->
      <div class="left-tree">
        <div class="search-box">
          <el-input v-model="searchText" placeholder="请输入关键字搜索" prefix-icon="el-icon-search" clearable />
        </div>
        <div class="add-org">
          <el-button type="text" icon="el-icon-plus" @click="addRootOrg">添加机构</el-button>
        </div>
        <div class="tree-container">
          <el-tree
            ref="orgTree"
            v-loading="loading"
            :data="treeData"
            node-key="departCode"
            default-expand-all
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            :draggable="true"
            :allow-drop="allowDrop"
            :allow-drag="allowDrag"
            @node-click="handleNodeClick"
            @node-drop="handleNodeDrop"
            @node-drag-start="handleNodeDragStart"
            @node-drag-enter="handleNodeDragEnter"
            @node-drag-leave="handleNodeDragLeave"
            @node-drag-end="handleNodeDragEnd"
          >
            <span
              slot-scope="{ node, data }"
              class="custom-tree-node"
              @mouseenter="handleNodeMouseEnter(data)"
              @mouseleave="handleNodeMouseLeave"
            >
              <span>{{ node.label }}</span>
              <span class="tree-action" :class="{ 'visible': hoveredNodeId === data.departCode }">
                <el-button type="text" icon="el-icon-plus" size="mini" @click.stop="() => addChild(data)" />
                <el-button type="text" icon="el-icon-edit" size="mini" @click.stop="() => editNode(data)" />
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  size="mini"
                  style="color: red"
                  @click.stop="() => removeNode(node, data)"
                />
              </span>
            </span>
          </el-tree>
          <div v-if="treeData.length === 0 && !loading" class="no-data">
            <el-empty description="暂无组织架构数据" />
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="right-content">
        <div class="title-bar">
          <h3>{{ isEdit ? '编辑' : '新增' }}</h3>
        </div>
        <div class="form-container">
          <el-form ref="orgForm" v-loading="formLoading" :model="orgForm" :rules="rules" label-width="80px">
            <el-form-item label="编号" prop="departCode">
              <el-input v-model="orgForm.departCode" :disabled="isEdit" placeholder="请输入编号" />
            </el-form-item>
            <el-form-item label="名称" prop="departName">
              <el-input v-model="orgForm.departName" placeholder="请输入名称" />
            </el-form-item>
            <el-form-item label="上一级">
              <el-input v-model="parentOrgName" disabled />
            </el-form-item>
            <el-form-item label="联系人">
              <el-input v-model="orgForm.contractName" placeholder="请输入联系人姓名" />
            </el-form-item>
            <el-form-item label="联系方式">
              <el-input v-model="orgForm.contractPhone" placeholder="请输入联系方式" />
            </el-form-item>
            <el-form-item label="地址">
              <el-input v-model="orgForm.address" placeholder="请输入地址" />
            </el-form-item>
            <el-form-item label="备注">
              <el-input v-model="orgForm.remark" type="textarea" placeholder="请输入说明" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" :loading="submitLoading" @click="saveOrg">保存</el-button>
              <el-button @click="resetForm">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getOrgTreeApi, saveOrgTreeApi, deleteOrgTreeApi } from '@/api/system'

export default {
  name: 'Basics',
  data() {
    return {
      searchText: '',
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'departName'
      },
      orgForm: {
        id: '',
        parentDepartCode: '',
        departCode: '',
        departName: '',
        contractName: '',
        contractPhone: '',
        address: '',
        remark: ''
      },
      parentOrgName: '',
      rules: {
        departCode: [{ required: true, message: '请输入编号', trigger: 'blur' }],
        departName: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      },
      isEdit: false,
      currentNode: null,
      loading: false,
      formLoading: false,
      submitLoading: false,
      hoveredNodeId: null,
      draggingNode: null // 当前拖拽的节点
    }
  },
  watch: {
    searchText(val) {
      this.$refs.orgTree.filter(val)
    }
  },
  created() {
    this.getOrgList()
  },
  methods: {
    // 获取组织列表
    async getOrgList() {
      try {
        this.loading = true
        // 根据用户ID获取医疗机构树
        const res = await getOrgTreeApi()
        if (res.code === 200) {
          this.treeData = res.data
          this.loading = false
        } else {
          this.$message.error(res.msg)
          this.loading = false
        }
      } catch (error) {
        this.$message.error('获取组织架构失败')
        console.error(error)
        this.loading = false
      }
    },
    // 点击树节点
    handleNodeClick(data) {
      this.isEdit = true
      this.currentNode = data
      this.formLoading = true

      this.orgForm = {
        id: data.id || '',
        departCode: data.departCode,
        departName: data.departName,
        parentDepartCode: data.parentDepartCode,
        contractName: data.contractName || '',
        contractPhone: data.contractPhone || '',
        address: data.address || '',
        remark: data.remark || ''
      }
      // 获取父级名称
      if (data.parentDepartCode) {
        const parentNode = this.findNodeById(this.treeData, data.parentDepartCode)
        this.parentOrgName = parentNode ? parentNode.departName : ''
      } else {
        this.parentOrgName = ''
      }

      this.$refs.orgForm.clearValidate()

      setTimeout(() => {
        this.formLoading = false
      }, 300)
    },
    // 根据ID查找节点
    findNodeById(tree, id) {
      for (const node of tree) {
        if (node.departCode === id) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(node.children, id)
          if (found) return found
        }
      }
      return null
    },
    // 添加根节点
    addRootOrg() {
      this.resetForm()
      this.isEdit = false
      this.currentNode = null
      this.parentOrgName = ''
    },
    // 添加子节点
    addChild(data) {
      this.resetForm()
      this.isEdit = false
      this.currentNode = data
      this.orgForm.parentDepartCode = data.departCode
      this.parentOrgName = data.departName
    },
    // 编辑节点
    editNode(data) {
      this.handleNodeClick(data)
    },
    // 删除节点
    removeNode(node, data) {
      this.$confirm('确定删除该组织机构吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          try {
            // API调用删除节点 - 使用id参数
            const res = await deleteOrgTreeApi({ id: data.id })
            if (res.code === 200) {
              this.$message({
                type: 'success',
                message: '删除成功!'
              })
              this.resetForm()
              // 刷新树形结构
              this.getOrgList()
            } else {
              this.$message.error(res.msg || '删除失败')
            }
          } catch (error) {
            this.$message.error(`删除失败：${error.msg}` || '未知错误')
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    // 保存组织
    saveOrg() {
      this.$refs.orgForm.validate(async(valid) => {
        if (valid) {
          try {
            this.submitLoading = true
            if (this.isEdit) {
              // 编辑现有节点
              await this.updateNode(this.orgForm)
            } else {
              // 添加新节点
              await this.addNode(this.orgForm)
            }
            this.submitLoading = false
          } catch (error) {
            this.submitLoading = false
            this.$message.error(`保存失败：${error.msg || '未知错误'}`)
          }
        } else {
          return false
        }
      })
    },
    // 更新节点
    async updateNode(form) {
      try {
        // 构建保存参数
        const params = {
          id: form.id,
          departCode: form.departCode,
          parentDepartCode: form.parentDepartCode,
          departName: form.departName,
          contractName: form.contractName,
          contractPhone: form.contractPhone,
          address: form.address,
          remark: form.remark
        }

        // API调用
        const res = await saveOrgTreeApi(params)

        if (res.code === 200) {
          this.$message.success('更新成功')
          // 刷新树形结构
          this.getOrgList()
        } else {
          this.$message.error(res.msg || '更新失败')
        }
      } catch (error) {
        this.$message.error(`更新失败：${error.msg}` || '未知错误')
      }
    },
    // 添加节点
    async addNode(form) {
      try {
        // 构建保存参数 - 新增不需要传id
        const params = {
          parentDepartCode: form.parentDepartCode,
          departCode: form.departCode,
          departName: form.departName,
          contractName: form.contractName,
          contractPhone: form.contractPhone,
          address: form.address,
          remark: form.remark
        }

        // API调用
        const res = await saveOrgTreeApi(params)

        if (res.code === 200) {
          this.$message.success('添加成功')
          this.resetForm()
          // 刷新树形结构
          this.getOrgList()
        } else {
          this.$message.error(res.msg || '添加失败')
        }
      } catch (error) {
        this.$message.error(`添加失败：${error.msg}` || '未知错误')
      }
    },
    // 重置表单
    resetForm() {
      if (this.$refs.orgForm) {
        this.$refs.orgForm.resetFields()
      }
      this.orgForm = {
        id: '',
        parentDepartCode: '',
        departCode: '',
        departName: '',
        contractName: '',
        contractPhone: '',
        address: '',
        remark: ''
      }
    },
    // 过滤节点
    filterNode(value, data) {
      if (!value) return true
      return data.departName.indexOf(value) !== -1 || (data.departCode && data.departCode.indexOf(value) !== -1)
    },
    handleNodeMouseEnter(data) {
      this.hoveredNodeId = data.departCode
    },
    handleNodeMouseLeave() {
      this.hoveredNodeId = null
    },
    // 拖拽相关方法
    allowDrop(draggingNode, dropNode, type) {
      // 不允许拖拽到自己身上
      if (draggingNode.data.departCode === dropNode.data.departCode) {
        return false
      }

      // 不允许拖拽到自己的子节点（避免循环引用）
      const isChild = this.isChildNode(draggingNode.data.departCode, dropNode.data.departCode)
      if (isChild) {
        return false
      }

      // 不允许拖拽到根节点作为子节点（如果需要限制的话）
      // if (type === 'inner' && dropNode.level === 0) {
      //   return false
      // }

      return true
    },

    // 检查是否是子节点关系
    isChildNode(parentCode, childCode) {
      const findNode = (nodes, targetCode) => {
        for (const node of nodes) {
          if (node.departCode === targetCode) {
            return node
          }
          if (node.children && node.children.length > 0) {
            const found = findNode(node.children, targetCode)
            if (found) return found
          }
        }
        return null
      }

      const parentNode = findNode(this.treeData, parentCode)
      if (!parentNode || !parentNode.children) {
        return false
      }

      // 递归检查所有子节点
      const checkChildren = (children, targetCode) => {
        for (const child of children) {
          if (child.departCode === targetCode) {
            return true
          }
          if (child.children && child.children.length > 0) {
            if (checkChildren(child.children, targetCode)) {
              return true
            }
          }
        }
        return false
      }

      return checkChildren(parentNode.children, childCode)
    },
    allowDrag(draggingNode) {
      // 允许拖拽节点
      return true
    },
    handleNodeDrop(draggingNode, dropNode, dropType, ev) {
      console.log('拖拽完成:', {
        draggingNode: draggingNode.data,
        dropNode: dropNode.data,
        dropType
      })

      // 获取目标父节点
      let targetParentNode = null
      let targetParentCode = null

      if (dropType === 'inner') {
        // 拖拽到节点内部作为子节点
        targetParentNode = dropNode.data
        targetParentCode = dropNode.data.departCode
      } else {
        // 拖拽到节点前面或后面，需要获取其父节点
        targetParentNode = dropNode.parent ? dropNode.parent.data : null
        targetParentCode = targetParentNode ? targetParentNode.departCode : ''
      }

      // 检查是否真的发生了位置变化
      const currentParentCode = draggingNode.data.parentDepartCode || ''
      if (targetParentCode === currentParentCode) {
        console.log('节点位置未发生变化，无需更新')
        return
      }

      // 构建确认消息
      let confirmMessage = ''
      if (dropType === 'inner') {
        confirmMessage = `确定将 "${draggingNode.data.departName}" 移动到 "${dropNode.data.departName}" 下作为子节点吗？`
      } else {
        const position = dropType === 'prev' ? '前面' : '后面'
        const targetName = dropNode.data.departName
        confirmMessage = `确定将 "${draggingNode.data.departName}" 移动到 "${targetName}" 的${position}吗？`
      }

      this.$confirm(confirmMessage, '确认移动', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          try {
            // 显示加载状态
            this.loading = true

            // 构建更新参数
            const params = {
              id: draggingNode.data.id,
              parentDepartCode: targetParentCode,
              // 保持其他字段不变
              departCode: draggingNode.data.departCode,
              departName: draggingNode.data.departName,
              contractName: draggingNode.data.contractName || '',
              contractPhone: draggingNode.data.contractPhone || '',
              address: draggingNode.data.address || '',
              remark: draggingNode.data.remark || ''
            }

            console.log('调用更新接口，参数:', params)

            // 调用保存接口
            const res = await saveOrgTreeApi(params)

            if (res.code === 200) {
              this.$message({
                type: 'success',
                message: '组织架构更新成功！'
              })

              // 刷新树结构
              await this.getOrgList()

              // 如果当前编辑的是被移动的节点，更新表单中的父级信息
              if (this.currentNode && this.currentNode.id === draggingNode.data.id) {
                this.orgForm.parentDepartCode = targetParentCode
                if (targetParentNode) {
                  this.parentOrgName = targetParentNode.departName
                } else {
                  this.parentOrgName = ''
                }
              }
            } else {
              throw new Error(res.msg || '更新失败')
            }
          } catch (error) {
            console.error('更新组织架构失败:', error)
            this.$message.error(`更新失败：${error.message || error.msg || '未知错误'}`)

            // 刷新数据，恢复原始状态
            await this.getOrgList()
          } finally {
            this.loading = false
          }
        })
        .catch(() => {
          // 用户取消操作
          this.$message({
            type: 'info',
            message: '已取消移动操作'
          })

          // 刷新数据，确保树结构正确
          this.getOrgList()
        })
    },
    handleNodeDragStart(node) {
      // 拖拽开始，可以在这里记录拖拽状态
      this.draggingNode = node.data
      console.log('开始拖拽节点:', node.data.departName)
    },
    handleNodeDragEnter(draggingNode, dropNode, dropType, ev) {
      // 拖拽进入目标节点，可以在这里高亮显示
      console.log('拖拽进入目标:', dropNode.data.departName)
    },
    handleNodeDragLeave(draggingNode, dropNode, dropType, ev) {
      // 拖拽离开目标节点，可以在这里取消高亮
      console.log('拖拽离开目标:', dropNode.data.departName)
    },
    handleNodeDragEnd(node, ev) {
      // 拖拽结束，清理拖拽状态
      this.draggingNode = null
      console.log('拖拽结束')
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/form-overrides.scss';

.app-container {
  height: 100%;
  padding: 16px;
}

.organization-container {
  display: flex;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.left-tree {
  width: 300px;
  border-right: 1px solid #e6e6e6;
  display: flex;
  flex-direction: column;

  .search-box {
    padding: 10px;
    border-bottom: 1px solid #e6e6e6;
  }

  .add-org {
    padding: 10px;
    display: flex;
    justify-content: center;
    border-bottom: 1px solid #e6e6e6;
  }

  .tree-container {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
  }
}

.right-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;

  .title-bar {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e6e6e6;
  }
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 8px;
  font-size: 14px;
  position: relative;
}

.tree-action {
  margin-left: 10px;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s, opacity 0.2s;
  position: absolute;
  right: 0;
  background-color: #fff;
  padding: 0 5px;
  z-index: 1;

  &.visible {
    visibility: visible;
    opacity: 1;
  }
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  margin-top: 20px;
}

// 拖拽相关样式
::v-deep .el-tree-node__content {
  &.is-drop-inner {
    background-color: #f0f9ff;
    border: 1px dashed #409eff;
  }
}

::v-deep .el-tree-node.is-drop-inner > .el-tree-node__content {
  background-color: #f0f9ff;
  border: 1px dashed #409eff;
}

// 拖拽时的节点样式
::v-deep .el-tree-node.is-dragging {
  opacity: 0.5;
}

// 拖拽提示线
::v-deep .el-tree-node__drop-prev,
::v-deep .el-tree-node__drop-next {
  border-top: 2px solid #409eff;
}

::v-deep .el-tree-node__drop-inner {
  border: 2px solid #409eff;
}
</style>
