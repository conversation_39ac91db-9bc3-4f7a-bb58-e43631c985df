const envModule = require('@/utils/env')
const wigroupLogo = require('@/assets/logo/logo-wigroupSmall.png')

const _env = envModule.getEnv()

const starcdsMap = {
  TITLE: '基层筛防工作站',
  TEXT: 'CSP.NET',
  LOGO: wigroupLogo
}
const testMap = {
  TITLE: '基层筛防工作站',
  TEXT: 'CSP.NET',
  // TEXT: '南京医科大学附属泰州人民医院',
  LOGO: wigroupLogo
}

const settingMap = {
  prod: starcdsMap,
  test: testMap
}
export const loginSetting = settingMap[_env]
