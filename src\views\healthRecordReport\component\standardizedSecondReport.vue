<!-- 规范管理报告 -->
<template>
  <div class="standardized-second-report">
    <div class="content">
      <div class="title">血糖监测</div>
      <div class="item">
        <el-table :data="drugMonitoringData.tableData1" border style="margin-top: 16px">
          <el-table-column prop="measureTime" label="时间" align="center" />
          <el-table-column prop="sugarType" label="血糖类型" align="center">
            <template slot-scope="scope">
              <!-- <el-select v-model="scope.row.sugarType" placeholder="请选择血糖类型">
                <el-option label="空腹血糖" :value="1" />
                <el-option label="餐后2h血糖" :value="3" />
                <el-option label="糖化血红蛋白" :value="4" />
              </el-select> -->
              <span>{{
                scope.row.sugarType === 1 ? '空腹血糖' : scope.row.sugarType === 3 ? '餐后2h血糖' : '糖化血红蛋白'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="sugarValue" label="血糖值" align="center" />
          <el-table-column prop="remark" label="备注" align="center" />
        </el-table>
      </div>
    </div>

    <div class="content">
      <div class="title">血压监测</div>
      <div class="item">
        <el-table :data="drugMonitoringData.tableData2" border style="margin-top: 16px">
          <el-table-column prop="measureTime" label="时间" align="center" />

          <el-table-column prop="sp" label="收缩压" align="center" />
          <el-table-column prop="dp" label="舒张压" align="center" />
          <el-table-column prop="remark" label="备注" align="center" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'StandardizedSecondReport',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: []
    }
  },
  computed: {
    drugMonitoringData() {
      const data1 = this.reportInfo.treatmentActionDetailVO.itemList.find((item) => item.itemCode === 'SUGAR_MONITOR').data
      const data2 = this.reportInfo.treatmentActionDetailVO.itemList.find(
        (item) => item.itemCode === 'PRESSURE_MONITOR'
      ).data

      return {
        tableData1: data1.itemList,
        tableData2: data2.itemList
      }
    }
  }
}
</script>

<style scoped lang="scss">
.standardized-second-report {
  padding: 10px;
  .content {
    margin-top: 8px;
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
    }
  }
}
</style>
