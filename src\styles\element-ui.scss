// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    min-width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}
// .el-skeleton.is-animated .el-skeleton__item{
//   background: linear-gradient(90deg, #fafbfc 25%, #497fed 37%, #fafbfc 63%);
// }

// 数据看板列表样式
.el-table.show_table .el-table__header th {
  background: linear-gradient(180deg, #dcf4e8 0%, #ebf9f2 100%);
}
// hover效果
.el-table.show_table .el-table__body tr:hover > td {
  background-color: inherit;
}
// 隔行变色
.el-table.show_table .el-table__body tr:nth-child(2n) > td {
  background: #f9fffb;
}

// popover 样式
body .el-popover.el-popper {
  // height: 300px;
  // overflow-y: auto;
}
.my-popconfirm {
  height: auto !important;
}
