<!-- 检验检查第十部分: 超声心动图 -->
<template>
  <div class="inspection-tenth-part">
    <div class="content">
      <div class="title">超声心动图</div>

      <div class="item">
        <div class="item-title">左心房及左心室结构评估</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in echocardiogram.leftHeart" :key="item.prop" :label="item.label">
            {{ echocardiogramData[item.prop] }}{{ item.append }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">右心房及右心室结构评估</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in echocardiogram.rightHeart" :key="item.prop" :label="item.label">
            {{ echocardiogramData[item.prop] }}{{ item.append }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">大动脉结构评估</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in echocardiogram.aorta" :key="item.prop" :label="item.label">
            {{ echocardiogramData[item.prop] }}{{ item.append }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">心功能评估-收缩功能</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item
            v-for="item in echocardiogram.heartFunctionContract"
            :key="item.prop"
            :label="item.label"
          >
            {{ echocardiogramData[item.prop] }}{{ item.append }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">心功能评估-舒张功能</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item
            v-for="item in echocardiogram.heartFunctionDiastole"
            :key="item.prop"
            :label="item.label"
          >
            {{ echocardiogramData[item.prop] }}{{ item.append }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </div>
</template>

<script>
import { echocardiogram } from '@/views/receptionCenter/patientReception/component/complicationsScreening'

export default {
  name: 'InspectionTenthPart',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      echocardiogram
    }
  },
  computed: {
    echocardiogramData() {
      const { data = {} } = this.reportInfo.itemList.find((item) => item.itemCode === 'ECHOCARDIOGRAM') || {}
      return data
    }
  }
}
</script>

<style lang="scss" scoped>
.inspection-tenth-part {
  .content {
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 6px;
    }
    .item {
      margin-bottom: 6px;
      .item-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 16px;
      }
    }
  }
  ::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell {
    padding: 4px 6px;
  }

  ::v-deep .el-descriptions .is-bordered td.el-descriptions-item__cell {
    width: 70px;
  }
}
</style>
