<template>
  <!-- 该页仅在居民健康数据库中使用 -->
  <div v-loading="tableLoad" class="treatmentPlan public_v2 overflow">
    <!-- ===== 随访转诊 start ====== -->
    <div class="treatmentPlan_list" style="display: none">
      <span class="treatmentPlan_label colorFFF flex_center fontSize_14">随访转诊</span>
      <div class="public_scrollbar overflow">
        <div class="treatmentPlan_listRecord public_width100 overflow">
          <ul class="public_width100 treatmentPlan_listHeader fontSize_14">
            <li class="treatmentPlan_list_1" />
            <li class="treatmentPlan_list_2">随访医生</li>
            <li class="treatmentPlan_list_3">收缩压</li>
            <li class="treatmentPlan_list_4">舒张压</li>
          </ul>
          <ul class="public_width100 fontSize_14">
            <li class="treatmentPlan_list_1">2024/04/01</li>
            <li class="treatmentPlan_list_4">何俊杰</li>
            <li class="treatmentPlan_list_2">98mmHg</li>
            <li class="treatmentPlan_list_3">144mmHg</li>
          </ul>
          <ul class="public_width100 fontSize_14">
            <li class="treatmentPlan_list_1">2024/04/01</li>
            <li class="treatmentPlan_list_4">何俊杰</li>
            <li class="treatmentPlan_list_2">98mmHg</li>
            <li class="treatmentPlan_list_3">144mmHg</li>
          </ul>
          <ul class="public_width100 fontSize_14">
            <li class="treatmentPlan_list_1">2024/04/01</li>
            <li class="treatmentPlan_list_4">何俊杰</li>
            <li class="treatmentPlan_list_2">98mmHg</li>
            <li class="treatmentPlan_list_3">144mmHg</li>
          </ul>
        </div>
        <div class="treatmentPlan_listInfo flex_start">
          <img src="@/assets/v2_manHeadImg.png" alt="头像" class="treatmentPlan_headImg">
          <div class="treatmentPlan_listInfoContent">
            <div class="fontSize_14 treatmentPlan_change">
              <span>转诊医生：张之力 </span>
              <span>转诊时间：2024/11/18</span>
            </div>
            <div class="fontSize_14">【高血脂、高血压、高血糖】</div>
            <div class="fontSize_14">随访队列：基层慢病管理队列</div>
            <div class="fontSize_14">
              <span>备注：</span>
              <span>拒绝转诊拒绝转诊拒绝转诊</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- ===== 随访转诊 end ====== -->
    <!-- ===== 双向转诊 start ====== -->
    <div v-for="item in tableData" :key="item.id" class="treatmentPlan_list">
      <span class="treatmentPlan_label colorFFF flex_center fontSize_14">双向转诊</span>
      <div class="public_scrollbar overflow">
        <div class="treatmentPlan_listRecord public_width100 overflow">
          <p class="treatmentPlan_listTitle fontSize_14">
            <span class="marginRight10">{{ item.createTimeStr }}</span>
            <span>{{ item.title }}</span>
          </p>
          <ul class="public_width100 overflow treatmentPlan_changeInfo marginTop4">
            <li class="public_width100 fontSize_14">
              <span>转入时间：</span>
              <span>{{ item.createTimeStr }}</span>
            </li>
            <li class="public_width100 fontSize_14">
              <span>转出时间：</span>
              <span>{{ item.createTimeStr }}</span>
            </li>

            <!-- <li class="public_width100 fontSize_14">
              <span>转诊科室：</span>
              <span>{{ - }}</span>
            </li> -->
            <li class="public_width100 fontSize_14">
              <span> 病情摘要及处置情况：</span>
              <span>{{ item.remark }}</span>
            </li>
          </ul>
        </div>
        <div class="treatmentPlan_listInfo flex_start">
          <img src="@/assets/v2_manHeadImg.png" alt="头像" class="treatmentPlan_headImg">
          <div class="treatmentPlan_listInfoContent">
            <div class="fontSize_14 treatmentPlan_change">
              <span>转诊医生：{{ item.createUsername }} </span>
            </div>
            <!-- <div class="fontSize_14">【高血脂、高血压、高血糖】</div> -->
          </div>
        </div>
      </div>
    </div>
    <!-- ===== 双向转诊 end ====== -->

    <div v-if="tableData.length === 0" class="flex_center" style="height: 100%">
      <noData />
    </div>
  </div>
</template>
<script>
import { transferTreatmentPageApi } from '@/api/personageHealth'

import { formatDate } from '@/utils'

export default {
  name: 'TreatmentPlan',
  data() {
    return {
      tableLoad: false,
      tableData: []
    }
  },
  created() {
    this.getTableData() // 获取双向转诊数据
  },
  methods: {
    /**
     * @description: 获取双向转诊数据
     * @author: LiSuwan
     * @Date: 2024-09-10 16:34:03
     */
    async getTableData() {
      this.tableLoad = true

      const res = await transferTreatmentPageApi({
        pageNo: 1,
        pageSize: 999999,
        patientId: sessionStorage.getItem('healthUserId')
      })

      this.tableLoad = false
      if (res.code === 200) {
        console.log(res.data.list, '获取双向转诊数据')
        res.data.list.forEach((val) => {
          // sex:性别 1:男 0:女
          val.headImg = val.sex === 1 ? '@/assets/v2_manHeadImg.png' : '@/assets/v2_womenHeadImg.png'
          val.createTimeStr = formatDate(val.createTime, 'YYYY/MM/DD')
          val.title = `由“${val.serviceStationSource.departName}”转至“${val.serviceStationTarget.departName}”`
        })
        this.tableData = res.data.list
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.treatmentPlan {
  width: 100%;
  padding: 1.7rem 2rem;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
}

.treatmentPlan_list {
  width: 22.8rem;
  height: 11rem;
  background: #ffffff;
  box-shadow: 0px 0px 0.4rem 0px rgba(0, 0, 0, 0.2);
  border-radius: 0.75rem;
  padding: 0.9rem;
  box-sizing: border-box;
  float: left;
  margin-right: 4rem;
  position: relative;
  cursor: pointer;
  &:hover {
    background: #65ccca;
    .treatmentPlan_listInfoContent {
      color: #fff;
    }
    .treatmentPlan_listRecord {
      border-color: #fff;
      color: #fff;
      > ul {
        color: #fff;
        &.treatmentPlan_changeInfo {
          li {
            span:nth-child(1) {
              color: #fff;
            }
          }
        }
      }
    }
  }
  .treatmentPlan_label {
    position: absolute;
    right: 0px;
    top: 0px;
    width: 4rem;
    height: 1.5rem;
    background: #38ba84;
    border-radius: 0px 0.75rem 0px 0.75rem;
  }
  .treatmentPlan_listRecord {
    padding-bottom: 0.6rem;
    border-bottom: 1px dashed #a3e0df;
    margin-top: 0.5rem;
    color: #222222;
    .treatmentPlan_listTitle {
      padding-top: 0.3rem;
    }
    .treatmentPlan_listTitle,
    .treatmentPlan_listHeader {
      font-weight: 600;
    }
    ul {
      color: #222222;

      &.treatmentPlan_changeInfo {
        li {
          padding: 0.2rem 1rem;
          text-align: left;
          span:nth-child(1) {
            color: #666;
          }
        }
      }

      li {
        float: left;
        padding: 0.3rem 0.3rem;
        box-sizing: border-box;
        text-align: center;
      }
      .treatmentPlan_list_1 {
        width: 6rem;
        text-align: left;
      }
      .treatmentPlan_list_2 {
        width: 5rem;
      }
      .treatmentPlan_list_3 {
        width: 5rem;
      }
      .treatmentPlan_list_4 {
        width: 5rem;
      }
    }
  }
  .treatmentPlan_listInfo {
    padding-top: 0.5rem;
  }
  .treatmentPlan_headImg {
    width: 2rem;
    height: 2rem;
    box-sizing: border-box;
  }
  .treatmentPlan_listInfoContent {
    margin-left: 1.1rem;
    color: #222;
    .treatmentPlan_change {
      span + span {
        margin-left: 0.3rem;
      }
    }

    > div + div {
      margin-top: 0.3rem;
    }
  }
}
</style>
