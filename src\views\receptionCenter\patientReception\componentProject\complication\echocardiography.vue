<!-- 心脏彩超 -->
<template>
  <div>
    <div v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail'">
      <audit-status v-if="auditInfo.status === 5" status="pass" style="margin-bottom: 10px; width: 50%" />

      <audit-status
        v-if="auditInfo.status === 9"
        status="reject"
        :reason="auditInfo.auditResult"
        style="margin-bottom: 10px; width: 50%"
      />

      <audit-status v-if="auditInfo.status === 1" status="pending" style="margin-bottom: 10px; width: 50%" />
    </div>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="180px">
      <el-row :gutter="20">
        <el-col v-for="item in echocardiography" :key="item.label" :span="8">
          <el-form-item :label="item.label">
            <custom-input-number v-model="form[item.prop]">
              <template v-if="item.append" #append>
                {{ item.append }}
              </template>
            </custom-input-number>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <CheckboxGroupField v-model="form.ecrResult" :item="checkboxItem" />
        </el-col>

        <el-col :span="24">
          <el-form-item label="检查所见：">
            <el-input v-model="form.ecrFinding" type="textarea" />
          </el-form-item>
        </el-col>

        <el-col v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail'" :span="24">
          <el-form-item label="上传图片：">
            <custom-upload v-model="form.attachmentPhotoUrl" />
          </el-form-item>
        </el-col>

        <el-col v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail'" :span="24">
          <el-form-item label="上传视频：">
            <UploadVideo v-model="form.attachmentVideoUrl" />
          </el-form-item>
        </el-col>

        <el-form-item
          v-if="auditInfo.status === 5 && $route.path !== '/qualityControl/ultrasonicQualityControl/detail'"
          label="质控结论："
        >
          <el-input v-model="auditInfo.auditResult" type="textarea" :rows="3" :disabled="true" />
        </el-form-item>
      </el-row>
    </el-form>
    <div v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail' && auditInfo.status !== 1">
      <span style="margin-right: 130px; font-size: 14px">质控人：{{ auditInfo.auditName }}</span>
      <span style="font-size: 14px">质控时间：{{ auditInfo.auditTime }}</span>
    </div>
  </div>
</template>

<script>
import { echocardiography } from '@/views/receptionCenter/patientReception/component/complicationsScreening.js'
import CustomUpload from '@/components/customUpload/index.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'
import UploadVideo from '@/components/uploadVideo/index.vue'
import AuditStatus from '@/components/auditStatus/index.vue'

export default {
  name: 'Echocardiography',
  components: { CustomUpload, CheckboxGroupField, UploadVideo, AuditStatus },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const form = {
      ecrResult: [],
      ecrFinding: '',
      attachmentPhotoUrl: '',
      attachmentVideoUrl: []
    }
    echocardiography.forEach((item) => {
      form[item.prop] = ''
    })
    return {
      echocardiography,
      form,
      checkboxItem: {
        label: '检查结果：',
        type: 'checkbox',
        required: true,
        mutuallyExclusive: true, // 互斥
        prop: 'ecrResult',
        options: [
          { label: '未见明显异常', value: '1' },
          { label: '轻度瓣膜病变', value: '2' },
          { label: '中重度瓣膜病变', value: '3' },
          { label: '左室舒张功能异常', value: '4' },
          { label: '左室收缩功能异常', value: '5' },
          { label: '其他', value: '6' }
        ]
      },
      rules: {
        ecrResult: [{ required: true, message: '请选择检查结果' }]
      },
      auditInfo: {}
    }
  },
  methods: {
    initData(data) {
      this.auditInfo = {
        status: data && data.status,
        auditResult: data && data.auditResult,
        auditName: data && data.auditName,
        auditTime: data && data.auditTime
      }
      Object.keys(this.form).forEach((key) => {
        if (key === 'ecrResult' || key === 'attachmentVideoUrl') {
          this.form[key] = data[key] ? data[key].split(',') : []
        } else {
          this.form[key] = data[key]
        }
      })
      this.form.id = data.id
    },
    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          ...this.form,
          attachmentVideoUrl: this.form.attachmentVideoUrl.join(','),
          ecrResult: this.form.ecrResult.join(','),
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    }
  }
}
</script>
