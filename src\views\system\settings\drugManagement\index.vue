<template>
  <div class="drugManagement">
    <div class="drugManagement-content">
      <el-card class="drugManagement-search">
        <el-form :model="queryParams" label-width="120px">
          <el-row>
            <el-col :span="6">
              <el-form-item label="药品名称：">
                <el-input v-model="queryParams.medicalName" placeholder="请输入药品名称" />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="药品类型：">
                <el-select v-model="queryParams.medicalType" placeholder="请选择" style="width: 100%">
                  <el-option label="降压药" :value="1" />
                  <el-option label="胰岛素" :value="2" />
                  <el-option label="降糖药" :value="3" />
                  <el-option label="调脂药" :value="4" />
                  <el-option label="房颤" :value="5" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6" style="text-align: center">
              <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
              <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <el-card>
        <div class="operation-btn">
          <el-button type="primary" @click="handleAdd">新增</el-button>
        </div>
        <base-table
          ref="baseTable"
          :table-data="tableData"
          :loading="loading"
          :stripe="true"
          :height="500"
          row-key="id"
          :columns="columns"
          :total="total"
          :page-info="queryParams"
          @pagination-change="handlePaginationChange"
        >
          <template #medicalType="scope">
            <span v-if="scope.row.medicalType === 1">降压药</span>
            <span v-if="scope.row.medicalType === 2">胰岛素</span>
            <span v-if="scope.row.medicalType === 3">降糖药</span>
            <span v-if="scope.row.medicalType === 4">调脂药</span>
            <span v-if="scope.row.medicalType === 5">房颤</span>
          </template>

          <template #operation="scope">
            <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" style="color: red" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </base-table>
      </el-card>
    </div>
    <ProDialog :title="title" :visible.sync="addDialogVisible" width="500px">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="药品名称：" prop="medicalName">
          <el-input v-model="form.medicalName" placeholder="请输入药品名称" />
        </el-form-item>
        <el-form-item label="药品类型：" prop="medicalType">
          <el-select v-model="form.medicalType" placeholder="请选择" style="width: 100%">
            <el-option label="降压药" :value="1" />
            <el-option label="胰岛素" :value="2" />
            <el-option label="降糖药" :value="3" />
            <el-option label="调脂药" :value="4" />
            <el-option label="房颤" :value="5" />
          </el-select>
        </el-form-item>
        <el-form-item label="单位：" prop="medicalUnit">
          <el-select v-model="form.medicalUnit" placeholder="请选择" style="width: 100%">
            <el-option label="mg" value="mg" />
            <el-option label="g" value="g" />
            <el-option label="IU" value="IU" />
            <el-option label="U" value="U" />
            <el-option label="片" value="片" />
            <el-option label="微克" value="微克" />
            <el-option label="揿" value="揿" />
            <el-option label="吸" value="吸" />
          </el-select>
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">关闭</el-button>
        <el-button type="primary" @click="handleConfirmAdd">确定</el-button>
      </span>
    </ProDialog>
  </div>
</template>

<script>
import tableMixin from '@/mixins/tableMixin'
import BaseTable from '@/components/BaseTable'
import ProDialog from '@/components/ProDialog'
import { getDrugManagementList, saveDrugManagement, deleteDrugManagement } from '@/api/drugManagement'

export default {
  name: 'DrugManagement',
  components: {
    BaseTable,
    ProDialog
  },
  mixins: [tableMixin],
  data() {
    return {
      tableData: [],
      title: '新增',
      form: {
        medicalName: '',
        medicalType: '',
        medicalUnit: ''
      },
      rules: {
        medicalName: [{ required: true, message: '请输入药品名称' }],
        medicalType: [{ required: true, message: '请选择药品类型' }],
        medicalUnit: [{ required: true, message: '请选择单位' }]
      },
      queryParams: {
        medicalName: '',
        medicalType: ''
      },
      columns: [
        {
          label: '药品名称',
          prop: 'medicalName'
        },
        {
          label: '药品类型',
          prop: 'medicalType',
          slot: 'medicalType'
        },
        {
          label: '单位',
          prop: 'medicalUnit'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        },
        {
          label: '操作',
          prop: 'operation',
          slot: 'operation'
        }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      return await getDrugManagementList(params)
    },
    async handleAdd() {
      this.title = '新增'
      this.form = {
        medicalName: '',
        medicalType: '',
        medicalUnit: ''
      }
      this.addDialogVisible = true
    },

    handleEdit(row) {
      this.title = '编辑'
      this.form = {
        id: row.id,
        medicalName: row.medicalName,
        medicalType: row.medicalType,
        medicalUnit: row.medicalUnit
      }
      this.addDialogVisible = true
    },

    handleDelete(row) {
      this.$confirm('确定删除该药品吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          await deleteDrugManagement({
            id: row.id
          })
          this.$message.success('删除成功')
          this.handleReset()
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },

    async handleConfirmAdd() {
      const valid = await this.$refs.form.validate()
      if (!valid) return
      await saveDrugManagement(this.form)
      this.addDialogVisible = false
      this.handleReset()
    },
    async handleCancel() {
      this.addDialogVisible = false
      this.form = {
        medicalName: '',
        medicalType: '',
        medicalUnit: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.drugManagement {
  .drugManagement-content {
    .el-card {
      margin: 16px;
    }
    .operation-btn {
      display: flex;
      margin-bottom: 16px;
    }
  }
}
</style>
