<!-- eslint-disable no-irregular-whitespace -->
<template>
  <div class="consentConfig">
    <div class="top">
      <div class="left">
        <div class="input-box">
          <span>分类标签：</span>
          <el-select v-model="searchData.tag" placeholder="请选择">
            <el-option label="全部" :value="''" />
            <el-option v-for="tag in tagList" :key="tag.id" :label="tag.value" :value="tag.code" />
          </el-select>
        </div>
        <div class="input-box">
          <span>知情同意书标题：</span>
          <ProInput v-model="searchData.title" placeholder="请输入" @debounce="getTableData" />
        </div>
        <div class="input-box">
          <el-button type="primary" plain size="small" @click="resetBtn">重置</el-button>
          <el-button type="primary" size="small" @click="getTableData()">搜索</el-button>
        </div>
      </div>
      <div class="right">
        <el-button type="primary" size="small" icon="el-icon-circle-plus-outline" @click="addBtnClick">新增</el-button>
      </div>
    </div>
    <div class="table-list">
      <el-table
        :data="tableData"
        style="width: 100%"
        class="applicationCustomPageTable applicationCustomPageTableVw appTableBgBorder"
      >
        <el-table-column prop="title" label="标题">
          <template slot-scope="{ row }">
            <span style="text-overflow: ellipsis; overflow: hidden; white-space: nowrap">{{ row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间">
          <template slot-scope="{ row }">
            <span>{{ parseTime(row.createTime, '{y}/{m}/{d} {h}:{i}') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createUsername" label="创建人" />
        <el-table-column prop="tags" label="分类标签">
          <template v-if="row.tags" slot-scope="{ row }">
            <div v-for="(tag, index) in row.tags" :key="index" style="display: inline-block; margin-right: 10px">
              <el-tag v-if="tagList.find(e => e.code === tag.code)" type="warning">
                {{ tagList.find(e => e.code === tag.code).value }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="originalFile" label="附件">
          <template slot-scope="{ row }">
            <span v-if="row.originalFile" style="display: flex; align-items: center">
              <i class="el-icon-s-order" style="color: #ffb026" />
              <!-- <el-button type="text" style="color: #FFB026;">下载</el-button> -->
              <el-link :underline="false" :href="row.originalFile.url" class="originalFile_elLink">下载</el-link>
            </span>
            <span v-else style="color: #999; font-size: 0.6rem">未上传</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="{ row }">
            <el-button type="text" @click="previewBtn(row)">预览</el-button>
            <el-button type="text" style="color: #37b4d8" @click="editBtn(row)">编辑</el-button>
            <el-button type="text" style="color: #ee5757" @click="removeBtn(row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :current-page="searchData.pageNo"
          :page-sizes="tablePageSizes"
          :page-size="searchData.pageSize"
          :layout="tablePaginationLayout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <!-- 知情同意书 新增/编辑 -->
    <ProDialog :visible="informedConsentShow" :width="'60vw'" :top="'15vh'" :before-close="informedConsentClose">
      <span slot="title" class="dialog-title">
        <span>{{ dialogTitle }}</span>
      </span>
      <el-form :model="informedConsentData" inline>
        <!-- 364 -->
        <el-form-item v-if="dialogTitle === '知情同意书编辑'" :label="'\u3000\u3000\u3000创建时间：'">
          <el-input :value="informedConsentData.createTime" disabled style="width: 240px" />
        </el-form-item>
        <!-- 308 -->
        <el-form-item v-if="dialogTitle === '知情同意书编辑'" label="创建人：">
          <el-input :value="informedConsentData.createUsername" disabled style="width: 240px" />
        </el-form-item>
        <div style="display: inline-block">
          <div style="display: flex; align-items: center">
            <div
              style="
                font-size: 0.7rem;
                line-height: 40px;
                display: inline-block;
                max-width: 250px;
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                margin-right: 10px;
              "
            >
              附件：{{
                informedConsentData.originalFile && informedConsentData.originalFile.name
                  ? informedConsentData.originalFile.name
                  : ''
              }}
            </div>
            <el-upload
              ref="uploadRef"
              class="upload-demo"
              action="/cspapi/backend/cos/uploadFile/private3"
              accept=".doc,.docx"
              :headers="{ Authorization: getToken() }"
              auto-upload
              :before-upload="beforeFileUpload"
              :on-success="handleFileSuccess"
              :data="{ folder: 'informed_consent', platform: 'minio' }"
              :on-error="handleFileError"
              :limit="1"
              :show-file-list="false"
              style="display: inline-block"
            >
              <el-button size="small" plain round type="primary" style="border: 0">点击上传</el-button>
            </el-upload>
          </div>
        </div>
        <div />

        <el-form-item label="知情同意书标题：">
          <el-input
            v-model="informedConsentData.title"
            style="width: 400px"
            resize="none"
            type="textarea"
            maxlength="30"
            show-word-limit
            :autosize="{ minRows: 4, maxRows: 4 }"
            placeholder=""
          />
        </el-form-item>
        <el-form-item label="分类标签：">
          <el-select v-model="informedConsentData.tags" style="width: 440px" multiple placeholder="请选择">
            <el-option v-for="tag in tagList" :key="tag.id" :label="tag.value" :value="tag.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="知情同意书内容：">
          <div id="informed-consent" style="max-width: 950px" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer flex_end public_v2">
        <el-button
          data-ids="bbbbbb"
          class="public_dialogButton2 color333 fontSize_14 bgColor_FFF flex_center"
          @click="informedConsentClose"
          >取消
        </el-button>
        <el-button
          class="public_dialogButton colorFFF fontSize_14 bgColor_42C9A300B2DC flex_center"
          data-ids="aaaaaa"
          @click="SubmitForm()"
          >保存</el-button
        >
      </span>
    </ProDialog>

    <!-- 知情同意书 预览 -->

    <ProDialog :visible="previewShow" :width="'60vw'" :top="'15vh'" :before-close="informedConsentClose">
      <span slot="title" class="dialog-title">
        <span>知情同意书预览</span>
      </span>
      <el-row :gutter="40" class="label-box">
        <el-col :span="6"> 创建时间：{{ parseTime(previewData.createTime, '{y}/{m}/{d} {h}:{i}') }} </el-col>
        <el-col :span="6"> 创建人：{{ previewData.createUsername }} </el-col>
        <!-- <el-col :span="15">
         标题：{{ previewData.title }}
        </el-col> -->
        <el-col :span="12">
          附件：
          <span class="label">{{
            previewData.originalFile && previewData.originalFile.name ? previewData.originalFile.name : '无'
          }}</span>
        </el-col>
      </el-row>
      <el-row :gutter="40" class="label-box">
        <el-col :span="12"> 标题：{{ previewData.title }} </el-col>
        <el-col :span="12">
          分类标签：
          <el-tag v-for="(tag, index) in previewData.tags" :key="index" type="warning" style="margin-right: 15px">
            {{ tag.name }}
          </el-tag>
        </el-col>
      </el-row>
      <el-row>
        <div class="content">
          <div v-html="previewData.content" />
        </div>
      </el-row>
    </ProDialog>
  </div>
</template>
<script>
import { getDictionaryValApi } from '@/api/dict'
import {
  getInformedConsentListApi,
  addInformedConsentListApi,
  putInformedConsentListApi,
  removeInformedConsentListApi
  // getInformedConsentDetailsApi
} from '@/api/system'
import { parseTime } from '@/utils'
import elDragDialog from '@/directive/el-drag-dialog'
import E from 'wangeditor'
import { getToken } from '@/utils/auth'
import ProInput from '@/components/ProInput/index.vue'
import ProDialog from '@/components/ProDialog/index.vue'

export default {
  name: 'ConsentConfig',
  components: { ProDialog, ProInput },
  directives: {
    elDragDialog
  },
  data() {
    return {
      searchData: {
        pageNo: 1,
        pageSize: 10,
        tag: '',
        title: ''
      },
      tableData: [],
      total: 0,
      tagList: [],
      dialogTitle: '',
      informedConsentShow: false,
      informedConsentData: {
        title: '',
        content: '',
        tags: [],
        originalFile: {
          name: '',
          url: '',
          size: ''
        }
      },
      editor: null,
      previewShow: false,
      previewData: {
        title: '',
        content: '',
        tags: [],
        originalFile: {
          name: '',
          url: '',
          size: ''
        }
      }
    }
  },
  created() {
    this.getTagList()
    this.getTableData()
  },
  methods: {
    async SubmitForm() {
      if (!this.informedConsentData.title) {
        this.$message.warning('请填写标题。')
        return false
      }
      if (!this.informedConsentData.tags.length) {
        this.$message.warning('请选择分类标签。')
        return false
      }
      if (!this.editor.txt.html()) {
        this.$message.warning('请填写知情同意书内容')
        return false
      }
      const obj = {}
      obj.title = this.informedConsentData.title
      obj.content = this.editor.txt.html()
      const signature = []
      // 签名 ____________  12
      for (let i = 0; i < obj.content.split('____________').length - 1; i++) {
        signature.push({ key: `signature_${i + 1}`, value: '', type: 'signature' })
      }
      // 手机号（家庭电话） +86___________  11
      for (let i = 0; i < obj.content.split('+86___________').length - 1; i++) {
        signature.push({ key: `phone_${i + 1}`, value: '', type: 'phone' })
      }
      // 日期 ______年____月____日  6 4 4
      for (let i = 0; i < obj.content.split('______年____月____日').length - 1; i++) {
        signature.push({ key: `date_${i + 1}`, value: '', type: 'date' })
      }
      obj.signature = signature
      const tags = []
      for (let i = 0; i < this.informedConsentData.tags.length; i++) {
        const e = this.informedConsentData.tags[i]
        tags.push({ code: e, name: this.tagList.find(c => c.code === e).value })
      }
      obj.tags = tags
      if (this.informedConsentData.originalFile.url) {
        obj.originalFile = this.informedConsentData.originalFile
      }
      console.log('知情同意书 obj', obj)
      if (this.dialogTitle === '新增知情同意书') {
        console.log('新建知情同意书 obj', obj)
        const res = await addInformedConsentListApi(obj)
        if (res.code === 200) {
          this.$message.success('新增成功')
          this.informedConsentClose()
          this.getTableData()
        }
      }
      if (this.dialogTitle === '知情同意书编辑') {
        obj.id = this.informedConsentData.id
        console.log('编辑知情同意书 obj', obj)
        const res = await putInformedConsentListApi(obj)
        if (res.code === 200) {
          this.$message.success('新增成功')
          this.informedConsentClose()
          this.getTableData()
        }
      }
    },
    beforeFileUpload(file) {
      console.log('filexxxfile', file)
      const isWord =
        file.type === 'application/msword' ||
        file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      //   const isLt2M = file.size / 1024 / 1024 < 5
      console.log('file.size', file.name)
      this.informedConsentData.originalFile.size = file.size
      this.informedConsentData.originalFile.name = file.name
      console.log('informedConsentData.name', this.informedConsentData.name)
      if (!isWord) {
        this.$message.error('上传文件只能是 word 格式!')
      }
      return isWord
    },
    handleFileSuccess(res, file, fileList) {
      console.log('handleFileSuccess', res)
      if (res.code === 200) {
        this.$message.success('上传成功。')
        this.informedConsentData.originalFile.url = this.globalHttpUrl(res.data[0].fullFileUrl)
        console.log('informedConsentData', this.informedConsentData)
        this.$refs.uploadRef.clearFiles()
      }
    },
    handleFileError(err, file, fileList) {
      console.log('err 上传失败', err)
      this.$message.warning('上传失败。')
      this.$refs.uploadRef.clearFiles()
    },
    // 初始化富文本
    initEditor() {
      this.editor = new E('#informed-consent')
      this.editor.config.excludeMenus = ['code', 'todo']
      this.editor.customConfig = this.editor.customConfig ? this.editor.customConfig : this.editor.config
      this.editor.customConfig.onchange = html => {
        // console.log('html',html,html.length);
        if (html.length > 12000) {
          const str = html.substring(0, 11900)
          this.editor.txt.html(str)
          this.$message('字数超出已自动删除！')
        }
      }
      this.editor.create()
    },
    addBtnClick() {
      this.dialogTitle = '新增知情同意书'
      this.informedConsentShow = true
      this.$nextTick(() => {
        if (!this.editor) {
          this.initEditor()
        }
      })
    },
    previewBtn(row) {
      this.previewShow = true
      this.previewData = {
        title: row.title,
        createTime: row.createTime,
        createUsername: row.createUsername,
        content: row.content,
        tags: row.tags,
        originalFile: row.originalFile
      }
    },
    editBtn(row) {
      this.informedConsentShow = true
      const arr = []
      if (row.tags) {
        for (let i = 0; i < row.tags.length; i++) {
          const e = row.tags[i]
          arr.push(e.code)
        }
      }
      this.informedConsentData = {
        id: row.id,
        title: row.title,
        createTime: row.createTime,
        createUsername: row.createUsername,
        content: row.content,
        tags: arr,
        originalFile: row.originalFile ? row.originalFile : { name: '', url: '', size: '' }
      }
      this.dialogTitle = '知情同意书编辑'
      this.$nextTick(() => {
        if (!this.editor) {
          this.initEditor()
        }
        this.editor.txt.html(row.content)
      })
    },
    removeBtn(id) {
      this.$confirm('是否删除该知情同意书?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await removeInformedConsentListApi(id)
          if (res.code === 200) {
            this.$message.success(res.msg || '删除成功')
            this.getTagList()
            this.getTableData()
          } else {
            this.$message.error(res.msg || '删除失败')
            this.getTagList()
            this.getTableData()
          }
        })
        .catch(() => {
          this.getTagList()
          this.getTableData()
        })
    },
    informedConsentClose() {
      this.previewShow = false
      this.informedConsentShow = false
      this.informedConsentData = {
        title: '',
        content: '',
        tags: [],
        originalFile: {
          name: '',
          url: '',
          size: ''
        }
      }
      this.editor.txt.html('')
    },
    async getTagList() {
      const res = await getDictionaryValApi('typeTag')
      if (res.code === 200) {
        this.tagList = res.data
      }
    },
    async getTableData() {
      const res = await getInformedConsentListApi(this.searchData)

      if (res.code === 200) {
        this.tableData = res.data.list.map(val => {
          if (val.originalFile && val.originalFile.url) {
            val.originalFile.url = this.globalHttpUrl(val.originalFile.url)
          }
          return val
        })

        this.total = res.data.total
      }
    },
    resetBtn() {
      this.searchData = {
        pageNo: 1,
        pageSize: 10,
        tag: '',
        title: ''
      }
      this.getTableData()
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.searchData.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.searchData.pageNo = val
      this.getTableData()
    },
    parseTime,
    getToken
  }
}
</script>
<style lang="scss" scoped>
.consentConfig {
  width: calc(100% - 60px);
  margin: 30px auto;
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      display: flex;
      align-items: center;
      .input-box {
        display: flex;
        align-items: center;
        margin-right: 30px;
        span {
          font-size: 0.75rem;
          font-weight: 500;
          color: #666666;
          white-space: nowrap;
        }
        ::v-deep .el-button {
          width: 90px;
        }
      }
    }
    .right {
      ::v-deep .el-button {
        width: 100px;
      }
    }
  }
  .table-list {
    margin-top: 30px;
    background: #ffffff;
    overflow: hidden;
    border-radius: 15px;
    border: 1px solid #dedede;
    .pagination {
      padding-right: 20px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: right;
    }
  }
}
.label-box {
  color: #666666;
  font-size: 0.7rem;
  line-height: 40px;
  padding-right: 50px;
}
.content {
  background: #f8f8f8;
  border-radius: 10px;
  border: 1px solid #dedede;
  padding: 30px;
}
::v-deep .el-form-item__label {
  font-weight: 500;
}
::v-deep .el-form--inline .el-form-item {
  margin-right: 30px;
}
::v-deep .el-dialog__body {
  max-height: 70vh;
  overflow: auto;
}

.originalFile_elLink {
  color: #ffb026;
  margin-left: 5px;
}
</style>
