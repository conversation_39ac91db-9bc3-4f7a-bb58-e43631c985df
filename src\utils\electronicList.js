export const electronicData = [
  {
    title: '基本信息',
    row: [
      {
        cssName: 'row1',
        txt: '姓名：',
        type: 'text',
        prototype: 'name'
      },
      {
        cssName: 'row1 bl1',
        txt: '性别：',
        type: 'select',
        multiple: false,
        prototype: 'sex',
        dataList: 'sex'
      },
      {
        cssName: 'row1 bl1',
        txt: '年龄：',
        type: 'text',
        prototype: 'age'
      },
      {
        cssName: 'row1 bl1',
        txt: '民族：',
        type: 'select',
        prototype: 'nation',
        dataList: 'nationList'
      },
      {
        cssName: 'row1 bl1',
        txt: '婚姻：',
        type: 'select',
        prototype: 'maritalStatusCode',
        dataList: 'maritalStatusCode'
      },
      {
        cssName: 'row1 bl1',
        txt: '职业：',
        type: 'select',
        prototype: 'occupationCategoryCode',
        dataList: 'occupationCategoryCode'
      },
      {
        cssName: 'row1',
        txt: '证件类型：',
        type: 'select',
        prototype: 'idType',
        dataList: 'idType'
      },
      {
        cssName: 'row2 bl1',
        txt: '证件号码：',
        type: 'input',
        prototype: 'idCard'
      },
      {
        cssName: 'row3 bl1',
        txt: '就诊日期：',
        type: 'time',
        typeS: 'datetime',
        valueFormat: 'yyyy-MM-dd HH:mm:ss',
        format: 'yyyy/MM/dd HH:mm:ss',
        prototype: 'jzsj'
      },
      {
        cssName: 'row1',
        txt: '陪伴者姓名：',
        type: 'input',
        prototype: 'aaaa'
      },
      {
        cssName: 'row2 bl1',
        txt: '与患者关系：',
        type: 'select',
        prototype: 'ybrgx',
        dataList: 'ybrgx'
      },
      {
        cssName: 'row3 bl1',
        txt: '联系电话：',
        type: 'input',
        prototype: 'telPhone',
        maxLength: 11
      }
    ]
  },
  {
    title: '患者主诉',
    row: [
      {
        cssName: 'row6',
        txt: '主诉：',
        row: [
          {
            txt: '',
            type: 'myInput',
            cssName: 'myInputWidth myInputWidth5',
            prototype: 'aaab',
            placeholder: '*主要症状*',
            required: true,
            dataList: 'aaab'
          },
          {
            txt: '',
            type: 'myInput',
            cssName: 'myInputWidth myInputWidth6',
            prototype: 'aaac',
            placeholder: '*数字*',
            required: true,
            dataList: 'aaac'
          },
          {
            txt: '',
            type: 'mySelect',
            cssName: 'myInputWidth myInputWidth6',
            prototype: 'aaad',
            placeholder: '*选择*',
            required: true,
            dataList: 'aaad'
          },
          {
            txt: '',
            type: 'myInput',
            cssName: 'myInputWidth myInputWidth5',
            prototype: 'aaae',
            placeholder: '次要症状',
            dataList: 'aaae'
          },
          {
            txt: '',
            type: 'myInput',
            cssName: 'myInputWidth myInputWidth6',
            prototype: 'aaaf',
            placeholder: '数字',
            dataList: 'aaaf'
          },
          {
            txt: '',
            type: 'mySelect',
            cssName: 'myInputWidth myInputWidth6',
            prototype: 'aaag',
            placeholder: '选择',
            dataList: 'aaad'
          }
        ]
      },
      {
        cssName: 'row6',
        txt: '现病史：',
        row: [
          {
            cssName: 'w10',
            txt: '',
            type: 'myInput',
            prototype: 'aaah',
            placeholder: '*不少于25个字*',
            required: true,
            dataList: 'aaah'
          }
        ]
      },
      {
        cssName: 'row6',
        txt: '既往史：',
        row: [
          {
            txt: '慢性疾病史：',
            type: 'mySelect',
            prototype: 'aaai',
            multiple: true,
            placeholder: '*选择*',
            required: true,
            dataList: 'aaai'
          },
          {
            txt: '其他：',
            type: 'myInput',
            cssName: 'myInputWidth myInputWidth5',
            prototype: 'aaaj',
            placeholder: '其他既往史',
            dataList: 'aaaj'
          },
          {
            txt: '其他病史：',
            type: 'myInput',
            cssName: 'myInputWidth myInputWidth5',
            prototype: 'aaak',
            placeholder: '手术外伤史',
            dataList: 'aaak'
          },
          {
            txt: '',
            type: 'myInput',
            cssName: 'myInputWidth myInputWidth6',
            prototype: 'aaal',
            placeholder: '个人史',
            dataList: 'aaal'
          },
          {
            txt: '',
            type: 'myInput',
            cssName: 'myInputWidth myInputWidth6',
            prototype: 'aaam',
            placeholder: '家族史',
            dataList: 'aaam'
          },
          {
            txt: '',
            type: 'myInput',
            cssName: 'myInputWidth myInputWidth6',
            prototype: 'aaan',
            placeholder: '月经史',
            dataList: 'aaan'
          },
          {
            txt: '',
            type: 'myInput',
            cssName: 'myInputWidth myInputWidth6',
            prototype: 'aaao',
            placeholder: '婚育史',
            dataList: 'aaao'
          },
          {
            txt: '',
            type: 'myInput',
            cssName: 'myInputWidth myInputWidth6',
            prototype: 'aaap',
            placeholder: '输血史',
            dataList: 'aaap'
          }
        ]
      },
      {
        cssName: 'row6',
        txt: '过敏史：',
        row: [
          {
            txt: '',
            cssName: 'w10',
            type: 'myInput',
            prototype: 'aaaq',
            placeholder: '过敏物',
            dataList: 'aaaq'
          }
        ]
      }
    ]
  },
  {
    title: '体格检查',
    row: [
      {
        cssName: 'row6',
        txt: '体温：',
        row: [
          {
            cssName: 'myInputWidth myInputWidthTw',
            txt: '体温：',
            type: 'myInput',
            prototype: 'bbba',
            placeholder: '*体温*',
            required: true,
            dataList: 'bbba',
            unit: '℃'
          }
        ]
      },
      {
        cssName: 'row6',
        txt: '身高体重：',
        row: [
          {
            cssName: 'myInputWidth',
            txt: '身高：',
            type: 'myInput',
            prototype: 'bbbb',
            placeholder: '数字',
            dataList: 'bbbb',
            unit: 'cm'
          },
          {
            cssName: 'myInputWidth',
            txt: '体重：',
            type: 'myInput',
            prototype: 'bbbc',
            placeholder: '数字',
            dataList: 'bbbc',
            unit: 'kg'
          },
          {
            cssName: 'myInputWidth',
            txt: '腰围：',
            type: 'myInput',
            prototype: 'bbbd',
            placeholder: '数字',
            dataList: 'bbbd',
            unit: 'cm'
          },
          {
            cssName: 'myInputWidth',
            txt: '臀围：',
            type: 'myInput',
            prototype: 'bbbe',
            placeholder: '数字',
            dataList: 'bbbe',
            unit: 'cm'
          },
          {
            cssName: 'myInputWidth myInputWidth2',
            txt: '体脂指数 (BMI)：',
            type: 'myInput',
            prototype: 'bbbd',
            placeholder: '数字',
            dataList: 'bbbd',
            unit: 'kg/m² (根据身高体重自动计算)'
          },
          {
            cssName: 'myInputWidth myInputWidth2',
            txt: '体表面积：',
            type: 'myInput',
            prototype: 'bbbe',
            placeholder: '数字',
            dataList: 'bbbe',
            unit: 'm² (系统自动计算)'
          },
          {
            cssName: 'myInputWidth',
            txt: '内在脂肪含量：',
            type: 'myInput',
            prototype: 'bbbf',
            placeholder: '数字',
            dataList: 'bbbf',
            unit: 'cm²'
          },
          {
            cssName: 'myInputWidth',
            txt: '皮下脂肪含量：',
            type: 'myInput',
            prototype: 'bbbg',
            placeholder: '数字',
            dataList: 'bbbg',
            unit: 'cm²'
          }
        ]
      },
      {
        cssName: 'row6',
        txt: '血压：',
        row: [
          {
            cssName: 'myInputWidth myInputWidth3',
            txt: '收缩压：',
            type: 'myInput',
            prototype: 'ccca',
            placeholder: '数字',
            dataList: 'ccca',
            unit: 'mmHg'
          },
          {
            cssName: 'myInputWidth myInputWidth3',
            txt: '舒张压：',
            type: 'myInput',
            prototype: 'cccb',
            placeholder: '数字',
            dataList: 'cccb',
            unit: 'mmHg'
          },
          {
            cssName: 'myInputWidth myInputWidth3',
            txt: '脉搏：',
            type: 'myInput',
            prototype: 'cccc',
            placeholder: '数字',
            dataList: 'cccc',
            unit: '次/分'
          }
        ]
      },
      {
        cssName: 'row6',
        txt: '血氧：',
        row: [
          {
            cssName: 'myInputWidth myInputWidth4',
            txt: '血氧饱和度：',
            type: 'myInput',
            prototype: 'ddda',
            placeholder: '数字',
            dataList: 'ddda',
            unit: '%'
          },
          {
            cssName: 'myInputWidth myInputWidth4',
            txt: '脉率 (PR)：',
            type: 'myInput',
            prototype: 'dddb',
            placeholder: '数字',
            dataList: 'dddb',
            unit: '次/分'
          },
          {
            cssName: 'myInputWidth myInputWidth4',
            txt: '血流灌注指数 (PI)：',
            type: 'myInput',
            prototype: 'dddc',
            placeholder: '数字',
            dataList: 'dddc'
          }
        ]
      }
    ]
  },
  {
    title: '实验室检查',
    row: [
      {
        cssName: 'row6',
        txt: '血糖：',
        row: [
          {
            cssName: 'myInputWidth myInputWidth3',
            txt: '血糖：',
            type: 'myInput',
            prototype: 'eeea',
            placeholder: '数字',
            dataList: 'eeea',
            unit: 'mmol/L'
          },
          {
            cssName: 'myInputWidth myInputWidth3',
            txt: '类型：',
            type: 'myInput',
            prototype: 'eeeb',
            placeholder: '数字',
            dataList: 'eeeb'
          }
        ]
      },
      {
        cssName: 'row6',
        txt: '血脂：',
        row: [
          {
            cssName: 'myInputWidth myInputWidth3',
            txt: '总胆固醇 (TC)：',
            type: 'myInput',
            prototype: 'eeec',
            placeholder: '数字',
            dataList: 'eeec',
            unit: 'mmol/L'
          },
          {
            cssName: 'myInputWidth myInputWidth3',
            txt: '甘油三酯 (TG)：',
            type: 'myInput',
            prototype: 'eeed',
            placeholder: '数字',
            dataList: 'eeed',
            unit: 'mmol/L'
          },
          {
            cssName: 'myInputWidth myInputWidth3',
            txt: '低密度脂蛋白 (LDL)：',
            type: 'myInput',
            prototype: 'eeee',
            placeholder: '数字',
            dataList: 'eeee',
            unit: 'mmol/L'
          },
          {
            cssName: 'myInputWidth myInputWidth3',
            txt: '高密度脂蛋白 (HDL)：',
            type: 'myInput',
            prototype: 'eeef',
            placeholder: '数字',
            dataList: 'eeef',
            unit: 'mmol/L'
          },
          {
            cssName: 'myInputWidth myInputWidth3',
            txt: '葡萄糖 (GLU)：',
            type: 'myInput',
            prototype: 'eeeg',
            placeholder: '数字',
            dataList: 'eeeg',
            unit: 'mmol/L'
          }
        ]
      },
      {
        cssName: 'row6',
        txt: '糖化血红蛋白：',
        row: [
          {
            cssName: 'myInputWidth myInputWidth3',
            txt: '糖化血红蛋白：',
            type: 'myInput',
            prototype: 'eeeh',
            placeholder: '数字',
            dataList: 'eeeh',
            unit: '%'
          }
        ]
      }
    ]
  },
  {
    title: '辅助检查',
    row: [
      {
        cssName: 'row6',
        txt: '心电图：',
        type: 'ecg',
        prototype: 'ecgdata'
      }
    ]
  },
  {
    title: '影像报告',
    type: 'yxbgTitle',
    row: [
      {
        cssName: 'row6',
        txt: '影像报告：',
        type: 'upload',
        prototype: 'yxbg'
      }
    ]
  },
  {
    title: '远程会诊',
    type: 'ychz',
    row: [
      {
        cssName: 'row6',
        txt: '诊断：',
        row: [
          {
            txt: '',
            type: 'dbclick',
            prototype: 'ychzzd',
            placeholder: '*双击录入门诊诊断*',
            cssName: 'red',
            dbclickFun: () => {
              console.log('入门诊诊断')
            }
          },
          {
            txt: '',
            type: 'dbclick',
            prototype: 'ychzblzd',
            placeholder: '双击选择病理诊断',
            typeS: '[]',
            dbclickFun: () => {
              console.log('入病理诊断')
            }
          }
        ]
      }
    ]
  },
  {
    title: '处理措施',
    row: [
      {
        cssName: 'row6',
        txt: '',
        row: [
          {
            txt: '',
            type: 'text',
            prototype: 'clcs1',
            placeholder: '*双击录入门诊诊断*',
            cssName: 'row6'
          },
          {
            txt: '',
            type: 'myInput',
            cssName: 'w10',
            prototype: 'clcs2',
            placeholder: '手写处置在这录入'
          }
        ]
      }
    ]
  },
  {
    title: '处方药：',
    type: 'addMedicine',
    addDataKey: '0',
    row: [
      {
        cssName: 'row6',
        prototype: 'cfyData',
        txt: '',
        type: 'table',
        tableTitle: [
          {
            txt: '开始时间',
            prototype: 'startTime',
            type: 'myTime',
            placeholder: '选择'
          },
          {
            txt: '结束时间',
            prototype: 'endTime',
            type: 'myTime',
            placeholder: '选择'
          },
          {
            txt: '药品名称',
            prototype: 'ypmc',
            type: 'mySelect',
            placeholder: '请输入药品名称',
            dataList: 'ypmcList'
          },
          {
            txt: '时段',
            prototype: 'sd',
            type: 'mySelect',
            placeholder: '选择',
            dataList: 'sdList'
          },
          {
            txt: '服药时间',
            prototype: 'fysj',
            type: 'mySelect',
            placeholder: '选择',
            dataList: 'fysjList'
          },
          {
            txt: '频次',
            prototype: 'pc',
            type: 'mySelect',
            placeholder: '选择',
            dataList: 'pcList'
          },
          {
            txt: '单次计量',
            row: [
              {
                txt: '',
                prototype: 'dcjl',
                type: 'myInput',
                placeholder: '数字'
              },
              {
                txt: '',
                prototype: 'dcjlUnit',
                type: 'mySelect',
                placeholder: '选择',
                dataList: 'unitList'
              }
            ]
          },
          {
            txt: '给药途径',
            prototype: 'gytj',
            type: 'mySelect',
            placeholder: '选择',
            dataList: 'gytjList'
          },
          {
            txt: '操作',
            type: 'doRow',
            btnRow: [
              {
                txt: '删除',
                cssName: 'red',
                clickFun: (data, row) => {
                  data.splice(row.index, 1)
                }
              }
            ]
          }
        ]
      },
      {
        cssName: 'row6',
        txt: '是否下转：',
        row: [
          {
            txt: '',
            type: 'mySelect',
            prototype: 'sfxz',
            required: true,
            placeholder: '*选择*',
            cssName: 'row6',
            dataList: 'sfxz'
          }
        ]
      },
      {
        cssName: 'row6',
        txt: '医生签字：',
        row: [
          {
            type: 'sign',
            prototype: 'doctorSign',
            clickFun: (data, row) => {
              data.splice(row.index, 1)
            }
          }
        ]
      }
    ]
  }
]
