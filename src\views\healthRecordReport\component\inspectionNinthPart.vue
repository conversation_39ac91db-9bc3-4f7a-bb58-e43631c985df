<!-- 检验检查第九部分: 动态血压心电图 -->
<template>
  <div class="inspection-ninth-part">
    <div class="content">
      <div class="title">动态血压心电图</div>
      <div class="item">
        <div class="item-title">心率</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in dynamicBlood.heartRate" :key="item.prop" :label="item.label">
            {{ dynamicBloodData[item.prop] }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">室性</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in dynamicBlood.ventricular" :key="item.prop" :label="item.label">
            {{ dynamicBloodData[item.prop] }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">房性</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in dynamicBlood.atrial" :key="item.prop" :label="item.label">
            {{ dynamicBloodData[item.prop] }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">交界性</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in dynamicBlood.beat" :key="item.prop" :label="item.label">
            {{ dynamicBloodData[item.prop] }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">HRV</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in dynamicBlood.hrv" :key="item.prop" :label="item.label">
            {{ dynamicBloodData[item.prop] }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">ST段分析</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in dynamicBlood.stSegment" :key="item.prop" :label="item.label">
            {{ dynamicBloodData[item.prop] }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">房颤/房扑</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in dynamicBlood.pause" :key="item.prop" :label="item.label">
            {{ dynamicBloodData[item.prop] }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">起搏</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in dynamicBlood.sympathetic" :key="item.prop" :label="item.label">
            {{ dynamicBloodData[item.prop] }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <el-descriptions :column="1" border style="margin-top: 6px">
        <el-descriptions-item label="检查结论">
          {{ dynamicBloodData.dynamicsEcgDescription }}
        </el-descriptions-item>
        <el-descriptions-item label="检查结果">
          {{
            dynamicBloodData.dynamicsEcgResult
              .split(',')
              .map(item => ecgOptions.find(option => option.value === item).label)
              .join('、')
          }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </div>
</template>

<script>
import { dynamicBlood } from '@/views/receptionCenter/patientReception/component/complicationsScreening'
import { ecgOptions } from '@/views/receptionCenter/patientReception/component/complicationsScreening.js'

export default {
  name: 'InspectionNinthPart',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      dynamicBlood,
      ecgOptions
    }
  },
  computed: {
    dynamicBloodData() {
      const { data = {} } = this.reportInfo.itemList.find((item) => item.itemCode === 'DYNAMICS_ECG') || {}
      return data
    }
  }
}
</script>

<style lang="scss" scoped>
.inspection-ninth-part {
  .content {
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 6px;
    }
    .item {
      margin-bottom: 6px;
      .item-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 8px;
      }
    }
  }
  ::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell {
    padding: 4px 10px;
  }
}
</style>
