import request from '@/utils/request'

// 查询登录用户消息通知列表
export const getMessageNotificationList = (data) => {
  return request({
    url: '/cspapi/backend/message/notice/refresh',
    method: 'post',
    data
  })
}

// 分页查询消息通知列表
export const getMessageNotificationPage = (data) => {
  return request({
    url: '/cspapi/backend/message/notice/page',
    method: 'post',
    data
  })
}

// 标记已读
export const markRead = (data) => {
  return request({
    url: '/cspapi/backend/message/notice/read',
    method: 'post',
    data
  })
}

// 清除消息
export const clearMessage = (data) => {
  return request({
    url: '/cspapi/backend/message/notice/clear',
    method: 'post',
    data
  })
}
