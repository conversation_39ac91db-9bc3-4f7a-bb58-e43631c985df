<!-- 高血压记录 -->

<template>
  <div class="hypertension">
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      row-key="id"
      :height="320"
      :columns="columns"
      :show-pagination="showPagination"
    />
    <blood-pressure-echart :table-data="tableData" />
  </div>
</template>

<script>
import { getUserDbHypertensionRecord } from '@/api/archives'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import BloodPressureEchart from '@/views/receptionCenter/managePatient/echarts/bloodPressureEchart.vue'

export default {
  name: 'Hypertension',
  components: {
    BaseTable,
    BloodPressureEchart
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        patientId: this.$route.query.id
      },
      columns: [
        {
          label: '时间',
          prop: 'measureTime'
        },
        {
          label: '收缩压',
          prop: 'sp'
        },
        {
          label: '舒张压',
          prop: 'dp'
        }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      return await getUserDbHypertensionRecord(params)
    },

    // 我这么麻烦转换是为了和规范管理那边使用同一个图表组件
    async fetchData() {
      try {
        this.loading = true
        const params = {
          ...this.queryParams,
          ...this.sortParams
        }
        const res = await this.getTableList(params)
        const list = this.showPagination ? res.data.list : res.data
        list.forEach((item) => {
          item.measureTime = item.pressureTime
        })
        this.tableData = list

        this.total = res.data.total || 0
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
