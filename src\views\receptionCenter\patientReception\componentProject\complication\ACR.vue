<!-- ACR检查 -->

<template>
  <div class="acr-check">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="180px">
      <el-form-item label="尿微量白蛋白：">
        <custom-input-number v-model="form.uma" style="width: 30%">
          <template slot="append">mg/L</template>
        </custom-input-number>
      </el-form-item>

      <el-form-item label="肌酐：">
        <custom-input-number v-model="form.creatinine" style="width: 30%">
          <template slot="append">
            <el-select v-model="form.creatinineUnit">
              <el-option label="mg/dL" :value="1" />
              <el-option label="mmol/L" :value="2" />
              <el-option label="μmol/L" :value="3" />
            </el-select>
          </template>
        </custom-input-number>
      </el-form-item>

      <el-form-item label="尿白蛋白/肌酐：">
        <custom-input-number v-model="form.umaCreatinine" style="width: 30%">
          <template slot="append">
            <el-select v-model="form.umaCreatinineUnit">
              <el-option label="mg/g" :value="1" />
              <el-option label="mg/mmol" :value="2" />
            </el-select>
          </template>
        </custom-input-number>
      </el-form-item>

      <el-form-item label="糖尿病ACR检查结果：" prop="acrResult">
        <el-radio-group v-model="form.acrResult">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="2">异常</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>

    <static-table :columns="acrCheck.columns" :table-data="acrCheck.staticTableData" />
  </div>
</template>

<script>
import { acrCheck } from './staticTableData'
import StaticTable from '@/components/staticTable/index.vue'

export default {
  name: 'ACRCheck',
  components: {
    StaticTable
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      acrCheck,
      form: {
        uma: '',
        creatinine: '',
        umaCreatinine: '',
        creatinineUnit: 1,
        umaCreatinineUnit: 1,
        acrResult: ''
      },
      rules: {
        acrResult: [{ required: true, message: '请选择糖尿病ACR检查结果' }]
      }
    }
  },
  methods: {
    initData(data) {
      this.form = {
        uma: data.uma,
        creatinine: data.creatinine,
        umaCreatinine: data.umaCreatinine,
        creatinineUnit: data.creatinineUnit,
        umaCreatinineUnit: data.umaCreatinineUnit,
        acrResult: data.acrResult,
        id: data.id
      }
    },
    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          ...this.form,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    }
  }
}
</script>

<style scoped lang="scss">
.acr-check {
  ::v-deep .el-input-group__append {
    width: 100px;
  }
}
</style>
