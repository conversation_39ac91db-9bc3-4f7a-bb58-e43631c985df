import request from '@/utils/request'

// 分页查询签约记录
export const getIndividualSigningList = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/signContractRecord/page',
    method: 'post',
    data
  })
}

// 查询签约记录详情
export const getIndividualSigningDetail = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/signContractRecord/detail',
    method: 'post',
    data
  })
}

// 创建签约记录
export const createIndividualSigning = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/signContractRecord/create',
    method: 'post',
    data
  })
}

// 查看签约协议模板详情
export const getIndividualSigningTemplateDetail = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/signContractRecord/protocol/detail',
    method: 'post',
    data
  })
}

// 取消签约
export const cancelIndividualSigning = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/signContractRecord/cancel',
    method: 'post',
    data
  })
}

// 查询当前时段是否有签约By患者
export const signContract = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/signContractRecord/exists',
    method: 'post',
    data
  })
}

// 查询团队列表By机构
export const getTeamListByDepart = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/team/list',
    method: 'post',
    data
  })
}

// 查询医生By团队
export const getDoctorListByTeam = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/team/doctor/list',
    method: 'post',
    data
  })
}

// 查询协议模板列表
export const getProtocolList = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/protocolModule/list',
    method: 'post',
    data
  })
}

// 查询服务包列表
export const getServicePackageList = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/servicePackage/list',
    method: 'post',
    data
  })
}

// 查询履约记录
export const getPerformanceList = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/signContractRecord/perform/list',
    method: 'post',
    data
  })
}

// 更新履约计划时间
export const updatePerformance = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/signContractRecord/perform/update',
    method: 'post',
    data
  })
}

// 完成履约
export const completePerformance = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/signContractRecord/perform/complete',
    method: 'post',
    data
  })
}
