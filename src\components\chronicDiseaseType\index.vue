<template>
  <div v-if="formattedDiseases.length" class="disease-type">
    <span v-for="(item, index) in formattedDiseases" :key="index" :class="['diseases', item.className, 'fontSize_14']">
      {{ item.diseasesStr }}
    </span>
  </div>
  <span v-else>-</span>
</template>

<script>
export default {
  name: 'ChronicDiseaseType',
  props: {
    diseaseList: {
      type: Array,
      default: () => [
        {
          code: 'tnb',
          value: '糖尿病'
        },
        {
          code: 'gxy',
          value: '高血压'
        },
        {
          code: 'COPD',
          value: '慢阻肺'
        },
        {
          code: 'fangchan',
          value: '房颤'
        }
      ]
    },
    record: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    formattedDiseases() {
      if (
        !this.record.diseases &&
        !this.diseaseList.length &&
        !this.record.confirmedDiseasesName &&
        !this.record.diseaseList.length
      ) return []

      const diseaseMap = {
        guanxinbing: { diseasesStr: '冠', className: 'diseases_FFA700' },
        xlsj: { diseasesStr: '衰', className: 'diseases_7B36FF' },
        fangchan: { diseasesStr: '颤', className: 'diseases_9F00E2' },
        nzz: { diseasesStr: '脑', className: 'diseases_3FABFD' },
        COPD: { diseasesStr: '慢', className: 'diseases_684949' },
        gxy: { diseasesStr: '高', className: 'diseases_EE3737' },
        tnb: { diseasesStr: '糖', className: 'diseases_138234' }
      }

      return this.diseaseList
        .filter((child) =>
          (
            this.record.diseases ||
            this.record.confirmedDiseasesName ||
            (this.record.diseaseList && this.record.diseaseList.join(',')) ||
            ''
          ).includes(child.value))
        .map((child) => diseaseMap[child.code] || {})
    }
  }
}
</script>

<style lang="scss" scoped>
.disease-type {
  display: inline-block;
}
.diseases {
  width: 1.2rem;
  height: 1.2rem;
  line-height: 1.2rem;
  border-radius: 50%;
  display: inline-block;
  color: #fff;
  text-align: center;
}

.diseases + .diseases {
  margin-left: 8px;
}

.diseases_684949 {
  background-color: #684949;
}
.diseases_EE3737 {
  background-color: #ee3737;
}
.diseases_138234 {
  background-color: #138234;
}
.diseases_9F00E2 {
  background-color: #9f00e2;
}
.diseases_FFA700 {
  background-color: #ffa700;
}
.diseases_3FABFD {
  background-color: #3fabfd;
}
.diseases_7B36FF {
  background-color: #7b36ff;
}
</style>
