#pro-table {
  th.el-table__cell {
    background: -webkit-gradient(linear, left top, left bottom, from(#eee), to(#f0f0f0));
    background: linear-gradient(180deg, #eee 0%, #f0f0f0 100%);
    color: #666666;
    height: 2.4rem;
    line-height: 0.8rem;
    font-weight: 600;
  }
  .el-form-item__label {
    line-height: 1.6rem;
  }
  .search-card {
    padding: 0.5rem 0.5rem 0 0.5rem;
    background: var(--bjColor);
    //box-shadow: 0px 3px 16px 0px rgba(9, 76, 1, 0.1);
    border-radius: 6px;
    margin-bottom: 1rem;

    .search-item {
      width: 100%;
      display: flex;
      font-size: 0.7rem;
      align-items: center;
    }
    .search-item-label {
      width: 30%;
      font-size: 0.7rem;
      text-align: center;
    }
  }
}

.pro-table-search-btn {
  display: flex;
}



