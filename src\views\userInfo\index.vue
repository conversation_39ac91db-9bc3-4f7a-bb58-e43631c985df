<template>
  <div class="info_box">
    <el-row>
      <el-col :span="6">
        <div class="left_info">
          <div class="avatar_info">
            <el-upload
              ref="upload"
              action="/cspapi/backend/cos/uploadFile/private2"
              :headers="{ Authorization: token }"
              accept=".jpg,.jepg,.png"
              :show-file-list="false"
              :on-success="handleUploadSuccess"
              :data="{ folder: 'doctorImg/' }"
              list-type="text"
              :auto-upload="true"
            >
              <div v-if="userForm.photo" class="avatar_text">
                <el-image class="avatar_img" :src="globalHttpUrl(userForm.photo)" />
              </div>
              <div v-else>
                <el-image class="avatar_img" :src="avatar" />
              </div>
              <div class="avatar_text">我的照片</div>
            </el-upload>
          </div>
        </div>
      </el-col>
      <el-col :span="18">
        <div class="right_info">
          <el-form
            ref="userForm"
            size="mini"
            :model="userForm"
            :rules="rules"
            class="demo-userForm"
            label-position="top"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="姓名" prop="name">
                  <el-input v-model="userForm.name" placeholder="请输入姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别">
                  <el-radio-group v-model="userForm.sex">
                    <el-radio :label="1">男</el-radio>
                    <el-radio :label="0">女</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="身份证号" prop="idCard">
                  <el-input v-model="userForm.idCard" placeholder="请输入身份证号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号" prop="phone">
                  <el-input v-model="userForm.phone" placeholder="请输入手机号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属机构" prop="departName">
                  <el-input v-model="userForm.departName" disabled placeholder="所属机构" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属科室" prop="departHospitalName">
                  <el-input v-model="userForm.departHospitalName" disabled placeholder="所属科室" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="医生职称" prop="professional">
                  <el-input v-model="userForm.professional" placeholder="请输入医生职称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职业资格证书" prop="certificateNo">
                  <el-input v-model="userForm.certificateNo" placeholder="请输入职业资格证书编号" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="简介" prop="description">
                  <el-input v-model="userForm.description" type="textarea" placeholder="请输入简介" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="擅长内容" prop="expertise">
                  <el-input v-model="userForm.expertise" type="textarea" placeholder="请输入擅长内容" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上传职业医师资格证" prop="honorPhotos">
                  <el-upload
                    ref="upload"
                    drag
                    action="/cspapi/backend/cos/uploadFile/private2"
                    :headers="{ Authorization: token }"
                    accept=".jpg,.jepg,.png"
                    :show-file-list="false"
                    :on-success="handleUploadSuccess2"
                    :data="{ folder: 'doctorImg/' }"
                    list-type="picture-card"
                    :auto-upload="true"
                    class="uploadDiv uploadListDiv"
                    style="width: 100%; min-height: 5rem"
                  >
                    <div
                      v-if="!userForm.honorPhotos.length"
                      class="pr h10 imgList btnDiv"
                      style="height: 5rem; width: 5rem"
                    />
                    <el-card v-else shadow="never" style="width: 100%; height: 225px">
                      <el-image
                        width="100%"
                        style="height: 225px; padding-bottom: 2rem"
                        :src="globalHttpUrl(userForm.honorPhotos[0])"
                      />
                    </el-card>
                  </el-upload>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="上传电子签名">
                  <div class="uploadDiv cp uploadListDiv">
                    <div
                      v-if="!userForm.sign"
                      class="pr h10 imgList btnDiv"
                      style="height: 5rem; width: 5rem"
                      @click="signatureStart"
                    />
                    <el-card v-else shadow="never" style="width: 100%; height: 225px">
                      <el-image :src="globalHttpUrl(userForm.sign)" class="w10" @click="signatureStart" />
                    </el-card>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item>
              <el-button type="primary" @click="submitForm('userForm')">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import leftBg from '@/assets/user/leftbg.png'
import avatar from '@/assets/user/avatar.png'
import { getToken, getUserId } from '@/utils/auth'
import { getDecrypt, getDoctorInfo, putDoctorInfo } from '@/views/userInfo/api'

export default {
  data() {
    return {
      leftBg,
      avatar,
      docTorInfo: {},
      fileList: [],
      userForm: {
        photo: '', // 头像
        name: '', // 姓名
        sex: '', // 性别
        idCard: '', // 身份证号
        phone: '', // 手机号
        departName: '', // 所属机构
        departHospitalName: '', // 所属科室
        professional: '', // 医生职称
        certificateNo: '', // 职业资格证书编号
        description: '', // 简介
        expertise: '', // 擅长内容
        honorPhotos: [], // 上传职业医师资格证
        sign: '' // 签名
      },
      rules: {
        name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
        idCard: [{ required: true, message: '请输入身份证号', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入手机号', trigger: 'change' }]
      }
    }
  },
  computed: {
    token() {
      return getToken()
    }
  },
  async created() {
    const { data } = await getDoctorInfo({ id: getUserId() })
    const phoneRes = await getDecrypt({ cipherText: data && data.phoneOrigin })
    const idRes = await getDecrypt({ cipherText: data && data.idCardOrigin })
    const inForm = {
      ...data,
      phone: phoneRes.data.originalNumber,
      idCard: idRes.data.originalNumber,
      honorPhotos: data && data.honorPhotos ? data.honorPhotos : []
    }
    this.docTorInfo = inForm
    this.userForm = inForm
  },
  methods: {
    // 签名上传后返回图片
    backImg(url) {
      this.userForm.sign = url
    },
    signatureStart() {
      this.$refs.signatureRef.show()
    },
    handleUploadSuccess(val) {
      this.userForm.photo = this.$allJs.handleUploadFilePath({ data: val.data })[0]
    },
    handleUploadSuccess2(val) {
      const newItem = this.$allJs.handleUploadFilePath({ data: val.data })[0]
      this.userForm.honorPhotos = [newItem]
    },
    submitForm(formName) {
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          const {
            id,
            name,
            photo,
            honorPhotos,
            sex,
            phone,
            idCard,
            professional,
            expertise,
            description,
            sign,
            certificateNo
          } = this.userForm
          const subForm = {
            id,
            status: 1,
            name,
            photo,
            honorPhotos,
            sex,
            phone,
            idCard,
            professional,
            certificateNo,
            expertise,
            description,
            sign
          }

          // {
          //   "code": 200,
          //   "msg": "操作成功",
          //   "data": null,
          //   "result": true
          // }
          const res = await putDoctorInfo(subForm)
          this.$message.success(res.msg)
          console.log(subForm)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    handleRemove(file) {
      console.log(file)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleDownload(file) {
      console.log(file)
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-upload-dragger {
  width: 100%;
}
.uploadDiv {
  height: unset;
  min-height: 5rem;
}
.info_box {
  padding: 20px;
  height: calc(100vh - 100px);
}
.left_info {
  width: 100%;
  height: 80vh;
  background-image: url('~@/assets/user/leftbg.png');
  background-position: center bottom;
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  justify-content: center;
  align-items: center;
  .avatar_info {
    text-align: center;
    .avatar_img {
      width: 7vw;
      height: 7vw;
      border-radius: 50%;
    }
    .avatar_text {
      padding-top: 12px;
      color: #4e4e4e;
    }
  }
}
.right_info {
  background: #fff;
  padding-top: 1%;
  padding-left: 5%;
  padding-right: 15%;
}
</style>
