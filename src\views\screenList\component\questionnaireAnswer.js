const massScreeningQuestion = [
  {
    label: '婚姻状况？',
    type: 'radio',
    prop: 'marriage',
    belong: 'all',
    options: [
      { label: '未婚', value: 1 },
      { label: '已婚', value: 2 },
      { label: '丧偶', value: 3 },
      { label: '离婚', value: 4 },
      { label: '未说明婚姻状况', value: 5 }
    ]
  },
  {
    label: '是否饮酒？',
    type: 'radio',
    prop: 'drink',
    belong: 'all',
    options: [
      { label: '从不', value: 1 },
      { label: '偶尔', value: 2 },
      { label: '经常', value: 3 },
      { label: '每天', value: 4 },
      { label: '已戒酒', value: 5 }
    ]
  },
  {
    label: '是否吸烟？',
    type: 'radio',
    prop: 'smoke',
    belong: 'all',
    options: [
      { label: '从不吸烟', value: 1 },
      { label: '已戒烟', value: 2 },
      { label: '吸烟', value: 3 }
    ]
  },
  {
    label: '职业？',
    type: 'radio',
    prop: 'career',
    belong: 'all',
    options: [
      { label: '国家机关、党群组织、企业、事业单位负责人', value: 1 },
      { label: '专业技术人员', value: 2 },
      { label: '军人', value: 3 },
      { label: '不便分类的其他从业人员', value: 4 },
      { label: '办事人员和有关人员', value: 5 },
      { label: '社会生产服务和生活服务人员', value: 6 },
      { label: '农、林、牧、渔、水利生产人员', value: 7 },
      { label: '生产、运输设备操作人员及有关人员', value: 8 },
      { label: '无职业', value: 9 }
    ]
  },
  {
    label: '职业类型？',
    type: 'radio',
    prop: 'careerType',
    belong: 'all',
    options: [
      { label: '轻劳动职业(如办公、操作一控、控制、查体运输等)', value: 1 },
      { label: '中等劳动职业(如建筑搬运、挖掘等)', value: 2 },
      { label: '重度劳动职业(搬运重物、挖掘等)', value: 3 },
      { label: '极重劳动职业(如大强度搬运重物、挖掘等)', value: 4 }
    ]
  },
  {
    label: '是否有糖尿病家族史？',
    type: 'checkbox',
    mutuallyExclusive: true, // 互斥
    prop: 'tnbFamilyHistory',
    belong: 'tnb',
    options: [
      { label: '无', value: '1' },
      { label: '一级亲属(父母、子女及兄弟姐妹)', value: '2' },
      { label: '二级亲属(叔、伯、姑、姨、祖父母等)', value: '3' },
      { label: '三级亲属(表兄妹、堂兄妹)', value: '4' }
    ]
  },
  {
    label: '是否有高血压家族史？',
    type: 'checkbox',
    prop: 'gxyFamilyHistory',
    belong: 'gxy',
    mutuallyExclusive: true, // 互斥
    options: [
      { label: '无', value: '1' },
      { label: '一级亲属(父母、子女及兄弟姐妹)', value: '2' },
      { label: '二级亲属(叔、伯、姑、姨、祖父母等)', value: '3' },
      { label: '三级亲属(表兄妹、堂兄妹)', value: '4' }
    ]
  },
  {
    label: '是否有慢阻肺家族史？',
    type: 'checkbox',
    prop: 'copdFamilyHistory',
    belong: 'copd',
    mutuallyExclusive: true, // 互斥
    options: [
      { label: '无', value: '1' },
      { label: '一级亲属(父母、子女及兄弟姐妹)', value: '2' },
      { label: '二级亲属(叔、伯、姑、姨、祖父母等)', value: '3' },
      { label: '三级亲属(表兄妹、堂兄妹)', value: '4' }
    ]
  },
  {
    label: '是否房颤家族史？',
    type: 'checkbox',
    prop: 'fcFamilyHistory',
    belong: 'fc',
    mutuallyExclusive: true, // 互斥
    options: [
      { label: '无', value: '1' },
      { label: '一级亲属(父母、子女及兄弟姐妹)', value: '2' },
      { label: '二级亲属(叔、伯、姑、姨、祖父母等)', value: '3' },
      { label: '三级亲属(表兄妹、堂兄妹)', value: '4' }
    ]
  },
  {
    label: '每日蔬菜摄入量？',
    type: 'radio',
    prop: 'vegetableIntake',
    belong: 'tnb,gxy',
    options: [
      { label: '非常少（<100g）', value: 1 },
      { label: '少（100-200g）', value: 2 },
      { label: '正常（300-500g）', value: 3 },
      { label: '多（>500g）', value: 4 }
    ]
  },
  {
    label: '您是否高盐/高脂饮食？',
    type: 'radio',
    prop: 'highSaltFatFlag',
    belong: 'tnb,gxy',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '您是否缺乏体力活动？',
    type: 'radio',
    prop: 'labourFlag',
    belong: 'tnb,gxy',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '您是否长期精神紧张，加班熬夜/夜班？',
    type: 'radio',
    prop: 'workOvertimeFlag',
    belong: 'tnb,gxy',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '过去一个月内，您感到气短有多频繁？',
    type: 'radio',
    prop: 'pantFlag',
    belong: 'copd',
    options: [
      { label: '从未感觉气短', value: 1 },
      { label: '很少感觉气短', value: 2 },
      { label: '有时感觉气短', value: 3 },
      { label: '经常感觉气短', value: 4 },
      { label: '总是感觉气短', value: 5 }
    ]
  },
  {
    label: '您是否曾咳出“东西”，例如粘液或痰？',
    type: 'radio',
    prop: 'coughFlag',
    belong: 'copd',
    options: [
      { label: '从未咳出', value: 1 },
      { label: '是的，但仅在偶尔感冒或胸部感染时咳出', value: 2 },
      { label: '是的，每月都咳几天', value: 3 },
      { label: '是的，大多数日子都咳', value: 4 },
      { label: '是的，每天都咳', value: 5 }
    ]
  },
  {
    label: '请选择能够最准确描述您过去12个月内日常生活状况的答案。因呼吸困难，我活动量比以前少了。',
    type: 'radio',
    prop: 'breatheFlag',
    belong: 'copd',
    options: [
      { label: '强烈反对', value: 1 },
      { label: '反对', value: 2 },
      { label: '不确定', value: 3 },
      { label: '同意', value: 4 },
      { label: '非常同意', value: 5 }
    ]
  },
  {
    label: '在您的生命中，您是否至少吸了100支烟？',
    type: 'radio',
    prop: 'hundredSmokeFlag',
    belong: 'copd',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 },
      { label: '不知道', value: 3 }
    ]
  },
  {
    label: '您今年多少岁？',
    type: 'radio',
    prop: 'ageFlag',
    belong: 'copd',
    options: [
      { label: '35-49岁', value: 1 },
      { label: '50-59岁', value: 2 },
      { label: '60-69岁', value: 3 },
      { label: '≥70岁', value: 4 }
    ]
  },
  {
    label: '闲暇时是否会进行身体锻炼？',
    type: 'radio',
    prop: 'exerciseFlag',
    belong: 'fc',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '是否有高血压？',
    type: 'radio',
    prop: 'gxyFlag',
    belong: 'fc',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '是否有糖尿病？',
    type: 'radio',
    prop: 'tnbFlag',
    belong: 'fc',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '是否有心悸，气短，乏力，晕厥等？',
    type: 'radio',
    prop: 'palpitationFlag',
    belong: 'fc',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  }
]

export { massScreeningQuestion }
