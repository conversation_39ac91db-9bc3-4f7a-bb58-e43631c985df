<template>
  <div class="public_contentRight flex_column" :style="{ 'margin-left': marginLeft, 'width': `calc(100% - ${width})` }">
    <slot />
  </div>
</template>

<script>
export default {
  name: 'ProTwoColumnsLayoutRight',
  components: {},

  props: {
    marginLeft: {
      type: String,
      default: '0.4rem'
    },
    width: {
      type: String,
      default: '15.4rem'
    }
  },
  data() {
    return {}
  },
  computed: {},
  methods: {}
}
</script>

<style lang="scss"></style>
