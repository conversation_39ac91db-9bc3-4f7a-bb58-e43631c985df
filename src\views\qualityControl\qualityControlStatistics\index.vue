<template>
  <div class="quality-control-statistics">
    <el-card>
      <statistics-chart :chart-data="chartData" />
    </el-card>
    <el-card class="search-card">
      <el-form :model="queryParams" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="上传时间：" prop="uploadTime">
              <el-date-picker
                v-model="queryParams.timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="table-card">
      <el-radio-group v-model="queryParams.statisticsType" style="margin-bottom: 10px" @change="handleRadioChange">
        <el-radio-button label="1">按机构</el-radio-button>
        <el-radio-button label="2">按质控员</el-radio-button>
      </el-radio-group>
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        row-key="id"
        :columns="columns"
        :show-pagination="showPagination"
      />
    </el-card>
  </div>
</template>

<script>
import {
  getQualityControlStatisticsByDepart,
  getQualityControlStatisticsByPerson,
  getQualityControlStatisticsByDate
} from '@/api/qualityControl'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import StatisticsChart from './component/statisticsChart.vue'

export default {
  name: 'QualityControlStatistics',
  components: {
    BaseTable,
    StatisticsChart
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        timeRange: [],
        statisticsType: '1'
      },
      showPagination: false,
      departColumns: [
        { label: '机构名称', prop: 'departName' },
        { label: '提交总数', prop: 'submitCount' },
        { label: '待质控', prop: 'waitCount' },
        { label: '已通过', prop: 'passCount' },
        { label: '未通过', prop: 'failCount' }
      ],
      personColumns: [
        { label: '质控员', prop: 'name' },
        { label: '质控总数', prop: 'auditCount' },
        { label: '已通过', prop: 'passCount' },
        { label: '未通过', prop: 'failCount' }
      ],
      chartData: []
    }
  },

  computed: {
    columns() {
      return this.queryParams.statisticsType === '1' ? this.departColumns : this.personColumns
    }
  },
  created() {
    this.getDateList()
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]
      const queryParams = {
        ...rest,
        startDate,
        endDate
      }

      return (await this.queryParams.statisticsType) === '1'
        ? getQualityControlStatisticsByDepart(queryParams)
        : getQualityControlStatisticsByPerson(queryParams)
    },

    async getDateList() {
      const res = await getQualityControlStatisticsByDate({})
      if (res.code === 200) {
        this.chartData = res.data
      }
    },

    handleRadioChange(value) {
      this.queryParams.statisticsType = value
      this.handleSearch()
    },
    handleReset() {
      this.queryParams = {
        timeRange: [],
        statisticsType: '1'
      }
      this.handleSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.quality-control-statistics {
  padding: 16px;
  .search-card {
    margin: 16px 0;
  }
}
</style>
