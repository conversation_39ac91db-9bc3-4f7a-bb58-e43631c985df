<!-- 慢病病种-高血压 -->
<template>
  <div class="chronic-diseases-hype">
    <el-tabs v-model="activeName">
      <el-tab-pane label="血压监测" name="1" />
      <el-tab-pane label="24小时动态血压" name="2" />
    </el-tabs>
    <div v-show="activeName === '1'">
      <el-form ref="hypertensionFormRef" :model="hypertensionForm" label-width="140px" :rules="rules">
        <el-row>
          <el-col :span="2">
            <div>第一次测量</div>
          </el-col>
          <el-col :span="6">
            <el-form-item label="收缩压" prop="firstSp">
              <custom-input-number v-model="hypertensionForm.firstSp">
                <template #append>mmHg</template>
              </custom-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="舒张压" prop="firstDp">
              <custom-input-number v-model="hypertensionForm.firstDp">
                <template #append>mmHg</template>
              </custom-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="2">
            <div>第二次测量</div>
          </el-col>
          <el-col :span="6">
            <el-form-item label="收缩压" prop="secondSp">
              <custom-input-number v-model="hypertensionForm.secondSp">
                <template #append>mmHg</template>
              </custom-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="舒张压" prop="secondDp">
              <custom-input-number v-model="hypertensionForm.secondDp">
                <template #append>mmHg</template>
              </custom-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="2">
            <div>第三次测量</div>
          </el-col>
          <el-col :span="6">
            <el-form-item label="收缩压" prop="thirdSp">
              <custom-input-number v-model="hypertensionForm.thirdSp">
                <template #append>mmHg</template>
              </custom-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="舒张压" prop="thirdDp">
              <custom-input-number v-model="hypertensionForm.thirdDp">
                <template #append>mmHg</template>
              </custom-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div v-show="activeName === '2'">
      <el-form :model="hypertension24Form" label-width="140px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="24小时平均血压">
              <div class="item">
                <custom-input-number v-model="hypertension24Form.allAvgSp">
                  <template #append>/</template>
                </custom-input-number>
                <custom-input-number v-model="hypertension24Form.allAvgDp">
                  <template #append>mmHg</template>
                </custom-input-number>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="日间平均血压">
              <div class="item">
                <custom-input-number v-model="hypertension24Form.dayAvgSp">
                  <template #append>/</template>
                </custom-input-number>
                <custom-input-number v-model="hypertension24Form.dayAvgDp">
                  <template #append>mmHg</template>
                </custom-input-number>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="夜间平均血压">
              <div class="item">
                <custom-input-number v-model="hypertension24Form.nightAvgSp">
                  <template #append>/</template>
                </custom-input-number>
                <custom-input-number v-model="hypertension24Form.nightAvgDp">
                  <template #append>mmHg</template>
                </custom-input-number>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="夜间血压下降率">
              <div class="item">
                <custom-input-number v-model="hypertension24Form.nightSpDeclineRate">
                  <template #append>/</template>
                </custom-input-number>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="晨峰血压">
              <div class="item">
                <custom-input-number v-model="hypertension24Form.morningSp">
                  <template #append>/</template>
                </custom-input-number>
                <custom-input-number v-model="hypertension24Form.morningDp">
                  <template #append>mmHg</template>
                </custom-input-number>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="动态血压系数">
              <custom-input-number v-model="hypertension24Form.dynamicCoefficient" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-col :span="24">
          <el-form-item label="检查结论">
            <el-input v-model="hypertension24Form.bpDescription" type="textarea" />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="上传报告图片">
            <custom-upload v-model="hypertension24Form.attachmentPhotoUrl" />
          </el-form-item>
        </el-col>
      </el-form>
    </div>
  </div>
</template>

<script>
import CustomUpload from '@/components/customUpload/index.vue'

export default {
  name: 'ChronicDiseasesHype',
  components: {
    CustomUpload
  },
  data() {
    return {
      hypertensionForm: {
        firstDp: '',
        firstSp: '',
        secondDp: '',
        secondSp: '',
        thirdDp: '',
        thirdSp: ''
      },
      hypertension24Form: {
        allAvgSp: '',
        allAvgDp: '',
        dayAvgSp: '',
        dayAvgDp: '',
        nightAvgSp: '',
        nightAvgDp: '',
        nightSpDeclineRate: '',
        nightDpDeclineRate: '',
        morningSp: '',
        morningDp: '',
        dynamicCoefficient: '',
        bpDescription: '',
        attachmentPhotoUrl: ''
      },
      rules: {
        firstSp: [{ required: true, message: '第一次测量收缩压', trigger: 'blur' }],
        firstDp: [{ required: true, message: '第一次测量舒张压', trigger: 'blur' }],
        secondSp: [{ required: true, message: '第二次测量收缩压', trigger: 'blur' }],
        secondDp: [{ required: true, message: '第二次测量舒张压', trigger: 'blur' }],
        thirdSp: [{ required: true, message: '第三次测量收缩压', trigger: 'blur' }],
        thirdDp: [{ required: true, message: '第三次测量舒张压', trigger: 'blur' }]
      },
      activeName: '1'
    }
  },
  methods: {
    initData(data) {
      const hypertensionData = data.find((item) => item.itemCode === 'PRESSURE') || {}
      const hypertension24Data = data.find((item) => item.itemCode === 'DYNAMICS_BLOOD_PRESSURE') || {}

      this.hypertensionForm = {
        ...Object.keys(this.hypertensionForm).reduce((acc, cur) => {
          acc[cur] = hypertensionData.data && hypertensionData.data[cur]
          return acc
        }, {}),
        itemCode: hypertensionData.itemCode,
        itemId: hypertensionData.id,
        itemDetailId: hypertensionData.data && hypertensionData.data.id
      }
      this.hypertension24Form = {
        ...Object.keys(this.hypertension24Form).reduce((acc, cur) => {
          acc[cur] = hypertension24Data.data && hypertension24Data.data[cur]
          return acc
        }, {}),
        itemCode: hypertension24Data.itemCode,
        itemId: hypertension24Data.id,
        itemDetailId: hypertension24Data.data && hypertension24Data.data.id
      }
    },

    async handleSave() {
      const result = {
        name: '高血压-血压监测',
        success: false,
        data: {
          hypertensionForm: {
            ...this.hypertensionForm,
            name: '血压监测',
            itemCode: 'PRESSURE'
          },
          hypertension24Form: {
            ...this.hypertension24Form,
            name: '24小时动态血压',
            itemCode: 'DYNAMICS_BLOOD_PRESSURE'
          }
        }
      }

      try {
        const valid = await this.$refs.hypertensionFormRef.validate()
        result.success = valid
      } catch (err) {
        console.warn('高血压-血压监测校验异常', err)
        result.success = false
      }

      return result
    }
  }
}
</script>

<style lang="scss" scoped>
.chronic-diseases-hype {
  padding: 16px;
  .item {
    display: flex;
    align-items: center;
    ::v-deep .el-input-group__append {
      width: 50px !important;
      text-align: center;
      padding: 0 10px;
    }
  }
}
</style>
