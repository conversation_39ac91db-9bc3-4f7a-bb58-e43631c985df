const envModule = require('@/utils/env')

export const URL_ENUM = envModule.genUrlEnum()

// 正则 - 手机号正则
export const REGEX_MOBILE = /^1[3456789]\d{9}$/

export const GENDER_ENUM = [
  { label: '女', value: 0 },
  { label: '男', value: 1 },
  { label: '未说明', value: 2 },
  { label: '未知', value: 3 }
]

export const screeningEnum = [
  {
    label: '一筛总人数',
    code: 'screeningFirstCount',
    value: ''
  },
  {
    label: '二筛总人数',
    code: 'screeningSecondCount',
    value: ''
  },

  {
    label: '动脉硬化检查人数',
    code: 'arteriosclerosisCount',
    value: ''
  },

  {
    label: '眼底相机检查人数',
    code: 'eyeGroundCount',
    value: ''
  },

  {
    label: 'ACR检查人数',
    code: 'acrCount',
    value: ''
  },

  {
    label: '震动阈值检查人数',
    code: 'vibrationThresholdCount',
    value: ''
  },

  {
    label: '动脉硬化检查阳性率',
    code: 'arteriosclerosisRate',
    value: ''
  },

  {
    label: '眼底相机检查阳性率',
    code: 'eyeGroundRate',
    value: ''
  },
  {
    label: 'ACR检查阳性率',
    code: 'acrRate',
    value: ''
  },
  {
    label: '震动阈值检查阳性率',
    code: 'vibrationThresholdRate',
    value: ''
  }
]

export const populationCompositionEnum = [
  {
    label: '35岁以下',
    code: 'youngCount',
    value: ''
  },
  {
    label: '35-59岁',
    code: 'normalCount',
    value: ''
  },
  {
    label: '60-64岁',
    code: 'oldCount',
    value: ''
  },
  {
    label: '65岁以上',
    code: 'soOldCount',
    value: ''
  },
  {
    label: '男性',
    code: 'maleCount',
    value: ''
  },
  {
    label: '女性',
    code: 'femaleCount',
    value: ''
  }
]
