<template>
  <div class="receptionWorkbench">
    <el-card class="receptionWorkbench-disease">
      <DiseaseCategory ref="diseaseCategory" :query-params="queryParams" @change="handleDiseaseChange" />
    </el-card>

    <el-card class="receptionWorkbench-search">
      <el-form :model="queryParams" label-width="110px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="接诊时间：">
              <el-date-picker
                v-model="queryParams.timeRange"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="接诊状态：">
              <el-select v-model="queryParams.receptionStatus" placeholder="请选择" style="width: 100%">
                <el-option label="接诊中" :value="1" />
                <el-option label="已完成" :value="5" />
                <el-option label="已取消" :value="9" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="接诊医生：">
              <el-input v-model="queryParams.receptionDoctorName" placeholder="请输入" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="姓名/身份证：">
              <el-input v-model="queryParams.keyword" placeholder="请输入" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col :span="4" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button
              icon="el-icon-search"
              type="primary"
              @click="
                () => {
                  handleSearch()
                  $refs.diseaseCategory.getDiseaseList()
                }
              "
            >
              查询
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="receptionWorkbench-table">
      <div class="operation-container">
        <el-button icon="el-icon-plus" type="primary" @click="handleAdd">新增接诊</el-button>
      </div>
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 100px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #disease="{ row }">
          <ChronicDiseaseType :record="row" />
        </template>

        <template #sex="{ row }">
          <span>{{ genderTransform(row.sex) }}</span>
        </template>

        <template #auditStatus="{ row }">
          <span :style="row.auditStatus === 5 ? 'color: #67c23a' : row.auditStatus === 9 ? 'color: #f56c6c' : ''">
            {{ row.auditStatus === 5 ? '✔' : row.auditStatus === 9 ? '✘' : '' }}
          </span>
        </template>

        <template #idCard="{ row }">
          <EncryptionStr :cipher-text="row.idCard" :replace="row.idCardReplace" />
        </template>

        <template #phone="{ row }">
          <EncryptionStr :cipher-text="row.phone" :replace="row.phoneReplace" />
        </template>

        <template #receptionStatus="{ row }">
          <el-tag v-if="row.receptionStatus === 1" type="primary">接诊中</el-tag>
          <el-tag v-else-if="row.receptionStatus === 5" type="success">已完成</el-tag>
          <el-tag v-else-if="row.receptionStatus === 9" type="danger">已取消</el-tag>
        </template>

        <template #operation="{ row }">
          <OperateMenu :row="row">
            <template #view>
              <el-button v-if="row.receptionStatus !== 1" type="text" size="small" @click="handleView(row)">
                详情</el-button>
            </template>
            <template v-if="row.receptionStatus === 1" #continue>
              <el-button type="text" size="small" @click="handleContinue(row)">继续接诊</el-button>
            </template>
            <template v-if="row.receptionStatus === 1" #cancel>
              <el-button type="text" size="small" @click="handleCancel(row)">取消接诊</el-button>
            </template>
            <template v-if="row.receptionStatus === 1" #finish>
              <el-button type="text" size="small" @click="handleFinish(row)">完成接诊</el-button>
            </template>
          </OperateMenu>
        </template>
      </base-table>
    </el-card>

    <ProDialog ref="addDialog" title="新增接诊" :visible.sync="addDialogVisible" width="960px">
      <PatientList ref="patientList" />
      <span slot="footer">
        <el-button @click="addDialogVisible = false">关闭</el-button>
        <el-button type="primary" :loading="buttonLoading" @click="handleAddConfirm">确定</el-button>
      </span>
    </ProDialog>

    <CancelModal ref="cancelDialog" @confirm="handleCancelConfirm" />
  </div>
</template>

<script>
import {
  getReceptionWorkbenchList,
  addReceptionWorkbench,
  cancelReceptionWorkbench,
  completeReceptionWorkbench
} from '@/api/receptionWorkbench'
import DiseaseCategory from '@/components/diseaseCategory/index.vue'
import tableMixin from '@/mixins/tableMixin'
import BaseTable from '@/components/BaseTable/index.vue'
import ProDialog from '@/components/ProDialog/index.vue'
import PatientList from './component/patientList.vue'
import OperateMenu from '@/components/operateMenu/index.vue'
import ChronicDiseaseType from '@/components/chronicDiseaseType/index.vue'
import CancelModal from './component/cancelModal.vue'
import EncryptionStr from '@/components/encryptionStr/index.vue'
import { genderTransform } from '@/utils/cspUtils'

export default {
  name: 'ReceptionWorkbench',
  components: {
    DiseaseCategory,
    BaseTable,
    ProDialog,
    PatientList,
    OperateMenu,
    ChronicDiseaseType,
    CancelModal,
    EncryptionStr
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        disease: '',
        timeRange: [],
        receptionStatus: '',
        receptionDoctorName: '',
        keyword: ''
      },
      buttonLoading: false,
      columns: [
        // { type: 'index', label: '序号' },
        { prop: 'patientName', label: '姓名', width: 120 },
        { prop: 'disease', label: '慢病病种', width: 150, slot: 'disease' },
        { prop: 'sex', label: '性别', slot: 'sex' },
        { prop: 'auditStatus', label: '质控状态', width: 120, slot: 'auditStatus' },
        { prop: 'age', label: '年龄' },
        { prop: 'idCardReplace', label: '身份证号', width: 190, slot: 'idCard' },
        { prop: 'phoneReplace', label: '手机号码', width: 140, slot: 'phone' },
        { prop: 'address', label: '地址', width: 180, showOverflowTooltip: true },
        { prop: 'receptionDoctorName', label: '接诊医生' },
        { prop: 'createTime', label: '创建时间', width: 155 },
        { prop: 'receptionDate', label: '接诊日期', width: 120, showOverflowTooltip: true },
        { prop: 'cancelReason', label: '取消接诊原因', width: 160, showOverflowTooltip: true },
        { prop: 'receptionStatus', label: '状态', slot: 'receptionStatus' },
        {
          prop: 'operation',
          label: '操作',
          slot: 'operation',
          width: 160,
          fixed: window.innerWidth < 1600 ? 'right' : false
        }
      ]
    }
  },
  methods: {
    genderTransform,
    async getTableList(params) {
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }

      return await getReceptionWorkbenchList(queryParams)
    },

    handleDiseaseChange(value) {
      this.queryParams.disease = value.diseaseCode === 'all' ? '' : value.diseaseCode
      this.handleSearch()
    },

    handleReset() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20
      }
      this.$refs.diseaseCategory.activeCode = 'all'
      this.fetchData()
    },

    handleAdd() {
      this.addDialogVisible = true
    },

    async handleAddConfirm() {
      try {
        this.buttonLoading = true
        const id = this.$refs.patientList.selectedRow
        if (!id) {
          this.$message.warning('请选择患者')
          return
        }
        const res = await addReceptionWorkbench({
          patientId: id
        })
        if (res.code === 200) {
          this.$message.success('添加成功')
          this.handleReset()
          this.addDialogVisible = false
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.buttonLoading = false
      }
    },

    handleCancel(row) {
      this.record = row
      this.$refs.cancelDialog.form.cancelReason = ''
      this.$refs.cancelDialog.visible = true
    },

    async handleCancelConfirm(reason) {
      const res = await cancelReceptionWorkbench({
        id: this.record.id,
        cancelReason: reason
      })
      if (res.code === 200) {
        this.$message.success('取消成功')
        this.handleReset()
        this.$refs.cancelDialog.visible = false
      }
    },

    handleContinue(row) {
      this.$store.commit('receptionWorkbench/SET_RECEPTION_WORKBENCH_DATA', {})
      this.$router.push({
        path: '/receptionCenter/patientReception',
        query: {
          id: row.id
        }
      })
    },

    async handleFinish(row) {
      const res = await completeReceptionWorkbench({
        id: row.id
      })
      if (res.code === 200) {
        this.$message.success('接诊完成')
        this.handleReset()
      }
    },

    handleView(row) {
      this.$store.commit('receptionWorkbench/SET_RECEPTION_WORKBENCH_DATA', {})
      this.$router.push({
        path: '/receptionCenter/patientReception',
        query: {
          id: row.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.receptionWorkbench {
  display: flex;
  flex-direction: column;
  height: 100%;
  .receptionWorkbench-disease {
    margin: 16px;
  }
  .receptionWorkbench-search {
    margin: 0 16px;
    height: 77px;
  }
  .receptionWorkbench-table {
    flex: 1;
    margin: 16px 16px 0 16px;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
  .operation-container {
    display: flex;
    margin-bottom: 16px;
  }
}
</style>
