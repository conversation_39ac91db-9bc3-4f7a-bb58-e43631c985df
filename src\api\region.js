import request from '@/utils/request'
import { getUserId } from '@/utils/auth'

// 30.13.4 预约记录-专家 列表
export function getlLstRoomMembersApi(roomId) {
  return request({
    url: `/cspapi/backend/remoteUsers/listRoomMembers/star?roomId=${roomId}`,
    method: 'get'
  })
}
// 远程专家的评价-满意度
export function remoteUsersStarApi(data) {
  return request({
    url: `/cspapi/backend/remoteUsers/star`,
    method: 'put',
    data
  })
}

export function getRegionData(data) {
  return request({
    url: '/cspapi/backend/healthRecord/patientDiagnosisRecord/page',
    method: 'get',
    params: data
  })
}

// 根据预约记录或者就诊记录查询房间信息
export function getByRegIdApi(data) {
  return request({
    url: '/cspapi/backend/remoteConsultation/getByRegId',
    method: 'get',
    params: data
  })
}
// 获取专家数据
export function getshouInfoTableDataApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/info/listByRemoter',
    method: 'get',
    params: data
  })
}

// 30.13.2-1 某房间的与会人员
export function getAllOnLineDoctorApi(data) {
  return request({
    url: '/cspapi/backend/remoteUsers/listDoctorByRoomId',
    method: 'get',
    params: data
  })
}
// 30.13.3 修改医生的会议状态
export function userJoinRemoteApi(data) {
  return request({
    url: '/cspapi/backend/remoteUsers',
    method: 'put',
    data
  })
}

// 拉人入会
export function remoteUsersApi(data) {
  return request({
    url: '/cspapi/backend/remoteUsers',
    method: 'post',
    data
  })
}
// 某医生入会/离会给其他人发通知/
export function putRemoteUsersApi(data) {
  return request({
    url: '/cspapi/backend/remoteUsers',
    method: 'put',
    data
  })
}

export function addDepartApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/depart',
    method: 'post',
    data
  })
}
export function uploadDepartApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/depart',
    method: 'put',
    data
  })
}
export function delDepartApi(id) {
  return request({
    url: `/cspapi/backend/remote/work/depart?id=${id}`,
    method: 'delete'
  })
}
// 批量新增
export function lotAddDepartApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/depart/batch',
    method: 'post',
    data
  })
}

// 新增 区域会诊-值班-科室
export function saveWorkInfoApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/info',
    method: 'post',
    data
  })
}
// 批量修改
export function lotUpdDepartApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/depart',
    method: 'put',
    data
  })
}
export function getDictionaryValApi() {
  return request({
    url: '/cspapi/backend/sys/department/hospital/list',
    method: 'get'
  })
}
// 查看-预约信息
export function getInfoDataApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/info',
    method: 'get',
    params: data
  })
}

// 审核-预约信息
export function putInfoDataApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/info',
    method: 'put',
    data
  })
}
// 根据用户身份证或者姓名查询用户信息
export function getHuanzheListApi(data) {
  return request({
    url: `/cspapi/backend/user/listPatientByIdCardOrName`,
    method: 'get',
    params: data
  })
}
// 根据moduleCode查询字典值list
export function getListByCodeApi(code) {
  return request({
    url: `/cspapi/backend/sys/dictionary/listByModuleCode/${code}`,
    method: 'get'
  })
}
export function getSectionOfficeDataApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/depart/list',
    method: 'get',
    params: data
  })
}
// 值班table总览

export function getTableDataApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/plan/table',
    method: 'get',
    params: data
  })
}

export function gettimeRangeApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/plan/listDateRangeByDepartId',
    method: 'get',
    params: data
  })
}
// 某个医生的值班总览(计划)
export function getDoctorInfoByIdPai(data) {
  return request({
    url: '/cspapi/backend/remote/work/plan/getByDoctorId',
    method: 'get',
    params: data
  })
}
// 某个医生的值班详情(AM_PM)
export function getDoctorPlanInfoByIdPai(data) {
  return request({
    url: '/cspapi/backend/remote/work/detail/getByDoctorId',
    method: 'get',
    params: data
  })
}
// 删除-某人某天某AMPM下的值班详情
export function deldoctorRegionApi(data) {
  return request({
    url: `/cspapi/backend/remote/work/detail/deleteByModel`,
    method: 'delete',
    params: data
  })
}

// 新增-医生值班
export function saveDoctorApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/plan',
    method: 'post',
    data
  })
}

// 根据科室查询是否有排班信息
export function isPaiBanApi(data) {
  return request({
    // url: '/cspapi/backend/remote/work/plan/listByDepartHospitalCode',
    url: '/cspapi/backend/remote/work/info/listByModel',
    method: 'get',
    params: data
  })
}

// 科室code查询是否有排班信息
export function getDoctorDataApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/detail/listDoctorByDayAndTimeRange',
    method: 'get',
    params: data
  })
}

// ====== LiSuwan add start ======
// 区域会诊-列表-村医端
export function getListByCunyiApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/info/listByCunyi',
    method: 'get',
    params: data
  })
}

export function putDoctorSignApi(data) {
  return request({
    url: '/cspapi/backend/remoteUsers/sign',
    method: 'put',
    data
  })
}
// 区域会诊-村医端-会诊机构-select框
export function getStationsByCunyiApi() {
  return request({
    url: '/cspapi/backend/remote/work/info/select/stationsByCunyi',
    method: 'get',
    params: { doctorId: getUserId() }
  })
}

// 区域会诊-村医端-会诊专家-select框
export function getSelectRemotersByCunyiApi() {
  return request({
    url: '/cspapi/backend/remote/work/info/select/remotersByCunyi',
    method: 'get',
    params: { doctorId: getUserId() }
  })
}

// 村医-柱状图统计
export function getWorkInfoStatisticApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/info/statistic',
    method: 'get',
    params: { month: 12, doctorId: getUserId() }
  })
}

// 管理员 列表 (按照区域)
export function getWorkInfoListByManagerAreaApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/info/listByManager/area',
    method: 'get',
    params: data
  })
}
// 管理员 列表 (按照专家)
export function getWorkInfoListByManagerApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/info/listByManager',
    method: 'get',
    params: data
  })
}

// 管理员 - 今日统计
export function getWorkInfoStatisticByDayApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/info/statisticByDay/manager',
    method: 'get',
    params: data
  })
}

// 今日统计-远程专家
export function getWorkInfoStatisticByDayRemoterApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/info/statisticByDay/remoter',
    method: 'get',
    params: data
  })
}

// 管理员 - 柱状图统计
export function getRemoteWorkInfoStatisticApi() {
  return request({
    url: '/cspapi/backend/remote/work/info/statistic',
    method: 'get',
    params: { month: 12 }
  })
}

// 管理员 - 专家端
export function getRemoteWorkInfoListByRemoterApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/info/listByRemoter',
    method: 'get',
    params: data
  })
}

// 管理员 列表(区域+专家)-详情
export function getRemoteWorkInfoListDetailListApi(data) {
  return request({
    url: '/cspapi/backend/remote/work/info/list/detailList',
    method: 'get',
    params: data
  })
}

// 专家端5个Card
export function getRemoteWorkInfoStatisticCardApi() {
  return request({
    url: '/cspapi/backend/remote/work/info/statistic/card',
    method: 'get'
  })
}

// ====== LiSuwan add end ======
