<template>
  <div class="follow-up-statistics">
    <el-card class="follow-up-statistics-card">
      <SearchForm
        ref="searchForm"
        :query-params="queryParams"
        :query-criteria="['timeRange', 'departCode']"
        @search="handleSearch"
        @reset="handleReset"
      />
    </el-card>

    <el-card class="follow-up-statistics-table">
      <BaseTable
        ref="baseTable"
        :columns="columns"
        :loading="loading"
        :table-data="tableData"
        :show-pagination="showPagination"
      />
    </el-card>
  </div>
</template>

<script>
import { getFollowUpStatistics } from '@/api/followUp'
import SearchForm from '@/views/statisticalReport/component/searchForm.vue'
import { localCache } from '@/utils/cache'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'

export default {
  name: 'FollowUpStatistics',
  components: {
    SearchForm,
    BaseTable
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        timeRange: [],
        departCode: localCache.getCache('userInfo').departCode || ''
      },
      columns: [
        { label: '医生姓名', prop: 'doctorName' },
        { label: '机构', prop: 'departName' },
        { label: '随访总次数', prop: 'count' },
        { label: '计划内随访次数', prop: 'inPlanCount' },
        { label: '计划外随访次数', prop: 'outPlanCount' },
        { label: '诊室随访次数', prop: 'hospitalCount' },
        { label: '电话随访次数', prop: 'phoneCount' },
        { label: '上门随访次数', prop: 'doorCount' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }
      return await getFollowUpStatistics(queryParams)
    },

    handleReset() {
      this.queryParams.timeRange = []
      this.queryParams.departCode = localCache.getCache('userInfo').departCode || ''
      this.fetchData()
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/form-overrides.scss';

.follow-up-statistics {
  padding: 16px;
  .follow-up-statistics-search {
    margin-bottom: 16px;
  }
}
</style>
