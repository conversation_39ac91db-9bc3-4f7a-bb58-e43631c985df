<!-- 眼底检查 -->
<template>
  <div class="fundus-examination">
    <div v-if="showDeviceCode($route.path)" class="device-barcode">
      <Barcode :code="`${getDeviceCode($route.path, getSourceData())}`" />
    </div>

    <flag-component title="眼底所见病灶与疾病" />
    <el-table :data="table" border style="width: 100%; margin-top: 30px">
      <el-table-column prop="diseaseItem" label="疾病检查项" align="center" />
      <el-table-column label="左眼" align="center">
        <el-table-column label="数量（个）" align="center">
          <template slot-scope="scope">
            <custom-input-number v-model="scope.row.leftCount" size="mini" />
          </template>
        </el-table-column>
        <el-table-column label="总面积（mm2）" align="center">
          <template slot-scope="scope">
            <custom-input-number v-model="scope.row.leftArea" size="mini" />
          </template>
        </el-table-column>
        <el-table-column label="结果" align="center">
          <template slot-scope="scope">
            <el-select v-model="scope.row.leftValue" size="mini">
              <el-option label="阴性" :value="0" />
              <el-option label="阳性" :value="1" />
            </el-select>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="右眼" align="center">
        <el-table-column label="数量（个）" align="center">
          <template slot-scope="scope">
            <custom-input-number v-model="scope.row.rightCount" size="mini" />
          </template>
        </el-table-column>
        <el-table-column label="总面积（mm2）" align="center">
          <template slot-scope="scope">
            <custom-input-number v-model="scope.row.rightArea" size="mini" />
          </template>
        </el-table-column>
        <el-table-column label="结果" align="center">
          <template slot-scope="scope">
            <el-select v-model="scope.row.rightValue" size="mini" style="width: 100%">
              <el-option label="阴性" :value="0" />
              <el-option label="阳性" :value="1" />
            </el-select>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>

    <flag-component title="眼底检查图像" style="margin: 16px 0" />
    <div class="fundus-image">
      <div v-if="fundusImage.left" class="fundus-image-item">
        <img :src="fundusImage.left" alt="眼底检查图像">
        <span v-if="fundusImage.left">左眼</span>
      </div>
      <div v-if="fundusImage.right" class="fundus-image-item">
        <img :src="fundusImage.right" alt="眼底检查图像">
        <span v-if="fundusImage.right">右眼</span>
      </div>
    </div>

    <el-form ref="formRef" :model="form" label-width="130px" style="margin-top: 30px">
      <flag-component title="眼底综合评估结论" style="margin-bottom: 10px" />
      <el-form-item label="左眼：">
        <el-input v-model="form.diseaseLeft" type="textarea" :rows="3" style="width: 50%" />
      </el-form-item>
      <el-form-item label="右眼：">
        <el-input v-model="form.diseaseRight" type="textarea" :rows="3" style="width: 50%" />
      </el-form-item>
      <el-form-item label="报告图片：">
        <custom-upload v-model="form.eyeAttachmentUrl" />
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { cloneDeep } from 'lodash'
import { mapDataToTable, getDeviceCode, showDeviceCode, splitUrl } from '@/utils/cspUtils'
import FlagComponent from '@/components/flagComponent/index.vue'
import CustomUpload from '@/components/customUpload/index.vue'
import Barcode from '@/components/barcode/barcode.vue'

export default {
  name: 'FundusExamination',
  components: {
    FlagComponent,
    CustomUpload,
    Barcode
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        diseaseLeft: '',
        diseaseRight: '',
        eyeAttachmentUrl: ''
      },
      fundusImage: {
        left: '',
        right: ''
      },
      table: [
        {
          diseaseItem: '微血管瘤',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightMaCount: 'rightCount',
          rightMaArea: 'rightArea',
          rightMaValue: 'rightValue',
          leftMaCount: 'leftCount',
          leftMaArea: 'leftArea',
          leftMaValue: 'leftValue'
        },
        {
          diseaseItem: '棉絮斑',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightCwCount: 'rightCount',
          rightCwArea: 'rightArea',
          rightCwValue: 'rightValue',
          leftCwCount: 'leftCount',
          leftCwArea: 'leftArea',
          leftCwValue: 'leftValue'
        },
        {
          diseaseItem: '硬性渗出',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightHeCount: 'rightCount',
          rightHeArea: 'rightArea',
          rightHeValue: 'rightValue',
          leftHeCount: 'leftCount',
          leftHeArea: 'leftArea',
          leftHeValue: 'leftValue'
        },
        {
          diseaseItem: '视网膜内出血',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightHmaCount: 'rightCount',
          rightHmaArea: 'rightArea',
          rightHmaValue: 'rightValue',
          leftHmaCount: 'leftCount',
          leftHmaArea: 'leftArea',
          leftHmaValue: 'leftValue'
        },
        {
          diseaseItem: '视网膜内微血管异常',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightIrmaCount: 'rightCount',
          rightIrmaArea: 'rightArea',
          rightIrmaValue: 'rightValue',
          leftIrmaCount: 'leftCount',
          leftIrmaArea: 'leftArea',
          leftIrmaValue: 'leftValue'
        },
        {
          diseaseItem: '新生血管或增殖膜',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightNvfpCount: 'rightCount',
          rightNvfpArea: 'rightArea',
          rightNvfpValue: 'rightValue',
          leftNvfpCount: 'leftCount',
          leftNvfpArea: 'leftArea',
          leftNvfpValue: 'leftValue'
        },
        {
          diseaseItem: '视网膜前出血或玻璃体出血',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightPrhvhCount: 'rightCount',
          rightPrhvhArea: 'rightArea',
          rightPrhvhValue: 'rightValue',
          leftPrhvhCount: 'leftCount',
          leftPrhvhArea: 'leftArea',
          leftPrhvhValue: 'leftValue'
        },
        {
          diseaseItem: '激光斑',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightLaserCount: 'rightCount',
          rightLaserArea: 'rightArea',
          rightLaserValue: 'rightValue',
          leftLaserCount: 'leftCount',
          leftLaserArea: 'leftArea',
          leftLaserValue: 'leftValue'
        },
        {
          diseaseItem: '豹纹状眼底',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightTafCount: 'rightCount',
          rightTafArea: 'rightArea',
          rightTafValue: 'rightValue',
          leftTafCount: 'leftCount',
          leftTafArea: 'leftArea',
          leftTafValue: 'leftValue'
        },
        {
          diseaseItem: '近视弧形斑',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightMcCount: 'rightCount',
          rightMcArea: 'rightArea',
          rightMcValue: 'rightValue',
          leftMcCount: 'leftCount',
          leftMcArea: 'leftArea',
          leftMcValue: 'leftValue'
        },
        {
          diseaseItem: '玻璃膜疣',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightDrusCount: 'rightCount',
          rightDrusArea: 'rightArea',
          rightDrusValue: 'rightValue',
          leftDrusCount: 'leftCount',
          leftDrusArea: 'leftArea',
          leftDrusValue: 'leftValue'
        },
        {
          diseaseItem: '动静脉交叉压迹',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightAvnCount: 'rightCount',
          rightAvnArea: 'rightArea',
          rightAvnValue: 'rightValue',
          leftAvnCount: 'leftCount',
          leftAvnArea: 'leftArea',
          leftAvnValue: 'leftValue'
        },
        {
          diseaseItem: '动静脉比异常',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightAarCount: 'rightCount',
          rightAarArea: 'rightArea',
          rightAarValue: 'rightValue',
          leftAarCount: 'leftCount',
          leftAarArea: 'leftArea',
          leftAarValue: 'leftValue'
        },
        {
          diseaseItem: '银丝或铜丝征',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightScwCount: 'rightCount',
          rightScwArea: 'rightArea',
          rightScwValue: 'rightValue',
          leftScwCount: 'leftCount',
          leftScwArea: 'leftArea',
          leftScwValue: 'leftValue'
        }
      ]
    }
  },
  methods: {
    showDeviceCode,
    getDeviceCode,
    getSourceData() {
      return this.$route.path === '/receptionCenter/patientReception'
        ? this.$store.getters.complicationsScreeningData
        : this.$store.state.patientExamination.patientExaminationData
    },
    initData(data) {
      this.form = {
        diseaseLeft: data.diseaseLeft,
        diseaseRight: data.diseaseRight,
        eyeAttachmentUrl: data.eyeAttachmentUrl,
        id: data.id
      }

      this.fundusImage = {
        left: data.imagesLeft ? splitUrl(data.imagesLeft) : '',
        right: data.imagesRight ? splitUrl(data.imagesRight) : ''
      }
      this.table = mapDataToTable(data, cloneDeep(this.table))
    },

    async handleSave() {
      const tableTemp = this.extractEyeFields(this.table)

      const result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          ...this.form,
          ...tableTemp,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }

      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    },

    extractEyeFields(tableData) {
      const result = {}

      tableData.forEach((row) => {
        // 遍历每一行数据，查找键值对应的字段
        Object.entries(row).forEach(([key, value]) => {
          // 如果key的值等于某个映射字段，则提取出对应的数据
          if (value && typeof value === 'string' && (value.startsWith('right') || value.startsWith('left'))) {
            // 获取映射后的真实数据值
            const actualValue = row[value]
            // 将该数据添加到结果对象中
            result[key] = actualValue
          }
        })
      })

      return result
    }
  }
}
</script>

<style scoped lang="scss">
.device-barcode {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.fundus-image {
  display: flex;
  align-items: center;
  margin-left: 30px;
  gap: 10px;
  .fundus-image-item {
    width: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    img {
      width: 100%;
    }
    span {
      font-size: 14px;
      color: #333;
    }
  }
}
</style>
