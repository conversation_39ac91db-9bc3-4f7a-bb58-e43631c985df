<!-- 并发症筛查 -->
<template>
  <div v-loading="$store.state.receptionWorkbench.loading" class="complications-screening">
    <questionnaire-tags :questionnaire-list="questionnaireList" @click="handleQuestionnaireNameClick" />
    <el-tabs
      v-show="questionnaireId === 'tnb'"
      v-model="activeTab"
      type="card"
      style="padding: 16px"
      addable
      :closable="showTabListTnb.length > 1"
      @edit="handleTabsEdit"
    >
      <el-tab-pane v-for="item in showTabListTnb" :key="item.value" :label="item.label" :name="item.value">
        <el-card>
          <div class="complications-screening-content">
            <component
              :is="item.component"
              v-if="item.component"
              :ref="`${item.component}Ref`"
              :item-temp="item"
              :active-tab="activeTab"
            />
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <el-tabs
      v-show="questionnaireId === 'gxy'"
      v-model="activeTab"
      type="card"
      style="padding: 16px"
      addable
      :closable="showTabListGxy.length > 1"
      @edit="handleTabsEdit"
    >
      <el-tab-pane v-for="item in showTabListGxy" :key="item.value" :label="item.label" :name="item.value">
        <el-card>
          <div class="complications-screening-content">
            <component
              :is="item.component"
              v-if="item.component"
              :ref="`${item.component}Ref`"
              :item-temp="item"
              :active-tab="activeTab"
            />
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <el-tabs
      v-show="questionnaireId === 'fangchan'"
      v-model="activeTab"
      type="card"
      style="padding: 16px"
      addable
      :closable="showTabListFc.length > 1"
      @edit="handleTabsEdit"
    >
      <el-tab-pane v-for="item in showTabListFc" :key="item.value" :label="item.label" :name="item.value">
        <el-card>
          <div class="complications-screening-content">
            <component
              :is="item.component"
              v-if="item.component"
              :ref="`${item.component}Ref`"
              :item-temp="item"
              :active-tab="activeTab"
            />
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>

    <el-card v-show="questionnaireId === 'pgjg'" style="margin: 16px">
      <EvaluationResults ref="EvaluationResultRef" :questionnaire-list="questionnaireList" />
    </el-card>
    <div>
      <ProDialog ref="proDialog" title="添加项目" :visible.sync="dialogVisible" width="500px" top="30vh">
        <div class="pro-dialog-content">
          <el-checkbox-group v-model="checkList">
            <el-col v-for="item in tabList" :key="item.value" :span="12" style="margin-bottom: 8px">
              <el-checkbox :label="item.value" :disabled="item.status === 2">
                {{ item.label }}
              </el-checkbox>
            </el-col>
          </el-checkbox-group>
        </div>
        <template #footer>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
          <el-button @click="dialogVisible = false">关闭</el-button>
        </template>
      </ProDialog>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import QuestionnaireTags from '@/components/questionnaireTags/index.vue'
import ProDialog from '@/components/ProDialog/index.vue'
import FingertipBloodGlucose from '../componentProject/complication/fingertipBloodGlucose.vue'
import PostprandialTwoSugar from '../componentProject/complication/postprandialTwoSugar.vue'
import FastingVenousBlood from '../componentProject/complication/fastingVenousBlood.vue'
import GlycosylatedHemoglobin from '../componentProject/complication/glycosylatedHemoglobin.vue'
import OGTT from '../componentProject/complication/OGTT.vue'
import Biochemistry from '../componentProject/complication/biochemistry.vue'
import CarotidUltrasound from '../componentProject/complication/carotidUltrasound.vue'
import Urinalysis from '../componentProject/complication/urinalysis.vue'
import Emg from '../componentProject/complication/emg.vue'
import FundusExamination from '../componentProject/complication/fundusExamination.vue'
import ACR from '../componentProject/complication/ACR.vue'
import VibrationThresholdCheck from '../componentProject/complication/vibrationThresholdCheck.vue'
import BloodPressure from '../componentProject/complication/bloodPressure.vue'
import ECG from '../componentProject/complication/ECG.vue'
import Arteriosclerosis from '../componentProject/complication/arteriosclerosis.vue'
import DynamicBlood from '../componentProject/complication/dynamicBlood.vue'
import Echocardiography from '../componentProject/complication/echocardiography.vue'
import BloodRoutine from '../componentProject/complication/bloodRoutine.vue'
import Echocardiogram from '../componentProject/complication/echocardiogram.vue'
import BNP from '../componentProject/complication/BNP.vue'
import EvaluationResults from '../componentProject/complication/evaluationResults.vue'
import PeripheralArtery from '../componentProject/complication/peripheralArtery.vue'
import { tabList } from './complicationsScreening.js'
import {
  getComplicationsScreeningList,
  getComplicationsScreeningProject,
  removeComplicationsScreeningItem,
  addComplicationsScreeningItem,
  saveComplicationsScreening
} from '@/api/receptionWorkbench'

export default {
  components: {
    ProDialog,
    QuestionnaireTags,
    FingertipBloodGlucose, // 指尖血糖
    PostprandialTwoSugar, // 餐后2h尿糖
    FastingVenousBlood, // 空腹静脉血糖
    GlycosylatedHemoglobin, // 糖化血红蛋白
    OGTT, // OGTT
    Biochemistry, // 生化
    CarotidUltrasound, // 颈动脉超声检查
    Emg, // 肌电图检查
    FundusExamination, // 眼底检查
    ACR, // ACR检查
    Urinalysis, // 尿常规
    VibrationThresholdCheck, // 震动阈值检查
    BloodPressure, // 24小时动态血压
    ECG, // 心电图检查
    Arteriosclerosis, // 动脉硬化
    DynamicBlood, // 动态血压心电图
    Echocardiography, // 心脏彩超
    BloodRoutine, // 血常规
    Echocardiogram, // 超声心动图
    BNP, // BNP
    PeripheralArtery, // 外周动脉
    EvaluationResults // 评估结果
  },
  props: {
    historyId: {
      type: String,
      default: ''
    }
  },
  data() {
    const questionnaireList = cloneDeep(this.$store.state.receptionWorkbench.receptionWorkbenchData.diseaseList).filter(
      (item) => item.id !== 'COPD'
    )
    questionnaireList.push({
      id: 'pgjg',
      name: '评估结果'
    })

    return {
      activeTab: '',
      questionnaireList,
      tabList,
      dialogVisible: false,
      removeOrAddId: '', // 移除和添加项目用的id
      checkList: [],
      showTabListTnb: [],
      showTabListGxy: [],
      showTabListFc: [],
      questionnaireId: questionnaireList[0].id
    }
  },

  created() {
    this.getComplicationsScreeningListFn()
    this.getComplicationsScreeningProjectFn()
  },

  methods: {
    // 并发症项目列表
    getComplicationsScreeningListFn() {
      const rrId = this.historyId || this.$route.query.id
      getComplicationsScreeningList({ rrId }).then((response) => {
        if (response.code !== 200) return

        const data = response.data || []
        const updatedTabList = this.tabList.map((tab) => {
          const matchedItem = data.find((d) => d.code === tab.value)
          return {
            ...tab,
            status: matchedItem ? matchedItem.status : tab.status
          }
        })

        this.tabList = updatedTabList
        this.checkList = updatedTabList.filter((item) => item.status === 2).map((item) => item.value)
      })
    },

    // 并发症筛查项目查询
    getComplicationsScreeningProjectFn() {
      this.$store.commit('receptionWorkbench/SET_LOADING', true)
      getComplicationsScreeningProject({ rrId: this.historyId || this.$route.query.id })
        .then((response) => {
          if (response.code !== 200) return

          this.$store.commit('receptionWorkbench/SET_COMPLICATIONS_SCREENING_DATA', response.data)

          setTimeout(() => {
            this.projectItemsInit(response.data)
          }, 1000)

          this.showTabListTnb = (response.data.tnbItemList || []).map((item) => {
            const tab = this.tabList.find((it) => it.value === item.itemCode)
            return {
              ...tab,
              id: item.id
            }
          })

          this.showTabListGxy = (response.data.gxyItemList || []).map((item) => {
            const tab = this.tabList.find((it) => it.value === item.itemCode)
            return {
              ...tab,
              id: item.id
            }
          })

          this.showTabListFc = (response.data.fcItemList || []).map((item) => {
            const tab = this.tabList.find((it) => it.value === item.itemCode)
            return {
              ...tab,
              id: item.id
            }
          })

          this.removeOrAddId = response.data.id
        })
        .finally(() => {
          if (this.questionnaireId === 'tnb') {
            this.activeTab = this.showTabListTnb[0].value
          }
          if (this.questionnaireId === 'gxy') {
            this.activeTab = this.showTabListGxy[0].value
          }
          if (this.questionnaireId === 'fangchan') {
            this.activeTab = this.showTabListFc[0].value
          }
        })
    },

    // 项目初始化赋值
    projectItemsInit(response) {
      try {
        const tabLists = [this.showTabListTnb, this.showTabListGxy, this.showTabListFc]
        const returnData = [
          ...(response.tnbItemList || []),
          ...(response.gxyItemList || []),
          ...(response.fcItemList || [])
        ]
        for (const list of tabLists) {
          list.forEach(
            (item) => {
              const { data } = returnData.find((it) => it.itemCode === item.value)
              if (data) {
                this.$refs[`${item.component}Ref`][0].initData(data)
              } else {
                this.$refs[`${item.component}Ref`][0].clearValidate &&
                  this.$refs[`${item.component}Ref`][0].clearValidate()
              }
            }
            // this.$refs[`${item.component}Ref`][0].initData(returnData.find(it => it.itemCode === item.value).data || {})
          )
        }
        this.$refs.EvaluationResultRef.initData(response)
      } catch (error) {
        console.log('error', error)
      } finally {
        this.$store.commit('receptionWorkbench/SET_LOADING', false)
      }
    },

    async handleTabsEdit(value, action) {
      const projectItem = this.tabList.find((it) => it.value === value)
      if (action === 'remove') {
        try {
          await this.$confirm(`确定移除 ${projectItem.label} 项目吗？`, '操作确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'error',
            iconClass: 'el-icon-delete-solid',
            customClass: 'remove-confirm',
            closeOnClickModal: false,
            confirmButtonClass: 'bgred'
          })
          const res = await removeComplicationsScreeningItem({
            csId: this.removeOrAddId,
            diseaseCode: this.questionnaireId,
            itemCodeList: [value]
          })
          if (res.code === 200) {
            this.$message.success('移除成功')
            this.getComplicationsScreeningListFn()
            this.getComplicationsScreeningProjectFn()
          }
        } catch (error) {
          this.$message.info('已取消移除')
        }
      }

      if (action === 'add') {
        this.dialogVisible = true
      }
    },

    handleConfirm() {
      addComplicationsScreeningItem({
        csId: this.removeOrAddId,
        diseaseCode: this.questionnaireId,
        itemCodeList: this.checkList
      }).then((res) => {
        if (res.code === 200) {
          this.$message.success('添加成功')
          this.getComplicationsScreeningListFn()
          this.getComplicationsScreeningProjectFn()
          this.dialogVisible = false
        }
      })
    },

    handleQuestionnaireNameClick(item) {
      this.questionnaireId = item.id
      if (this.questionnaireId === 'tnb') {
        this.activeTab = this.showTabListTnb[0].value
      }
      if (this.questionnaireId === 'gxy') {
        this.activeTab = this.showTabListGxy[0].value
      }
      if (this.questionnaireId === 'fangchan') {
        this.activeTab = this.showTabListFc[0].value
      }
    },

    async handleComplicationsScreeningSave(type) {
      const rrId = this.$route.query.id
      const disease = this.$store.state.receptionWorkbench.receptionWorkbenchData.diseaseList.map((it) => it.id).join(',')
      const id =
        (this.$store.getters.complicationsScreeningData && this.$store.getters.complicationsScreeningData.id) || ''
      let status =
        (this.$store.getters.complicationsScreeningData && this.$store.getters.complicationsScreeningData.status) || 1

      if (type === 'skip') {
        status = 9
        await saveComplicationsScreening({ rrId, id, status, disease })
        this.getComplicationsScreeningListFn()
        this.getComplicationsScreeningProjectFn()
        return status
      }

      // 重新编辑
      if (type === 'edit') {
        status = 1
        await saveComplicationsScreening({ rrId, id, status, disease })
        this.getComplicationsScreeningListFn()
        this.getComplicationsScreeningProjectFn()
        return status
      }

      if (status === 9) {
        return status
      }

      const tabLists = [this.showTabListTnb, this.showTabListGxy, this.showTabListFc]
      const allResults = []

      for (const list of tabLists) {
        const resultPromises = list.map((item) => {
          const ref = this.$refs[`${item.component}Ref`]
          return ref && ref[0] && ref[0].handleSave && ref[0].handleSave()
        })
        // eslint-disable-next-line no-await-in-loop
        const results = await Promise.all(resultPromises)
        allResults.push(...results)
      }

      const evaluationResult = await this.$refs.EvaluationResultRef.handleSave()

      // ==== 校验必填项 ====
      const warnings = []

      for (const result of allResults) {
        if (!result.success) {
          warnings.push(`${result.name}存在必填未填项！`)
        }
      }

      if (!evaluationResult.success) {
        warnings.push(`评估结果存在必填未填项！`)
      }

      // ==== 警告提示 ====
      if (warnings.length) {
        for (const msg of warnings) {
          this.$message.warning(msg)
          // eslint-disable-next-line no-await-in-loop
          await new Promise((resolve) => setTimeout(resolve, 500))
        }
      }

      status = warnings.length ? 1 : 5

      // ==== 参数准备 ====
      const itemDetailList = allResults.map((item) => ({
        ...item.data,
        name: item.name
      }))

      const params = {
        id,
        rrId,
        status,
        itemDetailList,
        disease: this.$store.state.receptionWorkbench.receptionWorkbenchData.diseaseList.map((it) => it.id).join(','),
        ...evaluationResult.data
      }

      const res = await saveComplicationsScreening(params)

      if (res.code === 200) {
        this.$message.success('保存成功')

        this.getComplicationsScreeningListFn()
        this.getComplicationsScreeningProjectFn()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.complications-screening {
  ::v-deep .el-tabs__new-tab {
    width: 80px;
    height: 30px;
    line-height: 30px;
  }
  ::v-deep .el-tabs__new-tab:after {
    content: '添加项目';
  }
  ::v-deep .el-tabs__new-tab:hover {
    color: #fff;
  }
  margin-top: 16px;
  .complications-screening-content {
    padding: 0 16px;
  }
}
</style>
