<template>
  <div class="tree-select-wrapper">
    <el-select
      ref="selectTree"
      v-model="selectedLabel"
      :style="{ width: width }"
      :placeholder="placeholder"
      :disabled="disabled"
      :clearable="clearable"
      @clear="handleClear"
    >
      <el-option :label="selectedLabel" :value="selectedValue" style="display: none" />
      <div :style="{ padding: dropdownPadding }">
        <!-- 搜索框 -->
        <el-input
          v-if="filterable"
          v-model="filterText"
          :placeholder="filterPlaceholder"
          style="margin-bottom: 12px"
          @input="handleFilterChange"
        >
          <i slot="suffix" class="el-icon-search el-input__icon" />
        </el-input>

        <!-- 树形结构 -->
        <el-tree
          ref="treeRef"
          class="filter-tree"
          :data="data"
          :props="treeProps"
          :default-expand-all="defaultExpandAll"
          :expand-on-click-node="expandOnClickNode"
          :filter-node-method="filterNode"
          :node-key="nodeKey"
          :default-expanded-keys="defaultExpandedKeys"
          :default-checked-keys="defaultCheckedKeys"
          :show-checkbox="showCheckbox"
          :check-strictly="checkStrictly"
          @node-click="handleNodeClick"
          @check="handleCheck"
        />
      </div>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'TreeSelect',
  props: {
    // 基础属性
    value: {
      type: [String, Number, Array],
      default: ''
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    width: {
      type: String,
      default: '100%'
    },

    // 树形数据
    data: {
      type: Array,
      default: () => []
    },
    props: {
      type: Object,
      default: () => ({
        children: 'children',
        label: 'label',
        value: 'value'
      })
    },

    // 搜索相关
    filterable: {
      type: Boolean,
      default: true
    },
    filterPlaceholder: {
      type: String,
      default: '输入关键字进行过滤'
    },

    // 树形配置
    defaultExpandAll: {
      type: Boolean,
      default: true
    },
    expandOnClickNode: {
      type: Boolean,
      default: false
    },
    nodeKey: {
      type: String,
      default: 'id'
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    },
    defaultCheckedKeys: {
      type: Array,
      default: () => []
    },
    showCheckbox: {
      type: Boolean,
      default: false
    },
    checkStrictly: {
      type: Boolean,
      default: false
    },

    // 样式配置
    dropdownPadding: {
      type: String,
      default: '20px'
    },

    // 自定义过滤方法
    filterMethod: {
      type: Function,
      default: null
    }
  },

  data() {
    return {
      filterText: '',
      selectedLabel: '',
      selectedValue: ''
    }
  },

  computed: {
    treeProps() {
      return {
        children: this.props.children || 'children',
        label: this.props.label || 'label',
        value: this.props.value || 'value'
      }
    }
  },

  watch: {
    value: {
      handler(newVal) {
        this.initSelectedValue(newVal)
      },
      immediate: true
    },

    data: {
      handler() {
        this.initSelectedValue(this.value)
      },
      deep: true
    }
  },

  methods: {
    // 初始化选中值
    initSelectedValue(value) {
      if (!value || !this.data.length) {
        this.selectedLabel = ''
        this.selectedValue = ''
        return
      }

      const node = this.findNodeByValue(this.data, value)
      if (node) {
        this.selectedLabel = node[this.treeProps.label]
        this.selectedValue = node[this.treeProps.value] || node[this.nodeKey]
      }
    },

    // 递归查找节点
    findNodeByValue(nodes, value) {
      for (const node of nodes) {
        const nodeValue = node[this.treeProps.value] || node[this.nodeKey]
        if (nodeValue === value) {
          return node
        }

        if (node[this.treeProps.children] && node[this.treeProps.children].length) {
          const found = this.findNodeByValue(node[this.treeProps.children], value)
          if (found) return found
        }
      }
      return null
    },

    // 过滤节点
    filterNode(value, data) {
      if (this.filterMethod) {
        return this.filterMethod(value, data)
      }

      if (!value) return true
      const label = data[this.treeProps.label]
      return label && label.indexOf(value) !== -1
    },

    // 处理过滤文本变化
    handleFilterChange(value) {
      this.$refs.treeRef.filter(value)
    },

    // 处理节点点击
    handleNodeClick(data, node) {
      if (this.showCheckbox) return // 如果是多选模式，不处理单击事件

      const value = data[this.treeProps.value] || data[this.nodeKey]
      const label = data[this.treeProps.label]

      this.selectedLabel = label
      this.selectedValue = value

      this.$emit('input', value)
      this.$emit('change', value, data, node)
      this.$emit('node-click', data, node)

      // 关闭下拉框
      this.$refs.selectTree.blur()
    },

    // 处理复选框选择（多选模式）
    handleCheck(data, checked) {
      if (!this.showCheckbox) return

      const checkedNodes = this.$refs.treeRef.getCheckedNodes()
      const checkedKeys = this.$refs.treeRef.getCheckedKeys()

      this.$emit('input', checkedKeys)
      this.$emit('change', checkedKeys, checkedNodes)
      this.$emit('check', data, checked)
    },

    // 处理清空
    handleClear() {
      this.selectedLabel = ''
      this.selectedValue = ''
      this.filterText = ''

      if (this.showCheckbox) {
        this.$refs.treeRef.setCheckedKeys([])
        this.$emit('input', [])
        this.$emit('change', [], [])
      } else {
        this.$emit('input', '')
        this.$emit('change', '', null)
      }

      this.$emit('clear')
    },

    // 公共方法：获取选中的节点
    getCheckedNodes(leafOnly = false, includeHalfChecked = false) {
      if (!this.showCheckbox) return []
      return this.$refs.treeRef.getCheckedNodes(leafOnly, includeHalfChecked)
    },

    // 公共方法：获取选中的键值
    getCheckedKeys(leafOnly = false) {
      if (!this.showCheckbox) return []
      return this.$refs.treeRef.getCheckedKeys(leafOnly)
    },

    // 公共方法：设置选中的键值
    setCheckedKeys(keys) {
      if (!this.showCheckbox) return
      this.$refs.treeRef.setCheckedKeys(keys)
    },

    // 公共方法：设置选中的节点
    setCheckedNodes(nodes) {
      if (!this.showCheckbox) return
      this.$refs.treeRef.setCheckedNodes(nodes)
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-select-wrapper {
  width: 100%;
}

.filter-tree {
  max-height: 300px;
  overflow-y: auto;
}

// 自定义滚动条样式
.filter-tree::-webkit-scrollbar {
  width: 6px;
}

.filter-tree::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.filter-tree::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.filter-tree::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
