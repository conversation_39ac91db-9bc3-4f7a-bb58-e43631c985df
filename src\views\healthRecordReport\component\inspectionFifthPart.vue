<!-- 检验检查第五部分: BNP 颈动脉超声检查 动脉硬化 -->
<template>
  <div class="inspection-fifth-part">
    <div class="content">
      <div class="title">BNP</div>
      <div class="item">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="BNP"> {{ bnpData.bnp }} ng/mL </el-descriptions-item>
          <el-descriptions-item label="NT-proBNP"> {{ bnpData.ntProBnp }} ng/L </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div class="content">
      <div class="title">颈动脉超声检查</div>
      <div class="item">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="内-中膜厚度"> {{ carotidUltrasoundData.imt }} mm </el-descriptions-item>
          <el-descriptions-item label="检查印象"> {{ carotidUltrasoundData.cuImpression }} </el-descriptions-item>
          <el-descriptions-item label="检查所见"> {{ carotidUltrasoundData.cuFinding }} </el-descriptions-item>
          <el-descriptions-item label="检查结果">
            {{
              carotidUltrasoundData.cuResult &&
                carotidUltrasoundData.cuResult
                  .split(',')
                  .map(item => options.find(option => option.value === item).label)
                  .join('、')
            }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div class="content">
      <div class="title">动脉硬化</div>
      <div class="item">
        <el-table
          :data="arteriosclerosisData.pressureData"
          border
          style="width: 100%; margin-bottom: 10px"
          :span-method="pressureSpanMethod"
        >
          <el-table-column label="位置" prop="part" align="center" />
          <el-table-column label="" prop="type" align="center" />
          <el-table-column label="左" prop="left" align="center" />
          <el-table-column label="右" prop="right" align="center" />
        </el-table>

        <el-table :data="arteriosclerosisData.resultData" border style="width: 100%; margin-top: 10px">
          <el-table-column label="检查结果" prop="type" align="center" />
          <el-table-column label="左" prop="left" align="center" />
          <el-table-column label="右" prop="right" align="center" />
        </el-table>

        <el-descriptions :column="1" border style="margin-top: 10px">
          <el-descriptions-item label="检查所见">
            {{ arteriosclerosisData.form.arteriosclerosisFinding }}
          </el-descriptions-item>
          <el-descriptions-item label="医生意见">
            {{ arteriosclerosisData.form.arteriosclerosisSuggest }}
          </el-descriptions-item>

          <el-descriptions-item label="检查结果">
            {{
              arteriosclerosisData.form.arteriosclerosisResult === 1
                ? '正常'
                : arteriosclerosisData.form.arteriosclerosisResult === 2
                  ? '异常'
                  : ''
            }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </div>
</template>

<script>
import { mapDataToTable } from '@/utils/cspUtils'
import { cloneDeep } from 'lodash'

export default {
  name: 'InspectionFifthPart',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      options: [
        { label: '未见明显异常', value: '1' },
        { label: '斑块', value: '2' },
        { label: '狭窄', value: '3' },
        { label: '硬化', value: '4' },
        { label: '毛躁', value: '5' }
      ],
      pressureData: [
        { part: '上臂', type: 'SBP', left: '', right: '', rightLegSbp: 'right', leftLegSbp: 'left' },
        { part: '上臂', type: 'MBP', left: '', right: '', rightLegMbp: 'right', leftLegMbp: 'left' },
        { part: '上臂', type: 'DBP', left: '', right: '', rightLegDbp: 'right', leftLegDbp: 'left' },
        { part: '上臂', type: 'PP', left: '', right: '', rightLegPp: 'right', leftLegPp: 'left' },
        { part: '脚踝', type: 'SBP', left: '', right: '', rightFootSbp: 'right', leftFootSbp: 'left' },
        { part: '脚踝', type: 'MBP', left: '', right: '', rightFootMbp: 'right', leftFootMbp: 'left' },
        { part: '脚踝', type: 'DBP', left: '', right: '', rightFootDbp: 'right', leftFootDbp: 'left' },
        { part: '脚踝', type: 'PP', left: '', right: '', rightFootPp: 'right', leftFootPp: 'left' }
      ],
      resultData: [
        { type: '踝臂指数ABI', left: '', right: '', rightAbi: 'right', leftAbi: 'left' },
        { type: '臂踝指数BAI', left: '', right: '', rightBai: 'right', leftBai: 'left' },
        { type: '脉搏波传导速度PWV', left: '', right: '', rightPwv: 'right', leftPwv: 'left' }
      ]
    }
  },
  computed: {
    // BNP 数据
    bnpData() {
      return (
        (this.reportInfo.itemList.find((item) => item.itemCode === 'BNP') &&
          this.reportInfo.itemList.find((item) => item.itemCode === 'BNP').data) ||
        {}
      )
    },
    // 颈动脉超声检查数据
    carotidUltrasoundData() {
      return (
        (this.reportInfo.itemList.find((item) => item.itemCode === 'CAROTID_ULTRASOUND') &&
          this.reportInfo.itemList.find((item) => item.itemCode === 'CAROTID_ULTRASOUND').data) ||
        {}
      )
    },
    // 动脉硬化数据
    arteriosclerosisData() {
      const { data = {} } = this.reportInfo.itemList.find((item) => item.itemCode === 'ARTERIOSCLEROSIS') || {}

      return {
        pressureData: mapDataToTable(data, cloneDeep(this.pressureData)),
        resultData: mapDataToTable(data, cloneDeep(this.resultData)),
        form: {
          arteriosclerosisFinding: data.arteriosclerosisFinding,
          arteriosclerosisSuggest: data.arteriosclerosisSuggest,
          arteriosclerosisResult: data.arteriosclerosisResult
        }
      }
    }
  },
  methods: {
    pressureSpanMethod({ rowIndex, columnIndex }) {
      // 合并“位置”列（第一列）
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return { rowspan: 4, colspan: 1 } // 上臂占 4 行
        }
        if (rowIndex === 4) {
          return { rowspan: 4, colspan: 1 } // 脚踝占 4 行
        }
        return { rowspan: 0, colspan: 0 } // 其余不显示
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.inspection-fifth-part {
  padding: 10px;
  .content {
    margin-top: 8px;
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
    }
  }
}
</style>
