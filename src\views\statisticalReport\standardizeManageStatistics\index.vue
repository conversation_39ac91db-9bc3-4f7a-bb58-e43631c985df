<!-- 规范管理统计 -->
<template>
  <div class="standardize-manage-statistics">
    <el-card class="standardize-manage-statistics-search">
      <SearchForm ref="searchForm" :query-params="queryParams" @search="handleSearch" @reset="handleReset" />
    </el-card>
    <el-card>
      <BaseTable
        ref="baseTable"
        :columns="columns"
        :loading="loading"
        :table-data="tableData"
        :show-pagination="showPagination"
      />
    </el-card>
  </div>
</template>

<script>
import { localCache } from '@/utils/cache'
import { getStandardizeManageStatistics } from '@/api/statisticalReport'
import SearchForm from '../component/searchForm.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'

export default {
  name: 'StandardizeManageStatistics',
  components: {
    SearchForm,
    BaseTable
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        timeRange: [],
        disease: 'tnb',
        departCode: localCache.getCache('userInfo').departCode || ''
      },
      columns: [
        { prop: 'departName', label: '机构名称', width: 180 },
        { prop: 'manageCount', label: '管理总人数' },
        { prop: 'manageCompleteCount', label: '已完成管理人数' },
        { prop: 'manageAfootCount', label: '进行中管理人数' },
        { prop: 'motionPrescriptionCount', label: '运动处方人数' },
        { prop: 'medicalNutritionCount', label: '医学营养人数' },
        { prop: 'medicalMonitorCount', label: '药物监测人数' },
        { prop: 'sugarMonitorCount', label: '血糖监测人数' },
        { prop: 'pressureMonitorCount', label: '血压监测人数' },
        { prop: 'surgicalTreatmentCount', label: '手术治疗人数' },
        { prop: 'standardCount', label: '达标人数' },
        { prop: 'standardRate', label: '达标率（%）' },
        { prop: 'noStandardCount', label: '未达标人数' },
        { prop: 'noStandardRate', label: '未达标率（%）' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }
      return await getStandardizeManageStatistics(queryParams)
    },

    handleReset() {
      this.queryParams.timeRange = []
      this.queryParams.disease = 'tnb'
      this.queryParams.departCode = localCache.getCache('userInfo').departCode || ''
      this.fetchData()
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/form-overrides.scss';

.standardize-manage-statistics {
  padding: 16px;
  .standardize-manage-statistics-search {
    margin-bottom: 16px;
  }
}
</style>
