<template>
  <div class="screening-data-container">
    <div v-for="item in screeningData" :key="item.code" class="data-item">
      <template v-if="item.label">
        <div class="label">{{ item.label }}</div>
        <div class="value">{{ item.value }}</div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ScreeningData',
  props: {
    screeningData: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped>
.screening-data-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}

.data-item {
  box-sizing: border-box;
  width: 25%;
  padding: 10px;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 88px;
}

.label {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.value {
  font-size: 24px;
  font-weight: bold;
  color: #3a8ee6;
}
</style>
