<template>
  <div class="dataBoard_home">
    <div class="pageContent">
      <CspHome />
    </div>

    <!-- ====== 修改密码弹出 start====== -->
    <el-dialog
      :visible.sync="dialogVisible"
      width="850px"
      top="10vh"
      :close-on-click-modal="false"
      class="hs"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <span style="display: flex; flex-direction: column; align-items: center">
        <span style="display: flex; padding-left: 2%; width: 100%; align-items: center">
          <!-- <span> -->
          <img
            src="@/assets/home-images/home-lament.png"
            alt=""
            style="margin-right: 10px; width: 20px; height: 20px"
          >
          <!-- </span> -->
          <span style="color: #35a897; font-size: 1rem">您的登录密码安全性较低</span>
        </span>
        <span class="backs">
          <span style="display: flex; flex-direction: column; align-items: center">
            <el-form
              ref="ruleForm"
              :model="ruleForm"
              :rules="rules"
              label-width="100px"
              style="display: flex; flex-direction: column; align-items: center"
            >
              <span style="font-size: 0.9rem; font-weight: 500; color: #222222; margin-bottom: 20px">修改密码</span>
              <span style="display: flex; flex-direction: column">
                <span style="display: flex; align-items: center">
                  <el-form-item label="旧密码：">
                    <el-input v-model="ruleForm.inp" disabled type="password" :readonly="true" style="width: 300px" />
                  </el-form-item>
                </span>
                <span style="display: flex">
                  <el-form-item label="新密码：" prop="namea">
                    <el-input
                      v-model="ruleForm.inps"
                      :show-password="true"
                      type="password"
                      style="width: 300px"
                      placeholder="请输入"
                      autocomplete="off"
                    />
                  </el-form-item>
                </span>
                <span style="display: flex">
                  <el-form-item label="确认密码：" prop="names">
                    <el-input
                      v-model="ruleForm.inpes"
                      :show-password="true"
                      type="password"
                      style="width: 300px"
                      placeholder="请输入"
                      autocomplete="off"
                    />
                  </el-form-item>
                </span>
              </span>
              <span style="font-weight: 500; margin-top: 10px">
                注：密码必须是
                <strong style="font-size: 0.8rem">6</strong>
                位以上英文字母、数字结合
                <span style="color: red">（不能是纯数字）</span>
              </span>
            </el-form>
          </span>
        </span>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          style="font-size: 0.8rem; width: 200px"
          :disabled="disab"
          @click="submitForm('ruleForm')"
        >确定修改</el-button>
      </span>
    </el-dialog>
    <!-- ====== 修改密码弹出 end ====== -->
  </div>
</template>
<script>
import CspHome from './components/cspHome.vue'
import { getUserId } from '@/utils/auth'
import { getroleMenuUser } from '@/api/system'
import { mapGetters } from 'vuex'
import { changePasswords } from '@/api/dashboard'
import CryptoJS from '@/utils/CryptoJS'

export default {
  components: {
    CspHome
  },
  props: {},
  data() {
    // 验证确认密码与第一次是否相同
    const quFun = (rule, value, callback) => {
      if (this.ruleForm.inpes === '') {
        callback(new Error('请再次输入密码'))
      } else if (this.ruleForm.inpes !== this.ruleForm.inps) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
      this.ButtFun()
    }
    const RegFun = (rule, value, callback) => {
      if (!this.validatePassword(this.ruleForm.inps)) {
        callback(new Error('请重新输入新密码'))
      } else {
        callback()
      }
      this.ButtFun()
    }
    return {
      level: '',
      show: false,
      dialogVisible: false,
      disab: true,
      ruleForm: {
        inp: '',
        inps: '',
        inpes: ''
      },
      rules: {
        namea: [{ required: true, trigger: 'change', validator: RegFun }],
        names: [{ required: true, trigger: 'change', validator: quFun }],
        butt: [{}]
      }
    }
  },
  computed: {
    ...mapGetters(['roles']),
    isHealthCommissio() {
      // 是否卫建委
      return this.roles.findIndex((i) => i.roleCode === 'Health Commissio')
    }
  },
  watch: {},
  created() {},
  mounted() {
    if (getUserId() === '1831679095516659712') {
      this.show = true
    }

    const password = sessionStorage.getItem('password')
    if (password) {
      const password2 = CryptoJS.decrypt(password)

      if (!this.validatePassword(password2)) {
        this.dialogVisible = true
        this.ruleForm.inp = password
      }
      this.ButtFun()
    }
  },
  methods: {
    // 获取密码
    submitForm(formName) {
      this.$refs[formName].validate(async(valid) => {
        if (valid) {
          this.changePassword()
        } else {
          return false
        }
      })
    },
    // 更改密码
    async changePassword() {
      const res = await changePasswords({ id: getUserId(), password: this.ruleForm.inps }, this.$route.token)
      if (res.code === 200) {
        this.dialogVisible = false
        this.resetMeetStatus(getUserId())
        // 清除所有消息通知
        this.$notify.closeAll()
        await this.$store.dispatch('user/logout')
        this.$router.push(`/login`)
        this.$message({
          message: '您的密码已修改，请重新登录',
          type: 'success'
        })
      }
    },
    // 正则验证
    validatePassword(password) {
      const regex = /^(?=.*[a-zA-Z])(?=.*\d).{6,}$/
      return regex.test(password)
    },
    // 按钮禁用
    ButtFun() {
      if (this.ruleForm.inps !== '' && this.ruleForm.inpes !== '') {
        if (this.validatePassword(this.ruleForm.inps)) {
          if (this.ruleForm.inps === this.ruleForm.inpes) {
            this.disab = false
          } else {
            this.disab = true
          }
        } else {
          this.disab = true
        }
      } else {
        this.disab = true
      }
    },

    async roleIdChange(id) {
      if (id === '') {
        await this.$store.dispatch('user/getInfo')
      } else {
        const res = await getroleMenuUser(id)
        console.log('res', res)
        const arr = []
        res.data.menus.forEach((e) => {
          const a = this.findMenu(e.menuId)
          if (a) {
            arr.push(a)
          }
        })
        this.$store.commit('user/SET_MENUS', arr)
      }
    },
    findMenu(id) {
      let a = null
      this.tree.forEach((e) => {
        if (
          `${e.parentId}` === '0' &&
          (e.component === 'Layout' || e.component === 'system/settings/') &&
          `${id}` === `${e.permissionId}`
        ) {
          a = e
        }
      })
      return a
    }
  }
}
</script>
<style lang="scss" scoped>
.dataBoard_home {
  width: 100%;
  height: 100%;
}
.pageContent {
  height: 100%;
}
.public_copyright {
  margin-top: 0rem;
}

section {
  width: 100%;
  height: 100%;
  background-color: #edf2f9;
  background-image: url('~@/assets/home-images/background.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  ::v-deep .hs .el-dialog {
    background-image: url('~@/assets/home-images/home-bc.png');
    background-size: 100% 40%;
    background-repeat: no-repeat;
    border-radius: 15px;
    .el-dialog__header {
      border-bottom: 0 !important;
    }
  }
  ::v-deep .el-dialog .el-dialog__footer {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .backs {
    display: flex;
    flex-direction: column;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.7) 100%);
    box-shadow: 0px 1px 7px 0px rgba(0, 0, 0, 0.15);
    border-radius: 10px;
    backdrop-filter: blur(7px);
    margin-top: 50px;
    padding: 20px 0;
    width: 700px;
  }
  ::v-deep .el-form-item__content {
    display: flex;
    align-items: center;
  }
  ::v-deep .el-form-item__label {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
  }
}
</style>
