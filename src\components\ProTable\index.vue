<script>
import request from '@/utils/request'
import ProInput from '@/components/ProInput/index.vue'
import './style.scss'

export default {
  props: {
    height: {
      type: String,
      default: '400px'
    },
    apiUrl: {
      type: String,
      default: ''
    },
    columns: {
      type: Array,
      default: () => {
        return []
      }
    },
    otherParams: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      searchParams: {},
      pageParams: {
        pageNum: 1,
        pageSize: 20
      },
      total: 0,
      tableData: []
    }
  },
  computed: {
    tableSearchList() {
      return this.columns.filter((column) => column.search)
    }
  },
  mounted() {
    this.fetchTableData()
  },
  methods: {
    readApi(params) {
      return request({
        url: this.apiUrl,
        method: 'get',
        params
      })
    },
    async fetchTableData() {
      this.loading = true
      const params = {
        ...this.otherParams,
        ...this.searchParams,
        pageNo: this.pageParams.pageNum,
        pageSize: this.pageParams.pageSize
      }
      console.log('params', params)
      this.readApi(params)
        .then((response) => {
          this.tableData = response.data.list
          this.total = response.data.total
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSearch() {
      this.pageParams.pageNum = 1
      this.fetchTableData()
    },
    handleReset() {
      this.searchParams = {}
      this.handleSearch()
    },
    handlePageChange(page) {
      this.pageParams.pageNum = page
      this.fetchTableData()
    }
  },
  render(h) {
    const searchForm = (
      <el-row gutter={20}>
        {this.tableSearchList.map((column) => (
          <el-col span={6} key={column.dataIndex}>
            <div class='search-item'>
              <span class='search-item-label'>{column.name}</span>
              <ProInput
                style={{ width: '80%' }}
                vModel={this.searchParams[column.dataIndex]}
                placeholder={`请输入${column.name}`}
                onDebounce={this.fetchTableData}
              />
            </div>
          </el-col>
        ))}
        <el-col span={4}>
          <div class='pro-table-search-btn'>
            <button type='button' class='btn btnCancel addBtn cp vam flex-box mr20' onClick={this.handleReset}>
              <iconSvg icon-class='sync' icon-id='icon-sync' class='iconfont' />
              <label>重置</label>
            </button>
            <button type='button' class='btn btnGreen addBtn cp vam flex-box' onClick={this.handleSearch}>
              <iconSvg icon-class='chaxunbai-01' icon-id='icon-chaxunbai-01' class='iconfont' />
              查询
            </button>
          </div>
        </el-col>
      </el-row>
    )

    // 渲染表格
    const table = (
      <el-table style='width: 100%' v-t-loading={this.loading} data={this.tableData} height={this.height}>
        {this.columns.map((column) => (
          <el-table-column
            showOverflowTooltip
            key={column.dataIndex}
            prop={column.dataIndex}
            label={column.name}
            width={column.width}
          >
            {column.scopeDom
              ? (scope) => column.scopeDom(scope)
              : (scope) => (
                <span class={column.showOverflowTooltip ? 'pro-tooltip' : ''}>
                  {scope.row[column.dataIndex] || '-'}
                </span>
              )}
          </el-table-column>
        ))}
      </el-table>
    )

    // 渲染分页
    const pagination = (
      <el-pagination
        layout='total, sizes, prev, pager, next, jumper'
        pageSizes={[5, 10, 15, 20]}
        total={this.total}
        currentPage={this.pageParams.pageNum}
        pageSize={this.pageParams.pageSize}
        on-current-change={this.handlePageChange}
      />
    )

    return (
      <div id='pro-table'>
        {this.tableSearchList.length > 0 && <div class='search-card'>{searchForm}</div>}
        {table}
        <div style={{ marginTop: '12px', marginBottom: '12px' }}>{pagination}</div>
      </div>
    )
  }
}
</script>
<style>
.pro-tooltip {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
}
</style>
