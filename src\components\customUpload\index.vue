<template>
  <div class="custom-upload-container">
    <el-upload
      :action="action"
      :headers="headers"
      :file-list="internalFileList"
      :on-preview="handlePictureCardPreview"
      :before-remove="beforeRemove"
      :on-remove="handleRemove"
      :on-success="handleSuccess"
      :on-error="handleError"
      :before-upload="beforeUpload"
      :on-exceed="handleExceed"
      :limit="limit"
      :disabled="disabled"
      multiple
      list-type="picture-card"
      class="upload-list-container"
    >
      <i class="el-icon-plus" />
    </el-upload>

    <!--  图片预览弹窗 -->
    <ProDialog title="图片预览" :visible.sync="previewVisible" top="10vh" width="60%">
      <img :src="previewImage" style="width: 100%">
    </ProDialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { splitUrl, getFullUrl } from '@/utils/cspUtils'
import { localCache } from '@/utils/cache'
import ProDialog from '@/components/ProDialog/index.vue'

export default {
  name: 'CustomUpload',
  components: {
    ProDialog
  },
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: [String, Array],
      default: () => []
    },
    action: {
      type: String,
      default: '/cspapi/backend/cos/uploadFile/private2'
    },
    limit: {
      type: [Number, null],
      default: null
    },
    fileSize: {
      type: Number,
      default: 10
    },
    fileType: {
      type: Array,
      default: () => ['png', 'jpg', 'jpeg']
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      internalFileList: [],
      previewImage: '',
      previewVisible: false,
      isUpdating: false
    }
  },
  computed: {
    headers() {
      return {
        Authorization: `${getToken()}`,
        'X-Tenant-Id': localCache.getCache('tenantId')
      }
    },
    supportedTypes() {
      return this.fileType.join('/')
    }
  },
  watch: {
    value: {
      handler(newValue) {
        if (this.isUpdating) return
        this.updateInternalFileList(newValue)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    updateInternalFileList(value) {
      let files = []
      if (typeof value === 'string' && value) {
        const urls = value.split(',')
        files = urls.map((url) => ({ url }))
      } else if (Array.isArray(value)) {
        files = value
      }

      this.internalFileList = files
        .map((item, index) => {
          const url = typeof item === 'string' ? item : item.url
          const name =
            typeof item === 'string' ? this.getFileNameFromUrl(url) : item.name || this.getFileNameFromUrl(url)

          return {
            uid: item.uid || new Date().getTime() + index,
            name,
            status: 'success',
            url: getFullUrl(url)
          }
        })
        .filter((item) => item.url)
    },

    getFileNameFromUrl(url) {
      if (!url) return 'unknown_file'
      try {
        return decodeURIComponent(url).split('/').pop()
      } catch (e) {
        return url.split('/').pop()
      }
    },

    emitValue() {
      this.isUpdating = true
      const newValue = this.internalFileList.map((f) => f.url).join(',')
      this.$emit('input', newValue)
      this.$nextTick(() => {
        this.isUpdating = false
      })
    },

    handleRemove(file, fileList) {
      console.log('1231')
      this.internalFileList = fileList
      this.emitValue()
    },

    // 预览图片
    handlePictureCardPreview(file) {
      this.previewImage = file.url
      this.previewVisible = true
    },

    handleSuccess(response, file, fileList) {
      if (response && response.data && response.data.length > 0) {
        const uploadedFile = response.data[0]
        const currentFile = fileList.find((f) => f.uid === file.uid)
        if (currentFile) {
          currentFile.name = file.raw.name
          currentFile.url = splitUrl(uploadedFile.fullFileUrl)
        }
        this.internalFileList = fileList
        this.emitValue()
      } else {
        this.$message.error('上传失败，服务器返回数据格式不正确')
        this.internalFileList = fileList.filter((f) => f.uid !== file.uid)
        this.emitValue()
      }
    },

    handleError(err, file, fileList) {
      this.$message.error('文件上传失败，请稍后重试')
      this.internalFileList = fileList.filter((f) => f.status !== 'fail')
      this.emitValue()
      console.error('Upload Error:', err)
    },

    beforeUpload(file) {
      const extension = file.name.split('.').pop().toLowerCase()
      const isTypeValid = this.fileType.includes(extension)
      if (!isTypeValid) {
        this.$message.error(`上传文件类型不支持，仅支持 ${this.supportedTypes} 格式!`)
        return false
      }

      const isSizeValid = file.size / 1024 / 1024 < this.fileSize
      if (!isSizeValid) {
        this.$message.error(`上传文件大小不能超过 ${this.fileSize}MB!`)
        return false
      }

      return true
    },

    handleExceed(files, fileList) {
      if (!this.limit) return
      this.$message.warning(
        `当前限制选择 ${this.limit} 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      )
    },

    beforeRemove(file, fileList) {
      return this.$confirm(`确定移除此图片？`, '删除确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
    }
  }
}
</script>

<style scoped lang="scss">
.custom-upload-container {
  width: 100%;
  ::v-deep .el-upload--picture-card {
    width: 150px;
    height: 150px;
    line-height: 150px;
  }
  ::v-deep .el-upload-list__item {
    width: 150px;
    height: 150px;
  }
}
.upload-list-container {
  ::v-deep .el-upload-list__item-name {
    color: #0a86c8;
  }
}
</style>
