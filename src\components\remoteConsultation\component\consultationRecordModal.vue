<!-- 会议记录弹窗 -->

<template>
  <ProDialog :visible.sync="dialogVisible" title="会诊记录详情" width="700" :height="500" append-to-body>
    <div class="consultation-record-modal">
      <div class="video">
        <h3>会诊视频</h3>
        <video
          :src="videoUrl"
          controls
          style="width: 100%; height: 450px; margin-top: 8px; border: 1px solid #c0c4cc"
        />
      </div>
      <div class="info">
        <div class="advice">
          <h3>诊治意见</h3>
          <el-input
            v-model="meetingResult"
            type="textarea"
            :rows="10"
            placeholder="请输入诊治意见"
            style="margin-top: 8px"
          />
        </div>
        <div class="prescription">
          <h3>处方</h3>
          <el-table border :data="medicalList" height="230" style="width: 100%; margin-top: 8px">
            <el-table-column align="center" prop="medicineName" label="药品名称" />
            <el-table-column align="center" prop="dayTimes" label="每日次数" />
            <el-table-column align="center" prop="dayCount" label="每次用量" />
            <el-table-column align="center" prop="unit" label="计量单位" />
            <el-table-column align="center" prop="remark" label="备注" />
          </el-table>
        </div>
      </div>
    </div>
  </ProDialog>
</template>

<script>
import ProDialog from '@/components/ProDialog/index.vue'

export default {
  components: {
    ProDialog
  },
  data() {
    return {
      dialogVisible: false,
      meetingResult: '',
      videoUrl: '',
      medicalList: []
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.consultation-record-modal {
  .info {
    display: flex;
    gap: 16px;
    margin-top: 16px;
    .advice {
      width: 30%;
    }
    .prescription {
      flex: 1;
    }
  }
}
</style>
