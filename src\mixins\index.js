/* eslint-disable no-useless-concat */
/* eslint-disable no-var */
/* eslint-disable no-use-before-define */
/* eslint-disable no-cond-assign */
import { mapGetters } from 'vuex'
import { URL_ENUM } from '@/utils/enum'
import { resetMeetStatus } from '@/api/dashboard'
import elDragDialog from '@/directive/el-drag-dialog'
import envModule from '@/utils/env'
import { backendParamDecryptApi } from '@/api/system'

export default {
  directives: {
    elDragDialog
  },
  data() {
    return {
      diseaseOptions: [
        {
          value: '',
          label: '全部'
        },
        {
          value: '1',
          label: '房早'
        },
        {
          value: '2',
          label: '室早'
        },
        {
          value: '3',
          label: '房颤'
        },
        {
          value: '4',
          label: '心率过高'
        },
        {
          value: '5',
          label: '心率过低'
        },
        {
          value: '6',
          label: '不确定'
        },
        {
          value: '7',
          label: '不确定（信号质量差）'
        },
        {
          value: '8',
          label: '呼吸睡眠与暂停'
        },
        {
          value: '9',
          label: '早搏'
        }
      ],
      readCardTime: 2500,
      envKey: envModule.getEnv(),
      filePath: URL_ENUM.FILE_HTTP_ENV,
      downloadHref: URL_ENUM.HTTP_ENV,
      tablePageSizes: [5, 10, 20, 50], // 表格分页参数配置
      tablePaginationLayout: 'total, sizes, prev, pager, next, jumper' // 表格分页的功能参数配置
    }
  },
  computed: {
    width1280() {
      return document.documentElement.clientWidth <= 1280
    },
    ...mapGetters(['permission', 'fz', 'parsedRouteParams'])
  },
  methods: {
    // 全局血糖状态是否异常判断
    globalBloodSugarStatus(record) {
      // 空腹小于7  非空腹小于11.1
      const isKf = record.type === 1 && record.value >= 7
      const isFkf = record.type !== 1 && record.value >= 11.1

      if (isKf || isFkf) {
        return {
          color: 'red'
        }
      }
      return null
    },
    globalHttpUrl(url) {
      let imageUrl = ''
      // 获取服务器地址
      const heardUrl =
        process.env.NODE_ENV === 'production'
          ? window.location.href.match(/^https?:\/\/[^/]+\/?/)[0]
          : URL_ENUM.FILE_HTTP_ENV

      if (url) {
        // 包含http说明是绝对路径需要初始化去除然后拼接当前HTTP_URL在
        if (url.includes('http')) {
          const result = url.replace(/^https?:\/\/[^/]+\/?/, '')
          imageUrl = `${heardUrl}${result}`
        } else {
          // 直接拼接当前HTTP_URL
          imageUrl = `${heardUrl}${url}`
        }
      }
      console.log('是否生产', process.env.NODE_ENV === 'production')
      console.log('imageUrl', imageUrl)
      return imageUrl
    },
    playOnlyMe(playId) {
      const audioList = document.getElementsByTagName('audio')
      for (let index = 0; index < audioList.length; index++) {
        const audioItem = audioList[index]
        if (audioItem.id !== playId) {
          audioItem.pause()
        }
      }
    },
    viewerImageList(images) {
      this.$viewerApi({
        images
      })
    },
    formatDateTime(date) {
      const y = date.getFullYear()
      let m = date.getMonth() + 1
      m = m < 10 ? `0${m}` : m
      let d = date.getDate()
      d = d < 10 ? `0${d}` : d
      let h = date.getHours()
      h = h < 10 ? `0${h}` : h
      let minute = date.getMinutes()
      minute = minute < 10 ? `0${minute}` : minute
      let second = date.getSeconds()
      second = second < 10 ? `0${second}` : second
      return `${y}-${m}-${d} ${h}:${minute}:${second}`
    },
    formatMoney(number, places, symbol) {
      number = number || 0
      places = !isNaN((places = Math.abs(places))) ? places : 2
      symbol = symbol !== undefined ? symbol : '￥'
      const negative = number < 0 ? '-' : ''
      const i = `${parseInt((number = Math.abs(+number || 0).toFixed(places)), 10)}`
      var j = (j = i.length) > 3 ? j % 3 : 0
      return (
        symbol +
        negative +
        (j ? `${i.substr(0, j)},` : '') +
        i.substr(j).replace(/(\d{3})(?=\d)/g, '$1' + ',') +
        (places
          ? `.${Math.abs(number - i)
            .toFixed(places)
            .slice(2)}`
          : '')
      )
    },
    getLocalTimes(ns) {
      let date = new Date(ns)
      const year = date.getFullYear()
      let month = date.getMonth() + 1
      let day = date.getDate()
      month = month < 10 ? `0${month}` : month
      day = day < 10 ? `0${day}` : day
      date = `${year}-${month}-${day}`
      return date
    },
    // 置顶ref全屏
    doVideoClick(_id) {
      this.launchIntoFullscreen(document.getElementById(_id))
    },
    doVideoDbClick() {
      this.exitFullScreen()
    },
    exitFullScreen() {
      if (document.exitFullScreen) {
        document.exitFullScreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
    },
    // 全屏封装
    launchIntoFullscreen(element) {
      if (element.requestFullscreen) {
        element.requestFullscreen()
      } else if (element.mozRequestFullScreen) {
        element.mozRequestFullScreen()
      } else if (element.webkitRequestFullscreen) {
        element.webkitRequestFullscreen()
      } else if (element.msRequestFullscreen) {
        element.msRequestFullscreen()
      }
    },
    // 重置远程会诊用户状态
    async resetMeetStatus(userId) {
      await resetMeetStatus({ userId })
    },
    // 显示原文
    /**
     * @description: 显示原文
     * @return {*}
     * @author: LiSuwan
     * @Date: 2025-01-16 17:01:42
     */
    async showOriginalText(cipherText) {
      const res = await backendParamDecryptApi({ cipherText })
      let originalText = ''
      if (res.code === 200) {
        originalText = res.data.originalNumber
      }
      return originalText
    }
  }
}
