<!-- 检验检查第六部分: 肌电图 -->
<template>
  <div class="inspection-sixth-part">
    <div class="content">
      <div class="title">肌电图</div>
      <div class="item">
        <el-table :data="emgData.mcv" border>
          <el-table-column label="神经传导-运动传导速度（MCV）" align="center">
            <el-table-column prop="nerve" label="神经" width="150" align="center" />
            <el-table-column prop="segment" label="节段" width="180" align="center" />
            <el-table-column prop="amplitude" label="波幅 (mV)" align="center" />
            <el-table-column prop="velocity" label="速度 (m/s)" align="center" />
          </el-table-column>
        </el-table>
        <el-table :data="emgData.scv" border style="margin-top: 10px">
          <el-table-column label="神经传导-感觉传导速度（SCV）" align="center">
            <el-table-column prop="nerve" label="神经" width="160" align="center" />
            <el-table-column prop="stimulate" label="刺激部位" width="120" align="center" />
            <el-table-column prop="record" label="记录部位" width="120" align="center" />
            <el-table-column prop="amplitude" label="波幅 (uV)" align="center" />
            <el-table-column prop="velocity" label="速度 (m/s)" align="center" />
          </el-table-column>
        </el-table>

        <el-descriptions :column="2" border style="margin-top: 10px">
          <el-descriptions-item label="检查结果">
            {{ emgData.form.emgResult === 1 ? '正常' : emgData.form.emgResult === 2 ? '异常' : '' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { mapDataToTable } from '@/utils/cspUtils'

export default {
  name: 'InspectionSixthPart',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableDataMvc: [
        {
          nerve: '右正中神经',
          segment: '肘→腕',
          amplitude: '',
          velocity: '',
          mcvRightMiddle: 'amplitude',
          mcvRightMiddleSpeed: 'velocity'
        },
        {
          nerve: '左正中神经',
          segment: '肘→腕',
          amplitude: '',
          velocity: '',
          mcvLeftMiddle: 'amplitude',
          mcvLeftMiddleSpeed: 'velocity'
        },
        {
          nerve: '右尺神经',
          segment: '肘→腕',
          amplitude: '',
          velocity: '',
          mcvRightElbow: 'amplitude',
          mcvRightElbowSpeed: 'velocity'
        },
        {
          nerve: '左尺神经',
          segment: '肘→腕',
          amplitude: '',
          velocity: '',
          mcvLeftElbow: 'amplitude',
          mcvLeftElbowSpeed: 'velocity'
        },
        {
          nerve: '右桡神经',
          segment: '肘→前臂中点',
          amplitude: '',
          velocity: '',
          mcvRightArm: 'amplitude',
          mcvRightArmSpeed: 'velocity'
        },
        {
          nerve: '左桡神经',
          segment: '肘→前臂中点',
          amplitude: '',
          velocity: '',
          mcvLeftArm: 'amplitude',
          mcvLeftArmSpeed: 'velocity'
        },
        {
          nerve: '右腓总神经',
          segment: '腓骨小头→踝前',
          amplitude: '',
          velocity: '',
          mcvRightFoot: 'amplitude',
          mcvRightFootSpeed: 'velocity'
        },
        {
          nerve: '左腓总神经',
          segment: '腓骨小头→踝前',
          amplitude: '',
          velocity: '',
          mcvLeftFoot: 'amplitude',
          mcvLeftFootSpeed: 'velocity'
        },
        {
          nerve: '右胫神经',
          segment: '腘窝→踝',
          amplitude: '',
          velocity: '',
          mcvRightLeg: 'amplitude',
          mcvRightLegSpeed: 'velocity'
        },
        {
          nerve: '左胫神经',
          segment: '腘窝→踝',
          amplitude: '',
          velocity: '',
          mcvLeftLeg: 'amplitude',
          mcvLeftLegSpeed: 'velocity'
        }
      ],

      tableDataScv: [
        {
          nerve: '右正中神经',
          stimulate: '中指',
          record: '腕',
          amplitude: '',
          velocity: '',
          scvRightMiddle: 'amplitude',
          scvRightMiddleSpeed: 'velocity'
        },
        {
          nerve: '左正中神经',
          stimulate: '中指',
          record: '腕',
          amplitude: '',
          velocity: '',
          scvLeftMiddle: 'amplitude',
          scvLeftMiddleSpeed: 'velocity'
        },
        {
          nerve: '右尺神经',
          stimulate: '小指',
          record: '腕',
          amplitude: '',
          velocity: '',
          scvRightElbow: 'amplitude',
          scvRightElbowSpeed: 'velocity'
        },
        {
          nerve: '左尺神经',
          stimulate: '小指',
          record: '腕',
          amplitude: '',
          velocity: '',
          scvLeftElbow: 'amplitude',
          scvLeftElbowSpeed: 'velocity'
        },
        {
          nerve: '右桡神经',
          stimulate: '拇指',
          record: '腕',
          amplitude: '',
          velocity: '',
          scvRightArm: 'amplitude',
          scvRightArmSpeed: 'velocity'
        },
        {
          nerve: '左桡神经',
          stimulate: '拇指',
          record: '腕',
          amplitude: '',
          velocity: '',
          scvLeftArm: 'amplitude',
          scvLeftArmSpeed: 'velocity'
        },
        {
          nerve: '右胫神经',
          stimulate: '腘窝',
          record: '踝',
          amplitude: '',
          velocity: '',
          scvRightLeg: 'amplitude',
          scvRightLegSpeed: 'velocity'
        },
        {
          nerve: '左胫神经',
          stimulate: '腘窝',
          record: '踝',
          amplitude: '',
          velocity: '',
          scvLeftLeg: 'amplitude',
          scvLeftLegSpeed: 'velocity'
        },
        {
          nerve: '右腓总神经',
          stimulate: '',
          record: '',
          amplitude: '',
          velocity: '',
          scvRightFoot: 'amplitude',
          scvRightFootSpeed: 'velocity'
        },
        {
          nerve: '左腓总神经',
          stimulate: '',
          record: '',
          amplitude: '',
          velocity: '',
          scvLeftFoot: 'amplitude',
          scvLeftFootSpeed: 'velocity'
        },
        {
          nerve: '右腓肠神经',
          stimulate: '跟骨上14厘米',
          record: '外踝',
          amplitude: '',
          velocity: '',
          scvRightFootBowel: 'amplitude',
          scvRightFootBowelSpeed: 'velocity'
        },
        {
          nerve: '左腓肠神经',
          stimulate: '跟骨上14厘米',
          record: '外踝',
          amplitude: '',
          velocity: '',
          scvLeftBowelFoot: 'amplitude',
          scvLeftFootBowelSpeed: 'velocity'
        },
        {
          nerve: '右腓浅神经',
          stimulate: '浅骨上肌',
          record: '足背',
          amplitude: '',
          velocity: '',
          scvRightFootShallow: 'amplitude',
          scvRightFootShallowSpeed: 'velocity'
        },
        {
          nerve: '左腓浅神经',
          stimulate: '浅骨上肌',
          record: '足背',
          amplitude: '',
          velocity: '',
          scvLeftFootShallow: 'amplitude',
          scvLeftFootShallowSpeed: 'velocity'
        }
      ]
    }
  },
  computed: {
    emgData() {
      const { data = {} } = this.reportInfo.itemList.find((item) => item.itemCode === 'EMG') || {}

      return {
        mcv: mapDataToTable(data, cloneDeep(this.tableDataMvc)),
        scv: mapDataToTable(data, cloneDeep(this.tableDataScv)),
        form: {
          emgResult: data.emgResult
        }
      }
    }
  },
  methods: {}
}
</script>

<style lang="scss">
.inspection-sixth-part {
  .content {
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    .el-table td.el-table__cell .cell {
      line-height: 1.6rem !important;
    }
  }
  @media print {
    // 增加选择器权重确保打印样式生效
    .content .el-table td.el-table__cell {
      padding: 0 !important;
    }
    .content .el-table td.el-table__cell .cell {
      line-height: 1.5rem !important;
    }
    .content .el-table th.el-table__cell {
      padding: 0 !important;
    }
    .content .el-table th.el-table__cell .cell {
      line-height: 1.5rem !important;
    }
  }
}
</style>
