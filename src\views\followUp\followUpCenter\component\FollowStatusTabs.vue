<template>
  <div class="status-tabs">
    <div
      v-for="(item, index) in tabsList"
      :key="index"
      :class="['status-tab-item', { active: activeTabIndex === index }]"
      @click="handleTabClick(index)"
    >
      <div class="tab-icon" :style="{ backgroundColor: item.color }">
        <i v-if="item.icon" class="el-icon" :class="item.icon" />
      </div>
      <div class="tab-content">
        <div class="tab-title">{{ item.diseaseName }}</div>
        <div class="tab-count">{{ item.count }} {{ unit }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FollowStatusTabs',
  props: {
    // 选项卡列表数据
    tabs: {
      type: Array,
      required: true,
      default: () => []
    },
    // 默认激活的选项卡索引
    defaultActive: {
      type: Number,
      default: 0
    },
    // 数量单位
    unit: {
      type: String,
      default: '条'
    }
  },
  data() {
    return {
      activeTabIndex: this.defaultActive,
      tabsList: []
    }
  },
  watch: {
    tabs: {
      handler(val) {
        this.initTabs(val)
      },
      immediate: true,
      deep: true
    },
    defaultActive(val) {
      this.activeTabIndex = val
    }
  },
  methods: {
    // 初始化选项卡数据
    initTabs(tabs) {
      // 设置默认颜色
      const defaultColors = [
        '#5694FF', // 蓝色
        '#FF9F43', // 橙色
        '#00CF83', // 绿色
        '#FFCC00', // 黄色
        '#FF6B6B' // 红色
      ]

      this.tabsList = tabs.map((item, index) => {
        return {
          ...item,
          color: item.color || defaultColors[index % defaultColors.length]
        }
      })
    },
    // 处理选项卡点击事件
    handleTabClick(index) {
      if (this.activeTabIndex !== index) {
        this.activeTabIndex = index
        this.$emit('tab-change', index, this.tabsList[index])
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.status-tabs {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 4px;

  .status-tab-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    cursor: pointer;

    &:not(:last-child)::after {
      content: '';
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      height: 60%;
      width: 1px;
      background-color: #ebeef5;
    }

    &.active {
      border-bottom: 4px solid #5694ff;

      .tab-title {
        font-weight: bold;
        color: #333;
      }

      .tab-count {
        color: #333;
      }
    }

    .tab-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;

      i {
        color: #fff;
        font-size: 18px;
      }
    }

    .tab-content {
      display: flex;
      flex-direction: column;

      .tab-title {
        font-size: 14px;
        color: #666;
        margin-bottom: 4px;
      }

      .tab-count {
        font-size: 18px;
        font-weight: bold;
        color: #666;
      }
    }
  }
}
</style>
