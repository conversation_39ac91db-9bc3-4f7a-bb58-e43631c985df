module.exports = {
  getTime({ time, format, hour } = {}) {
    let date = new Date()
    if (time) {
      date = new Date(time)
    }
    if (hour) {
      date = date.getTime() + hour * 60 * 60 * 1000
      date = new Date(date)
    }
    let dateArr = [
      date.getFullYear(),
      date.getMonth() + 1,
      date.getDate(),
      date.getHours(),
      date.getMinutes(),
      date.getSeconds()
    ]
    dateArr = dateArr.map(v => {
      return v < 10 ? '0' + v : v
    })
    let weekData = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    format = format || 'yy/mm/dd hh:mo:ss'
    format = format
      .replaceAll('yy', dateArr[0])
      .replaceAll('mm', dateArr[1])
      .replaceAll('dd', dateArr[2])
      .replaceAll('hh', dateArr[3])
      .replaceAll('mo', dateArr[4])
      .replaceAll('ss', dateArr[5])
      .replaceAll('星期', weekData[date.getDay()])
    return format
  },
  // isObjValue
  isObjValue(val) {
    return val !== '' && val !== null && val !== undefined && JSON.stringify(val) !== '{}'
  },
  // 脱敏数据
  desensitize({ key, data, show } = {}) {
    let txt = data
    if (txt && !show) {
      switch (key) {
        case 'phone':
          txt = `${data.slice(0, 3)}****${data.slice(data.length - 4, data.length)}`
          break
        case 'idCard':
          txt = `${data.slice(0, 6)}****${data.slice(data.length - 4, data.length)}`
          break
        case '2-1':
          txt = `${data.slice(0, 2)}****${data.slice(data.length - 1, data.length)}`
          break
        case '2-2':
          txt = `${data.slice(0, 2)}****${data.slice(data.length - 2, data.length)}`
          break
        default:
          break
      }
    }
    return txt
  },
  // 上传文件处理
  handleUploadFilePath({ data } = {}) {
    return data.map(v => {
      return v.fileUrl.split('/')[0] + '/' + v.fullFileUrl.split(v.fileUrl.split('/')[0])[1]
    })
  },
  // 将空转化 ‘-/-’
  changeNull({ val, format = '-/-' } = {}) {
    let txt = val
    if (!isValue(txt)) {
      txt = format
    }
    return txt
  },
  // 校验
  rulesFun(val, type) {
    // 数组 - 身份证各位权重
    const ID_CARD_WEIGHTS = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
    // 字符串 - 身份证可包含字符
    const IC_CARD_CHARS = '10X98765432'
    // 正则 - 手机号正则
    const REGEX_MOBILE = /^1[3456789]\d{9}$/
    // 正则 - 身份证号正则
    const REGEX_IDCARD = /(^\d{15}$)|(^\d{17}(\d|X|x)$)/
    let a = {
      type: false,
      msg: ''
    }
    switch (type) {
      case 'idCard':
        // 身份证号码校验
        let sum = 0
        for (let i = 0; i < val.length - 1; i++) {
          sum += val[i] * ID_CARD_WEIGHTS[i]
        }
        const last = IC_CARD_CHARS[sum % 11]
        if (val[val.length - 1] !== last || val.length < 15) {
          a.msg = '你输入的身份证号非法'
          a.type = true
        } else if (val === '') {
          a.msg = '请输入身份证号'
          a.type = true
        }
        break
      default:
        break
    }
    return a
  },
  // 根据身份证号返回出生日期
  getBirthDate(idCard) {
    var pattern = /^(\d{6})(\d{4})(\d{2})(\d{2})/
    var match = pattern.exec(idCard)
    if (match) {
      var year = match[2]
      var month = match[3]
      var day = match[4]
      return year + '-' + month + '-' + day
    }
    return null
  },
  // 根据身份证号返回性别 字符串 0女 1男
  getGender(id) {
    // 身份证号的倒数第二位，奇数为男性，偶数为女性
    var genderCode = parseInt(id.charAt(id.length - 2))
    return genderCode % 2 === 0 ? '0' : '1'
  },
  // 根据身份证号返回年龄
  getAgeFromIdCard(idCard) {
    var birthday = ''
    if (idCard.length == 18) {
      birthday = idCard.slice(6, 10) + '-' + idCard.slice(10, 12) + '-' + idCard.slice(12, 14)
    } else if (idCard.length == 15) {
      birthday = '19' + idCard.slice(6, 8) + '-' + idCard.slice(8, 10) + '-' + idCard.slice(10, 12)
    } else {
      throw new Error('Wrong id card format!')
    }
    var age = new Date().getFullYear() - new Date(birthday).getFullYear()
    return age
  },
  // 文字超过的长度显示。。。
  txtOh(val, len) {
    len = len ? len : 1
    if (val) {
      let txt = val.replace(/<[^>]+>/g, '')
      if (val.length > len) {
        return txt.substring(0, len) + '...'
      }
      return txt.substring(0, len)
    }
    return val
  },
  // 根据id返回组织架构的全路径
  backArrID(menuId,row){
    const stack = [...row];
    while (stack.length > 0) {
      const item = stack.pop();
      item.parent = item.parent?item.parent:[]
      if (item.departCode === menuId) {
        // 找到目标项
        return [...item.parent,item.departCode];
      }
      if (item.children && item.children.length > 0) {
        stack.push(...item.children.map(child => ({ ...child, parent: [...item.parent,item.departCode] })));
      }
    }
    // 未找到目标项
    return null;
  }
}
