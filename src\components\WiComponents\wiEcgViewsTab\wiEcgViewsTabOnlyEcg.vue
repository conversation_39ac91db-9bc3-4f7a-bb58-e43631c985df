<!-- eslint-disable max-len -->
<template>
  <section class="ecgBox" :class="{ ecgBoxWithInId: inId !== '', previewSty: !previewShow }">
    <p
      v-if="!previewShow"
      class="standard_ecg_title"
      :style="`padding: 0 1.2vw;font-size: ${fontSize ? fontSize : '14px'};margin:${fontSize ? '10px 0' : '0 0 10px'};`"
    >
      报告结论：{{ reviewContent || '- / -' }}
    </p>
    <svg id="standard_ecg" version="1.1" xmlns="http://www.w3.org/2000/svg" class="dn">
      <g id="grid_standard_ecg">
        <!--点-->
        <path id="dotGrid_standard_ecg" />
        <!--线-->
        <path id="lineGrid_standard_ecg" />
      </g>
    </svg>
  </section>
</template>

<!-- eslint-disable no-undef -->
<!-- eslint-disable no-prototype-builtins -->
<script>
import ecgDemoMixin from './ecgVender/ecgMixin.js'

import { VLP_DATA } from './mock/vlpData'
import { VECTOR_DATA } from './mock/vectorData'
import { deepClone } from '@/utils'

let ecgJson = {}
// 波形参数设置
const ecgParme = {
  gridHeight: 960,
  gridWidthPrint: 1064,
  gridHeightPrint: 775,
  waveColor: '#000000', // 波形颜色
  dotColor: 'red', // 点颜色
  gridColor: '#000000', // 网格颜色
  waveLineWidth: '1', // 波形线宽
  dotLineWidth: '1', // 点线宽
  gridLineWidth: '1', // 网格线宽
  lineWidth: 1, // 坐标轴、定标电压线宽
  pix_mm: 3.777,
  isPrint: 0, // 0不打印，1打印
  gain: 10,
  speed: 25,
  qrsGain: 60,
  tGain: 80,
  division: 4,
  begin_ms: 0,
  printDirection: 1, // 打印纸张方向
  layoutIndex: 0, // 布局
  ecgType: 0, // 心电类型
  textColor: '#000000', // 文字颜色
  rhythm: 0,
  rhythm1: 0,
  rhythm2: 0,
  rhythm3: 0,
  leadIndex: -1, // 导联索引
  displayMode: 0, // 0同步，1顺序
  opacity: 0.5, // 测量标尺的透明度
  backColor: '#ffffff', // 背景色
  multiple: 1, // 波形放大倍数
  previewEcg: false
}

ecgJson = {}

let paramset = null

export default {
  name: 'EcgDemo',
  components: {},
  mixins: [ecgDemoMixin],
  props: {
    ecgJsonIncome: {
      type: Object,
      default: () => {
        return {}
      }
    },
    ecgResult: {
      type: Object,
      default: () => {
        return {}
      }
    },
    inId: {
      type: [String, Number],
      default: ''
    },
    fontSize: {
      type: [String, Number],
      default: ''
    },
    reviewContent: {
      type: [String],
      default: ''
    },
    previewShow: {
      type: Boolean,
      default: true
    },
    isPrinting: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeName: '常规心电图',

      speedOptions: ['5', '6.25', '10', '12.5', '25', '50'],
      gainOptions: ['2.5', '5', '10', '20', '40'],
      layoutOptions: [
        { value: 0, label: '3*4' },
        { value: 1, label: '3*4+1' },
        { value: 2, label: '3*4+3' },
        { value: 3, label: '6*2' },
        { value: 4, label: '6*2+1' },
        { value: 5, label: '12*1' }
      ],
      leadIndexOptions: [
        { value: -1, label: '全部' },
        { value: 0, label: 'I' },
        { value: 1, label: 'II' },
        { value: 2, label: 'III' },
        { value: 3, label: 'AVR' },
        { value: 4, label: 'AVL' },
        { value: 5, label: 'AVF' },
        { value: 6, label: 'V1' },
        { value: 7, label: 'V2' },
        { value: 8, label: 'V3' },
        { value: 9, label: 'V4' },
        { value: 10, label: 'V5' },
        { value: 11, label: 'V6' }
      ],
      multipleOptions: [
        { value: 1, label: '1.0x' },
        { value: 2, label: '2.0x' },
        { value: 3, label: '3.0x' },
        { value: 4, label: '4.0x' },
        { value: 5, label: '5.0x' }
      ],

      vueEcgParme: {
        // 纸速
        speed: '25',
        // 增益
        gain: '10',
        // 布局
        layoutIndex: 4,
        // 导联显示
        leadIndex: -1,
        // 缩放倍率
        multiple: 1,
        // ecgParme["begin_ms"] = vueEcgParme.beginMs * 1000;
        beginMs: 0
      }
    }
  },
  mounted() {
    console.log(zqStandard)
  },
  methods: {
    genFirstDraw() {
      ecgJson = Object.assign(ecgJson, this.ecgJsonIncome)
      const parameter = zqCommon.GetPathParameter()
      const { pid } = parameter
      lockService.background(pid, 'ecg') // 开启锁定
      // const ecgUrl = `${ctx}ecg/list/getEcg2/${pid}`
      getParamset()
      /**
       * 获取波形参数
       */
      function getWaveSet() {
        ecgParme.waveColor = '#000000'
        ecgParme.dotColor = '#ff0000'
        ecgParme.gridColor = '#ff0000'
      }
      getWaveSet()

      function getParamset() {
        const dataJson = {
          reportPrint: {
            searchValue: null,
            createBy: null,
            createTime: '2022-08-02 16:22:17',
            updateBy: null,
            updateTime: '2023-07-18 17:47:13',
            remark: null,
            params: {},
            id: 49,
            userId: 1,
            value: {
              newTip: null,
              erTip: null,
              undiagTip: null,
              undiagTipInterval: 5,
              paramsExTip: 'on'
            },
            print: '1',
            filter: '50,1,100'
          },
          print: {
            searchValue: null,
            createBy: null,
            createTime: null,
            updateBy: null,
            updateTime: null,
            remark: null,
            params: {},
            id: 57,
            userId: 1,
            mode: 1,
            speed: '25',
            gain: '10',
            layout: '4',
            direction: '0',
            paper: '0',
            lead: 12
          },
          report: {
            searchValue: null,
            createBy: null,
            createTime: null,
            updateBy: null,
            updateTime: null,
            remark: null,
            params: {},
            id: 47,
            deptId: 190,
            rightUp: 'checkTime',
            rightDown: 'reviewTime',
            title1: '测试医院',
            title2: '测试医院',
            type: 'ecg'
          },
          diagnostic: {
            searchValue: null,
            createBy: null,
            createTime: null,
            updateBy: null,
            updateTime: null,
            remark: null,
            params: {},
            id: 51,
            userId: 1,
            speed: '25',
            gain: '10',
            layout: '4',
            lead: 12,
            displayMode: 1
          },
          rhythm: {
            searchValue: null,
            createBy: null,
            createTime: null,
            updateBy: null,
            updateTime: null,
            remark: null,
            params: {},
            id: 67,
            userId: 1,
            rhythm: '0',
            rhythm1: '0',
            rhythm2: '1',
            rhythm3: '2',
            lead: 12
          }
        }
        paramset = dataJson
        const diagnosticPara = dataJson.diagnostic
        ecgParme.speed = diagnosticPara.speed
        ecgParme.gain = diagnosticPara.gain
        ecgParme.layoutIndex = diagnosticPara.layout
        ecgParme.displayMode = diagnosticPara.displayMode ? diagnosticPara.displayMode - 1 : 0
        const rhythmPara = dataJson.rhythm
        ecgParme.rhythm = rhythmPara.rhythm
        ecgParme.rhythm1 = rhythmPara.rhythm1
        ecgParme.rhythm2 = rhythmPara.rhythm2
        ecgParme.rhythm3 = rhythmPara.rhythm3
      }

      // setTimeout(getParamset(ecgJson['lead']),0);

      this.initECG()
    },

    initECG() {
      const ecgSvgBoxWidth = document.getElementsByClassName('ecgSvgBox')[0].offsetWidth * 0.97
      ecgParme.gridWidth = ecgSvgBoxWidth
      ecgParme.gridOtherWidth = ecgSvgBoxWidth
      ecgParme.gridHeight = !this.isPrinting ? '500' : ecgParme.gridWidth / 1.5
      ecgParme.gridOtherHeight = ecgSvgBoxWidth / 1.5

      const ecgParmeTmp = deepClone(ecgParme)
      const { gridWidth } = ecgParmeTmp

      // 常规心电图
      ecgParmeTmp.leadIndex = -1
      ecgParmeTmp.layoutIndex = 4
      ecgParmeTmp.speed = 25
      ecgParmeTmp.gain = 10
      ecgParmeTmp.multiple = 1
      /**
       * 常规心电绘制
       * 调用 zqStandard.draw_ecg_standard()
       */
      if (gridWidth !== undefined) {
        zqStandard.draw_ecg_standard(
          'standard_ecg',
          ecgJson,
          ecgParmeTmp,
          paramset,
          0,
          gridWidth,
          ecgParmeTmp.gridHeight
        )
      }
      /**
       * 频谱绘制
       * 调用zqSpectrum.draw_ecg_spectrum()
       */
      zqSpectrum.clearEcgWave('spectrum_ecg')
      zqSpectrum.draw_ecg_spectrum(
        'spectrum_ecg',
        ecgJson,
        ecgParmeTmp,
        0,
        ecgParmeTmp.gridOtherWidth,
        ecgParmeTmp.gridOtherHeight
      )
      /**
       * QT离散度绘制
       * 调用zqQt.draw_ecg_qt()
       */
      zqQt.clearEcgWave('qt_ecg')
      zqQt.draw_ecg_qt('qt_ecg', ecgJson, ecgParmeTmp, 0, ecgParmeTmp.gridOtherWidth, ecgParmeTmp.gridOtherHeight)
      /**
       * 时间向量绘制
       * 调用zqTimeVector.draw_ecg_timeVector()
       */
      zqTimeVector.clearEcgWave('time_vector_ecg')
      zqTimeVector.draw_ecg_timeVector(
        'time_vector_ecg',
        ecgJson,
        ecgParmeTmp,
        0,
        ecgParmeTmp.gridOtherWidth,
        ecgParmeTmp.gridOtherHeight
      )
      /**
       * 心室晚电位 【暂无数据】
       * 调用 zqVlp.draw_ecg_vlp()
       */
      // let vlpData = {};
      // let vlpDllUrl = ctx + "ecg/list/getAnalysisData/"+ecgId + "/3";
      // zqCommon.httpRequest({async: false, url : vlpDllUrl, method : "get"}).then(data => {
      //     if(data.data){
      //         vlpData = data;
      //     }
      // },(error)=>{
      //     console.log(error);
      // })
      // NEED_REQUEST_DATA_VLP 应该为 上个接口的 data.data （赋值后的 vlpData.data）
      const NEED_REQUEST_DATA_VLP = VLP_DATA
      zqVlp.draw_ecg_vlp(
        'vlp_ecg',
        ecgJson,
        NEED_REQUEST_DATA_VLP,
        ecgParmeTmp,
        0,
        ecgParmeTmp.gridOtherWidth,
        ecgParmeTmp.gridOtherHeight
      )

      /**
       * 向量心电图 【暂无数据】
       * 调用 zqVector.draw_ecg_vector()
       */
      // let vectorData = {};
      // let vectorDllUrl = ctx + "ecg/list/getAnalysisData/"+ecgId + "/1";
      // zqCommon.httpRequest({async: false, url : vectorDllUrl, method : "get"}).then(data => {
      //     if(data.data){
      //         vectorData = data;
      //     }
      //     },(error)=>{
      //     console.log(error);
      // })
      // NEED_REQUEST_DATA_VECTOR 应该为 上个接口的 data.data （赋值后的 vectorData.data）
      const NEED_REQUEST_DATA_VECTOR = VECTOR_DATA
      zqVector.draw_ecg_vector(
        'vector_ecg',
        ecgJson,
        NEED_REQUEST_DATA_VECTOR,
        ecgParmeTmp,
        0,
        ecgParmeTmp.gridOtherWidth,
        ecgParmeTmp.gridOtherHeight
      )
    },

    // 选项修改，仅修改常规心电图
    selectChange() {
      const ecgParmeTmp = deepClone(ecgParme)
      const { leadIndex, layoutIndex, speed, gain, multiple, beginMs } = this.vueEcgParme
      const { gridWidth } = ecgParmeTmp

      ecgParmeTmp.gridHeight = document.body.offsetHeight - 160
      // 常规心电图
      if (this.activeName === '常规心电图') {
        ecgParmeTmp.leadIndex = leadIndex
        ecgParmeTmp.layoutIndex = layoutIndex
        ecgParmeTmp.speed = speed * multiple
        ecgParmeTmp.gain = gain * multiple
        ecgParmeTmp.multiple = multiple
        ecgParmeTmp.begin_ms = beginMs * 1000
      } else {
        ecgParmeTmp.leadIndex = -1
        ecgParmeTmp.layoutIndex = 4
        ecgParmeTmp.speed = 25
        ecgParmeTmp.gain = 10
        ecgParmeTmp.multiple = 1
      }
      if (leadIndex < 0) {
        /**
         * 常规心电绘制
         * 调用 zqStandard.draw_ecg_standard()
         */
        if (gridWidth !== undefined) {
          zqStandard.draw_ecg_standard(
            'standard_ecg',
            ecgJson,
            ecgParmeTmp,
            paramset,
            0,
            gridWidth,
            ecgParmeTmp.gridHeight
          )
        }
      } else {
        zqStandard.draw_ecg_single('standard_ecg', ecgJson, ecgParmeTmp, 0, gridWidth, ecgParmeTmp.gridHeight)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ecgBox {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  border-radius: 0.625vw;

  ::v-deep .el-tabs__content {
    padding: 1.2vw 0 0.8vw;
  }
  .applicationCustomTabs {
    ::v-deep .el-tabs__nav.is-top .el-tabs__item.is-top {
      height: 2.6vw;
      line-height: 2.6vw;
      font-size: 0.73vw;
    }
  }
  .detailInfo {
    padding: 0 1.2vw;
    margin: 0 0 0.65vw 0;
  }
  .operateBar {
    height: 38px;
    line-height: 38px;
    margin: 0 0 0.65vw 0;
    padding: 0 1.2vw;
    .startTime {
      width: 64px;
      padding: 0 24px 0 0;
      line-height: 36px;
      display: inline-block;
    }
    .sliderSelf {
      width: 500px;
    }
    .endTime {
      width: 64px;
      padding: 0 0 0 24px;
      line-height: 36px;
      display: inline-block;
    }
    .inputSelf {
      width: calc(100% - 70px);
      max-width: 120px;
    }
    .smallMax {
      max-width: 70px;
    }
  }
  svg {
    margin: 0 1%;
    border-radius: 0;
  }
  h1,
  h2 {
    margin: 0;
  }

  &.ecgBoxWithInId {
    border-radius: 0;
    .applicationCustomTabs {
      border-radius: 0;

      ::v-deep .el-tabs__item.is-active {
        background: #ffffff;
      }
    }
    svg {
      margin: 0 1.5%;
      border-radius: 0;
    }
  }
  &.previewSty {
    svg {
      margin: 0;
      border-radius: 0;
    }
  }
}
.no-title {
  ::v-deep .el-tabs__header {
    display: none;
  }
}
</style>
