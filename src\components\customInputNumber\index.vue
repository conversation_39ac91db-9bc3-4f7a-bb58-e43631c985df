<template>
  <div class="custom-input-number">
    <el-input
      v-model="inputValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :readonly="readonly"
      :clearable="clearable"
      :size="size"
      :maxlength="maxlength"
      :prefix-icon="prefixIcon"
      :suffix-icon="suffixIcon"
      :class="customClass"
      @blur="handleBlur"
      @focus="handleFocus"
      @change="handleChange"
      @input="handleInput"
    >
      <template v-if="$slots.prepend" slot="prepend">
        <slot name="prepend" />
      </template>
      <template v-if="$slots.append" slot="append">
        <slot name="append" />
      </template>
      <template v-if="$slots.prefix" slot="prefix">
        <slot name="prefix" />
      </template>
      <template v-if="$slots.suffix" slot="suffix">
        <slot name="suffix" />
      </template>
    </el-input>
  </div>
</template>

<script>
export default {
  name: 'CustomInputNumber',
  props: {
    // 与v-model绑定的值
    value: {
      type: [String, Number],
      default: ''
    },
    // 输入框占位文本
    placeholder: {
      type: String,
      default: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否只读
    readonly: {
      type: Boolean,
      default: false
    },
    // 是否可清空
    clearable: {
      type: Boolean,
      default: false
    },
    // 输入框尺寸，可选值：medium / small / mini
    size: {
      type: String,
      default: ''
    },
    // 最大输入长度
    maxlength: {
      type: [String, Number],
      default: ''
    },
    // 输入框头部图标
    prefixIcon: {
      type: String,
      default: ''
    },
    // 输入框尾部图标
    suffixIcon: {
      type: String,
      default: ''
    },
    // 是否允许小数
    allowDecimal: {
      type: Boolean,
      default: true
    },
    // 是否允许负数
    allowNegative: {
      type: Boolean,
      default: false
    },
    // 小数位数
    decimalPlaces: {
      type: Number,
      default: null
    },
    // 自定义类名
    customClass: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      inputValue: ''
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.inputValue = newVal
      }
    }
  },
  methods: {
    // 处理输入事件
    handleInput(value) {
      // 使用cspUtils中的validateOnlyNumber函数处理输入值
      let processedValue = this.$validateOnlyNumber(value)

      // 根据允许的小数位数处理
      if (this.decimalPlaces !== null && processedValue.includes('.')) {
        const parts = processedValue.split('.')
        if (parts[1] && parts[1].length > this.decimalPlaces) {
          parts[1] = parts[1].substring(0, this.decimalPlaces)
          processedValue = parts.join('.')
        }
      }

      // 如果不允许小数，移除小数点及其后面的数字
      if (!this.allowDecimal) {
        processedValue = processedValue.split('.')[0]
      }

      // 如果不允许负数，移除负号
      if (!this.allowNegative && processedValue.startsWith('-')) {
        processedValue = processedValue.substring(1)
      }

      // 避免无限循环
      if (this.inputValue !== processedValue) {
        this.inputValue = processedValue
      }

      // 向父组件发送更新后的值
      this.$emit('input', processedValue)
    },

    // 处理失焦事件
    handleBlur(event) {
      this.$emit('blur', event)
    },

    // 处理聚焦事件
    handleFocus(event) {
      this.$emit('focus', event)
    },

    // 处理变更事件
    handleChange(value) {
      this.$emit('change', value)
    }
  }
}
</script>

<style scoped>
.custom-input-number {
  display: inline-block;
  width: 100%;
}
</style>
