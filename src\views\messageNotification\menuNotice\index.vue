<template>
  <div class="menu-notice">
    <div class="menu-notice-icon" @click="handleClick">
      <el-badge :value="msgCount">
        <i class="el-icon-message-solid" style="font-size: 20px" />
      </el-badge>
    </div>
    <MessageNotice ref="messageNotice" @refreshMessage="refreshMessage" />
  </div>
</template>

<script>
import { getMessageNotificationList, markRead } from '@/api/messageNotification'
import { getUserId } from '@/utils/auth'
import MessageNotice from './component/messageNotice.vue'

export default {
  name: 'MenuNotice',
  components: {
    MessageNotice
  },
  data() {
    return {
      msgCount: null,
      timer: null,
      rightBottomCount: 0,
      showTimer: null,
      lastMessage: [] // 上一次弹出的消息
    }
  },
  created() {
    this.getMessageNotificationListFn()
    this.pollingMessageNotificationListFn()
  },
  beforeDestroy() {
    this.clearPollingMessageNotificationFn()
  },
  methods: {
    handleClick() {
      this.$refs.messageNotice.drawer = true
    },
    async getMessageNotificationListFn() {
      const res = await getMessageNotificationList({
        userId: getUserId()
      })
      if (res.code === 200) {
        this.msgCount = res.data.freshCount === 0 ? null : res.data.freshCount
        this.$refs.messageNotice && (this.$refs.messageNotice.messageList = res.data.typeList)
        // 右下角超声质控消息通知
        const ultrasoundControl =
          res.data.typeList &&
          res.data.typeList.find((item) => item.type === '超声质控') &&
          res.data.typeList.find((item) => item.type === '超声质控').messageVOList.filter((it) => it.status === 0)

        if (ultrasoundControl && ultrasoundControl.length > 0) {
          this.rightBottomMessage(ultrasoundControl)
        }
      }
    },

    // 轮询获取消息通知列表
    async pollingMessageNotificationListFn() {
      this.timer = setInterval(() => {
        this.getMessageNotificationListFn()
      }, 15000)
    },

    // 清除轮询消息通知
    clearPollingMessageNotificationFn() {
      clearInterval(this.timer)
      this.timer = null
    },

    // 右下角消息通知
    // 最多展示三条，少于三条则补充
    rightBottomMessage(ultrasoundControl) {
      if (!Array.isArray(ultrasoundControl) || !ultrasoundControl.length) return

      const h = this.$createElement
      const maxCount = 3

      for (const item of ultrasoundControl) {
        if (this.rightBottomCount >= maxCount) break // 超过最大数量就停止

        const isExist = this.lastMessage.findIndex((it) => it.id === item.id)

        if (isExist !== -1) {
          continue
        }

        setTimeout(() => {
          let instance = null
          let isReadClicked = false // 标记是否是点击"请及时查看"关闭的

          const messageVNode = h('div', [
            h('span', item.content),
            h(
              'a',
              {
                style: {
                  color: '#409eff',
                  marginLeft: '6px',
                  cursor: 'pointer'
                },
                on: {
                  click: () => {
                    // 标记为主动点击关闭，避免onClose重复处理
                    isReadClicked = true
                    instance && instance.close()
                    this.handleRead(item)
                  }
                }
              },
              '请及时查看！'
            )
          ])

          instance = this.$notify({
            title: item.title,
            message: messageVNode,
            position: 'bottom-right',
            duration: 0,
            onClose: () => {
              // 只有非主动点击关闭时才调用handleClose
              if (!isReadClicked) {
                this.handleClose()
              }
            }
          })
        }, this.rightBottomCount * 1000)

        this.rightBottomCount++
        this.lastMessage.push(item)
      }
    },

    // 清除setTimeout
    clearRightBottomMessage() {
      this.showTimer && clearTimeout(this.showTimer)
      this.showTimer = null
    },

    // 点击右下角消息通知
    async handleRead(item) {
      const res = await markRead({
        userId: getUserId(),
        messageId: item.id
      })
      if (res.code === 200) {
        this.rightBottomCount--
        this.$nextTick(() => {
          this.getMessageNotificationListFn()
          let routeConfig = null

          switch (item.title) {
            case '发起超声质控申请':
              routeConfig = {
                path: '/qualityControl/ultrasonicQualityControl/detail',
                query: {
                  id: item.bizRecord,
                  itemCode: item.bizModule
                }
              }
              break

            case '超声质控审批回复':
              routeConfig = {
                path:
                  item.bizModule === 'check'
                    ? '/receptionCenter/patientExamination'
                    : '/receptionCenter/patientReception',
                query: { id: item.bizRecord }
              }
              break
          }

          if (routeConfig) {
            this.$router.push(routeConfig)
          }
        })
      }
    },

    // 关闭右下角消息通知
    async handleClose() {
      this.rightBottomCount--
      this.$nextTick(() => {
        this.getMessageNotificationListFn()
      })
    },

    // 回调
    refreshMessage() {
      this.getMessageNotificationListFn()
    }
  }
}
</script>

<style lang="scss" scoped>
.menu-notice {
  cursor: pointer;
  padding: 0 10px;
  height: 100%;
  ::v-deep .el-badge__content.is-fixed {
    top: 10px;
    border: unset;
  }
}
</style>
