import request from '@/utils/request'

// 获取菜单列表
export function getMenuList(params) {
  return request({
    url: '/cspapi/backend/sys/menu2/tree',
    method: 'get',
    params
  })
}

// 根据id获取菜单详情
export function getMenuById(params) {
  return request({
    url: `/cspapi/backend/sys/menu2`,
    method: 'get',
    params
  })
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/cspapi/backend/sys/menu2',
    method: 'post',
    data
  })
}

// 更新菜单
export function updateMenu(data) {
  return request({
    url: '/cspapi/backend/sys/menu2',
    method: 'put',
    data
  })
}
// 删除菜单
export function deleteMenu(params) {
  return request({
    url: `/cspapi/backend/sys/menu2`,
    method: 'delete',
    params
  })
}

// 根据角色查菜单
export function getMenuByRole(roleId) {
  return request({
    url: `/cspapi/backend/sys/menu2/roleMenuTreeSelect/${roleId}`,
    method: 'get'
  })
}
