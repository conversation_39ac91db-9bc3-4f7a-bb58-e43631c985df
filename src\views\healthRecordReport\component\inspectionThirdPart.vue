<!-- 检查第三部分: 生化 -->
<template>
  <div class="inspection-third-part">
    <div class="content">
      <div class="title">生化</div>
      <div class="item">
        <el-table :data="biochemicalData" style="width: 100%" border>
          <el-table-column align="center" prop="name" label="名称" />
          <el-table-column align="center" prop="unit" label="单位" />
          <el-table-column align="center" prop="range" label="参考范围" />
          <el-table-column align="center" prop="value" label="值" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InspectionThirdPart',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    // 生化数据
    biochemicalData() {
      return (
        this.reportInfo.itemList.find((item) => item.itemCode === 'BIOCHEMISTRY') &&
        this.reportInfo.itemList.find((item) => item.itemCode === 'BIOCHEMISTRY').data.itemList
      )
    }
  }
}
</script>

<style lang="scss" scope>
.inspection-third-part {
  padding: 10px;
  .content {
    margin-top: 8px;
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
    }
  }
}
</style>
