/* eslint-disable */

export default {
  created() {
    /**
     * 模块说明
     * @module  zqSvg svg通用绘制函数
     * @rely  zqCommon 依赖zq.common.js
     */
    function ZQ_SVG() {}
    ZQ_SVG.prototype = {
      /*
       * @method  绘制线条
       * @param  {string}	svg_ecg svg
       * @param  {string}	x1, x2, y1,y2,className,styleName
       * @param  {string}	strokeDasharray 创建虚线 "2 2"
       * @param  {string}	transform  transform='rotate(90 250 250)'绕着(250,250)点旋转 90°
       */
      line(svg_ecg, x1, x2, y1, y2, className, styleName, strokeDasharray, transform, widgetid) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'line')
        widget.setAttribute('x1', x1)
        widget.setAttribute('x2', x2)
        widget.setAttribute('y1', y1)
        widget.setAttribute('y2', y2)
        widget.setAttribute('class', className)
        widget.setAttribute('style', styleName)
        strokeDasharray ? widget.setAttribute('stroke-dasharray', strokeDasharray) : ''
        transform ? widget.setAttribute('transform', transform) : ''
        widgetid ? widget.setAttribute('id', widgetid) : ''
        svg_ecg.appendChild(widget)
      },
      /*
       * @method  绘制文字
       * @param  {string}	svg_ecg svg
       * @param  {string}	styleName,className,x,y
       * @param  {string}	align 文字水平对齐 start/middle/end 左/中/右
       * @param  {string}	baseline 文字垂直对齐 auto/Middle/Hanging   上/中/下
       * @param  {string}	content 文字内容
       * @param  {string}	transform 旋转transform='rotate(90 250 250)'绕着(250,250)点旋转 90°
       */
      text(svg_ecg, className, x, y, styleName, align, baseline, content, transform, widgetid) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'text')
        widget.setAttribute('class', className)
        widget.setAttribute('x', x)
        widget.setAttribute('y', y)
        widget.setAttribute('style', styleName)
        widget.setAttribute('text-anchor', align || 'start')
        widget.setAttribute('dominant-baseline', baseline)
        widget.textContent = content
        transform ? widget.setAttribute('transform', transform) : ''
        widgetid ? widget.setAttribute('id', widgetid) : ''
        svg_ecg.appendChild(widget)
      },
      /*
       * @method  绘制矩形
       * @param  {string}	svg_ecg svg
       * @param  {string}	styleName fill:"+fillColor+";stroke:"+strokeColor+";stroke-width:"+strokeWidth
       * @param  {string} className,x,y,w,h
       * @param  {string} strokeDasharray 边框虚线 "2 2"
       */
      rect(svg_ecg, className, x, y, w, h, styleName, strokeDasharray, widgetid) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
        widget.setAttribute('class', className)
        widget.setAttribute('x', x)
        widget.setAttribute('y', y)
        widget.setAttribute('width', w)
        widget.setAttribute('height', h)
        widget.setAttribute('style', styleName)
        strokeDasharray ? widget.setAttribute('stroke-dasharray', strokeDasharray) : ''
        widgetid ? widget.setAttribute('id', widgetid) : ''
        svg_ecg.appendChild(widget)
      },
      /*
       * @method  绘制圆
       * @param  {string}	svg_ecg svg
       * @param  {string}	styleName,className,cx,cy,cr
       */
      circle(svg_ecg, cx, cy, cr, styleName, className, strokeDasharray) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'circle')
        widget.setAttribute('cx', cx)
        widget.setAttribute('cy', cy)
        widget.setAttribute('r', cr)
        widget.setAttribute('style', styleName)
        widget.setAttribute('class', className)
        strokeDasharray ? widget.setAttribute('stroke-dasharray', strokeDasharray) : '' // "3 5"
        svg_ecg.appendChild(widget)
      },
      /*
       * @method  绘制椭圆
       * @param  {string}	svg_ecg svg
       * @param  {string}	styleName,className,cx,cy,rx,ry
       */
      ellipse(svg_ecg, cx, cy, rx, ry, styleName, className) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'ellipse')
        widget.setAttribute('cx', cx)
        widget.setAttribute('cy', cy)
        widget.setAttribute('rx', rx)
        widget.setAttribute('ry', ry)
        widget.setAttribute('style', styleName)
        widget.setAttribute('class', className)
        svg_ecg.appendChild(widget)
      },
      /*
       * @method  绘制折线
       * @param  {string}	svg_ecg svg
       * @param  {string}	styleName,className
       * @param  {string}	data 折线坐标点
       */
      polyline(svg_ecg, className, data, styleName, widgetid) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'polyline')
        widget.setAttribute('class', className)
        widget.setAttribute('points', data)
        widget.setAttribute('style', styleName)
        widgetid ? widget.setAttribute('id', widgetid) : ''
        svg_ecg.appendChild(widget)
      },
      /*
       * @method  绘制多边形
       * @param  {string}	svg_ecg svg
       * @param  {string}	styleName,className
       * @param  {string}	data 多边形坐标点
       */
      polygon(svg_ecg, data, styleName, className) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'polygon')
        widget.setAttribute('points', data)
        widget.setAttribute('style', styleName)
        widget.setAttribute('class', className)
        svg_ecg.appendChild(widget)
      },
      /*
       * @method  绘制路径
       * @param  {string}	svg_ecg svg
       * @param  {string}	styleName, "fill:"+fillColor+";stroke:"+strokeColor+";stroke-width:"+strokeWidth
       * @param  {string} className
       * @param  {string}	data 路径坐标点
       */
      path(svg_ecg, className, data, styleName, widgetid) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'path')
        widget.setAttribute('class', className)
        widget.setAttribute('d', data)
        widget.setAttribute('style', styleName)
        widgetid ? widget.setAttribute('id', widgetid) : ''
        svg_ecg.appendChild(widget)
      },
      /*
       * @method  绘制图片
       * @param  {string}	svg_ecg svg
       * @param  {string}	styleName,className，pw, ph, px, py
       * @param  {string}	url 图片路径
       */
      painter_signature(svg_ecg, pw, ph, px, py, url, styleName, className) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'image')
        widget.setAttributeNS(null, 'height', pw)
        widget.setAttributeNS(null, 'width', ph)
        widget.setAttributeNS(null, 'x', px)
        widget.setAttributeNS(null, 'y', py)
        widget.setAttributeNS('http://www.w3.org/1999/xlink', 'href', url)
        widget.setAttribute('style', styleName)
        widget.setAttribute('class', className)
        svg_ecg.appendChild(widget)
      },
      /*
       * @method  绘制裁切路径
       * @param  {string}	svg_ecg
       * @param  {string}	id，指定裁切路径Id
       * @param  {string}	rectX, rectY, rectW, rectH 裁切范围
       */
      clipPath(svg_ecg, id, rectX, rectY, rectW, rectH) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'defs')
        const widgetClipPath = document.createElementNS('http://www.w3.org/2000/svg', 'clipPath')
        widgetClipPath.setAttribute('id', id)
        widget.appendChild(widgetClipPath)
        var rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
        rect.setAttribute('x', rectX)
        rect.setAttribute('y', rectY)
        rect.setAttribute('width', rectW)
        rect.setAttribute('height', rectH)
        widgetClipPath.appendChild(rect)
        svg_ecg.appendChild(widget)
      },
      /*
       * @method  绘制g组合
       * @param  {string}	svg_ecg
       * @param  {string}	id
       * @param  {string}	clipPath 裁切路径
       */
      g(svg_ecg, id, clipPath, className) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'g')
        id ? widget.setAttribute('id', id) : ''
        clipPath ? clipPath.setAttribute('clip-path', clipPath) : ''
        className ? widget.setAttribute('class', className) : ''
        svg_ecg.appendChild(widget)
      },
      /*
       * @method  上下左右偏差值获取
       * @param  {string}	svg_ecg, ecgParme, rect, gridWidth, gridHeight
       */
      painter_rect(svg_ecg, pix_mm, rect, gridWidth, gridHeight) {
        const get_clients = {}
        // 计算横纵向各有多少个网格
        get_clients.wGrid = parseInt((gridWidth - rect.left - rect.right) / pix_mm / 5)
        get_clients.hGrid = parseInt((gridHeight - rect.top - rect.bottom) / pix_mm / 5)
        // 计算网格左右上下的差值
        get_clients.w_error_amount = (gridWidth - rect.left - rect.right - get_clients.wGrid * (pix_mm * 5)) / 2
        get_clients.h_error_amount = (gridHeight - rect.top - rect.bottom - get_clients.hGrid * (pix_mm * 5)) / 2
        get_clients.left = rect.left + get_clients.w_error_amount
        get_clients.right = rect.right + get_clients.w_error_amount
        get_clients.top = rect.top + get_clients.h_error_amount
        get_clients.bottom = rect.bottom + get_clients.h_error_amount
        return get_clients
      },
      /*
       * @method  网格绘制
       * @param  {string}	svg_ecg, svg_elem_id, ecgParme, out_rect, gridWidth, gridHeight
       */
      painter_grid(svg_ecg, svg_elem_id, ecgParme, out_rect, gridWidth, gridHeight) {
        const multiple = parseInt(ecgParme.multiple)
        // 点
        const dotGrid = svg_ecg.getElementById(`dotGrid_${svg_elem_id}`)
        // 线
        const lineGrid = svg_ecg.getElementById(`lineGrid_${svg_elem_id}`)

        const val = dotGrid.getAttribute('d')
        console.log(`multiple`, multiple)
        if (val == null) {
          let dotGridValue = ''
          dotGrid.setAttribute(
            'style',
            `fill:transparent;stroke:${ecgParme.dotColor};stroke-width:${ecgParme.dotLineWidth}`
          )
          for (let i = 0; i < (gridWidth - out_rect.left - out_rect.right) / ecgParme.pix_mm; i += multiple) {
            if (i % (5 * multiple) == 0) {
              continue
            }
            for (let j = 0; j < (gridHeight - out_rect.top - out_rect.bottom) / ecgParme.pix_mm; j += multiple) {
              if (j % (5 * multiple) == 0) {
                continue
              }
              dotGridValue += `M${i * ecgParme.pix_mm + out_rect.left} ${j * ecgParme.pix_mm + out_rect.top} h 1 `
            }
          }
          dotGrid.setAttribute('d', dotGridValue)

          let lineGridvalue = ''
          lineGrid.setAttribute(
            'style',
            `fill:transparent;stroke:${ecgParme.gridColor};stroke-width:${ecgParme.gridLineWidth}`
          )
          // for (let i = 0; i <= ((gridWidth - out_rect["left"] - out_rect["right"]) / ecgParme.pix_mm); i += (5 * multiple)) {
          //     lineGridvalue += "M" + (i * ecgParme.pix_mm + out_rect["left"]) + " " + (out_rect["top"]) + " v " + (gridHeight - out_rect["top"] - out_rect["bottom"]) + " ";
          // }
          // for (let j = 0; j <= ((gridHeight - out_rect["top"] - out_rect["bottom"]) / ecgParme.pix_mm); j += (5 * multiple)) {
          //     lineGridvalue += "M" + (out_rect["left"]) + " " + (out_rect["top"] + j * ecgParme.pix_mm) + " h " + (gridWidth - out_rect["left"] - out_rect["right"]) + " ";
          // }
          for (let i = 0; i <= out_rect.wGrid; i++) {
            lineGridvalue += `M${i * 5 * multiple * ecgParme.pix_mm + out_rect.left} ${out_rect.top} v ${
              gridHeight - out_rect.top - out_rect.bottom
            } `
          }
          for (let j = 0; j <= out_rect.hGrid; j++) {
            lineGridvalue += `M${out_rect.left} ${out_rect.top + j * 5 * multiple * ecgParme.pix_mm} h ${
              gridWidth - out_rect.left - out_rect.right
            } `
          }
          lineGrid.setAttribute('d', lineGridvalue)
        }
      },
      /*
       * @method  网格绘制无网点
       * @param  {string}	svg_ecg, svg_elem_id, ecgParme, out_rect, gridWidth, gridHeight
       */
      painter_line_grid(svg_ecg, svg_elem_id, ecgParme, pix_mm, out_rect, gridWidth, gridHeight) {
        // 线
        const lineGrid = svg_ecg.getElementById(`lineGrid_${svg_elem_id}`)
        const val = lineGrid.getAttribute('d')
        if (val == null) {
          let lineGridvalue = ''
          lineGrid.setAttribute(
            'style',
            `fill:transparent;stroke:${ecgParme.gridColor};stroke-width:${ecgParme.gridLineWidth}`
          )
          for (let i = 0; i <= out_rect.wGrid; i++) {
            lineGridvalue += `M${i * 5 * pix_mm + out_rect.left} ${out_rect.top} v ${
              gridHeight - out_rect.top - out_rect.bottom
            } `
          }
          for (let j = 0; j <= out_rect.hGrid; j++) {
            lineGridvalue += `M${out_rect.left} ${out_rect.top + j * 5 * pix_mm} h ${
              gridWidth - out_rect.left - out_rect.right
            } `
          }
          lineGrid.setAttribute('d', lineGridvalue)
        }
      },
      /*
       * @method  定标电压数据换算
       * @param  {string}	pix_mm, px, py,
       * @param  {string}	gain 高度为增益高度 1网格 = 5mm 10mm/mv纵向占2个网格
       * @param  {string}	speed 中间部分横向宽度 = 纸速 / 5 25mm/s横向占1个网格
       */
      set_gain_point(pix_mm, px, py, gain, speed) {
        var points = ''
        /*
              ---
             |   |
             |   |
            _|   |_
            从左到右，总六个点,占用 5mm */
        // var gain_w = 5.0;
        var gain_w = speed / 5
        var p_x
        var p_y
        // 1
        p_x = px
        p_y = py + (pix_mm * gain) / 2
        points = `${points + p_x},${p_y} ` // 第一个点
        // 2
        p_x = px + pix_mm
        p_y = py + (pix_mm * gain) / 2
        points = `${points + p_x},${p_y} ` // 第二个点
        // 3
        p_x = px + pix_mm
        p_y = py - (pix_mm * gain) / 2
        points = `${points + p_x},${p_y} ` // 第三个点
        // 4
        p_x = px + pix_mm + gain_w * pix_mm
        p_y = py - (pix_mm * gain) / 2
        points = `${points + p_x},${p_y} ` // 第四个点
        // 5
        p_x = px + pix_mm + gain_w * pix_mm
        p_y = py + (pix_mm * gain) / 2
        points = `${points + p_x},${p_y} ` // 第五个点
        // 6
        p_x = px + pix_mm + gain_w * pix_mm + pix_mm
        p_y = py + (pix_mm * gain) / 2
        points = `${points + p_x},${p_y} ` // 第六个点
        return points
      },
      /*
       * @method  波形颜色
       * @param  {string}	leadIdx 波形导联确定
       */
      getEcgColor(leadIdx) {
        let wave_color = 'RGB(247, 211, 85)'
        switch (leadIdx) {
          case 0:
            wave_color = 'RGB(247, 211, 85)'
            break
          case 1:
            wave_color = 'RGB(218, 37, 29)'
            break
          case 2:
            wave_color = 'RGB(80, 80, 80)'
            break
          case 3:
            wave_color = 'RGB(100, 100, 100)'
            break
          case 4:
            wave_color = 'RGB(120, 120, 120)'
            break
          case 5:
            wave_color = 'RGB(0, 146, 63)'
            break
          case 6:
            wave_color = 'RGB(241, 184, 85)'
            break
          case 7:
            wave_color = 'RGB(173, 41, 41)'
            break
          case 8:
            wave_color = 'RGB(126, 90, 143)'
            break
          case 9:
            wave_color = 'RGB(20, 20, 20)'
            break
          case 10:
            wave_color = 'RGB(113, 98, 91)'
            break
          case 11:
            wave_color = 'RGB(180, 180, 180)'
            break
          case 12:
            wave_color = 'RGB(160, 160, 160)'
            break
          case 13:
            wave_color = 'RGB(140, 140, 140)'
            break
          case 14:
            wave_color = 'RGB(131, 194, 46)'
            break
          case 15:
            wave_color = 'RGB(0, 0, 0)'
            break
          case 16:
            wave_color = 'RGB(0, 147, 221)'
            break
          case 17:
            wave_color = 'RGB(229, 129, 132)'
            break
        }
        return wave_color
      },
      /*
       * @method  12导联名称获取
       * @param  {string}	lead_sys systemType导联体系
       */
      get_12_lead_system_name(lead_sys) {
        let lead_name = []
        const lead_sys_type = [
          '标准导联体系',
          '后壁导联体系',
          '右胸导联体系',
          '右胸后壁导联体系',
          '上一肋间导联体系',
          '下一肋间导联体系',
          'FRANK导联体系',
          'CUBRERA导联体系',
          '自定义导联体系'
        ]
        const ln1 = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V1', 'V2', 'V3', 'V4', 'V5', 'V6']
        const ln2 = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V1', 'V2', 'V3', 'V7', 'V8', 'V9']
        const ln3 = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V1', 'V2', 'V3', 'V3R', 'V4R', 'V5R']
        const ln4 = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V3R', 'V4R', 'V5R', 'V7', 'V8', 'V9']
        const ln5 = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V`1', 'V`2', 'V`3', 'V`4', 'V`5', 'V`6']
        const ln6 = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V.1', 'V.2', 'V.3', 'V.4', 'V.5', 'V.6']
        const ln7 = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'I', 'E', 'C', 'A', 'M', 'H']
        const ln8 = ['aVL', 'I', '-aVR', 'II', 'aVF', 'III', 'V1', 'V2', 'V3', 'V4', 'V5', 'V6']
        const ln9 = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V1', 'V2', 'V3', 'V4', 'V5', 'V6']
        lead_name = [ln1, ln2, ln3, ln4, ln5, ln6, ln7, ln8, ln9]
        if (lead_sys < 0 && lead_sys > lead_sys_type.size() - 1) {
          return '导联体系索引越界！'
        }
        return lead_name[lead_sys]
      },
      /*
       * @method  15导联名称获取
       * @param  {string}	lead_sys systemType导联体系
       */
      get_15_lead_system_name(lead_sys) {
        var lead_name = []
        const ln1 = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V1', 'V2', 'V3', 'V4', 'V5', 'V6', 'V7', 'V8', 'V9']
        const ln2 = ['I', 'II', 'III', 'aVR', 'aVL', 'aVF', 'V1', 'V2', 'V3', 'V4', 'V5', 'V6', 'V3R', 'V4R', 'V5R']
        lead_name = [ln1, ln2]
        return lead_name[lead_sys]
      },
      /*
       * @method  18导联名称获取
       * @param  {string}	lead_sys systemType导联体系
       */
      get_18_lead_system_name() {
        const lead_name = [
          'I',
          'II',
          'III',
          'aVR',
          'aVL',
          'aVF',
          'V1',
          'V2',
          'V3',
          'V4',
          'V5',
          'V6',
          'V7',
          'V8',
          'V9',
          'V3R',
          'V4R',
          'V5R'
        ]
        return lead_name
      },
      /**
       * @method  获取打印值
       * @param key
       */
      getPrintTimeName(key) {
        let timeName = ''
        switch (key) {
          case 'uploadTime':
            timeName = '上传时间:'
            break
          case 'printTime':
            timeName = '打印时间:'
            break
          case 'checkTime':
            timeName = '检查时间:'
            break
          case 'checkTime':
            timeName = '采集时间:'
            break
          case 'reviewTime':
            timeName = '审核时间:'
            break
          case 'diagnoseTime':
            timeName = '诊断时间:'
            break
        }
        return timeName
      },
      drawScaleRule(svg_ecg, svg_elem_id, ecgParme, rect) {
        const grid_ = svg_ecg.getElementById(svg_elem_id)

        this.g(svg_ecg, `scaleRule_${svg_elem_id}`)

        const scaleRuleObj = document.getElementById(`scaleRule_${svg_elem_id}`)

        for (let i = 0; i < rect.offsetWidth / ecgParme.pix_mm; i += 0.5) {
          if (i % 25 == 0) {
            this.line(
              scaleRuleObj,
              i * ecgParme.pix_mm + rect.offsetLeft,
              i * ecgParme.pix_mm + rect.offsetLeft,
              ecgParme.gridHeightPrint - rect.bottom - 8,
              ecgParme.gridHeightPrint - rect.bottom,
              `scaleRule_line_${svg_elem_id}`,
              `stroke-width:2;stroke:${ecgParme.gridColor}`
            )
            continue
          }
          if (i % 2.5 == 0) {
            this.line(
              scaleRuleObj,
              i * ecgParme.pix_mm + rect.offsetLeft,
              i * ecgParme.pix_mm + rect.offsetLeft,
              ecgParme.gridHeightPrint - rect.bottom - 4,
              ecgParme.gridHeightPrint - rect.bottom,
              `scaleRule_line_${svg_elem_id}`,
              `stroke-width:1;stroke:${ecgParme.gridColor}`
            )
          }
        }
      }
    }
    /**
     * 模块说明
     * @module  zqCommon	通用函数
     */
    function ZqCommon() {
      this.doc = document
    }
    ZqCommon.prototype = {
      /*
       * @method  getID
       * @param  {string}	id
       * @return 获取id元素
       */
      getID(id) {
        return this.doc.getElementById(id)
      },
      /*
       * @method  getClassTag
       * @param  {string}	classTag
       * @return 获取所有class,tag元素
       */
      getClassTag(classTag) {
        return this.doc.querySelectorAll(classTag)
      },
      /*
       * @method  ajax转Promise返回获取结果
       * @param  {string}	url
       * @return 返回ajax结果
       */
      httpRequest(opts, data) {
        const promise = new Promise((resolve, reject) => {
          resolve(1)
        })
        return promise
      },
      /*
       * @method  GetPathParameter 获取当前路径url传参
       * @return 返回url中"?"符后的字串
       */
      GetPathParameter() {
        var url = window.location.href
        var theRequest = new Object()
        if (url.indexOf('?') != -1) {
          var str = url.split('?')
          const strs = str[1].split('&')
          for (var i = 0; i < strs.length; i++) {
            theRequest[strs[i].split('=')[0]] = decodeURI(strs[i].split('=')[1])
          }
        }
        return theRequest
      },
      /*
       * @method  GetPathParameter 获取当前路径url传参
       * @return 返回url中"?"符后的字串
       */
      GetPathParameterEx() {
        var url = window.location.href
        var theRequest = ''
        if (url.indexOf('/') != -1) {
          var str = url.split('/')
          theRequest = str[str.length - 1]
        }
        return theRequest
      },
      /*
       * @method  清除id下所有元素
       * @param  {string}	id
       */
      clearIdChildren(id) {
        const ele = this.getID(id)
        var { children } = ele
        if (children.length > 0) {
          for (let i = children.length - 1; i > -1; i--) {
            ele.removeChild(children[i])
          }
        }
      },
      /*
       * @method  清除所有class元素
       * @param  {string}	className
       */
      clearClassElement(className) {
        var ele = this.getClassTag(className)
        if (ele.length > 0) {
          for (let i = ele.length - 1; i > -1; i--) {
            ele[i].parentNode.removeChild(ele[i])
          }
        }
      },
      /*
       * @method  动态添加css样式文件
       * @param  {string}	cssUrl
       */
      addCss(id, cssUrl, parent) {
        const linkObj = parent.createElement('link')
        linkObj.setAttribute('href', cssUrl)
        linkObj.setAttribute('id', id)
        linkObj.setAttribute('rel', 'stylesheet')
        parent.body.appendChild(linkObj)
      },
      /*
       * @method  动态删除ss样式文件
       * @param  {string}	cssId
       */
      removecssfile(cssDocumentId) {
        cssDocumentId.parentNode.removeChild(cssDocumentId)
      },
      /*
       * @method  getMax取数组最大值
       * @param  arr.reduce(zqCommon.getMax, 0)
       */
      getMax(a, b) {
        return b > a ? b : a
      },
      /*
       * @method  getMin取数组最小值
       * @param  arr.reduce(zqCommon.getMin, 0)
       */
      getMin(a, b) {
        return b < a ? b : a
      },
      /*
       * @method  sort数组从大到小排序
       * @param
       */
      sort(a, b) {
        return b - a
      },
      /*
       * @method  sort数组从小到大排序
       * @param
       */
      resort(a, b) {
        return a - b
      },
      /*
       * @method  sort数组总和计算
       * @param  arr.reduce(zqCommon.count, 0)
       */
      count(total, value, index, array) {
        return total + value
      },
      /*
       * @method  图片转换成base64
       * @param  img
       */
      drawBase64Image(img) {
        var canvas = document.createElement('canvas')
        canvas.width = img.width
        canvas.height = img.height
        var ctx = canvas.getContext('2d')
        ctx.drawImage(img, 0, 0, img.width, img.height)
        var dataURL = canvas.toDataURL('image/png')
        return dataURL
      },
      /*
       * @method  url转换图片
       * @param  url
       */
      getBase64Image(url, callback) {
        var that = this
        var image = new Image()
        image.src = `${url}?v=${Math.random()}` // 处理缓存
        image.crossOrigin = '*' // 支持跨域图片
        image.onload = function () {
          var base64 = that.drawBase64Image(image)
          callback(base64)
          // that.$refs[ref].src = base64
        }
      },
      /**
       * @method  获取年龄
       * @param key
       * @returns {string}
       */
      switchAge(age, ageUnit) {
        if (age) {
          return age + this.switchAgeUnit(ageUnit)
        }
        return '-'
      },
      /**
       * @method  获取年龄单位
       * @param key
       * @returns {string}
       */
      switchAgeUnit(key) {
        let name = ''
        switch (key) {
          case 'Y':
            name = '岁'
            break
          case 'M':
            name = '月'
            break
          case 'D':
            name = '天'
            break
          default:
            name = '-'
            break
        }
        return name
      },
      /**
       * @method  获取性别
       * @param key
       * @returns {string}
       */
      switchSex(key) {
        let sex = ''
        switch (key) {
          case 'M':
            sex = '男'
            break
          case 'F':
            sex = '女'
            break
          case 'U':
            sex = '未知'
            break
          default:
            sex = '-'
            break
        }
        return sex
      },
      /**
       *
       * @param m
       * @returns {string}
       */
      MDHMSadd0(m) {
        return m < 10 ? `0${m}` : m
      },
      /**
       * 时间戳转时间
       * @param shijianchuo
       * @returns {string}
       */
      timestampToYMDHMS(timestamp) {
        var time = new Date(timestamp)
        var y = time.getFullYear()
        var m = time.getMonth() + 1
        var d = time.getDate()
        var h = time.getHours()
        var mm = time.getMinutes()
        var s = time.getSeconds()
        return `${y}-${this.MDHMSadd0(m)}-${this.MDHMSadd0(d)} ${this.MDHMSadd0(h)}:${this.MDHMSadd0(
          mm
        )}:${this.MDHMSadd0(s)}`
      },
      /*
       * @method  波形颜色
       * @param  {string}	leadIdx 波形导联确定
       */
      getRRColor(type) {
        let wave_color = '#00FF00'
        switch (type) {
          case 'N':
            wave_color = '#00FF00'
            break
          case 'S':
            wave_color = '#FF2F18'
            break
          case 'V':
            wave_color = '#000082'
            break
          case 'P':
            wave_color = '#00FFFF'
            break
          case 'X':
            wave_color = '#0000FF'
            break
          case 'O':
            wave_color = '#FFFF80'
            break
        }
        return wave_color
      },
      getServerSystemTime(flag, print, svg_elem_id, gridWidth, rightUp, rightDown, rect, title, id) {
        // $.ajax({
        //     async: false,
        //     url : "/ecg/list/getSystemTime",
        //     type: "get",
        //     success: function(data) {
        //         if(flag == 'up'){
        //             zqSvg.text(print,"text_" + svg_elem_id,
        //                 gridWidth - rect["right"], 20,
        //                 "font-size:14px", "end", "Hanging", rightUp + data.msg
        //             )
        //         }
        //         else{
        //             zqSvg.text(print,"text_" + svg_elem_id,
        //                 gridWidth - rect["right"], 40,
        //                 "font-size:14px", "end", "Hanging", rightDown + data.msg
        //             )
        //         }
        //     },
        //     error: function(error) {
        //     }
        // });
        // zqCommon.httpRequest({async: false, url : '/ecg/list/getSystemTime', method : "get"}).then(data => {
        //     if(flag == 'up'){
        //         zqSvg.text(print,"text_" + svg_elem_id,
        //             gridWidth-ecgParme.pix_mm*2, 38,
        //             "font-size:14px", "end", "Hanging", rightUp + data.msg
        //         )
        //     }
        //     else{
        //         zqSvg.text(print,"text_" + svg_elem_id,
        //             gridWidth-ecgParme.pix_mm*2, 55,
        //             "font-size:14px", "end", "Hanging", rightDown + data.msg
        //         )
        //     }
        // },(error)=>{
        // })
      }
    }
    const zqSvg = new ZQ_SVG()
    window.zqCommon = new ZqCommon()
    /**
     * 模块说明
     * @module  zqDataProcessing	 zqecg数据二次解析
     * @rely  zqDataResolution	 依赖zq.data..resolution.js
     */
    function ZqDataProcessing() {
      this.UNIT32_SAMPLE_RATE = 1000
    }
    ZqDataProcessing.prototype = {
      /**
       * @method  get_r_pos_array  获取某一导R波位置存入数组
       * @param oneLeadData 某一导波形数据
       * @returns 返回某一导R波位置存入数组
       */
      get_r_pos_array(oneLeadData) {
        // 获取R波所有位置点--二进制的位置
        const r_pos_arrays = zqDataResolution.get_r_wave_position(oneLeadData, oneLeadData.length)
        if (r_pos_arrays == 0) return -1
        return r_pos_arrays
      },
      /**
       * @method  get_r_data_array  获取某一导R波数值存入数组
       * @param oneLeadData 某一导波形数据
       * @returns 返回某一导R波数值存入数组
       */
      get_r_data_array(oneLeadData) {
        var r_data_arrays = []
        const r_pos_arrays = this.get_r_pos_array(oneLeadData)
        for (let i = 0; i < r_pos_arrays.length; i++) {
          let newData = oneLeadData[r_pos_arrays[i]]
          if (newData > 2000) {
            newData = 2000
          } else if (newData < 200) {
            newData = 200
          }
          r_data_arrays.push(newData)
        }
        return r_data_arrays
      },
      /**
       * @method  get_r_r_pos_array  获取某一导R波相邻定位差值存入数组,即RR值
       * @param oneLeadData 某一导波形数据
       * @returns 返回某一导R波相邻差值存入数组,平均RR间期,HR
       */
      get_r_r_pos_array(oneLeadData) {
        zqDataResolution.init_var()
        const r_pos_arrays = this.get_r_pos_array(oneLeadData)
        var r_r_arrays = []
        var r_interval_sum = 0
        var r_interval_len = r_pos_arrays.length - 1
        for (let i = 0; i < r_pos_arrays.length - 1; i++) {
          const newData = r_pos_arrays[i + 1] - r_pos_arrays[i]
          // if(newData > 2000){
          //     newData = 2000
          // }else if(newData < 200){
          //     newData = 200
          // }
          r_r_arrays.push(newData)
          r_interval_sum += newData
        }
        // 平均RR间期
        var RR_pos = parseInt(r_interval_sum / r_interval_len)
        // HR
        const heart_rate = parseInt((60 * this.UNIT32_SAMPLE_RATE) / RR_pos)
        return [r_r_arrays, heart_rate, RR_pos]
      },
      /**
       * @method  get_rr_average  获取平均RR间期
       * @param r_pos_array 某一导R波位置
       * @returns 返回平均RR间期
       */
      get_rr_average(r_pos_array) {
        if (r_pos_array == 0) return -1
        var r_interval_len = r_pos_array.length - 1
        var r_interval_array = new Array(r_interval_len)
        var r_interval_sum = 0
        for (var i = 0; i < r_interval_len; i++) {
          r_interval_array[i] = r_pos_array[i + 1] - r_pos_array[i]
          r_interval_sum += r_interval_array[i]
        }
        var RR_pos = parseInt(r_interval_sum / r_interval_len)
        return RR_pos
      },
      /**
       * @method  get_QT_pos_array  获取某一导Q、T波存入数组
       * @param oneLeadData 某一导波形数据
       * @param QTPosition 赋值对象
       */
      get_QT_pos_array(oneLeadData, QTPosition) {
        // 获取第二导R波数组位置点
        const r_pos_array = zqDataResolution.get_r_wave_position(oneLeadData, oneLeadData.length)

        // 获取平均RR间期
        var RR_pos = this.get_rr_average(r_pos_array)
        // HR算法
        const heart_rate = parseInt((60 * this.UNIT32_SAMPLE_RATE) / RR_pos)
        zqDataResolution.qrs_xt(oneLeadData, oneLeadData.length)
        for (let i = 0; i < r_pos_array.length; i++) {
          var rPosition = r_pos_array[i]
          var qBegin = zqDataResolution.get_medq_begin(rPosition, heart_rate, oneLeadData, 10000)
          QTPosition.qBegin_pos_arr.push(qBegin)
          var pPosition = zqDataResolution.get_medp_position(oneLeadData, qBegin)
          zqDataResolution.p_av_xt(oneLeadData, pPosition)
          var pBegin = zqDataResolution.get_medp_begin(pPosition, heart_rate, oneLeadData, 10000)
          var pEnd = zqDataResolution.get_medp_end(pPosition, heart_rate, oneLeadData, 10000)
          var sEnd = zqDataResolution.get_meds_end(rPosition, qBegin, heart_rate, oneLeadData)
          var tPosition = zqDataResolution.get_medt_position(sEnd, heart_rate, oneLeadData, 10000)
          zqDataResolution.t_av_xt(oneLeadData, tPosition, rPosition, oneLeadData.length)
          var tBegin = zqDataResolution.get_medt_begin(
            oneLeadData.length,
            tPosition,
            rPosition,
            heart_rate,
            oneLeadData,
            10000
          )
          var tEnd = zqDataResolution.get_medt_end(
            oneLeadData.length,
            tPosition,
            rPosition,
            heart_rate,
            oneLeadData,
            10000
          )
          /* if (parseInt(tEnd - pointArray[i][1]) > 0) {
					tEnd = pointArray[i][1] - 100;
				}*/
          QTPosition.tEnd_pos_arr.push(tEnd)
        }
      },
      /**
       * @method  get_one_cardiac  获取某一导波形某一个心博区间，确定一个波峰开始结束位置，Q,T,S,P,R均不可超出这个区域,   P1，P2，Q,R,S,T1，T2
       * @param oneLeadData 某一导波形数据
       * @param QTPosition 赋值对象
       * @param peakIndex 心博索引
       */
      get_one_cardiac(oneLeadData, QTPosition, peakIndex) {
        // 获取R波所以位置点--二进制的位置
        const r_pos_array = zqDataResolution.get_r_wave_position(oneLeadData, oneLeadData.length)
        if (r_pos_array == 0) return -1
        // var index=parseInt(r_pos_array.length / 2);
        var index = peakIndex
        /* var index = begin_ms / 1000 + 2;
			if(index >= r_pos_array.length ){
				index = r_pos_array.length - 2;

			}*/
        // 获取平均RR间期
        var RR_val = this.get_rr_average(r_pos_array)
        QTPosition.RR_pos = RR_val
        var startPoint = r_pos_array[index] - parseInt(RR_val * 0.4)
        var endPoint = r_pos_array[index] + parseInt(RR_val * 0.6)
        var pointArray = []
        pointArray[0] = startPoint
        pointArray[1] = endPoint
        return pointArray
      },
      /**
       * @method  get_one_Q_pos  Q波在某一导波形某一个心博定位
       * @param oneLeadData 某一导波形数据
       * @param peakIndex 心博索引
       * @returns 返回某一导波形某一个心博Q定位
       */
      get_one_Q_pos(oneLeadData, peakIndex) {
        // 获取第二导R波数组位置点
        const r_pos_array = zqDataResolution.get_r_wave_position(oneLeadData, oneLeadData.length)
        // var index=parseInt(r_pos_array.length / 2);
        var index = peakIndex
        var rPosition = r_pos_array[index]
        // 获取平均RR间期
        var RR_pos = this.get_rr_average(r_pos_array)
        // HR算法
        const heart_rate = parseInt((60 * this.UNIT32_SAMPLE_RATE) / RR_pos)
        zqDataResolution.qrs_xt(oneLeadData, oneLeadData.length)
        var qBegin = zqDataResolution.get_medq_begin(rPosition, heart_rate, oneLeadData, 10000)
        return qBegin
      },
      /**
       * @method  get_all_cardiac  获取所有波形某一导心博区间
       * @param oneLeadData 所有波形数据
       * @param peakIndex 心博索引
       */
      get_all_cardiac(LeadData, peakIndex) {
        var r_pos_array = []
        var pointArray = []
        for (let i = 0; i < LeadData.length; i++) {
          // var index=begin_ms / 1000 + 2;
          r_pos_array[i] = []
          r_pos_array[i] = zqDataResolution.get_r_wave_position(LeadData[i], LeadData[i].length)
          if (r_pos_array[i] == 0) return -1
          // var index=11;
          // var index=parseInt(r_pos_array[i].length / 2);
          const index = peakIndex
          /* if(index >= r_pos_array[i].length ){
					index = r_pos_array[i].length - 2;

				}*/
          // 获取平均RR间期
          var RR_pos = this.get_rr_average(r_pos_array[i])
          var startPoint = r_pos_array[i][index] - parseInt(RR_pos * 0.4)
          var endPoint = r_pos_array[i][index] + parseInt(RR_pos * 0.6)
          pointArray[i] = []
          pointArray[i][0] = startPoint
          pointArray[i][1] = endPoint
        }
        return pointArray
      },
      /**
       * @method  getT_all_Position  获取所有波形某一个心博T位置
       * @param LeadData
       * @param peakIndex
       */
      getT_all_Position(LeadData, peakIndex) {
        var r_pos_array = []
        var TpointArray = []
        for (let i = 0; i < LeadData.length; i++) {
          // var index=begin_ms / 1000 + 2;
          r_pos_array[i] = []
          r_pos_array[i] = zqDataResolution.get_r_wave_position(LeadData[i], LeadData[i].length)
          if (r_pos_array[i] == 0) return -1
          // var index=11;
          // var index=parseInt(r_pos_array[i].length / 2);
          var index = peakIndex
          /* if(index >= r_pos_array[i].length ){
					index = r_pos_array[i].length - 2;

				}*/
          var rPosition = r_pos_array[i][index]
          // 获取平均RR间期
          var RR_pos = this.get_rr_average(r_pos_array[i])
          // HR算法
          const heart_rate = parseInt((60 * this.UNIT32_SAMPLE_RATE) / RR_pos)

          // var pointArray = this.getT_all_Point(LeadData);
          zqDataResolution.qrs_xt(LeadData[i], LeadData[i].length)
          var qBegin = zqDataResolution.get_medq_begin(rPosition, heart_rate, LeadData[i], 10000)
          var pPosition = zqDataResolution.get_medp_position(LeadData[i], qBegin)
          zqDataResolution.p_av_xt(LeadData[i], pPosition)
          var pBegin = zqDataResolution.get_medp_begin(pPosition, heart_rate, LeadData[i], 10000)
          var pEnd = zqDataResolution.get_medp_end(pPosition, heart_rate, LeadData[i], 10000)
          var sEnd = zqDataResolution.get_meds_end(rPosition, qBegin, heart_rate, LeadData[i])
          var tPosition = zqDataResolution.get_medt_position(sEnd, heart_rate, LeadData[i], 10000)
          zqDataResolution.t_av_xt(LeadData[i], tPosition, rPosition, LeadData[i].length)
          var tBegin = zqDataResolution.get_medt_begin(
            LeadData[i].length,
            tPosition,
            rPosition,
            heart_rate,
            LeadData[i],
            10000
          )
          var tEnd = zqDataResolution.get_medt_end(
            LeadData[i].length,
            tPosition,
            rPosition,
            heart_rate,
            LeadData[i],
            10000
          )
          /* if (parseInt(tEnd - pointArray[i][1]) > 0) {
					tEnd = pointArray[i][1] - 100;
				}*/
          TpointArray[i] = tEnd

          /* this.pointsJson["pBegin"] = pBegin;
				this.pointsJson["pEnd"] = pEnd;
				this.pointsJson["QBegin"] = qBegin;
				this.pointsJson["sEnd"] = sEnd;
				this.pointsJson["tBegin"] = tBegin;
				this.pointsJson["tEnd"] = tEnd;
				this.pointsJson["tPosition"] = tPosition
				this.pointsJson["rPosition"] = rPosition*/
        }
        return TpointArray
      },
      /**
       * @method  getPointsPosition  获取某一导波形Q,R,S,T1,T2
       * @param QTPosition 赋值对象
       */
      getPointsPosition(oneLeadData, QTPosition) {
        const pointsJson = {}
        var r_pos_array = []
        // 获取一导R波数组位置点
        r_pos_array = zqDataResolution.get_r_wave_position(oneLeadData, oneLeadData.length)
        var rPosition = r_pos_array[1]
        // 获取平均RR间期
        var RR_pos = this.get_rr_average(r_pos_array, QTPosition)
        // HR算法
        const heart_rate = parseInt((60 * this.UNIT32_SAMPLE_RATE) / RR_pos)

        var pointArray = this.get_one_cardiac(oneLeadData, QTPosition, 1)
        zqDataResolution.qrs_xt(oneLeadData, oneLeadData.length)
        var qBegin = zqDataResolution.get_medq_begin(rPosition, heart_rate, oneLeadData, 10000)
        var pPosition = zqDataResolution.get_medp_position(oneLeadData, qBegin)
        zqDataResolution.p_av_xt(oneLeadData, pPosition)
        var pBegin = zqDataResolution.get_medp_begin(pPosition, heart_rate, oneLeadData, 10000)
        var pEnd = zqDataResolution.get_medp_end(pPosition, heart_rate, oneLeadData, 10000)
        var sEnd = zqDataResolution.get_meds_end(rPosition, qBegin, heart_rate, oneLeadData)
        var tPosition = zqDataResolution.get_medt_position(sEnd, heart_rate, oneLeadData, 10000)
        zqDataResolution.t_av_xt(oneLeadData, tPosition, rPosition, oneLeadData.length)
        var tBegin = zqDataResolution.get_medt_begin(
          oneLeadData.length,
          tPosition,
          rPosition,
          heart_rate,
          oneLeadData,
          10000
        )
        var tEnd = zqDataResolution.get_medt_end(
          oneLeadData.length,
          tPosition,
          rPosition,
          heart_rate,
          oneLeadData,
          10000
        )
        if (parseInt(tEnd - pointArray[1]) > 0) {
          tEnd = pointArray[1] - 100
        }
        pointsJson.pBegin = pBegin
        pointsJson.pEnd = pEnd
        pointsJson.QBegin = qBegin
        pointsJson.sEnd = sEnd
        pointsJson.tBegin = tBegin
        pointsJson.tEnd = tEnd
        pointsJson.tPosition = tPosition
        pointsJson.rPosition = rPosition
        return pointsJson
      },
      /**
       * @method  get_rr_dif  获取某一导波形RR差值
       * @param r_pos_array 某一导R波
       * @returns 返回某一导R波差值存入数组
       */
      get_rr_dif(r_pos_array) {
        var RR_pos = []
        if (r_pos_array == 0) return -1
        for (let i = 1; i < r_pos_array.length; i++) {
          RR_pos[i - 1] = r_pos_array[i] - r_pos_array[i - 1]
        }
        return RR_pos
      },
      /**
       * @method  getPos_all  获取所有波形某一导心博区间
       * @param r_pos_array 某一导R波
       * @returns 返回某一导R波差值存入数组
       */
      getPos_all(r_pos_array) {
        var pos = []
        if (r_pos_array == 0) return false
        for (let i = 0; i < r_pos_array.length - 1; i++) {
          const newData = r_pos_array[i + 1] - r_pos_array[i]
          // pos[i-1] = r_pos_array[i-1] + (r_pos_array[i] - r_pos_array[i-1]) / 2
          pos.push(newData)
        }
        return pos
      },
      /**
       * @method  get_all_hr  获取所有波形hr
       * @param rr 所有波形rr平均值
       * @returns 返回所有波形hr
       */
      get_all_hr(rr) {
        var HR_pos = []
        for (let i = 0; i < rr.length; i++) {
          HR_pos[i] = parseInt((60 * 1000) / rr[i])
        }
        return HR_pos
      }
    }
    const zqDataProcessing = new ZqDataProcessing()

    /**
     * 模块说明
     * @module  zqDataResolution	 zqecg数据解析
     */
    function ZqDataResolution() {
      this.Auto_3_x = new Array(33).fill(0)
      this.Auto_3_y = new Array(3).fill(0)
      this.Deri_vativeX_derv = new Array(4).fill(0)
      this.Mov_Win_x = new Array(32).fill(0)
      this.x_derv = new Array(4).fill(0)
      this.Mov_Win_ptr = 0
      this.Mov_Win_sum = 0
      this.qR = 0
      this.Rs = 0
      this.QS = 0
      this.Qr = 0
      this.rS = 0
      this.RsR = 0
      this.qrs_begin = 0
      this.p_va_z = 0
      this.p_va_f = 0
      this.p_va_zf = 0
      this.t_va_z = 0
      this.t_va_f = 0
      this.t_va_zf = 0
      this.UNIT32_SAMPLE_RATE = 1000
    }
    ZqDataResolution.prototype = {
      /**
       * @method  init_var  初始化
       */
      init_var() {
        for (let i = 0; i < 33; i++) {
          this.Auto_3_x[i] = 0
        }
        for (let i = 0; i < 3; i++) {
          this.Auto_3_y[i] = 0
        }
        for (let i = 0; i < 4; i++) {
          this.Deri_vativeX_derv[i] = 0
          this.x_derv[i] = 0
        }
        for (let i = 0; i < 32; i++) {
          this.Mov_Win_x[i] = 0
        }
        this.Mov_Win_ptr = 0
        this.Mov_Win_sum = 0
        this.qR = 0
        this.Rs = 0
        this.QS = 0
        this.Qr = 0
        this.rS = 0
        this.RsR = 0
        this.qrs_begin = 0
        this.p_va_z = 0
        this.p_va_f = 0
        this.p_va_zf = 0
        this.t_va_z = 0
        this.t_va_f = 0
        this.t_va_zf = 0
      },
      /**
       * @method  Auto_3  数据换算
       * @param inputData
       */
      Auto_3(inputData) {
        for (let i = 32; i > 0; i--) {
          this.Auto_3_x[i] = this.Auto_3_x[i - 1]
        }
        for (let i = 2; i > 0; i--) {
          this.Auto_3_y[i] = this.Auto_3_y[i - 1]
        }
        this.Auto_3_x[0] = inputData

        this.Auto_3_y[0] =
          1 * this.Auto_3_x[0] -
          2 * this.Auto_3_x[16] +
          1 * this.Auto_3_x[32] +
          2 * this.Auto_3_y[1] -
          1 * this.Auto_3_y[2]
        var outputData = this.Auto_3_y[0] / 256

        return outputData
      },
      /**
       * @method  Deri_vative  数据换算
       * @param inputData
       */
      Deri_vative(inputData) {
        var outputData
        outputData =
          2 * inputData - 1 * this.Deri_vativeX_derv[3] + 1 * this.Deri_vativeX_derv[1] - this.Deri_vativeX_derv[0] * 2
        outputData /= 8
        for (let i = 0; i < 3; i++) {
          this.Deri_vativeX_derv[i] = this.Deri_vativeX_derv[i + 1]
        }
        this.Deri_vativeX_derv[3] = inputData
        return outputData
      },
      /**
       * @method  Deri_med_vative  数据换算
       * @param inputData
       */
      Deri_med_vative(data, data_state) {
        var y = 0
        var i = 0
        if (data_state == 1) {
          for (let i = 0; i < 3; i++) this.x_derv[i] = 0
          return 0
        }
        y = data * 2 - this.x_derv[3] + this.x_derv[1] - this.x_derv[0] * 2
        y /= 8
        for (let i = 0; i < 3; i++) this.x_derv[i] = this.x_derv[i + 1]
        this.x_derv[3] = data
        return y
      },
      /**
       * @method  Mov_Win_Int  数据换算
       * @param inputData
       */
      Mov_Win_Int(inputData) {
        var temp
        var outputData
        if (++this.Mov_Win_ptr >= 32) this.Mov_Win_ptr = 0
        this.Mov_Win_sum -= this.Mov_Win_x[this.Mov_Win_ptr]
        this.Mov_Win_sum += inputData
        this.Mov_Win_x[this.Mov_Win_ptr] = inputData
        temp = this.Mov_Win_sum / 32
        if (temp > 32400) this.outputData = 32400
        else outputData = parseInt(temp)
        return outputData
      },
      /**
       * @method  GetMax_2  数据换算
       * @param inputData
       */
      GetMax_2(array, Start_Point, End_Point, isize) {
        var pp = parseInt(Start_Point)
        var max = 0
        if (End_Point >= isize || Start_Point > End_Point || Start_Point >= isize) {
          return 0
        }
        for (let i = parseInt(Start_Point); i <= parseInt(End_Point); ++i) {
          if (i >= isize) break
          if (array[i] >= max) {
            max = array[i]
            pp = i
          }
        }
        return pp
      },
      /**
       * @method  GetMin_2  数据换算
       * @param inputData
       */
      GetMin_2(array, Start_Point, End_Point, isize) {
        var pp = parseInt(Start_Point)
        var min = 0
        if (Start_Point < 0 || End_Point >= isize || Start_Point > End_Point || Start_Point >= isize) {
          return 0
        }
        for (let i = parseInt(Start_Point); i <= parseInt(End_Point); i++) {
          if (i >= isize) break
          if (array[i] <= min) {
            min = array[i]
            pp = i
          }
        }
        return pp
      },
      /**
       * @method  GetMax_2_t  数据换算
       * @param inputData
       */
      GetMax_2_t(array, Start_Point, End_Point, isize) {
        var pp = parseInt(Start_Point)
        var max = 0
        if (End_Point >= isize || Start_Point > End_Point || Start_Point >= isize) {
          return 0
        }
        max = 0
        for (let i = parseInt(Start_Point); i <= parseInt(End_Point); ++i) {
          if (i >= isize) break
          if (array[i] >= max) {
            max = array[i]
            pp = i
          }
        }
        return pp
      },
      /**
       * @method  GetMin_2_t  数据换算
       * @param inputData
       */
      GetMin_2_t(array, Start_Point, End_Point, isize) {
        var pp = Start_Point
        var min = 0
        if (Start_Point < 0 || End_Point >= isize || Start_Point > End_Point || Start_Point >= isize) {
          return 0
        }
        min = array[Start_Point]
        for (let i = Start_Point; i <= End_Point; i++) {
          if (i >= isize) break
          if (array[i] <= min) {
            min = array[i]
            pp = i
          }
        }
        return pp
      },
      /**
       * @method  get_r_wave_position  获取R定位
       * @param ecg_data 数据
       * @param data_len 数据长度
       */
      get_r_wave_position(ecg_data, data_len) {
        if (data_len <= 0) {
          return 0
        }
        var WEIFEN_data = new Array(data_len)
        var Tem1 = 0
        var Tem2 = 0
        var index
        for (index = 0; index < data_len; ++index) {
          Tem1 = this.Auto_3(ecg_data[index])
          Tem2 = this.Deri_vative(Tem1)
          WEIFEN_data[index] = this.Mov_Win_Int(Tem2 * Tem2)
        }
        var temp_weifen = 0
        var max_value = [0, 0, 0, 0, 0, 0]
        var max_value_count = 0
        var weifen_index = 0
        var rr_average = 0
        for (index = 0; index < 6; ++index) {
          weifen_index = this.GetMax_2(WEIFEN_data, index * 1.5 * 1000 + 20, (index + 1) * 1.5 * 1000, data_len)
          if (WEIFEN_data[weifen_index] > 0) {
            temp_weifen =
              WEIFEN_data[this.GetMax_2(WEIFEN_data, index * 1.5 * 1000 + 20, (index + 1) * 1.5 * 1000, data_len)]
            if (temp_weifen >= 10) {
              max_value[max_value_count++] = temp_weifen
            }
          }
        }

        var max = 0
        var min = 0
        max = max_value[this.GetMax_2(max_value, 0, max_value_count - 1, max_value_count)]
        min = max_value[this.GetMin_2(max_value, 0, max_value_count - 1, max_value_count)]
        var yu_zhi = 0
        if (max < 9) {
          rr_average = 0
          return 0
        } else if (max_value_count > 4) {
          if (max - max_value[0] < max / 4 && max - max_value[1] < max / 4) {
            yu_zhi = max * 0.5
          } else {
            yu_zhi = min * 0.6
          }
        } else {
          yu_zhi = max * 0.6
        }
        var QRS_Data = new Array(data_len / 2)
        var QRS_Count = 0
        for (index = 30; index < data_len; ++index) {
          if (WEIFEN_data[index] > yu_zhi) {
            QRS_Data[QRS_Count] = index

            QRS_Count++
          }
        }
        var rrmin_wth
        var t_r_count = 0
        var SumRR = 0
        var fj_Bg_pos = new Array(data_len / 2)
        rrmin_wth = (1000 * 3) / 20
        for (index = 0; index < QRS_Count; ++index) {
          if (index == 0) {
            fj_Bg_pos[t_r_count++] = QRS_Data[index]
          } else if (QRS_Data[index] - QRS_Data[index - 1] > rrmin_wth) {
            fj_Bg_pos[t_r_count++] = QRS_Data[index]
          }
        }
        var t_pos_array = new Array(t_r_count).fill(0)
        var t_interval_array = new Array(t_r_count).fill(0)
        var begin_wth = 0
        var end_wth = 0
        var r_pos_index = 0
        var max_jizhi_pos1 = 0
        var max_jizhi_pos2 = 0
        var min_pos = 0
        begin_wth = 1000 / 20
        end_wth = 1000 / 20
        for (index = 0; index < t_r_count; ++index) {
          if (fj_Bg_pos[index] > rrmin_wth) {
            if (fj_Bg_pos[index] + end_wth >= data_len) {
              min_pos = this.GetMin_2(ecg_data, fj_Bg_pos[index] - begin_wth, fj_Bg_pos[index], data_len)
            } else {
              min_pos = this.GetMin_2(ecg_data, fj_Bg_pos[index] - begin_wth, fj_Bg_pos[index] + end_wth, data_len)
            }
            max_jizhi_pos1 = this.GetMax_2(ecg_data, fj_Bg_pos[index] - begin_wth, min_pos, data_len)
            max_jizhi_pos2 = this.GetMax_2(ecg_data, min_pos, fj_Bg_pos[index] + end_wth, data_len)
          }
          if (ecg_data[max_jizhi_pos1] > ecg_data[max_jizhi_pos2]) {
            if (max_jizhi_pos1 != 0) {
              if (ecg_data[max_jizhi_pos1] > ecg_data[max_jizhi_pos1 + 30]) {
                t_pos_array[r_pos_index++] = max_jizhi_pos1
              } else {
                t_pos_array[r_pos_index++] = max_jizhi_pos2
              }
            }
          } else if (max_jizhi_pos2 != 0) {
            if (ecg_data[max_jizhi_pos2] > ecg_data[max_jizhi_pos2 + 30]) {
              t_pos_array[r_pos_index++] = max_jizhi_pos2
            } else {
              t_pos_array[r_pos_index++] = max_jizhi_pos1
            }
          }
          if (r_pos_index > 1) {
            t_interval_array[r_pos_index - 2] = t_pos_array[r_pos_index - 1] - t_pos_array[r_pos_index - 2]
          }
        }
        if (r_pos_index < 2) {
          rr_average = 0
          return 0
        } else if (r_pos_index > 2) {
          for (index = 0; index < r_pos_index - 2; ++index) {
            SumRR += t_interval_array[index]
          }
          rr_average = parseInt(SumRR / (r_pos_index - 2))
        } else {
          rr_average = parseInt(t_interval_array[0])
        }
        var r_pos_array = t_pos_array
        r_pos_array.length = r_pos_index
        return r_pos_array
      },
      /**
       * @method  qrs_xt  qrs换算
       * @param ecg_data 数据
       * @param data_len 数据长度
       */
      qrs_xt(sysdata, data_len) {
        var qrs_left_max = 0
        var qrs_right_max = 0
        var qrs_min = 0
        var qrs_max = 0
        var qrs_max_value = 0
        var qrs_min_value = 0
        var qrsdata_begin = 0
        var qrsdata_end = 0
        var Tem1 = 0
        var return_value = 0
        var qrs_yuzi = 0
        var qrs_count = 0
        var med_weifen = new Array(2000)
        var med_weifen_yj = new Array(2000)
        var r_down_count = 0
        var quzhi_begin = 0
        var refer_value = 0
        var left_r_count = 0
        var right_r_count = 0
        var refer_count = 0
        var refer_pos = 0
        for (var i = 0; i < data_len; i++) {
          if (i >= 2000) break
          Tem1 = this.Deri_med_vative(sysdata[i], 0)
          med_weifen_yj[i] = Tem1
          med_weifen[i] = Tem1 * Tem1
        }
        quzhi_begin = data_len * 0.2
        qrs_max_value = med_weifen[this.GetMax_2(med_weifen, quzhi_begin, data_len - 50, 10000)]
        qrs_yuzi = qrs_max_value * 0.6
        for (let i = quzhi_begin; i < data_len; i++) {
          if (med_weifen[i] > qrs_yuzi) {
            qrs_count += 1
          }
          if (qrs_count >= 3) {
            this.qrs_begin = i - qrs_count
            break
          }
        }
        for (let i = this.qrs_begin; i > 10; i--) {
          if (
            med_weifen[i] <= 1 &&
            med_weifen[i - 1] <= 1 &&
            med_weifen[i - 2] <= 1 &&
            med_weifen[i - 3] <= 1 &&
            med_weifen[i - 4] <= 1 &&
            med_weifen[i - 5] <= 1 &&
            med_weifen[i - 6] <= 1 &&
            med_weifen[i - 7] <= 1 &&
            med_weifen[i - 8] <= 1 &&
            med_weifen[i - 9] <= 1 &&
            med_weifen[i - 10] <= 1 &&
            med_weifen[i - 11] <= 1
          ) {
            refer_value = sysdata[i]
            refer_count += 1
            refer_pos = i
            break
          }
        }
        if (refer_count == 0) {
          refer_value = (sysdata[5] + sysdata[6] + sysdata[7] + sysdata[8] + sysdata[9]) / 5
          if (this.qrs_begin - 50 < quzhi_begin) {
            refer_pos = quzhi_begin
          } else {
            refer_pos = this.qrs_begin - 50
          }
        }
        qrs_min = this.GetMin_2(sysdata, refer_pos, this.qrs_begin + 80, 10000)
        qrs_max = this.GetMax_2(sysdata, refer_pos, this.qrs_begin + 80, 10000)
        if (qrs_min < qrs_max) {
          qrs_left_max = this.GetMax_2(sysdata, refer_pos, qrs_min, 10000)
          qrs_right_max = qrs_max
          for (var i = qrs_left_max - 20; i < qrs_left_max && i >= 0; i++) {
            if (sysdata[i] < sysdata[qrs_left_max]) {
              left_r_count += 1
            }
          }
        }
        if (qrs_min > qrs_max) {
          qrs_right_max = this.GetMax_2(sysdata, qrs_min, qrs_begin + 80, 10000)
          qrs_left_max = qrs_max
          for (let i = qrs_right_max; i < qrs_left_max + 20; i++) {
            if (sysdata[i] < sysdata[qrs_right_max]) {
              right_r_count += 1
            }
          }
        }
        if (sysdata[qrs_left_max] - refer_value >= 30 && sysdata[qrs_right_max] - refer_value >= 30) {
          if (qrs_left_max >= 20 && qrs_right_max - qrs_left_max >= 30 && qrs_right_max - qrs_left_max <= 100) {
            if (
              right_r_count >= 15 ||
              (left_r_count >= 15 &&
                sysdata[qrs_left_max] - sysdata[qrs_left_max - 20] >= 20 &&
                sysdata[qrs_right_max] - sysdata[qrs_right_max + 20] >= 20)
            ) {
              this.RsR = 1
            }
          }
        } else {
          if (qrs_max == qrs_left_max && sysdata[qrs_left_max] - refer_value >= 30) {
            if (sysdata[qrs_max] + sysdata[qrs_min] - 2 * refer_value >= 0) {
              this.Rs = 1
            } else {
              this.rS = 1
            }
          }
          if (qrs_max == qrs_right_max && sysdata[qrs_right_max] - refer_value >= 30) {
            if (sysdata[qrs_max] + sysdata[qrs_min] - 2 * refer_value >= 0) {
              this.qR = 1
            } else {
              this.Qr = 1
            }
          }
          if (
            sysdata[qrs_left_max] - refer_value < 50 &&
            sysdata[qrs_right_max] - refer_value < 30 &&
            refer_value - sysdata[qrs_min] > 60
          ) {
            this.QS = 1
          }
        }
        if (this.RsR == 0 && this.Rs == 0 && this.rS == 0 && this.qR == 0 && this.Qr == 0 && this.QS == 0) {
          this.rS = 1
        }
        if (this.RsR == 1) {
          return_value = 101
        }
        if (this.Rs == 1) {
          return_value = 102
        }
        if (this.rS == 1) {
          return_value = 103
        }
        if (this.qR == 1) {
          return_value = 104
        }
        if (this.Qr == 1) {
          return_value = 105
        }
        if (this.QS == 1) {
          return_value = 106
        }
        return return_value
      },
      /**
       * @method  get_medq_begin  获取q定位
       * @param r_position r定位
       * @param heart_rate hr
       * @param sysdata 数据
       * @param isize 数据长度
       */
      get_medq_begin(r_position, heart_rate, sysdata, isize) {
        var mean_rr = 0
        var return_value = 0
        var refe_vale = 0
        var q_value = 0
        var re_f = 0
        var data_begin = 0
        var q_begin_count = 0
        var q_pos = 0
        var qbgn_count = 0
        var qmax_pos = 0
        if (heart_rate != 0) {
          mean_rr = (0.15 * this.UNIT32_SAMPLE_RATE * 60) / heart_rate
        }
        if (r_position >= mean_rr) {
          data_begin = r_position - mean_rr
        }
        refe_vale = (sysdata[5] + sysdata[6] + sysdata[7] + sysdata[8] + sysdata[9]) / 5
        if (this.RsR == 1) {
          q_pos = this.GetMin_2(sysdata, data_begin, r_position, 10000)
          qmax_pos = this.GetMin_2(sysdata, data_begin, q_pos, 10000)
          if (qmax_pos >= 2) {
            for (let i = qmax_pos; i > data_begin; i--) {
              if (
                Math.abs(sysdata[i] - sysdata[i - 1]) <= 2 &&
                Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 2 &&
                Math.abs(sysdata[qmax_pos] - sysdata[i]) >= 60
              ) {
                return_value = i
                break
              }
            }
          }
          if (r_position - return_value >= 100) {
            return_value = r_position - 100
          }
        }
        if (this.Rs == 1 || this.qR == 1) {
          q_pos = this.GetMin_2(sysdata, data_begin, r_position, 10000)
          q_value = refe_vale - sysdata[q_pos]
          if (q_value >= 60 && q_pos >= 2) {
            for (let i = q_pos; i > data_begin; i--) {
              if (
                Math.abs(sysdata[i] - sysdata[i - 1]) <= 2 &&
                Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 2 &&
                Math.abs(sysdata[i] - refe_vale) <= 20
              ) {
                return_value = i
                break
              }
            }
          } else if (r_position >= 2) {
            for (var i = r_position; i > data_begin; i--) {
              if (
                Math.abs(sysdata[i] - sysdata[i - 1]) <= 2 &&
                Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 2 &&
                Math.abs(sysdata[i] - refe_vale) <= 20
              ) {
                return_value = i
                break
              }
            }
          }
          if (r_position - return_value >= 60) {
            return_value = r_position - 60
          }
        }
        if (this.rS == 1 && r_position >= 2) {
          for (let i = r_position; i > data_begin; i--) {
            if (
              Math.abs(sysdata[i] - sysdata[i - 1]) <= 2 &&
              Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 2 &&
              Math.abs(sysdata[r_position] - sysdata[i]) >= 50
            ) {
              return_value = i
              break
            }
          }
          if (r_position - return_value >= 30) {
            return_value = r_position - 30
          }
        }
        if (this.Qr == 1 || this.QS == 1) {
          q_pos = this.GetMin_2(sysdata, data_begin, r_position + 50, 10000)
          qmax_pos = this.GetMin_2(sysdata, data_begin, q_pos, 10000)
          if (sysdata[qmax_pos] >= refe_vale) {
            for (let i = q_pos; i > data_begin; i--) {
              if (sysdata[i] >= refe_vale) {
                return_value = i
                break
              }
            }
          } else if (q_pos >= 2) {
            for (let i = q_pos; i > data_begin; i--) {
              if (
                Math.abs(sysdata[i] - sysdata[i - 1]) <= 2 &&
                Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 2 &&
                Math.abs(sysdata[i] - sysdata[q_pos]) >= 100
              ) {
                return_value = i
                break
              }
            }
          }
          if (r_position - return_value >= 80) {
            return_value = r_position - 80
          }
        }
        if (r_position >= (0.03 * this.UNIT32_SAMPLE_RATE * 60) / heart_rate) {
          if (return_value == 0) {
            return_value = r_position - (0.03 * this.UNIT32_SAMPLE_RATE * 60) / heart_rate
          }
        }
        return parseInt(return_value)
      },
      /**
       * @method  get_meds_begin  获取s1定位
       * @param q_position q定位
       * @param s2_pos_array s2定位
       * @param sysdata 数据
       */
      get_meds_begin(q_pos_array, s2_pos_array, sysdata) {
        const q_s2_data_arr = sysdata.filter((val, index) => {
          return index >= q_pos_array && index <= s2_pos_array
        })
        let diff_value = q_s2_data_arr[1] - q_s2_data_arr[0]
        let s1_position = 0
        for (let i = 1; i < q_s2_data_arr.length - 1; i++) {
          if (diff_value > 0 && q_s2_data_arr[i + 1] - q_s2_data_arr[0] <= 0) {
            s1_position = i + 1 + q_pos_array
            return s1_position
          }
          diff_value = q_s2_data_arr[i + 1] - q_s2_data_arr[0]
        }
      },
      /**
       * @method  get_meds_end  获取s2定位
       * @param r_position r定位
       * @param q_begin q定位
       * @param heart_rate hr
       * @param sysdata 数据
       */
      get_meds_end(r_position, q_begin, heart_rate, sysdata) {
        var return_value = 0
        var con_end = 0
        var con_begin = 0
        var s_end_count = 0
        var send_max = 0
        var s_end_min_pos = 0
        var temp_s_cf = new Array(20)
        var refe_vale = 0
        if (q_begin >= 4) {
          refe_vale =
            (sysdata[q_begin - 4] +
              sysdata[q_begin - 3] +
              sysdata[q_begin - 2] +
              sysdata[q_begin - 1] +
              sysdata[q_begin]) /
            5
        } else {
          refe_vale = sysdata[q_begin]
        }
        con_end = r_position + (0.15 * this.UNIT32_SAMPLE_RATE * 60) / heart_rate
        s_end_min_pos = this.GetMin_2(sysdata, r_position, con_end, 10000)
        if (this.RsR == 1) {
          send_max = this.GetMax_2(sysdata, s_end_min_pos, con_end, 10000)
          for (let i = send_max + 5; i < con_end; i++) {
            if (
              Math.abs(sysdata[i] - sysdata[i - 1]) <= 2 &&
              Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 2 &&
              Math.abs(sysdata[send_max] - sysdata[i]) >= 60
            ) {
              return_value = i
              break
            }
          }
        }
        if (this.Qr == 1) {
          for (let i = r_position + 5; i < con_end; i++) {
            if (
              Math.abs(sysdata[i] - sysdata[i - 1]) <= 2 &&
              Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 2 &&
              Math.abs(sysdata[r_position] - sysdata[i]) >= 15
            ) {
              return_value = i
              break
            }
          }
        }
        if (this.QS == 1 || this.rS == 1) {
          send_max = this.GetMax_2(sysdata, s_end_min_pos, con_end, 10000)
          if (sysdata[send_max] < refe_vale) {
            for (let i = send_max + 2; i < con_end; i++) {
              if (
                Math.abs(sysdata[i] - sysdata[i - 1]) <= 2 &&
                Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 2 &&
                Math.abs(sysdata[i] - sysdata[s_end_min_pos]) >= 50
              ) {
                return_value = i
                break
              }
            }
          } else {
            for (let i = s_end_min_pos + 1; i < con_end; i++) {
              if (sysdata[i] >= refe_vale) {
                return_value = i
                break
              }
            }
          }
        }
        if (this.Rs == 1 || this.qR == 1) {
          if (refe_vale - sysdata[s_end_min_pos] >= 100) {
            for (let i = s_end_min_pos + 2; i < con_end; i++) {
              if (
                Math.abs(sysdata[i] - sysdata[i - 1]) <= 2 &&
                Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 2 &&
                Math.abs(sysdata[i] - sysdata[s_end_min_pos]) >= 40
              ) {
                return_value = i
                break
              }
            }
          } else {
            for (let i = s_end_min_pos + 2; i < con_end; i++) {
              if (Math.abs(sysdata[i] - sysdata[i - 1]) <= 2 && Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 2) {
                return_value = i
                break
              }
            }
          }
        }
        if (return_value == 0) {
          if (sysdata[r_position] + sysdata[s_end_min_pos] >= 0) {
            return_value = r_position + (0.05 * this.UNIT32_SAMPLE_RATE * 60) / heart_rate
          } else {
            for (let i = s_end_min_pos + 2; i < con_end; i++) {
              if (Math.abs(sysdata[i] - sysdata[i - 1]) <= 2 && Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 2) {
                return_value = i
                break
              }
            }
          }
        }
        return parseInt(return_value)
      },
      /**
       * @method  get_medp_position  获取p定位
       * @param sysdata 数据
       * @param med_qbegin q定位
       */
      get_medp_position(sysdata, med_qbegin) {
        var p_minpos = 0
        var p_maxpos = 0
        var refe_vale = 0
        var rf_2 = 0
        var return_value = 0
        var temax_value = 0
        var temin_value = 0
        var con_begin = med_qbegin - 150
        var con_end = med_qbegin - 30
        if (con_end <= 0) {
          return 0
        }
        p_minpos = this.GetMin_2(sysdata, con_begin, con_end, 10000)
        p_maxpos = this.GetMax_2(sysdata, con_begin, con_end, 10000)
        refe_vale = parseInt((sysdata[5] + sysdata[6] + sysdata[7] + sysdata[8] + sysdata[9]) / 5)
        if (med_qbegin >= 9) {
          rf_2 = parseInt(
            (sysdata[med_qbegin - 5] +
              sysdata[med_qbegin - 6] +
              sysdata[med_qbegin - 7] +
              sysdata[med_qbegin - 8] +
              sysdata[med_qbegin - 9]) /
              5
          )
        }
        if (rf_2 < refe_vale) {
          refe_vale = rf_2
        }
        temax_value = sysdata[p_maxpos] - refe_vale
        temin_value = refe_vale - sysdata[p_minpos]
        if (temax_value >= temin_value) {
          return_value = p_maxpos
        } else {
          return_value = p_minpos
        }

        if (return_value == 0) {
          return_value = p_maxpos
        }

        return return_value
      },
      /**
       * @method  p_av_xt  数据换算
       * @param sysdata 数据
       * @param p_pos p定位
       */
      p_av_xt(sysdata, p_pos) {
        if (p_pos < 40) {
          return
        }
        var p_min_vale = 0
        var refer_value = 0
        var p_begin = 0
        var p_end = 0
        var p_min_pos = 0
        var p_max_pos = 0
        p_begin = p_pos - 40
        p_end = p_pos + 40
        refer_value = (sysdata[5] + sysdata[6] + sysdata[7] + sysdata[8] + sysdata[9]) / 5
        if (sysdata[p_pos] > refer_value) {
          p_min_pos = this.GetMin_2(sysdata, p_begin, p_end, 10000)
          if (refer_value - sysdata[p_min_pos] >= 20) {
            if (sysdata[p_pos] - refer_value >= 20) {
              this.p_va_zf = 1
            }
          } else {
            this.p_va_z = 1
          }
        } else {
          p_max_pos = this.GetMax_2(sysdata, p_begin, p_end, 10000)
          if (sysdata[p_max_pos] - refer_value >= 20) {
            if (refer_value - sysdata[p_pos] >= 20) {
              this.p_va_zf = 1
            }
          } else {
            this.p_va_f = 1
          }
        }
      },
      /**
       * @method  get_medp_begin  获取p1定位
       * @param heart_rate hr
       * @param sysdata 数据
       * @param isize 数据长度
       */
      get_medp_begin(p_position, heart_rate, sysdata, isize) {
        var return_value = 0
        var con_end = 20
        var pmax = 0
        var pmin = 0
        var refe_vale = 0
        refe_vale = (sysdata[5] + sysdata[6] + sysdata[7] + sysdata[8] + sysdata[9]) / 5
        if (this.p_va_z == 1 && p_position >= 51) {
          for (let i = p_position - 1; i > p_position - 50; i--) {
            if (
              Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
              Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
              Math.abs(sysdata[i] - refe_vale) <= 10
            ) {
              return_value = i
              break
            }
          }
        }
        if (this.p_va_f == 1 && p_position >= 51) {
          for (let i = p_position - 1; i > p_position - 50; i--) {
            if (
              Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
              Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
              Math.abs(refe_vale - sysdata[i]) <= 10
            ) {
              return_value = i
              break
            }
          }
        }
        if (this.p_va_zf == 1 && p_position >= 51) {
          if (sysdata[p_position] > refe_vale) {
            pmin = this.GetMin_2(sysdata, p_position - 50, p_position + 50, 10000)
            if (pmin < p_position && pmin >= 3) {
              for (let i = pmin - 1; i > p_position - 50; i--) {
                if (
                  Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
                  Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
                  Math.abs(sysdata[i] - sysdata[pmin]) >= 10
                ) {
                  return_value = i
                  break
                }
              }
            } else {
              for (let i = p_position - 1; i > p_position - 50; i--) {
                if (
                  Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
                  Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
                  Math.abs(sysdata[i] - sysdata[p_position]) >= 10
                ) {
                  return_value = i
                  break
                }
              }
            }
          } else {
            pmax = this.GetMax_2(sysdata, p_position - 50, p_position + 50, 10000)
            if (pmax < p_position && pmax >= 3) {
              for (let i = pmax - 1; i > p_position - 50; i--) {
                if (
                  Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
                  Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
                  Math.abs(sysdata[pmax] - sysdata[i]) >= 10
                ) {
                  return_value = i
                  break
                }
              }
            } else if (p_position >= 3) {
              for (let i = p_position - 1; i > p_position - 50; i--) {
                if (
                  Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
                  Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
                  Math.abs(sysdata[i] - sysdata[p_position]) >= 10
                ) {
                  return_value = i
                  break
                }
              }
            }
          }
        }
        if (return_value == 0 && p_position > (0.06 * this.UNIT32_SAMPLE_RATE * 60) / heart_rate) {
          return_value = p_position - (0.06 * this.UNIT32_SAMPLE_RATE * 60) / heart_rate
        }
        return parseInt(return_value)
      },
      /**
       * @method  get_medp_end  获取p2定位
       * @param heart_rate hr
       * @param sysdata 数据
       * @param isize 数据长度
       */
      get_medp_end(p_position, heart_rate, sysdata, isize) {
        var return_value = 0
        var pmax = 0
        var pmin = 0
        var refe_vale = 0
        refe_vale = (sysdata[5] + sysdata[6] + sysdata[7] + sysdata[8] + sysdata[9]) / 5
        if (this.p_va_z == 1) {
          for (let i = p_position + 1; i < p_position + 50; i++) {
            if (
              Math.abs(sysdata[i + 1] - sysdata[i]) <= 1 &&
              Math.abs(sysdata[i + 1] - sysdata[i + 2]) <= 1 &&
              Math.abs(sysdata[i] - refe_vale) <= 10
            ) {
              return_value = i
              break
            }
          }
        }
        if (this.p_va_f == 1) {
          for (let i = p_position + 1; i < p_position + 50; i++) {
            if (
              Math.abs(sysdata[i] - sysdata[i + 1]) <= 1 &&
              Math.abs(sysdata[i + 1] - sysdata[i + 2]) <= 1 &&
              Math.abs(refe_vale - sysdata[i]) <= 10
            ) {
              return_value = i
              break
            }
          }
        }
        if (this.p_va_zf == 1 && p_position >= 50) {
          if (sysdata[p_position] > refe_vale) {
            pmin = this.GetMin_2(sysdata, p_position - 50, p_position + 50, 10000)
            if (pmin < p_position) {
              for (let i = p_position + 1; i < p_position + 50; i++) {
                if (
                  Math.abs(sysdata[i] - sysdata[i + 1]) <= 1 &&
                  Math.abs(sysdata[i + 1] - sysdata[i + 2]) <= 1 &&
                  Math.abs(sysdata[i] - sysdata[p_position]) >= 10
                ) {
                  return_value = i
                  break
                }
              }
            } else {
              for (let i = pmin + 1; i < pmin + 50; i++) {
                if (
                  Math.abs(sysdata[i] - sysdata[i + 1]) <= 1 &&
                  Math.abs(sysdata[i + 1] - sysdata[i + 2]) <= 1 &&
                  Math.abs(sysdata[i] - sysdata[pmin]) >= 10
                ) {
                  return_value = i
                  break
                }
              }
            }
          } else {
            pmax = this.GetMax_2(sysdata, p_position - 50, p_position + 50, 10000)
            if (pmax < p_position) {
              for (let i = p_position + 1; i < p_position + 50; i++) {
                if (
                  Math.abs(sysdata[i] - sysdata[i + 1]) <= 1 &&
                  Math.abs(sysdata[i + 1] - sysdata[i + 2]) <= 1 &&
                  Math.abs(sysdata[i] - sysdata[p_position]) >= 10
                ) {
                  return_value = i
                  break
                }
              }
            } else {
              for (let i = pmax + 1; i < pmax + 50; i++) {
                if (
                  Math.abs(sysdata[i] - sysdata[i + 1]) <= 1 &&
                  Math.abs(sysdata[i + 1] - sysdata[i + 2]) <= 1 &&
                  Math.abs(sysdata[pmax] - sysdata[i]) >= 10
                ) {
                  return_value = i
                  break
                }
              }
            }
          }
        }
        if (return_value == 0) {
          return_value = p_position + (0.06 * this.UNIT32_SAMPLE_RATE * 60) / heart_rate
        }
        return parseInt(return_value)
      },
      /**
       * @method  get_medt_position  获取t定位
       * @param heart_rate hr
       * @param sysdata 数据
       * @param isize 数据长度
       */
      get_medt_position(s_end, heart_rate, sysdata, isize) {
        var return_value = 0
        var con_end = 0
        var con_begin = 0
        var tem_max_pos = 0
        var tem_min_pos = 0
        var tempt_max = 0
        var tempt_min = 0
        var refer_prior_value = 0
        var refer_after_value = 0
        var refe_vale = 0
        if (heart_rate <= 0) return -1
        con_begin = s_end + (60 * this.UNIT32_SAMPLE_RATE * 0.01) / heart_rate
        con_end = s_end + (60 * this.UNIT32_SAMPLE_RATE * 0.4) / heart_rate
        if ((60 * this.UNIT32_SAMPLE_RATE * 0.35) / heart_rate <= 150) {
          con_end = s_end + 150
        }
        if (con_end >= 4) {
          refer_prior_value =
            (sysdata[con_end] +
              sysdata[con_end - 1] +
              sysdata[con_end - 2] +
              sysdata[con_end - 3] +
              sysdata[con_end - 4]) /
            5
          refer_after_value =
            (sysdata[con_end - 4] +
              sysdata[con_end - 3] +
              sysdata[con_end - 2] +
              sysdata[con_end - 1] +
              sysdata[con_end]) /
            5
        }
        if (refer_prior_value <= refer_after_value) {
          refe_vale = refer_prior_value
        } else {
          refe_vale = refer_after_value
        }
        tem_max_pos = this.GetMax_2(sysdata, con_begin, con_end, isize)
        tempt_max = sysdata[tem_max_pos] - refe_vale
        tem_min_pos = this.GetMin_2(sysdata, con_begin, con_end, isize)
        tempt_min = refe_vale - sysdata[tem_min_pos]
        if (tempt_max >= tempt_min) {
          return_value = tem_max_pos
        } else {
          return_value = tem_min_pos
        }
        if (return_value == 0) {
          return_value = s_end + (60 * this.UNIT32_SAMPLE_RATE * 0.2) / heart_rate
        }
        return return_value
      },
      /**
       * @method  t_av_xt  数据换算
       * @param sysdata 数据
       * @param t_pos t定位
       * @param r_pos r定位
       * @param data_lenth 数据长度
       */
      t_av_xt(sysdata, t_pos, r_pos, data_lenth) {
        var p_min_vale = 0
        var refer_value = 0
        var t_begin = 0
        var t_end = 0
        var t_min_pos = 0
        var t_max_pos = 0
        t_begin = (r_pos + t_pos) / 2
        t_end = data_lenth
        refer_value = sysdata[(t_pos + t_end) / 2]
        if (sysdata[t_pos] > refer_value) {
          t_min_pos = this.GetMin_2(sysdata, t_begin, t_end, 10000)
          if (refer_value - sysdata[t_min_pos] >= 50) {
            if (sysdata[t_pos] - refer_value >= 50) {
              this.t_va_zf = 1
            }
          } else {
            this.t_va_z = 1
          }
        } else {
          t_max_pos = this.GetMax_2(sysdata, t_begin, t_end, 10000)
          if (sysdata[t_max_pos] - refer_value >= 50) {
            if (refer_value - sysdata[t_pos] >= 50) {
              this.t_va_zf = 1
            }
          } else {
            this.t_va_f = 1
          }
        }
      },
      /**
       * @method  get_medt_position  获取t1定位
       * @param data_lenth 数据长度
       * @param r_pos r定位
       * @param heart_rate hr
       * @param sysdata 数据
       * @param isize 数据长度
       */
      get_medt_begin(data_lenth, t_position, r_pos, heart_rate, sysdata, isize) {
        var return_value = 0
        var tem_pos = 0
        var tmin = 0
        var tmax = 0
        var refer_vale = 0
        refer_vale = sysdata[(t_position + data_lenth) / 2]
        if (this.t_va_z == 1 && t_position >= 3) {
          for (let i = t_position - 1; i > r_pos; i--) {
            if (
              Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
              Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
              Math.abs(sysdata[t_position] - sysdata[i]) >= 40
            ) {
              return_value = i
              break
            }
          }
        }
        if (this.t_va_f == 1 && t_position >= 3) {
          for (let i = t_position - 1; i > r_pos; i--) {
            if (
              Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
              Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
              Math.abs(sysdata[i] - sysdata[t_position]) >= 40
            ) {
              return_value = i
              break
            }
          }
        }
        if (this.t_va_zf == 1) {
          if (sysdata[t_position] > refer_vale) {
            tmin = this.GetMin_2(sysdata, (t_position + r_pos) / 2, data_lenth, 10000)
            if (tmin < t_position && tmin >= 3) {
              for (let i = tmin - 1; i > r_pos; i--) {
                if (
                  Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
                  Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
                  Math.abs(sysdata[i] - sysdata[tmin]) >= 30
                ) {
                  return_value = i
                  break
                }
              }
            } else if (t_position >= 3) {
              for (let i = t_position - 1; i > r_pos; i--) {
                if (
                  Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
                  Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
                  Math.abs(sysdata[t_position] - sysdata[i]) >= 30
                ) {
                  return_value = i
                  break
                }
              }
            }
          } else {
            tmax = this.GetMax_2(sysdata, (t_position + r_pos) / 2, data_lenth, 10000)
            if (tmax < t_position && tmax >= 3) {
              for (let i = tmax - 1; i > r_pos; i--) {
                if (
                  Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
                  Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
                  Math.abs(sysdata[tmax] - sysdata[i]) >= 30
                ) {
                  return_value = i
                  break
                }
              }
            } else if (t_position >= 3) {
              for (let i = t_position - 1; i > r_pos; i--) {
                if (
                  Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
                  Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
                  Math.abs(sysdata[i] - sysdata[t_position]) >= 30
                ) {
                  return_value = i
                  break
                }
              }
            }
          }
        }
        if (return_value == 0 && t_position > this.UNIT32_SAMPLE_RATE / 13) {
          return_value = t_position - this.UNIT32_SAMPLE_RATE / 13
        }
        return parseInt(return_value)
      },
      /**
       * @method  get_medt_end  获取t2定位
       * @param t_position t定位
       * @param heart_rate hr
       * @param sysdata 数据
       * @param isize 数据长度
       */
      get_medt_end(data_lenth, t_position, r_pos, heart_rate, sysdata, isize) {
        var return_value = 0
        var tmin = 0
        var tmax = 0
        var refer_vale = 0
        refer_vale = sysdata[(t_position + data_lenth) / 2]
        if (this.t_va_z == 1) {
          for (let i = t_position + 2; i < data_lenth; i++) {
            if (
              Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
              Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
              Math.abs(sysdata[t_position] - sysdata[i]) >= 40
            ) {
              return_value = i
              break
            }
          }
        }
        if (this.t_va_f == 1) {
          for (let i = t_position + 2; i < data_lenth; i++) {
            if (
              Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
              Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
              Math.abs(sysdata[i] - sysdata[t_position]) >= 40
            ) {
              return_value = i
              break
            }
          }
        }
        if (this.t_va_zf == 1) {
          if (sysdata[t_position] > refer_vale) {
            tmin = this.GetMin_2(sysdata, (t_position + r_pos) / 2, data_lenth, 10000)
            if (tmin < t_position) {
              for (let i = t_position + 2; i < data_lenth; i++) {
                if (
                  Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
                  Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
                  Math.abs(sysdata[t_position] - sysdata[i]) >= 30
                ) {
                  return_value = i
                  break
                }
              }
            } else if (tmin >= 3) {
              for (let i = tmin - 1; i < data_lenth; i++) {
                if (
                  Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
                  Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
                  Math.abs(sysdata[i] - sysdata[tmin]) >= 30
                ) {
                  return_value = i
                  break
                }
              }
            }
          } else {
            tmax = this.GetMax_2(sysdata, (t_position + r_pos) / 2, data_lenth, 10000)
            if (tmax < t_position) {
              for (let i = t_position + 2; i < data_lenth; i++) {
                if (
                  Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
                  Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
                  Math.abs(sysdata[i] - sysdata[t_position]) >= 30
                ) {
                  return_value = i
                  break
                }
              }
            } else {
              for (let i = tmax + 2; i < data_lenth; i++) {
                if (
                  Math.abs(sysdata[i] - sysdata[i - 1]) <= 1 &&
                  Math.abs(sysdata[i - 1] - sysdata[i - 2]) <= 1 &&
                  Math.abs(sysdata[tmax] - sysdata[i]) >= 30
                ) {
                  return_value = i
                  break
                }
              }
            }
          }
        }
        if (return_value == 0) {
          return_value = t_position + this.UNIT32_SAMPLE_RATE / 13
        }
        return parseInt(return_value)
      }
    }
    const zqDataResolution = new ZqDataResolution()
    /**
     * 模块说明
     * @module  zqStandard	 标准心电图封装
     * @rely  zqSvg
     */
    function zqStandard() {
      this.patientInfoOffset = {}
      this.userInfoSpace = 20
      this.out_rect = null
    }
    zqStandard.prototype = {
      /**
       * @method  draw_ecg_standard  常规心电绘制入口函数
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_ecg_standard(svg_elem_id, ecgJson, ecgParme, paramset, isPrint, gridWidth, gridHeight) {
        this.clearContentWave(svg_elem_id)
        this.paramset = paramset
        var svg_ecg = document.getElementById(svg_elem_id)
        this.creatEcgChild(svg_ecg, svg_elem_id)
        svg_ecg.setAttribute('width', gridWidth)
        svg_ecg.setAttribute('height', gridHeight)
        this.draw_standard_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight)
      },
      /**
       * @method  creatEcgChild  svg内部组件创建
       * @param svg_ecg
       * @param svg_elem_id
       */
      creatEcgChild(svg_ecg, svg_elem_id) {
        zqSvg.g(svg_ecg, `content_${svg_elem_id}`)
        const content = document.getElementById(`content_${svg_elem_id}`)
        zqSvg.g(content, `print_${svg_elem_id}`)
        zqSvg.g(content, `wave_${svg_elem_id}`)
        zqSvg.g(content, `message_${svg_elem_id}`)
      },
      /**
       * @method  draw_standard_lead  常规心电绘制入口函数
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_standard_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight) {
        var rect = { left: 0, right: 0, top: 0, bottom: 0 }
        if (isPrint == 1) {
          // rect = {"left":15,"right":15,"top":180,"bottom":45};
          rect = { left: 30, right: 10, top: 180, bottom: 45 }
          if (ecgParme.previewEcg) {
            rect.top += 10
          }
        }
        let out_lcr = null
        switch (ecgJson.lead) {
          case 12:
            out_lcr = this.get_12_lead_lcr(ecgParme.layoutIndex)
            break
          case 15:
            out_lcr = this.get_15_lead_lcr(ecgParme.layoutIndex)
          case 18:
            out_lcr = this.get_18_lead_lcr(ecgParme.layoutIndex)
            break
        }
        ecgJson.floor = Math.floor(ecgJson.timeMs / out_lcr.col)

        var out_rect = zqSvg.painter_rect(svg_ecg, ecgParme.pix_mm, rect, gridWidth, gridHeight)
        out_rect.offsetLeft = out_rect.left + (ecgParme.speed / 5) * ecgParme.pix_mm + ecgParme.pix_mm * 2
        out_rect.offsetWidth = gridWidth - out_rect.offsetLeft - out_rect.right
        zqSvg.painter_grid(svg_ecg, svg_elem_id, ecgParme, out_rect, gridWidth, gridHeight)
        this.painter_gain(svg_ecg, svg_elem_id, ecgParme, out_rect.left, out_rect.top + (gridHeight - out_rect.top) / 2)
        switch (ecgJson.lead) {
          case 12:
            this.draw_12_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, isPrint, gridWidth, gridHeight)
            break
          case 15:
            this.draw_15_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, isPrint, gridWidth, gridHeight)
            break
          case 18:
            this.draw_18_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, isPrint, gridWidth, gridHeight)
            break
          default:
            return -1
            break
        }

        if (isPrint == 1) {
          zqSvg.drawScaleRule(svg_ecg, svg_elem_id, ecgParme, out_rect)
        }
      },
      /**
       * @method  painter_gain  绘制定标电压
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme  除波形之外的其他变量参数
       * @param px        x坐标位置
       * @param py        y坐标位置
       */
      painter_gain(svg_ecg, svg_elem_id, ecgParme, px, py) {
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        var gainpolylineDate = zqSvg.set_gain_point(ecgParme.pix_mm, px, py, ecgParme.gain, ecgParme.speed)
        zqSvg.polyline(
          wave_,
          `gainpolyline_${svg_elem_id}`,
          gainpolylineDate,
          `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.lineWidth}`
        )
      },
      /**
       * @method get_12_lead_lcr 12导获取布局参数
       * @param layout_index 布局索引
       */
      get_12_lead_lcr(layout_index) {
        if (layout_index < 0 || layout_index > 5) {
          return
        }
        var layout_paras = []
        layout_paras = [
          [3, 4, 0],
          [3, 4, 1],
          [3, 4, 3],
          [6, 2, 0],
          [6, 2, 1],
          [12, 1, 0]
        ]
        var out_lcr = {}
        out_lcr.ln = layout_paras[layout_index][0]
        out_lcr.col = layout_paras[layout_index][1]
        out_lcr.rhythm = layout_paras[layout_index][2]
        return out_lcr
      },
      /**
       * @method get_15_lead_lcr 15导获取布局参数
       * @param layout_index 布局索引
       */
      get_15_lead_lcr(layout_index) {
        if (layout_index < 0 || layout_index > 1) {
          return
        }
        var layout_paras = []
        layout_paras = [
          [5, 3, 0],
          [5, 3, 1]
        ]
        var out_lcr = {}
        out_lcr.ln = layout_paras[layout_index][0]
        out_lcr.col = layout_paras[layout_index][1]
        out_lcr.rhythm = layout_paras[layout_index][2]
        return out_lcr
      },
      /**
       * @method get_18_lead_lcr 18导获取布局参数
       * @param layout_index 布局索引
       */
      get_18_lead_lcr(layout_index) {
        if (layout_index < 0 || layout_index > 3) {
          return
        }
        var layout_paras = []
        layout_paras = [
          [6, 3, 0],
          [6, 3, 1],
          [9, 2, 0],
          [9, 2, 1]
        ]
        var out_lcr = {}
        out_lcr.ln = layout_paras[layout_index][0]
        out_lcr.col = layout_paras[layout_index][1]
        out_lcr.rhythm = layout_paras[layout_index][2]
        return out_lcr
      },
      /**
       * @method calc_12_lead_coodinates      获取导联起始点坐标
       * @param layout_index                  布局索引
       * @param left                          距离左侧位置
       * @param top                           距离顶部位置
       * @param pix_mm
       * @param w_pix                         svg宽度
       * @param h_pix                         svg高度
       * @returns {any[]}                     导联起始点坐标数组
       */
      calc_12_lead_coodinates(layout_index, left, top, pix_mm, w_pix, h_pix) {
        const out_lcr = this.get_12_lead_lcr(layout_index)
        var t_max_ln = out_lcr.ln + out_lcr.rhythm
        var t_revise_index
        var t_revise_array = [
          [0, 3, 6, 9, 1, 4, 7, 10, 2, 5, 8, 11],
          [0, 3, 6, 9, 1, 4, 7, 10, 2, 5, 8, 11, 12, -2],
          [0, 3, 6, 9, 1, 4, 7, 10, 2, 5, 8, 11, 12, -1, -1, -1, 13, -1, -1, -1, 14, -1, -1, -1],
          [0, 6, 1, 7, 2, 8, 3, 9, 4, 10, 5, 11],
          [0, 6, 1, 7, 2, 8, 3, 9, 4, 10, 5, 11, 12, -2],
          [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
        ]
        var t_revise = t_revise_array[layout_index]
        var t_basis_x
        var t_basis_y
        var out_points = new Array(t_max_ln)
        for (let t_ln = 0; t_ln < t_max_ln; t_ln++) {
          for (let t_col = 0; t_col < out_lcr.col; t_col++) {
            t_basis_x = parseInt(w_pix / out_lcr.col) * t_col
            t_basis_y = (h_pix / t_max_ln) * t_ln + h_pix / t_max_ln / 2
            t_revise_index = t_revise[t_ln * out_lcr.col + t_col]
            if (t_revise_index == -2) {
              break
            } else if (t_revise_index != -1) {
              var out_point = []
              out_point.x = t_basis_x + left
              out_point.y = t_basis_y + top
              out_points[t_revise_index] = out_point
            }
          }
        }
        return out_points
      },
      /**
       * calc_12_lead_coodinates_type     12导同步、顺序导联起始点坐标
       * @param displayMode                   同步或异步
       * @param layout_index              布局索引
       * @param floor
       * @returns {any[]}                 导联起始点坐标数组
       */
      calc_12_lead_coodinates_type(ecgParme, out_rect, floor) {
        const out_lcr = this.get_12_lead_lcr(ecgParme.layoutIndex)
        // let floor = Math.floor(out_rect.offsetWidth / out_lcr["col"] * 1000 / ecgParme.speed / ecgParme.pix_mm)
        // let floor = Math.floor(timeMs / out_lcr["col"])
        var t_max_ln = out_lcr.ln + out_lcr.rhythm
        var t_revise_index
        var t_revise_array = [
          [0, 3, 6, 9, 1, 4, 7, 10, 2, 5, 8, 11],
          [0, 3, 6, 9, 1, 4, 7, 10, 2, 5, 8, 11, 12, -2],
          [0, 3, 6, 9, 1, 4, 7, 10, 2, 5, 8, 11, 12, -1, -1, -1, 13, -1, -1, -1, 14, -1, -1, -1],
          [0, 6, 1, 7, 2, 8, 3, 9, 4, 10, 5, 11],
          [0, 6, 1, 7, 2, 8, 3, 9, 4, 10, 5, 11, 12, -2],
          [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]
        ]
        var t_revise = t_revise_array[ecgParme.layoutIndex]
        var out_points = new Array(t_max_ln)
        for (let t_ln = 0; t_ln < t_max_ln; t_ln++) {
          for (let t_col = 0; t_col < out_lcr.col; t_col++) {
            t_revise_index = t_revise[t_ln * out_lcr.col + t_col]
            if (t_revise_index == -2) {
              break
            } else if (t_revise_index != -1) {
              var out_point = ecgParme.displayMode == 0 ? 0 : floor * t_col
              out_points[t_revise_index] = out_point
            }
          }
        }
        return out_points
      },
      /**
       * @method calc_15_lead_coodinates      15导获取导联起始点坐标
       * @param layout_index                  布局索引
       * @param left                          距离左侧位置
       * @param top                           距离顶部位置
       * @param pix_mm
       * @param w_pix                         svg宽度
       * @param h_pix                         svg高度
       * @returns {any[]}                     导联起始点坐标数组
       */
      calc_15_lead_coodinates(layout_index, left, top, pix_mm, w_pix, h_pix) {
        const out_lcr = this.get_15_lead_lcr(layout_index)
        var t_max_ln = out_lcr.ln + out_lcr.rhythm
        var t_revise_index
        var t_revise_array = [
          [0, 5, 10, 1, 6, 11, 2, 7, 12, 3, 8, 13, 4, 9, 14],
          [0, 5, 10, 1, 6, 11, 2, 7, 12, 3, 8, 13, 4, 9, 14, 15, -2]
        ]
        var t_revise = t_revise_array[layout_index]
        var t_basis_x
        var t_basis_y
        var out_points = new Array(t_max_ln)
        for (let t_ln = 0; t_ln < t_max_ln; t_ln++) {
          for (let t_col = 0; t_col < out_lcr.col; t_col++) {
            t_basis_x = (w_pix / out_lcr.col) * t_col
            t_basis_y = (h_pix / t_max_ln) * t_ln + h_pix / t_max_ln / 2
            t_revise_index = t_revise[t_ln * out_lcr.col + t_col]
            if (t_revise_index == -2) {
              break
            } else if (t_revise_index != -1) {
              var out_point = []
              out_point.x = t_basis_x + left
              out_point.y = t_basis_y + top
              out_points[t_revise_index] = out_point
            }
          }
        }
        return out_points
      },
      /**
       * calc_15_lead_coodinates_type     15导同步、顺序导联起始点坐标
       * @param displayMode                   同步或异步
       * @param layout_index              布局索引
       * @param floor
       * @returns {any[]}                 导联起始点坐标数组
       */
      calc_15_lead_coodinates_type(ecgParme, out_rect, floor) {
        const out_lcr = this.get_15_lead_lcr(ecgParme.layoutIndex)
        var t_max_ln = out_lcr.ln + out_lcr.rhythm
        var t_revise_index
        var t_revise_array = [
          [0, 5, 10, 1, 6, 11, 2, 7, 12, 3, 8, 13, 4, 9, 14],
          [0, 5, 10, 1, 6, 11, 2, 7, 12, 3, 8, 13, 4, 9, 14, 15, -2]
        ]
        var t_revise = t_revise_array[ecgParme.layoutIndex]
        var out_points = new Array(t_max_ln)
        for (let t_ln = 0; t_ln < t_max_ln; t_ln++) {
          for (let t_col = 0; t_col < out_lcr.col; t_col++) {
            t_revise_index = t_revise[t_ln * out_lcr.col + t_col]
            if (t_revise_index == -2) {
              break
            } else if (t_revise_index != -1) {
              var out_point = ecgParme.displayMode == 0 ? 0 : floor * t_col
              out_points[t_revise_index] = out_point
            }
          }
        }
        return out_points
      },
      /**
       * @method calc_18_lead_coodinates      18导获取导联起始点坐标
       * @param layout_index                  布局索引
       * @param left                          距离左侧位置
       * @param top                           距离顶部位置
       * @param pix_mm
       * @param w_pix                         svg宽度
       * @param h_pix                         svg高度
       * @returns {any[]}                     导联起始点坐标数组
       */
      calc_18_lead_coodinates(layout_index, left, top, pix_mm, w_pix, h_pix) {
        const out_lcr = this.get_18_lead_lcr(layout_index)
        var t_max_ln = out_lcr.ln + out_lcr.rhythm
        var t_revise_index
        var t_revise_array = [
          [0, 6, 12, 1, 7, 13, 2, 8, 14, 3, 9, 15, 4, 10, 16, 5, 11, 17],
          [0, 6, 12, 1, 7, 13, 2, 8, 14, 3, 9, 15, 4, 10, 16, 5, 11, 17, 18, -2],
          [0, 9, 1, 10, 2, 11, 3, 12, 4, 13, 5, 14, 6, 15, 7, 16, 8, 17],
          [0, 9, 1, 10, 2, 11, 3, 12, 4, 13, 5, 14, 6, 15, 7, 16, 8, 17, 18, -2]
        ]
        var t_revise = t_revise_array[layout_index]
        var t_basis_x
        var t_basis_y
        var out_points = new Array(t_max_ln)
        for (let t_ln = 0; t_ln < t_max_ln; t_ln++) {
          for (let t_col = 0; t_col < out_lcr.col; t_col++) {
            t_basis_x = (w_pix / out_lcr.col) * t_col
            t_basis_y = (h_pix / t_max_ln) * t_ln + h_pix / t_max_ln / 2
            t_revise_index = t_revise[t_ln * out_lcr.col + t_col]
            if (t_revise_index == -2) {
              break
            } else if (t_revise_index != -1) {
              var out_point = []
              out_point.x = t_basis_x + left
              out_point.y = t_basis_y + top
              out_points[t_revise_index] = out_point
            }
          }
        }
        return out_points
      },
      /**
       * calc_18_lead_coodinates_type     18导同步、顺序导联起始点坐标
       * @param displayMode                   同步或异步
       * @param layout_index              布局索引
       * @param floor
       * @returns {any[]}                 导联起始点坐标数组
       */
      calc_18_lead_coodinates_type(ecgParme, out_rect, floor) {
        const out_lcr = this.get_18_lead_lcr(ecgParme.layoutIndex)
        var t_max_ln = out_lcr.ln + out_lcr.rhythm
        var t_revise_index
        var t_revise_array = [
          [0, 6, 12, 1, 7, 13, 2, 8, 14, 3, 9, 15, 4, 10, 16, 5, 11, 17],
          [0, 6, 12, 1, 7, 13, 2, 8, 14, 3, 9, 15, 4, 10, 16, 5, 11, 17, 18, -2],
          [0, 9, 1, 10, 2, 11, 3, 12, 4, 13, 5, 14, 6, 15, 7, 16, 8, 17],
          [0, 9, 1, 10, 2, 11, 3, 12, 4, 13, 5, 14, 6, 15, 7, 16, 8, 17, 18, -2]
        ]
        var t_revise = t_revise_array[ecgParme.layoutIndex]
        var out_points = new Array(t_max_ln)
        for (let t_ln = 0; t_ln < t_max_ln; t_ln++) {
          for (let t_col = 0; t_col < out_lcr.col; t_col++) {
            t_revise_index = t_revise[t_ln * out_lcr.col + t_col]
            if (t_revise_index == -2) {
              break
            } else if (t_revise_index != -1) {
              var out_point = ecgParme.displayMode == 0 ? 0 : floor * t_col
              out_points[t_revise_index] = out_point
            }
          }
        }
        return out_points
      },
      // drawScaleRule(svg_elem_id, rect){
      //     let grid_ = document.getElementById("standard_ecg_print");
      //
      //     zqSvg.g(grid_, "scaleRule_" + svg_elem_id);
      //
      //     let scaleRuleObj = document.getElementById("scaleRule_" + svg_elem_id);
      //
      //     for (let i = 0; i < (rect.offsetWidth / ecgParme.pix_mm); i++) {
      //         if ((i % 25) == 0) {
      //             zqSvg.line(scaleRuleObj, (i * ecgParme.pix_mm +  rect.offsetLeft),(i * ecgParme.pix_mm +  rect.offsetLeft),
      //                 ecgParme.gridHeightPrint - rect.bottom - 8,ecgParme.gridHeightPrint - rect.bottom,
      //                 "scaleRule_line_"+svg_elem_id, "stroke-width:2;stroke:"+ecgParme.gridColor
      //             )
      //             continue
      //         }
      //         zqSvg.line(scaleRuleObj, (i * ecgParme.pix_mm +  rect.offsetLeft),(i * ecgParme.pix_mm +  rect.offsetLeft),
      //             ecgParme.gridHeightPrint - rect.bottom - 4,ecgParme.gridHeightPrint - rect.bottom,
      //             "scaleRule_line_"+svg_elem_id, "stroke-width:1;stroke:"+ecgParme.gridColor
      //         )
      //     }
      //
      // },
      /**
       * @method painter_12_wave      绘制12导波形
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param rect                  波形绘制区域
       * @param lead_sys              导联体系
       * @param data                  波形数据
       * @param size                  数据长度
       * @param point                 每导联起始点坐标
       * @param out_points_type       同步、顺序每导联起始点坐标
       */
      painter_12_wave(
        svg_ecg,
        ecgJson,
        svg_elem_id,
        ecgParme,
        rect,
        lead_sys,
        data,
        size,
        point,
        out_points_type,
        gridWidth,
        gridHeight
      ) {
        if (ecgParme.begin_ms * 26 > size) {
          console.error('painter_12_wave 开始打印位置超过有效的大小!')
          return
        }
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        const out_lcr = this.get_12_lead_lcr(ecgParme.layoutIndex)
        const array_data_len = ecgParme.displayMode == 0 ? (size - ecgParme.begin_ms * 26) / 26 : ecgJson.floor
        var vector = []
        // var single_interval = parseInt(gridWidth - rect["left"] - rect["right"]);
        var single_interval = rect.offsetWidth
        if (out_lcr.col > 1) {
          single_interval = parseInt((point[11].x - point[0].x) / (out_lcr.col - 1))
        }
        for (let lead_idx = 0; lead_idx < 12; lead_idx++) {
          for (let array_idx = 0; array_idx < array_data_len; array_idx++) {
            var p_x
            var p_y
            p_x = (array_idx * ecgParme.speed * ecgParme.pix_mm) / 1000
            if (p_x > single_interval) {
              break
            }
            if (array_idx + ecgParme.begin_ms >= ecgJson.floor && ecgParme.displayMode != 0) {
              break
            }
            p_y = data[lead_idx][array_idx + ecgParme.begin_ms + out_points_type[lead_idx]]
            p_y = (-1 * p_y * ecgParme.gain * ecgParme.pix_mm) / 1000
            p_x += point[lead_idx].x
            p_y += point[lead_idx].y
            vector.push(p_x)
            vector.push(p_y)
          }
          zqSvg.polyline(
            wave_,
            `convention_polyline_${svg_elem_id}`,
            vector,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          // var p_x2 = vector[vector.length-6]-vector[0];
          // this.draw_rect(svg_ecg, ecgJson, lead_idx, vector[0], vector[1]-16, p_x2,32);
          vector = []
        }
      },
      /**
       * @method painter_15_wave      绘制15导波形
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param rect                  波形绘制区域
       * @param lead_sys              导联体系
       * @param data                  波形数据
       * @param size                  数据长度
       * @param point                 每导联起始点坐标
       * @param out_points_type       同步、顺序每导联起始点坐标
       */
      painter_15_wave(
        svg_ecg,
        ecgJson,
        svg_elem_id,
        ecgParme,
        rect,
        lead_sys,
        data,
        size,
        point,
        out_points_type,
        gridWidth,
        gridHeight
      ) {
        if (ecgParme.begin_ms * 32 > size) {
          console.error('painter_15_wave 开始打印位置超过有效的大小!')
          return
        }
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        const out_lcr = this.get_15_lead_lcr(ecgParme.layoutIndex)
        const array_data_len = ecgParme.displayMode == 0 ? (size - ecgParme.begin_ms * 32) / 32 : ecgJson.floor
        var vector = []
        var single_interval = rect.offsetWidth
        if (out_lcr.col > 1) {
          single_interval = (point[14].x - point[0].x) / (out_lcr.col - 1)
        }
        for (let lead_idx = 0; lead_idx < 15; lead_idx++) {
          for (let array_idx = 0; array_idx < array_data_len; array_idx++) {
            var p_x
            var p_y
            p_x = (array_idx * ecgParme.speed * ecgParme.pix_mm) / 1000
            if (p_x > single_interval) {
              break
            }
            if (array_idx + ecgParme.begin_ms >= ecgJson.floor && ecgParme.displayMode != 0) {
              break
            }
            p_y = data[lead_idx][array_idx + ecgParme.begin_ms + out_points_type[lead_idx]]
            p_y = (-1 * p_y * ecgParme.gain * ecgParme.pix_mm) / 1000
            p_x += point[lead_idx].x
            p_y += point[lead_idx].y
            vector.push(p_x)
            vector.push(p_y)
          }
          zqSvg.polyline(
            wave_,
            `convention_polyline_${svg_elem_id}`,
            vector,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          // var p_x2 = vector[vector.length-6]-vector[0];
          // this.draw_rect(svg_ecg, ecgJson, lead_idx, vector[0], vector[1]-16, p_x2,32);
          vector = []
        }
      },
      /**
       * @method painter_18_wave      绘制18导波形
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param rect                  波形绘制区域
       * @param lead_sys              导联体系
       * @param data                  波形数据
       * @param size                  数据长度
       * @param point                 每导联起始点坐标
       * @param out_points_type       同步、顺序每导联起始点坐标
       */
      painter_18_wave(svg_ecg, ecgJson, svg_elem_id, ecgParme, rect, lead_sys, data, size, point, out_points_type) {
        if (ecgParme.begin_ms * 38 > size) {
          console.error('painter_18_wave 开始打印位置超过有效的大小!')
          return
        }
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        const out_lcr = this.get_18_lead_lcr(ecgParme.layoutIndex)
        const array_data_len = ecgParme.displayMode == 0 ? (size - ecgParme.begin_ms * 38) / 38 : ecgJson.floor
        var vector = []
        var single_interval = rect.offsetWidth
        // var single_interval = ecgParme.gridWidth - rect["left"] - rect["right"];
        if (out_lcr.col > 1) {
          single_interval = (point[17].x - point[0].x) / (out_lcr.col - 1)
        }
        for (let lead_idx = 0; lead_idx < 18; lead_idx++) {
          for (let array_idx = 0; array_idx < array_data_len; array_idx++) {
            var p_x
            var p_y
            p_x = (array_idx * ecgParme.speed * ecgParme.pix_mm) / 1000
            if (p_x > single_interval) {
              break
            }
            if (array_idx + ecgParme.begin_ms >= ecgJson.floor && ecgParme.displayMode != 0) {
              break
            }
            p_y = data[lead_idx][array_idx + ecgParme.begin_ms + out_points_type[lead_idx]]
            p_y = (-1 * p_y * ecgParme.gain * ecgParme.pix_mm) / 1000

            p_x += point[lead_idx].x
            p_y += point[lead_idx].y
            vector.push(p_x)
            vector.push(p_y)
          }
          zqSvg.polyline(
            wave_,
            `convention_polyline_${svg_elem_id}`,
            vector,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          // var p_x2 = vector[vector.length-6]-vector[0];
          // this.draw_rect(svg_ecg, ecgJson, lead_idx, vector[0], vector[1]-16, p_x2,32);
          vector = []
        }
      },
      /**
       *
       * @method painter_ecg_parameter hr rr 参数绘制调用
       * @param svg_ecg
       * @param ecgJson
       * @param ecgParme              除波形之外的其他变量参数
       * @param out_points            绘制的点
       * @param out_rect              绘制区域
       * @param lead                  导联
       * @param pos                   位置
       * @param RR_pos                rr位置
       * @param HR_pos                hr位置
       */
      painter_ecg_parameter(svg_ecg, ecgJson, ecgParme, out_points, out_rect, lead, pos, RR_pos, HR_pos) {
        this.painter_HR_RR(svg_ecg, ecgJson, ecgParme, 'hr_', pos, HR_pos, out_points, out_rect, lead, 15)
        this.painter_HR_RR(svg_ecg, ecgJson, ecgParme, 'rr_', pos, RR_pos, out_points, out_rect, lead, 30)
      },
      /**
       * @method painter_HR_RR         hr rr 参数绘制
       * @param svg_ecg
       * @param ecgJson
       * @param ecgParme              除波形之外的其他变量参数
       * @param id                    hr或rr
       * @param pos                   位置
       * @param data                  数据
       * @param out_points            点的位置
       * @param out_rect              绘制区域
       * @param lead                  导联
       * @param y                     y坐标
       */
      painter_HR_RR(svg_ecg, ecgJson, ecgParme, id, pos, data, out_points, out_rect, lead, y) {
        var single_interval = parseInt(ecgParme.gridWidth - out_rect.left - out_rect.right)
        var out_lcr = this.get_12_lead_lcr(ecgParme.layoutIndex)
        switch (lead) {
          case 12:
            out_lcr = this.get_12_lead_lcr(ecgParme.layoutIndex)
            if (out_lcr.col > 1) {
              single_interval = parseInt((out_points[11].x - out_points[0].x) / (out_lcr.col - 1))
            }
            break
          case 15:
            out_lcr = this.get_15_lead_lcr(ecgParme.layoutIndex)
            if (out_lcr.col > 1) {
              single_interval = parseInt(out_points[14].x - out_points[0].x) / (out_lcr.col - 1)
            }
            break
          case 18:
            out_lcr = this.get_18_lead_lcr(ecgParme.layoutIndex)
            if (out_lcr.col > 1) {
              single_interval = parseInt(out_points[17].x - out_points[0].x) / (out_lcr.col - 1)
            }
            break
        }
        var svg_ecg_id = svg_ecg.getAttribute('id')
        var text = document.createElementNS('http://www.w3.org/2000/svg', 'text')
        text.setAttribute('id', id + svg_ecg_id)
        text.setAttribute('x', 0)
        text.setAttribute('y', 0)
        text.setAttribute('style', `fill:${ecgParme.textColor};font-size:10px;text-anchor: middle;`)
        const offseX = ecgParme.speed * ecgParme.pix_mm * (ecgParme.begin_ms / 1000)
        const len = ecgParme.displayMode == '0' ? data.length : ecgJson.floor / 1000
        for (let i = ecgParme.begin_ms / 1000; i < len; i++) {
          let x = ((pos[i] + (pos[i + 1] - pos[i]) / 2) * ecgParme.speed * ecgParme.pix_mm) / 1000
          x = x + out_points[0].x - offseX
          if (x > single_interval) {
            break
          }
          var tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan')
          tspan.setAttribute('x', x)
          tspan.setAttribute('y', y)
          var textString = document.createTextNode(data[i])
          tspan.appendChild(textString)
          text.appendChild(tspan)
        }
        svg_ecg.appendChild(text)
      },
      /**
       * @method painter_12_rhythm    绘制12导节律
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param rect                  波形绘制区域
       * @param data                  波形数据
       * @param size                  波形数据长度
       * @param point                 起始坐标
       * @param rhythm_index          节律索引
       */
      painter_12_rhythm(svg_ecg, svg_elem_id, ecgParme, rect, data, size, point, rhythm_index, gridWidth, gridHeight) {
        var array_data_len
        if (ecgParme.begin_ms * 26 > size) {
          console.error('painter_12_wave 开始打印位置超过有效的大小!')
        }
        var out_lcr = this.get_12_lead_lcr(ecgParme.layoutIndex)
        if (out_lcr.rhythm == 0) {
          return
        }
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        array_data_len = (size - ecgParme.begin_ms * 26) / 26
        var vector = []
        var single_interval = rect.offsetWidth
        for (let lead_idx = 0; lead_idx < out_lcr.rhythm; lead_idx++) {
          if (rhythm_index[lead_idx] >= 0 && rhythm_index[lead_idx] < 12) {
            for (let array_idx = 0; array_idx < array_data_len; array_idx++) {
              var p_x
              var p_y
              p_x = (array_idx * ecgParme.speed * ecgParme.pix_mm) / 1000
              if (p_x > single_interval) {
                break
              }
              p_y = data[rhythm_index[lead_idx]][array_idx + ecgParme.begin_ms]
              p_y = (-1 * p_y * ecgParme.gain * ecgParme.pix_mm) / 1000
              p_x += point[out_lcr.ln * out_lcr.col + lead_idx].x
              p_y += point[out_lcr.ln * out_lcr.col + lead_idx].y
              vector.push(p_x)
              vector.push(p_y)
            }
          } else {
            break
          }
          zqSvg.polyline(
            wave_,
            `convention_rhythm_polyline_${svg_elem_id}`,
            vector,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          vector = []
        }
      },
      /**
       * @method painter_15_rhythm    绘制15导节律
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param rect                  波形绘制区域
       * @param data                  波形数据
       * @param size                  波形数据长度
       * @param point                 起始坐标
       * @param rhythm_index          节律索引
       */
      painter_15_rhythm(svg_ecg, svg_elem_id, ecgParme, rect, data, size, point, rhythm_index, gridWidth, gridHeight) {
        var array_data_len
        if (ecgParme.begin_ms * 32 > size) {
          console.error('painter_15_rhythm 开始打印位置超过有效的大小!')
        }
        var out_lcr = this.get_15_lead_lcr(ecgParme.layoutIndex)
        if (out_lcr.rhythm == 0) {
          return
        }
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        array_data_len = (size - ecgParme.begin_ms * 32) / 32
        var vector = []
        var single_interval = rect.offsetWidth
        for (let lead_idx = 0; lead_idx < out_lcr.rhythm; lead_idx++) {
          if (rhythm_index[lead_idx] >= 0 && rhythm_index[lead_idx] < 15) {
            for (let array_idx = 0; array_idx < array_data_len; array_idx++) {
              var p_x
              var p_y
              p_x = (array_idx * ecgParme.speed * ecgParme.pix_mm) / 1000
              if (p_x > single_interval) {
                break
              }
              p_y = data[rhythm_index[lead_idx]][array_idx + ecgParme.begin_ms]
              p_y = (-1 * p_y * ecgParme.gain * ecgParme.pix_mm) / 1000
              p_x += point[out_lcr.ln * out_lcr.col + lead_idx].x
              p_y += point[out_lcr.ln * out_lcr.col + lead_idx].y
              vector.push(p_x)
              vector.push(p_y)
            }
          } else {
            break
          }
          zqSvg.polyline(
            wave_,
            `convention_rhythm_polyline_${svg_elem_id}`,
            vector,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          vector = []
        }
      },
      /**
       * @method painter_18_rhythm    绘制18导节律
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param rect                  波形绘制区域
       * @param data                  波形数据
       * @param size                  波形数据长度
       * @param point                 起始坐标
       * @param rhythm_index          节律索引
       */
      painter_18_rhythm(svg_ecg, svg_elem_id, ecgParme, rect, data, size, point, rhythm_index, gridWidth, gridHeight) {
        var array_data_len
        if (ecgParme.begin_ms * 38 > size) {
          console.error('painter_18_wave 开始打印位置超过有效的大小!')
        }
        var out_lcr = this.get_18_lead_lcr(ecgParme.layoutIndex)
        if (out_lcr.rhythm == 0) {
          return
        }
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        array_data_len = (size - ecgParme.begin_ms * 38) / 38
        var vector = []
        var single_interval = rect.offsetWidth
        for (let lead_idx = 0; lead_idx < out_lcr.rhythm; lead_idx++) {
          if (rhythm_index[lead_idx] >= 0 && rhythm_index[lead_idx] < 18) {
            for (let array_idx = 0; array_idx < array_data_len; array_idx++) {
              var p_x
              var p_y
              p_x = (array_idx * ecgParme.speed * ecgParme.pix_mm) / 1000
              if (p_x > single_interval) {
                break
              }
              p_y = data[rhythm_index[lead_idx]][array_idx + ecgParme.begin_ms]
              p_y = (-1 * p_y * ecgParme.gain * ecgParme.pix_mm) / 1000
              p_x += point[out_lcr.ln * out_lcr.col + lead_idx].x
              p_y += point[out_lcr.ln * out_lcr.col + lead_idx].y
              vector.push(p_x)
              vector.push(p_y)
            }
          } else {
            break
          }
          zqSvg.polyline(
            wave_,
            `convention_rhythm_polyline_${svg_elem_id}`,
            vector,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          vector = []
        }
      },
      /**
       * @method painter_single12_wave    12导绘制单导联
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme                  除波形之外的其他变量参数
       * @param rect                      绘制波形区域
       * @param data                      波形数据
       * @param size                      波形数据长度
       * @param point                     起始点坐标
       * @param avg                       绘制行数
       */
      painter_single12_wave(svg_ecg, svg_elem_id, ecgParme, rect, data, size, point, avg, speedTimes) {
        var array_data_len
        if (ecgParme.begin_ms * 26 > size) {
          console.error('painter_12_wave 开始打印位置超过有效的大小!')
          return
        }
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        array_data_len = (size - ecgParme.begin_ms * 26) / 26 / avg
        var vector = []
        var single_interval = rect.offsetWidth
        for (let lead_idx = 0; lead_idx < avg; lead_idx++) {
          for (let array_idx = 0; array_idx < array_data_len; array_idx++) {
            var p_x
            var p_y
            p_x = (array_idx * ecgParme.speed * speedTimes * ecgParme.pix_mm) / 1000
            if (p_x > single_interval) {
              break
            }
            p_y = data[ecgParme.leadIndex][array_idx + array_data_len * lead_idx + ecgParme.begin_ms]
            p_y = (-1 * p_y * ecgParme.gain * ecgParme.pix_mm) / 1000
            p_x += point.x
            p_y = p_y + point.y * (lead_idx + 1) + rect.top
            vector.push(p_x)
            vector.push(p_y)
          }
          zqSvg.polyline(
            wave_,
            `convention_polyline_${svg_elem_id}`,
            vector,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          vector = []
        }
      },
      /**
       * @method painter_single15_wave    15导绘制单导联
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme                  除波形之外的其他变量参数
       * @param rect                      绘制波形区域
       * @param data                      波形数据
       * @param size                      波形数据长度
       * @param point                     起始点坐标
       * @param avg                       绘制行数
       */
      painter_single15_wave(svg_ecg, svg_elem_id, ecgParme, rect, data, size, point, avg) {
        var array_data_len
        if (ecgParme.begin_ms * 32 > size) {
          console.error('painter_12_wave 开始打印位置超过有效的大小!')
          return
        }
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        array_data_len = (size - ecgParme.begin_ms * 32) / 32 / avg
        var vector = []
        var single_interval = rect.offsetWidth
        for (let lead_idx = 0; lead_idx < avg; lead_idx++) {
          for (let array_idx = 0; array_idx < array_data_len; array_idx++) {
            var p_x
            var p_y
            p_x = (array_idx * ecgParme.speed * ecgParme.pix_mm) / 1000
            if (p_x > single_interval) {
              break
            }
            p_y = data[ecgParme.leadIndex][array_idx + array_data_len * lead_idx + ecgParme.begin_ms]
            p_y = (-1 * p_y * ecgParme.gain * ecgParme.pix_mm) / 1000
            p_x += point.x
            p_y = p_y + point.y * (lead_idx + 1) + rect.top
            vector.push(p_x)
            vector.push(p_y)
          }
          zqSvg.polyline(
            wave_,
            `convention_polyline_${svg_elem_id}`,
            vector,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          vector = []
        }
      },
      /**
       * @method painter_single18_wave    18导绘制单导联
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme                  除波形之外的其他变量参数
       * @param rect                      绘制波形区域
       * @param data                      波形数据
       * @param size                      波形数据长度
       * @param point                     起始点坐标
       * @param avg                       绘制行数
       */
      painter_single18_wave(svg_ecg, svg_elem_id, ecgParme, rect, data, size, point, avg) {
        var array_data_len
        if (ecgParme.begin_ms * 38 > size) {
          console.error('painter_18_wave 开始打印位置超过有效的大小!')
          return
        }
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        array_data_len = (size - ecgParme.begin_ms * 38) / 38 / avg
        var vector = []
        var single_interval = rect.offsetWidth
        for (let lead_idx = 0; lead_idx < avg; lead_idx++) {
          for (let array_idx = 0; array_idx < array_data_len; array_idx++) {
            var p_x
            var p_y
            p_x = (array_idx * ecgParme.speed * ecgParme.pix_mm) / 1000
            if (p_x > single_interval) {
              break
            }
            p_y = data[ecgParme.leadIndex][array_idx + array_data_len * lead_idx + ecgParme.begin_ms]
            p_y = (-1 * p_y * ecgParme.gain * ecgParme.pix_mm) / 1000
            p_x += point.x
            p_y = p_y + point.y * (lead_idx + 1) + rect.top
            vector.push(p_x)
            vector.push(p_y)
          }
          zqSvg.polyline(
            wave_,
            `convention_polyline_${svg_elem_id}`,
            vector,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          vector = []
        }
      },
      /**
       * @method painter_leadnameEx   绘制导联名称
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param font_fixel_size       导联字体大小
       * @param lead_name             导联名称
       * @param position              导联位置
       */
      painter_leadnameEx(svg_ecg, svg_elem_id, ecgParme, font_fixel_size, lead_name, position) {
        var svg_ecg_id = svg_ecg.getAttribute('id')
        if (lead_name.length != position.length) {
          return
        }
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        for (var array_index = 0; array_index < lead_name.length; array_index++) {
          zqSvg.text(
            wave_,
            `convention_text_${svg_ecg_id}`,
            position[array_index].x,
            position[array_index].y - 5 * ecgParme.pix_mm,
            `fill:${ecgParme.textColor};font-size:${font_fixel_size}px`,
            'start',
            'middle',
            lead_name[array_index]
          )
          zqSvg.line(
            wave_,
            position[array_index].x,
            position[array_index].x,
            position[array_index].y - 5 * ecgParme.pix_mm - font_fixel_size,
            position[array_index].y + 5 * ecgParme.pix_mm,
            `convention_line_${svg_ecg_id}`,
            `stroke-width:${ecgParme.lineWidth};stroke:${ecgParme.waveColor}`,
            '5 5'
          )
        }
      },
      /**
       * @method painter_leadnameEx_single    单导联绘制导联名称
       * @param ecgParme              除波形之外的其他变量参数
       * @param svg_ecg
       * @param svg_elem_id
       * @param pix_mm
       * @param font_fixel_size               导联字体大小
       * @param lead_name                     导联名称
       * @param position                      导联位置坐标
       * @param rect                          绘制区域
       * @param lead
       * @param color                         导联名称颜色
       */
      painter_leadnameEx_single(
        ecgParme,
        svg_ecg,
        svg_elem_id,
        pix_mm,
        font_fixel_size,
        lead_name,
        position,
        rect,
        lead,
        color
      ) {
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        zqSvg.text(
          wave_,
          `convention_text_${svg_ecg}`,
          position.x,
          position.y - 5 * pix_mm + rect.top,
          `fill:${color};font-size:${font_fixel_size}px`,
          'start',
          'middle',
          lead_name[ecgParme.leadIndex]
        )
      },
      /**
       * @method painter_message      绘制信息
       * @param svg_ecg_id
       * @param svg_elem_id
       * @param id
       * @param data
       * @param px                    信息位置X坐标
       * @param py                    信息位置y坐标
       * @param font_fixel_size       字体大小
       * @param align                 位置
       * @param color                 颜色
       */
      painter_message(svg_ecg, svg_elem_id, id, data, px, py, font_fixel_size, align, color) {
        const wave_ = document.getElementById(`wave_${svg_elem_id}`)
        zqSvg.text(
          wave_,
          `print_text_${svg_elem_id}`,
          px,
          py,
          `fill:${color}` ? color : `${'' + ';font-size:'}${font_fixel_size}`,
          align,
          'middle',
          data
        )
      },
      /**
       * @method draw_Line    绘制直线
       * @param svg_ecg
       * @param widgetId
       * @param widgetStyle   直线样式
       * @param widgetX1      起点X坐标
       * @param widgetY1      起点y坐标
       * @param widgetX2      终点X坐标
       * @param widgetY2      终点y坐标
       */
      draw_Line(svg_ecg, widgetId, widgetStyle, widgetX1, widgetY1, widgetX2, widgetY2) {
        var svg_ecg_id = svg_ecg.getAttribute('id')
        var widget = document.createElementNS('http://www.w3.org/2000/svg', 'line')
        widget.setAttribute('id', widgetId + svg_ecg_id)
        widget.setAttribute('style', widgetStyle)
        widget.setAttribute('x1', widgetX1)
        widget.setAttribute('y1', widgetY1)
        widget.setAttribute('x2', widgetX2)
        widget.setAttribute('y2', widgetY2)
        svg_ecg.appendChild(widget)
      },
      /**
       * @method check_statu_pace         检测pace信号状态
       * @param data                      数据
       * @returns {number}                pace信号状态
       */
      check_statu_pace(data) {
        var pace_value = (data & 0xc000) == 0x4000 ? 1 : 0
        return pace_value
      },
      /**
       * @method paint_pace               绘制pace信号
       * @param svg_ecg
       * @param pix_mm
       * @param w_pix
       * @param h_pix
       * @param rect                      区域
       * @param layout_lcr                布局
       * @param data                      数据
       * @param size                      数据大小
       * @param point                     点
       * @param speed                     纸速
       * @param begin_ms                  开始时间
       */
      paint_pace(svg_ecg, pix_mm, w_pix, h_pix, rect, layout_lcr, data, size, point, speed, begin_ms) {
        var p_x
        var p_y
        var one_array_size = (out_lcr.ln * out_lcr.col + 1) * 2
        if (begin_ms * one_array_size > size) {
          console.error('paint_pace 开始打印位置超过有效的大小!')
          return
        }
        var data_offset
        const array_data_len = (size - begin_ms * one_array_size) / one_array_size
        const single_interval = (w_pix - rect.left - rect.right) / out_lcr.col
        const rhythm_interval = w_pix - rect.left - rect.right
        const single_ln_height = (w_pix - rect.top - rect.bottom) / (out_lcr.ln + out_lcr.rhythm)
        var pace_obj_id = 0
        var hasPaceFlag = false
        var elements = svg_ecg.getElementsByClassName('pace_line')
        var elCount = elements.length
        if (elements != null) {
          for (var i = 0; i < elCount; i++) {
            var elem_id = `pace_${i}`
            var line = document.getElementById(elem_id)
            if (line != null) {
              svg_ecg.removeChild(line)
            }
          }
        }
        for (var array_idx = 0; array_idx < array_data_len; array_idx++) {
          p_x = (array_idx * speed * pix_mm) / 1000
          data_offset = array_idx * one_array_size + (one_array_size - 2)
          data_offset += begin_ms * one_array_size
          var pace_flag = new DataView(data.slice(data_offset, data_offset + 2)).getInt16(0, true)
          if (hasPaceFlag == false) {
            if (this.check_statu_pace(pace_flag) == 1) {
              for (var lead_idx = 0; lead_idx < point.length; lead_idx++) {
                if (lead_idx < point.length - out_lcr.rhythm) {
                  if (p_x >= single_interval) {
                    continue
                  }
                } else if (p_x >= rhythm_interval) {
                  break
                }
                const x1 = point[lead_idx].x + p_x
                const y1 = point[lead_idx].y - pix_mm * 10
                const x2 = x1
                const y2 = point[lead_idx].y - pix_mm * 9
                var elem_id = `pace_${pace_obj_id}`
                var line
                line = document.createElementNS('http://www.w3.org/2000/svg', 'line')
                svg_ecg.appendChild(line)
                pace_obj_id = 1 + pace_obj_id
                line.setAttribute('class', 'pace_line')
                line.setAttribute('id', elem_id)
                line.setAttribute('x1', x1)
                line.setAttribute('y1', y1)
                line.setAttribute('x2', x2)
                line.setAttribute('y2', y2)
                line.setAttribute('style', 'stroke-width:0.5;stroke:black;')
              }
            }
            hasPaceFlag = this.check_statu_pace(pace_flag)
          }
          hasPaceFlag = false
        }
      },
      /**
       * @method get12OneLeadData     12导获取某一导联数据
       * @param lead_idx              导联索引
       * @param data                  所有导联数据
       * @param size                  所有导联数据大小
       * @returns {any[]}             某一导联数据
       */
      get12OneLeadData(lead_idx, data, size) {
        var data_offset
        const array_data_len = size / 26
        var oneLeadData = new Array()
        for (var array_idx = 0; array_idx < array_data_len; array_idx++) {
          const p_y = data[lead_idx][array_idx]
          oneLeadData[array_idx] = p_y
        }
        return oneLeadData
      },
      /**
       * @method get12OneLeadData     15导获取某一导联数据
       * @param lead_idx              导联索引
       * @param data                  所有导联数据
       * @param size                  所有导联数据大小
       * @returns {any[]}             某一导联数据
       */
      get15OneLeadData(lead_idx, data, size) {
        var data_offset
        const array_data_len = size / 32
        var oneLeadData = new Array()
        for (var array_idx = 0; array_idx < array_data_len; array_idx++) {
          const p_y = data[lead_idx][array_idx]
          oneLeadData[array_idx] = p_y
        }
        return oneLeadData
      },
      /**
       * @method get12OneLeadData     18导获取某一导联数据
       * @param lead_idx              导联索引
       * @param data                  所有导联数据
       * @param size                  所有导联数据大小
       * @returns {any[]}             某一导联数据
       */
      get18OneLeadData(lead_idx, data, size) {
        var data_offset
        const array_data_len = size / 38
        var oneLeadData = new Array()
        for (var array_idx = 0; array_idx < array_data_len; array_idx++) {
          const p_y = data[lead_idx][array_idx]
          oneLeadData[array_idx] = p_y
        }
        return oneLeadData
      },
      /**
       * @method draw_rect    绘制区域
       * @param svg_ecg
       * @param widgetId
       * @param widgetX       区域左上角X坐标
       * @param widgetY       区域左上角y坐标
       * @param widgetW       区域宽度
       * @param widgetH       区域高度
       */
      draw_rect(svg_ecg, ecgJson, widgetId, widgetX, widgetY, widgetW, widgetH) {
        var svg_ecg_id = svg_ecg.getAttribute('id')
        var widget = document.getElementById(widgetId + svg_ecg_id)
        if (widget == null) {
          widget = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
          widget.setAttribute('id', `ecg_rect_${widgetId}${svg_ecg_id}`)
          widget.setAttribute('class', `svg_ecg_${svg_ecg_id}`)
          widget.setAttribute('value', ecgJson.code[widgetId])
          widget.style.zIndex = 999
          widget.setAttribute('x', widgetX)
          widget.setAttribute('y', widgetY)
          widget.setAttribute('width', widgetW)
          widget.setAttribute('height', widgetH)
          widget.setAttribute('style', 'fill:transparent;stroke:red;stroke-width:0')
          svg_ecg.appendChild(widget)
        } else {
          svg_ecg.appendChild(widget)
        }
      },

      /**
       * @method painter_12_avgWave   绘制12导平均模板波形
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param rect                  绘制区域
       * @param lead_sys              导联体系
       * @param leadIndex             导联索引
       * @param data                  所有导联的波形数据
       * @param size                  波形数据大小
       * @param PR                    pr
       * @param QRS                   qrs
       * @param QT                    qt
       * @param QTC                   qtc
       * @param StartEndPoint         某一导波形某一个心博区间
       * @param point                 起点坐标
       * @param speed                 纸速
       * @param gain                  增益
       * @param pd_avgObj             平均模板对象
       * @param pointsJson            某一导波形Q,R,S,T1,T2
       */
      painter_12_avgWave(
        svg_ecg,
        svg_elem_id,
        ecgParme,
        rect,
        lead_sys,
        leadIndex,
        data,
        size,
        PR,
        QRS,
        QT,
        QTC,
        StartEndPoint,
        point,
        speed,
        gain,
        pd_avgObj,
        pointsJson
      ) {
        var svg_ecg_id = svg_ecg.getAttribute('id')
        var array_data_len
        var data_offset
        var vector = []
        var pBeginPoint = [0, 0]
        var pEndPoint = [0, 0]
        var QBeginPoint = [0, 0]
        var sEndPoint = [0, 0]
        var tBeginPoint = [0, 0]
        var tEndPoint = [0, 0]
        var tPoint = [0, 0]
        var index = 0
        for (var array_idx = StartEndPoint[0]; array_idx < StartEndPoint[1]; array_idx++) {
          var p_x
          var p_y
          p_x = (index * speed * ecgParme.pix_mm) / 1000
          p_y = data[leadIndex][array_idx]
          p_y = (-1 * p_y * gain * ecgParme.pix_mm) / 1000
          p_x += point.x
          p_y += point.y
          vector.push(p_x)
          vector.push(p_y)
          if (array_idx == pointsJson.pBegin) {
            pBeginPoint[0] = p_x
            pBeginPoint[1] = p_y
          }
          if (array_idx == pointsJson.pEnd) {
            pEndPoint[0] = p_x
            pEndPoint[1] = p_y
          }
          if (array_idx == pointsJson.QBegin) {
            QBeginPoint[0] = p_x
            QBeginPoint[1] = p_y
          }
          if (array_idx == pointsJson.sEnd) {
            sEndPoint[0] = p_x
            sEndPoint[1] = p_y
          }
          if (array_idx == pointsJson.tBegin) {
            tBeginPoint[0] = p_x
            tBeginPoint[1] = p_y
          }
          if (array_idx == pointsJson.tEnd) {
            tEndPoint[0] = p_x
            tEndPoint[1] = p_y
          }
          if (array_idx == parseInt(pointsJson.tPosition)) {
            tPoint[0] = p_x
            tPoint[1] = p_y
          }
          index++
        }
        if (pBeginPoint[0] <= 0) {
          pBeginPoint[0] += 20
        }
        if (pEndPoint[0] < pBeginPoint[0]) {
          pEndPoint[0] = pBeginPoint[0] + 20
        }
        if (QBeginPoint[0] < pEndPoint[0]) {
          QBeginPoint[0] = pEndPoint[0] + 20
        }
        if (sEndPoint[0] < QBeginPoint[0]) {
          sEndPoint[0] = QBeginPoint[0] + 20
        }
        if (tBeginPoint[0] < sEndPoint[0]) {
          tBeginPoint[0] = sEndPoint[0] + 20
        }
        if (tEndPoint[0] < tBeginPoint[0]) {
          tEndPoint[0] = tBeginPoint[0] + 20
        }
        pd_avgObj.LeadStartPoints[leadIndex] = vector[0]
        pd_avgObj.LeadEndPoints[leadIndex] = vector[vector.length - 2]
        // var lead_name = zqSvg.get_12_lead_system_name(lead_sys);
        const lead_name = []
        ecgJson.leadName.forEach(item => {
          lead_name.push(item)
        })
        const wave_color = zqSvg.getEcgColor(parseInt(leadIndex))
        const avg_ = document.getElementById(`avg_${svg_elem_id}`)
        zqSvg.polyline(
          avg_,
          `convention_polyline_${svg_elem_id}`,
          vector,
          `fill:transparent;stroke:${wave_color};stroke-width:${ecgParme.waveLineWidth}`
        )
        if ((ecgParme.leadIndex == -1 && leadIndex == 0) || ecgParme.leadIndex != -1) {
          zqSvg.line(
            avg_,
            pBeginPoint[0],
            pBeginPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_0${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            pBeginPoint[0] + 10,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'P1',
            '',
            `avg_text_0${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            pBeginPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_0${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            pBeginPoint[0],
            pBeginPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_1${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            pBeginPoint[0] + 10,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'P2',
            '',
            `avg_text_1${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            pEndPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_1${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            QBeginPoint[0],
            QBeginPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_2${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            QBeginPoint[0] + 7,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'Q',
            '',
            `avg_text_2${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            QBeginPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_2${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            sEndPoint[0],
            sEndPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_3${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            sEndPoint[0] + 7,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'S',
            '',
            `avg_text_3${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            sEndPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_3${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            tEndPoint[0],
            tEndPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_4${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            tEndPoint[0] + 7,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'T2',
            '',
            `avg_text_4${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            tEndPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_4${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            tBeginPoint[0],
            tBeginPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_5${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            tBeginPoint[0] + 7,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'T1',
            '',
            `avg_text_5${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            tBeginPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_5${svg_ecg_id}`
          )
        }
        vector = []
      },
      /**
       * @method painter_15_avgWave   绘制15导平均模板波形
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param rect                  绘制区域
       * @param lead_sys              导联体系
       * @param leadIndex             导联索引
       * @param data                  所有导联的波形数据
       * @param size                  波形数据大小
       * @param PR                    pr
       * @param QRS                   qrs
       * @param QT                    qt
       * @param QTC                   qtc
       * @param StartEndPoint         某一导波形某一个心博区间
       * @param point                 起点坐标
       * @param speed                 纸速
       * @param gain                  增益
       * @param pd_avgObj             平均模板对象
       * @param pointsJson            某一导波形Q,R,S,T1,T2
       */
      painter_15_avgWave(
        svg_ecg,
        svg_elem_id,
        ecgParme,
        rect,
        lead_sys,
        leadIndex,
        data,
        size,
        PR,
        QRS,
        QT,
        QTC,
        StartEndPoint,
        point,
        speed,
        gain,
        pd_avgObj,
        pointsJson
      ) {
        var svg_ecg_id = svg_ecg.getAttribute('id')
        var array_data_len
        var data_offset
        var vector = []
        var pBeginPoint = new Array(2)
        var pEndPoint = new Array(2)
        var QBeginPoint = new Array(2)
        var sEndPoint = new Array(2)
        var tBeginPoint = new Array(2)
        var tEndPoint = new Array(2)
        var tPoint = new Array(2)
        var index = 0
        for (var array_idx = StartEndPoint[0]; array_idx < StartEndPoint[1]; array_idx++) {
          var p_x
          var p_y
          p_x = (index * speed * ecgParme.pix_mm) / 1000
          p_y = data[leadIndex][array_idx]
          p_y = (-1 * p_y * gain * ecgParme.pix_mm) / 1000
          p_x += point.x
          p_y += point.y
          vector.push(p_x)
          vector.push(p_y)
          if (array_idx == pointsJson.pBegin) {
            pBeginPoint[0] = p_x
            pBeginPoint[1] = p_y
          }
          if (array_idx == pointsJson.pEnd) {
            pEndPoint[0] = p_x
            pEndPoint[1] = p_y
          }
          if (array_idx == pointsJson.QBegin) {
            QBeginPoint[0] = p_x
            QBeginPoint[1] = p_y
          }
          if (array_idx == pointsJson.sEnd) {
            sEndPoint[0] = p_x
            sEndPoint[1] = p_y
          }
          if (array_idx == pointsJson.tBegin) {
            tBeginPoint[0] = p_x
            tBeginPoint[1] = p_y
          }
          if (array_idx == pointsJson.tEnd) {
            tEndPoint[0] = p_x
            tEndPoint[1] = p_y
          }
          if (array_idx == parseInt(pointsJson.tPosition)) {
            tPoint[0] = p_x
            tPoint[1] = p_y
          }
          index++
        }
        if (pBeginPoint[0] <= 0) {
          pBeginPoint[0] += 20
        }
        if (pEndPoint[0] < pBeginPoint[0]) {
          pEndPoint[0] = pBeginPoint[0] + 20
        }
        if (QBeginPoint[0] < pEndPoint[0]) {
          QBeginPoint[0] = pEndPoint[0] + 20
        }
        if (sEndPoint[0] < QBeginPoint[0]) {
          sEndPoint[0] = QBeginPoint[0] + 20
        }
        if (tBeginPoint[0] < sEndPoint[0]) {
          tBeginPoint[0] = sEndPoint[0] + 20
        }
        if (tEndPoint[0] < tBeginPoint[0]) {
          tEndPoint[0] = tBeginPoint[0] + 20
        }
        pd_avgObj.LeadStartPoints[leadIndex] = vector[0]
        pd_avgObj.LeadEndPoints[leadIndex] = vector[vector.length - 2]
        // var lead_name = zqSvg.get_15_lead_system_name(lead_sys);
        const lead_name = []
        ecgJson.leadName.forEach(item => {
          lead_name.push(item)
        })
        wave_color = zqSvg.getEcgColor(parseInt(leadIndex))
        const avg_ = document.getElementById(`avg_${svg_elem_id}`)
        zqSvg.polyline(
          avg_,
          `convention_polyline_${svg_elem_id}`,
          vector,
          `fill:transparent;stroke:${wave_color};stroke-width:${ecgParme.waveLineWidth}`
        )
        if ((ecgParme.leadIndex == -1 && leadIndex == 0) || ecgParme.leadIndex != -1) {
          zqSvg.line(
            avg_,
            pBeginPoint[0],
            pBeginPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_0${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            pBeginPoint[0] + 10,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'P1',
            '',
            `avg_text_0${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            pBeginPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_0${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            pBeginPoint[0],
            pBeginPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_1${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            pBeginPoint[0] + 10,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'P2',
            '',
            `avg_text_1${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            pEndPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_1${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            QBeginPoint[0],
            QBeginPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_2${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            QBeginPoint[0] + 7,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'Q',
            '',
            `avg_text_2${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            QBeginPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_2${svg_ecg_id}`
          )
          zqSvg.line(
            avg_,
            sEndPoint[0],
            sEndPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_3${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            sEndPoint[0] + 7,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'S',
            '',
            `avg_text_3${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            sEndPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_3${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            tEndPoint[0],
            tEndPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_4${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            tEndPoint[0] + 7,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'T2',
            '',
            `avg_text_4${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            tEndPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_4${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            tBeginPoint[0],
            tBeginPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_5${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            tBeginPoint[0] + 7,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'T1',
            '',
            `avg_text_5${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            tBeginPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_5${svg_ecg_id}`
          )
        }
        vector = []
      },
      /**
       * @method painter_18_avgWave   绘制18导平均模板波形
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param rect                  绘制区域
       * @param lead_sys              导联体系
       * @param leadIndex             导联索引
       * @param data                  所有导联的波形数据
       * @param size                  波形数据大小
       * @param PR                    pr
       * @param QRS                   qrs
       * @param QT                    qt
       * @param QTC                   qtc
       * @param StartEndPoint         某一导波形某一个心博区间
       * @param point                 起点坐标
       * @param speed                 纸速
       * @param gain                  增益
       * @param pd_avgObj             平均模板对象
       * @param pointsJson            某一导波形Q,R,S,T1,T2
       */
      painter_18_avgWave(
        svg_ecg,
        svg_elem_id,
        ecgParme,
        rect,
        lead_sys,
        leadIndex,
        data,
        size,
        PR,
        QRS,
        QT,
        QTC,
        StartEndPoint,
        point,
        speed,
        gain,
        pd_avgObj,
        pointsJson
      ) {
        var svg_ecg_id = svg_ecg.getAttribute('id')
        var array_data_len
        var vector = []
        var pBeginPoint = new Array(2)
        var pEndPoint = new Array(2)
        var QBeginPoint = new Array(2)
        var sEndPoint = new Array(2)
        var tBeginPoint = new Array(2)
        var tEndPoint = new Array(2)
        var tPoint = new Array(2)
        var index = 0
        for (var array_idx = StartEndPoint[0]; array_idx < StartEndPoint[1]; array_idx++) {
          var p_x
          var p_y
          p_x = (index * speed * ecgParme.pix_mm) / 1000
          p_y = data[leadIndex][array_idx]
          p_y = (-1 * p_y * gain * ecgParme.pix_mm) / 1000
          p_x += point.x
          p_y += point.y
          vector.push(p_x)
          vector.push(p_y)
          if (array_idx == pointsJson.pBegin) {
            pBeginPoint[0] = p_x
            pBeginPoint[1] = p_y
          }
          if (array_idx == pointsJson.pEnd) {
            pEndPoint[0] = p_x
            pEndPoint[1] = p_y
          }
          if (array_idx == pointsJson.QBegin) {
            QBeginPoint[0] = p_x
            QBeginPoint[1] = p_y
          }
          if (array_idx == pointsJson.sEnd) {
            sEndPoint[0] = p_x
            sEndPoint[1] = p_y
          }
          if (array_idx == pointsJson.tBegin) {
            tBeginPoint[0] = p_x
            tBeginPoint[1] = p_y
          }
          if (array_idx == pointsJson.tEnd) {
            tEndPoint[0] = p_x
            tEndPoint[1] = p_y
          }
          if (array_idx == parseInt(pointsJson.tPosition)) {
            tPoint[0] = p_x
            tPoint[1] = p_y
          }
          index++
        }
        if (pBeginPoint[0] <= 0) {
          pBeginPoint[0] += 20
        }
        if (pEndPoint[0] < pBeginPoint[0]) {
          pEndPoint[0] = pBeginPoint[0] + 20
        }
        if (QBeginPoint[0] < pEndPoint[0]) {
          QBeginPoint[0] = pEndPoint[0] + 20
        }
        if (sEndPoint[0] < QBeginPoint[0]) {
          sEndPoint[0] = QBeginPoint[0] + 20
        }
        if (tBeginPoint[0] < sEndPoint[0]) {
          tBeginPoint[0] = sEndPoint[0] + 20
        }
        if (tEndPoint[0] < tBeginPoint[0]) {
          tEndPoint[0] = tBeginPoint[0] + 20
        }
        pd_avgObj.LeadStartPoints[leadIndex] = vector[0]
        pd_avgObj.LeadEndPoints[leadIndex] = vector[vector.length - 2]
        // var lead_name = zqSvg.get_18_lead_system_name(lead_sys);
        const lead_name = []
        ecgJson.leadName.forEach(item => {
          lead_name.push(item)
        })
        const avg_ = document.getElementById(`avg_${svg_elem_id}`)
        wave_color = zqSvg.getEcgColor(parseInt(leadIndex))
        zqSvg.polyline(
          avg_,
          `convention_polyline_${svg_elem_id}`,
          vector,
          `fill:transparent;stroke:${wave_color};stroke-width:${ecgParme.waveLineWidth}`
        )
        if ((ecgParme.leadIndex == -1 && leadIndex == 0) || ecgParme.leadIndex != -1) {
          zqSvg.line(
            avg_,
            pBeginPoint[0],
            pBeginPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_0${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            pBeginPoint[0] + 10,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'P1',
            '',
            `avg_text_0${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            pBeginPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_0${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            pBeginPoint[0],
            pBeginPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_1${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            pBeginPoint[0] + 10,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'P2',
            '',
            `avg_text_1${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            pEndPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_1${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            QBeginPoint[0],
            QBeginPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_2${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            QBeginPoint[0] + 7,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'Q',
            '',
            `avg_text_2${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            QBeginPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_2${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            sEndPoint[0],
            sEndPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_3${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            sEndPoint[0] + 7,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'S',
            '',
            `avg_text_3${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            sEndPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_3${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            tEndPoint[0],
            tEndPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_4${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            tEndPoint[0] + 7,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'T2',
            '',
            `avg_text_4${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            tEndPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_4${svg_ecg_id}`
          )

          zqSvg.line(
            avg_,
            tBeginPoint[0],
            tBeginPoint[0],
            point.y - 310,
            point.y + 310,
            `convention_line_${svg_elem_id}`,
            'stroke:black;stroke-width:1.5;stroke-dasharray:3',
            '',
            '',
            `avg_Line_5${svg_ecg_id}`
          )
          zqSvg.text(
            avg_,
            `convention_text_${svg_elem_id}`,
            tBeginPoint[0] + 7,
            point.y - 260,
            'font-family:arial;font-size: 12pt;text-anchor: middle;',
            'middle',
            'middle',
            'T1',
            '',
            `avg_text_5${svg_ecg_id}`
          )
          zqSvg.rect(
            avg_,
            `convention_rect_${svg_elem_id}`,
            tBeginPoint[0] - 8,
            point.y - 310,
            16,
            ecgParme.gridHeight,
            'fill:transparent;stroke:red;stroke-width:0',
            '',
            `ecg_rect_5${svg_ecg_id}`
          )
        }
        vector = []
      },
      /**
       * @method painter_text     绘制文本
       * @param svg_ecg
       * @param x                 x坐标
       * @param y                 y坐标
       * @param align             位置
       * @param baseline
       * @param fontsize          字体大小
       * @param content           内容
       * @param style             样式
       * @param color             颜色
       */
      painter_text(svg_ecg, x, y, align, baseline, fontsize, content, style, color) {
        var htext = document.createElementNS('http://www.w3.org/2000/svg', 'text')
        htext.setAttribute('class', style)
        htext.setAttribute('x', x)
        htext.setAttribute('y', y)
        htext.setAttribute('style', `fill:${color};font-size:${fontsize}`)
        htext.setAttribute('text-anchor', align)
        htext.setAttribute('dominant-baseline', 'auto')
        htext.textContent = content
        svg_ecg.appendChild(htext)
      },
      /**
       * @method getTextWidth     计算文本宽度
       * @param text              文本内容
       * @param font              字体
       * @returns {number}        文本宽度
       */
      getTextWidth(text, font) {
        var canvas = document.createElement('canvas')
        var context = canvas.getContext('2d')
        context.font = font
        var metrics = context.measureText(text)
        return metrics.width
      },
      /**
       * @method getTextHeight     计算文本高度
       * @param text              文本内容
       * @param font              字体
       * @returns {number}        文本宽度
       */
      getTextHeight(text, font) {
        // re-use canvas object for better performance
        var canvas = document.createElement('canvas')
        var context = canvas.getContext('2d')
        context.font = font
        var metrics = context.measureText(text)
        return metrics.height
      },
      /**
       * @method insertStr    在字符串的指定位置插入字符
       * @param soure         源字符串
       * @param start         插入位置
       * @param newStr        插入字符
       * @returns {*}         新的字符串
       */
      insertStr(soure, start, newStr) {
        return soure.slice(0, start) + newStr + soure.slice(start)
      },
      /**
       * @method  print  打印内容
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson
       * @param ecgParme
       */
      print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, rect) {
        const print = document.getElementById(`print_${svg_elem_id}`)
        let { title1 } = this.paramset.report
        if (title1.trim() == '') {
          title1 = ecgJson.checkSection
        }
        const { title2 } = this.paramset.report
        // if(title2 == ''){
        //     title2 = ecgJson['checkDept']
        // }

        const title1_y = 20
        const title2_y = 42
        let line_y = 7 * ecgParme.pix_mm
        if (ecgParme.previewEcg) {
          line_y = 7 * ecgParme.pix_mm + 10
        }
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          gridWidth / 2 - 80,
          title1_y,
          'font-size:18px',
          'middle',
          'Hanging',
          title1
        )
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          gridWidth / 2 - 80,
          title2_y,
          'font-size:14px',
          'middle',
          'Hanging',
          title2
        )
        zqSvg.line(
          print,
          ecgParme.pix_mm * 5,
          gridWidth - ecgParme.pix_mm * 2,
          line_y,
          line_y,
          `line_${svg_elem_id}`,
          'stroke-width:1;stroke:#000000'
        )
        const rightDown = zqSvg.getPrintTimeName(this.paramset.report.rightDown)
        const rightUp = zqSvg.getPrintTimeName(this.paramset.report.rightUp)
        if (this.paramset.report.rightUp == 'reviewTime' || this.paramset.report.rightUp == 'diagnoseTime') {
          zqCommon.getServerSystemTime(
            'up',
            print,
            svg_elem_id,
            gridWidth,
            rightUp,
            rightDown,
            rect,
            this.paramset.report.rightUp,
            ecgJson.id
          )
        } else if (ecgJson[this.paramset.report.rightUp] != null) {
          zqSvg.text(
            print,
            `text_${svg_elem_id}`,
            gridWidth - rect.right,
            title1_y,
            'font-size:14px',
            'end',
            'Hanging',
            rightUp + ecgJson[this.paramset.report.rightUp]
          )
        }
        if (this.paramset.report.rightDown == 'reviewTime' || this.paramset.report.rightDown == 'diagnoseTime') {
          zqCommon.getServerSystemTime(
            'down',
            print,
            svg_elem_id,
            gridWidth,
            rightUp,
            rightDown,
            rect,
            this.paramset.report.rightDown,
            ecgJson.id
          )
        } else {
          if (ecgJson[this.paramset.report.rightDown] != null) {
            zqSvg.text(
              print,
              `text_${svg_elem_id}`,
              gridWidth - rect.right,
              title2_y,
              'font-size:14px',
              'end',
              'Hanging',
              rightDown + ecgJson[this.paramset.report.rightDown]
            )
          }
        }

        const foot1_y = gridHeight - 20
        const foot2_y = gridHeight - 35

        if (ecgJson.diagnoseDoctorSign != null) {
          zqSvg.text(
            print,
            `text_${svg_elem_id}`,
            (gridWidth / 4) * 3 - 80,
            foot1_y,
            'font-size:12px',
            'end',
            'Hanging',
            '诊断医生:'
          )
          zqCommon.getBase64Image(ecgJson.diagnoseDoctorSign, base64 => {
            const diagnoseDoctorSign = base64
            zqSvg.painter_signature(
              print,
              80,
              80,
              (gridWidth / 4) * 3 - 75,
              foot2_y - 20,
              diagnoseDoctorSign,
              '',
              `image_${svg_elem_id}`
            )
          })
        }
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          (gridWidth / 4) * 3 + 50,
          foot1_y,
          'font-size:12px',
          'start',
          'Hanging',
          '审核医生:'
        )

        zqCommon.getBase64Image(ecgJson.reviewDoctorSign, base64 => {
          const reviewDoctorSign = base64
          zqSvg.painter_signature(
            print,
            80,
            80,
            (gridWidth / 4) * 3 + 105,
            foot2_y - 20,
            reviewDoctorSign,
            '',
            `image_${svg_elem_id}`
          )
        })

        const filter = `${ecgJson.baseline > 0 ? ecgJson.baseline : 'OFF '} - ${
          ecgJson.myoelect > 0 ? `${ecgJson.myoelect}Hz` : 'OFF '
        } AC ${ecgJson.frequency > 0 ? `${ecgJson.frequency}Hz ` : 'OFF '}`
        const speedGain = `${ecgParme.speed}mm/s ${ecgParme.gain}mm/mv`

        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          foot2_y,
          'font-size:12px',
          'start',
          'Hanging',
          filter + speedGain
        )
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          foot1_y,
          'font-size:12px',
          'start',
          'Hanging',
          '此报告仅供临床参考，不做任何证明材料用'
        )
      },
      getServerEcgTime(flag, print, svg_elem_id, gridWidth, rightUp, rightDown, rect, title, id) {},
      /**
       * @method  painter_userInfo_all  患者信息绘制
       * @param svg_elem_id
       * @param ecgJson
       * @param ecgParme
       * @param gridWidth
       * @param gridHeight
       */
      painter_userInfo_all(svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, offsetLeft) {
        const userInfo = document.getElementById(`userInfo_${svg_elem_id}`)
        if (userInfo != null) {
          userInfo.parentNode.removeChild(userInfo)
        }
        var svg_ecg = document.getElementById(svg_elem_id)
        zqSvg.g(svg_ecg, `userInfo_${svg_elem_id}`)
        const userInfo1 = [
          ['编号:', ecgJson.pid],
          ['姓名:', ecgJson.name != null ? ecgJson.name : ''],
          ['年龄:', ecgJson.age + zqCommon.switchAgeUnit(ecgJson.ageUnit)],
          ['性别:', zqCommon.switchSex(ecgJson.sex)],
          ['科室:', ecgJson.checkDept],
          ['床号:', ecgJson.bedNum != null ? ecgJson.bedNum : '']
        ]
        const userInfo2 = [
          ['心率:', `${ecgJson.hr} bpm`],
          ['PR:', `${ecgJson.pr} ms`],
          ['QRS:', `${ecgJson.qrs} ms`],
          ['QT/QTC:', `${ecgJson.qt}/${ecgJson.qtc} ms`],
          ['P/QRS/T:', `${ecgJson.p}/${ecgJson.qrs_}/${ecgJson.t} °`],
          ['RV5/SV1:', `${ecgJson.rv5}/${ecgJson.sv1} mv`],
          ['RV5+SV1:', `${(parseFloat(ecgJson.rv5) + parseFloat(ecgJson.sv1)).toFixed(3)} mv`]
        ]
        this.painter_userInfo(svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, userInfo1, offsetLeft, 0)
        this.painter_userInfo(svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, userInfo2, gridWidth / 4, 1)
        this.painter_result(svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, gridWidth / 2)
      },
      /**
       * @method  painter_userInfo  参数绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param gridWidth svg宽
       * @param gridHeight svg高
       * @param userInfo 参数
       * @param topY 距离顶部距离
       */
      painter_userInfo(svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, userInfo, offsetLeft, flag) {
        const userInfoWidget = document.getElementById(`userInfo_${svg_elem_id}`)
        var widget = document.createElementNS('http://www.w3.org/2000/svg', 'text')
        widget.setAttribute('x', offsetLeft)
        widget.setAttribute('y', 0)
        widget.setAttribute('text-anchor', 'start')
        widget.setAttribute('dominant-baseline', 'auto')
        widget.setAttribute('style', 'font-size:12px;')
        userInfoWidget.appendChild(widget)
        for (let i = 0; i < userInfo.length; i++) {
          const offsetPer = gridWidth / userInfo.length
          for (let j = 0; j < userInfo[i].length; j++) {
            var tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan')
            let offsetX = 0
            if (flag == 0) {
              offsetX = j == 0 ? ecgParme.pix_mm * 5 : ecgParme.pix_mm * 15
            } else {
              offsetX = j == 0 ? ecgParme.pix_mm * 5 : ecgParme.pix_mm * 20
            }
            let offsetY = ecgParme.pix_mm * 8 + i * this.userInfoSpace + 25
            if (ecgParme.previewEcg) {
              offsetY += 10
            }
            tspan.setAttribute('x', offsetX + offsetLeft)
            tspan.setAttribute('y', offsetY)
            var textString = document.createTextNode(userInfo[i][j])
            tspan.appendChild(textString)
            widget.appendChild(tspan)
          }
        }
      },
      /**
       * @method  painter_result  诊断结论绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param gridWidth svg宽
       * @param gridHeight svg高
       * @param userInfo 参数
       * @param topY 距离顶部距离
       */
      painter_result(svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, topX) {
        const userInfoWidget = document.getElementById(`userInfo_${svg_elem_id}`)
        var widget = document.createElementNS('http://www.w3.org/2000/svg', 'text')
        widget.setAttribute('x', topX)
        widget.setAttribute('y', 0)
        widget.setAttribute('text-anchor', 'start')
        widget.setAttribute('dominant-baseline', 'auto')
        widget.setAttribute('style', 'font-size:12px;')
        userInfoWidget.appendChild(widget)
        if (ecgJson.advice) {
          var advice = ecgJson.advice.split('\n')
          advice.unshift('诊断结论:')
          let index = 0
          for (let idx = 0; idx < advice.length; idx++) {
            const textWidth = this.getTextWidth('诊', 'normal 12px sans-serif')
            const preLineFontCount = Math.floor(gridWidth / 4 / textWidth) - 2
            if (advice[idx].length > preLineFontCount) {
              const subAdvice = []
              const oneAdviceLine = Math.ceil(advice[idx].length / preLineFontCount)
              for (let i = 0; i < oneAdviceLine; i++) {
                subAdvice.push(advice[idx].slice(i * preLineFontCount, (i + 1) * preLineFontCount))
              }
              for (let i = 0; i < subAdvice.length; i++) {
                var tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan')
                let offsetY = ecgParme.pix_mm * 15 + index * this.userInfoSpace
                tspan.setAttribute('x', topX)
                if (index > 6) {
                  tspan.setAttribute('x', topX + gridWidth / 4)
                  offsetY = ecgParme.pix_mm * 15 + (index - 6) * this.userInfoSpace
                }
                tspan.setAttribute('y', offsetY)
                var textString = document.createTextNode(subAdvice[i])
                tspan.appendChild(textString)
                widget.appendChild(tspan)
                index++
              }
            } else {
              var tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan')
              let offsetY = ecgParme.pix_mm * 15 + index * this.userInfoSpace
              tspan.setAttribute('x', topX)
              if (index > 6) {
                tspan.setAttribute('x', topX + gridWidth / 4)
                offsetY = ecgParme.pix_mm * 15 + (index - 6) * this.userInfoSpace
              }
              tspan.setAttribute('y', offsetY)
              var textString = document.createTextNode(advice[idx])
              tspan.appendChild(textString)
              widget.appendChild(tspan)
              index++
            }
          }
        } else {
          var tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan')
          const offsetY = ecgParme.pix_mm * 15
          tspan.setAttribute('x', topX)
          tspan.setAttribute('y', offsetY)
          var textString = document.createTextNode('诊断结论:')
          tspan.appendChild(textString)
          widget.appendChild(tspan)
        }
      },
      /**
       * @method draw_12_lead     绘制12导
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson           波形数据
       * @param ecgParme          除波形之外的其他变量参数
       * @param out_rect          绘制区域
       */
      draw_12_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, isPrint, gridWidth, gridHeight) {
        const cs2 = this
        // var printTitle = document.getElementById( "print_"+svg_ecg_id );
        let lead_sys = ecgJson.systemType
        if (lead_sys == null) {
          lead_sys = 0
        }
        var var_data = ecgJson.data
        const var_size = ecgJson.timeMs * 26
        if (isPrint == 1) {
          this.print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, out_rect)
          this.painter_userInfo_all(svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, out_rect.left)
        }
        var out_points = cs2.calc_12_lead_coodinates(
          ecgParme.layoutIndex,
          out_rect.offsetLeft,
          out_rect.top,
          ecgParme.pix_mm,
          out_rect.offsetWidth,
          gridHeight - out_rect.top - out_rect.bottom
        )
        var out_points_type = cs2.calc_12_lead_coodinates_type(ecgParme, out_rect, ecgJson.floor)

        if (isPrint != 1) {
          const message_ = document.getElementById(`message_${svg_elem_id}`)
          this.draw_ecg_parameter(message_, ecgJson, ecgParme, out_points, out_rect, 12)
          // this.draw_ecg_parameter(svg_ecg, pix_mm, p_w, p_h, speed, out_points, out_rect, layout_index, wave_color, begin_ms, 12)
        }
        // cs2.painter_12_wave(svg_ecg, pix_mm, p_w, p_h, out_rect, lead_sys, layout_index, var_data, var_size, out_points, out_points_type,ecgType,speed, gain, wave_color, begin_ms, "1");
        cs2.painter_12_wave(
          svg_ecg,
          ecgJson,
          svg_elem_id,
          ecgParme,
          out_rect,
          lead_sys,
          var_data,
          var_size,
          out_points,
          out_points_type,
          gridWidth,
          gridHeight
        )
        if (isPrint == 0) {
          cs2.painter_message(
            svg_ecg,
            svg_elem_id,
            'gain_speed_tip',
            `${ecgParme.speed}mm/s     ${ecgParme.gain}mm/mv`,
            '98%',
            20,
            '14PX',
            'end',
            ecgParme.textColor
          )
        }
        // let lead_name = zqSvg.get_12_lead_system_name(lead_sys);

        // let lead_name = ecgJson["leadName"]
        const lead_name = []
        ecgJson.leadName.forEach(item => {
          lead_name.push(item)
        })
        const out_lcr = cs2.get_12_lead_lcr(ecgParme.layoutIndex)
        const rhythm_count = out_lcr.rhythm
        if (rhythm_count > 0) {
          var rhythm_index = [1, 2, 3]

          if (rhythm_count == 3) {
            rhythm_index[0] = ecgParme.rhythm1
            rhythm_index[1] = ecgParme.rhythm2
            rhythm_index[2] = ecgParme.rhythm3
          } else {
            rhythm_index[0] = ecgParme.rhythm
          }
          for (var index = 0; index < rhythm_count; index++) {
            var leadIndex = rhythm_index[index]
            lead_name.push(lead_name[leadIndex])
          }
        }
        cs2.painter_12_rhythm(
          svg_ecg,
          svg_elem_id,
          ecgParme,
          out_rect,
          var_data,
          var_size,
          out_points,
          rhythm_index,
          gridWidth,
          gridHeight
        )
        cs2.painter_leadnameEx(svg_ecg, svg_elem_id, ecgParme, 14, lead_name, out_points)

        // cs2.painter_12_rhythm(svg_ecg, pix_mm, p_w, p_h, out_rect, lead_sys, layout_index, var_data, var_size, out_points, speed, gain, rhythm_color, rhythm_index, begin_ms,rhythmVal);
        // cs2.painter_leadnameEx(svg_ecg, pix_mm, 14, lead_name, out_points, 12,wave_color);
      },
      /**
       * @method draw_15_lead     绘制15导
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson           波形数据
       * @param ecgParme          除波形之外的其他变量参数
       * @param out_rect          绘制区域
       */
      draw_15_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, isPrint, gridWidth, gridHeight) {
        const cs2 = this
        // var printTitle = document.getElementById( "print_" + svg_elem_id );
        var var_data = ecgJson.data
        const var_size = ecgJson.timeMs * 32
        let lead_sys = ecgJson.systemType
        if (lead_sys == null) {
          lead_sys = 0
        }
        if (ecgJson.type == 8) {
          lead_sys = 1
        }
        if (isPrint == 1) {
          this.print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, out_rect)
          this.painter_userInfo_all(svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, out_rect.left)
        }
        var out_points = cs2.calc_15_lead_coodinates(
          ecgParme.layoutIndex,
          out_rect.offsetLeft,
          out_rect.top,
          ecgParme.pix_mm,
          out_rect.offsetWidth,
          gridHeight - out_rect.top - out_rect.bottom
        )
        var out_points_type = cs2.calc_15_lead_coodinates_type(ecgParme, out_rect, ecgJson.floor)
        if (isPrint != 1) {
          const message_ = document.getElementById(`message_${svg_elem_id}`)
          this.draw_ecg_parameter(message_, ecgJson, ecgParme, out_points, out_rect, 15)
        }
        cs2.painter_15_wave(
          svg_ecg,
          ecgJson,
          svg_elem_id,
          ecgParme,
          out_rect,
          lead_sys,
          var_data,
          var_size,
          out_points,
          out_points_type,
          gridWidth,
          gridHeight
        )
        if (isPrint == 0) {
          cs2.painter_message(
            svg_ecg,
            svg_elem_id,
            'gain_speed_tip',
            `${ecgParme.speed}mm/s     ${ecgParme.gain}mm/mv`,
            '98%',
            20,
            '14PX',
            'end',
            ecgParme.textColor
          )
        }
        // var lead_name = zqSvg.get_15_lead_system_name(lead_sys);
        const lead_name = []
        ecgJson.leadName.forEach(item => {
          lead_name.push(item)
        })
        var out_lcr = cs2.get_15_lead_lcr(ecgParme.layoutIndex)
        const rhythm_count = out_lcr.rhythm
        if (rhythm_count > 0) {
          var rhythm_index = [1, 2, 3]

          if (rhythm_count == 3) {
            rhythm_index[0] = ecgParme.rhythm1
            rhythm_index[1] = ecgParme.rhythm2
            rhythm_index[2] = ecgParme.rhythm3
          } else {
            rhythm_index[0] = ecgParme.rhythm
          }
          for (var index = 0; index < rhythm_count; index++) {
            var leadIndex = rhythm_index[index]
            lead_name.push(lead_name[leadIndex])
          }
        }
        cs2.painter_15_rhythm(
          svg_ecg,
          svg_elem_id,
          ecgParme,
          out_rect,
          var_data,
          var_size,
          out_points,
          rhythm_index,
          gridWidth,
          gridHeight
        )
        cs2.painter_leadnameEx(svg_ecg, svg_elem_id, ecgParme, 14, lead_name, out_points)
      },
      /**
       * @method draw_18_lead     绘制18导
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson           波形数据
       * @param ecgParme          除波形之外的其他变量参数
       * @param out_rect          绘制区域
       */
      draw_18_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, isPrint, gridWidth, gridHeight) {
        const cs2 = this
        // var printTitle = document.getElementById( "print_" + svg_elem_id );
        var var_data = ecgJson.data
        const var_size = ecgJson.timeMs * 38

        let lead_sys = ecgJson.systemType
        if (lead_sys == null) {
          lead_sys = 0
        }
        if (isPrint == 1) {
          this.print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, out_rect)
          this.painter_userInfo_all(svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, out_rect.left)
        }
        var out_points = cs2.calc_18_lead_coodinates(
          ecgParme.layoutIndex,
          out_rect.offsetLeft,
          out_rect.top,
          ecgParme.pix_mm,
          out_rect.offsetWidth,
          gridHeight - out_rect.top - out_rect.bottom
        )
        var out_points_type = cs2.calc_18_lead_coodinates_type(ecgParme, out_rect, ecgJson.floor)
        if (isPrint != 1) {
          const message_ = document.getElementById(`message_${svg_elem_id}`)
          this.draw_ecg_parameter(message_, ecgJson, ecgParme, out_points, out_rect, 18)
        }
        cs2.painter_18_wave(
          svg_ecg,
          ecgJson,
          svg_elem_id,
          ecgParme,
          out_rect,
          lead_sys,
          var_data,
          var_size,
          out_points,
          out_points_type
        )
        if (isPrint == 0) {
          cs2.painter_message(
            svg_ecg,
            svg_elem_id,
            'gain_speed_tip',
            `${ecgParme.speed}mm/s     ${ecgParme.gain}mm/mv`,
            '98%',
            20,
            '14PX',
            'end',
            ecgParme.textColor
          )
        }
        // var lead_name = zqSvg.get_18_lead_system_name(lead_sys);
        const lead_name = []
        ecgJson.leadName.forEach(item => {
          lead_name.push(item)
        })
        var out_lcr = cs2.get_18_lead_lcr(ecgParme.layoutIndex)
        const rhythm_count = out_lcr.rhythm
        if (rhythm_count > 0) {
          var rhythm_index = [1, 2, 3]

          if (rhythm_count == 3) {
            rhythm_index[0] = ecgParme.rhythm1
            rhythm_index[1] = ecgParme.rhythm2
            rhythm_index[2] = ecgParme.rhythm3
          } else {
            rhythm_index[0] = ecgParme.rhythm
          }
          for (var index = 0; index < rhythm_count; index++) {
            var leadIndex = rhythm_index[index]
            lead_name.push(lead_name[leadIndex])
          }
        }
        cs2.painter_18_rhythm(svg_ecg, svg_elem_id, ecgParme, out_rect, var_data, var_size, out_points, rhythm_index)
        cs2.painter_leadnameEx(svg_ecg, svg_elem_id, ecgParme, 14, lead_name, out_points)
      },
      /**
       * @method draw_ecg_parameter       绘制波形参数
       * @param svg_ecg
       * @param ecgJson
       * @param ecgParme                  除波形之外的其他变量参数
       * @param out_points                数据点
       * @param out_rect                  绘制区域
       * @param lead                      导联索引
       */
      draw_ecg_parameter(svg_ecg, ecgJson, ecgParme, out_points, out_rect, lead) {
        const cs2 = this

        var QTVector = ecgJson.data
        var r_pos_array = zqDataProcessing.get_r_pos_array(QTVector[1])
        var RR_pos = zqDataProcessing.getPos_all(r_pos_array)
        var HR_pos = zqDataProcessing.get_all_hr(RR_pos)
        cs2.painter_ecg_parameter(svg_ecg, ecgJson, ecgParme, out_points, out_rect, lead, r_pos_array, RR_pos, HR_pos)
      },
      /**
       * @method draw_ecg_single      绘制单导联
       * @param svg_elem_id
       * @param ecgJson               波形数据
       * @param ecgParme              除波形之外的其他变量参数
       * @param isPrint               是否打印
       * @param gridWidth             区域宽度
       * @param gridHeight            区域高度
       */
      draw_ecg_single(svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight, speedTimes) {
        this.clearEcgWave(svg_elem_id)
        var svg_ecg = document.getElementById(svg_elem_id)
        this.creatEcgChild(svg_ecg, svg_elem_id)
        svg_ecg.setAttribute('width', gridWidth)
        svg_ecg.setAttribute('height', gridHeight)
        this.draw_single_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight, speedTimes ? speedTimes : 1)
      },
      /**
       * @method draw_single_lead     绘制单导联
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson               波形数据
       * @param ecgParme              除波形之外的其他变量参数
       * @param isPrint               是否打印
       * @param gridWidth             区域宽度
       * @param gridHeight            区域高度
       */
      draw_single_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight, speedTimes) {
        const cs2 = this
        let lead_sys = ecgJson.systemType
        if (lead_sys == null) {
          lead_sys = 0
        }
        var var_data = ecgJson.data
        let var_size = 0
        if (ecgJson.lead === 12) {
          var_size = ecgJson.timeMs * 26
        } else if (ecgJson.lead === 15) {
          var_size = ecgJson.timeMs * 32
        } else {
          var_size = ecgJson.timeMs * 38
        }

        var rect = {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0
        }
        // if (isPrint == 1) {
        //     var rect = {
        //         "left": 0,
        //         "right": 0,
        //         "top": 200,
        //         "bottom": 20
        //     };
        //     this.printInfo(svg_ecg_id, printTitle,p_w, p_h, speed, gain, 12, wave_color, "")
        // }
        var out_rect = zqSvg.painter_rect(svg_ecg, ecgParme.pix_mm, rect, gridWidth, gridHeight)
        out_rect.offsetLeft = out_rect.left + (ecgParme.speed / 5) * ecgParme.pix_mm + ecgParme.pix_mm * 2
        out_rect.offsetWidth = gridWidth - out_rect.offsetLeft - out_rect.right
        zqSvg.painter_grid(svg_ecg, svg_elem_id, ecgParme, out_rect, gridWidth, gridHeight)
        this.painter_gain(svg_ecg, svg_elem_id, ecgParme, out_rect.left, out_rect.top + (gridHeight - out_rect.top) / 2)

        // var avg = Math.ceil((ecgJson["timeMs"] / 1000 * ecgParme.speed * ecgParme.pix_mm) / gridWidth);
        var avg = 1
        var out_points = {}
        out_points.x = out_rect.offsetLeft
        out_points.y = (gridHeight - out_rect.top) / (avg + 1)
        let lead_name, out_lcr
        switch (ecgJson.lead) {
          case 12:
            cs2.painter_single12_wave(svg_ecg, svg_elem_id, ecgParme, out_rect, var_data, var_size, out_points, avg, speedTimes)
            // lead_name = zqSvg.get_12_lead_system_name(lead_sys);
            lead_name = ecgJson.leadName
            out_lcr = cs2.get_12_lead_lcr(ecgParme.layoutIndex)
            cs2.painter_leadnameEx_single(
              ecgParme,
              svg_ecg,
              svg_elem_id,
              ecgParme.pix_mm,
              14,
              lead_name,
              out_points,
              out_rect,
              12,
              ecgParme.waveColor
            )
            break
          case 15:
            cs2.painter_single15_wave(svg_ecg, svg_elem_id, ecgParme, out_rect, var_data, var_size, out_points, avg)
            // lead_name = zqSvg.get_15_lead_system_name(lead_sys);
            lead_name = ecgJson.leadName
            out_lcr = cs2.get_15_lead_lcr(ecgParme.layoutIndex)
            cs2.painter_leadnameEx_single(
              ecgParme,
              svg_ecg,
              svg_elem_id,
              ecgParme.pix_mm,
              14,
              lead_name,
              out_points,
              out_rect,
              15,
              ecgParme.waveColor
            )
            break
          case 18:
            cs2.painter_single18_wave(svg_ecg, svg_elem_id, ecgParme, out_rect, var_data, var_size, out_points, avg)
            // lead_name = zqSvg.get_18_lead_system_name(lead_sys);
            lead_name = ecgJson.leadName
            out_lcr = cs2.get_18_lead_lcr(ecgParme.layoutIndex)
            cs2.painter_leadnameEx_single(
              ecgParme,
              svg_ecg,
              svg_elem_id,
              ecgParme.pix_mm,
              14,
              lead_name,
              out_points,
              out_rect,
              18,
              ecgParme.waveColor
            )
            break
        }
      },

      /**
       * @method  creatAvgChild  svg内部组件创建
       * @param svg_ecg
       * @param svg_elem_id
       */
      creatAvgChild(svg_ecg, svg_elem_id) {
        zqSvg.g(svg_ecg, `avg_${svg_elem_id}`)
      },
      /**
       * @method  clearEcgWave  svg清除函数
       * @param svg_elem_id
       */
      clearAvgWave(svg_elem_id) {
        // avg
        var avg = document.getElementById(`avg_${svg_elem_id}`)
        if (avg != null) {
          avg.parentNode.removeChild(avg)
        }
      },
      /**
       * @method draw_avg_wave    平均模板绘制函数
       * @param svg_elem_id
       * @param ecgJson           波形数据
       * @param ecgParme          除波形之外的其他变量参数
       * @param gridWidth         区域宽度
       * @param gridHeight        区域高度
       * @param pd_avgObj         平均模板对象
       */
      draw_avg_wave(svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, pd_avgObj) {
        this.clearAvgWave(svg_elem_id)
        var svg_ecg = document.getElementById(svg_elem_id)
        this.creatAvgChild(svg_ecg, svg_elem_id)
        svg_ecg.setAttribute('width', gridWidth)
        svg_ecg.setAttribute('height', gridHeight)

        switch (ecgJson.lead) {
          case 12:
            if (ecgParme.leadIndex < 0) {
              for (var i = 0; i < 12; i++) {
                this.draw_12_avgWave(svg_ecg, svg_elem_id, ecgParme, ecgJson, i, pd_avgObj)
              }
            } else {
              this.draw_12_avgWave(svg_ecg, svg_elem_id, ecgParme, ecgJson, ecgParme.leadIndex, pd_avgObj)
            }
            break
          case 15:
            if (ecgParme.leadIndex < 0) {
              for (var i = 0; i < 15; i++) {
                this.draw_15_avgWave(svg_ecg, svg_elem_id, ecgParme, ecgJson, i, pd_avgObj)
              }
            } else {
              this.draw_15_avgWave(svg_ecg, svg_elem_id, ecgParme, ecgJson, ecgParme.leadIndex, pd_avgObj)
            }
            break
          case 18:
            if (ecgParme.leadIndex < 0) {
              for (var i = 0; i < 18; i++) {
                this.draw_18_avgWave(svg_ecg, svg_elem_id, ecgParme, ecgJson, i, pd_avgObj)
              }
            } else {
              this.draw_18_avgWave(svg_ecg, svg_elem_id, ecgParme, ecgJson, ecgParme.leadIndex, pd_avgObj)
            }
            break
        }
      },
      /**
       * @method draw_12_avgWave      12导平均模板数据换算函数
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param ecgJson               波形数据
       * @param leadIndex             导联索引
       * @param pd_avgObj             平均模板对象
       */
      draw_12_avgWave(svg_ecg, svg_elem_id, ecgParme, ecgJson, leadIndex, pd_avgObj) {
        const cs2 = this
        var var_data = ecgJson.data
        const var_size = ecgJson.timeMs * 26
        var PR = ecgJson.pr
        var QRS = ecgJson.qrs
        var QT = ecgJson.qt
        var QTC = ecgJson.qtc
        var rect = {
          left: 20,
          right: 0,
          top: 0,
          bottom: 0
        }
        let lead_sys = ecgJson.systemType
        if (lead_sys == null) {
          lead_sys = 0
        }

        var out_rect = zqSvg.painter_rect(svg_ecg, ecgParme.pix_mm, rect, ecgParme.gridWidth, ecgParme.gridHeight)
        zqSvg.painter_grid(svg_ecg, svg_elem_id, ecgParme, out_rect, ecgParme.gridWidth, ecgParme.gridHeight)

        var out_point = {}
        out_point.x = 30
        out_point.y = ecgParme.gridHeight / 2
        var oneLeadData = cs2.get12OneLeadData(leadIndex, var_data, var_size)
        var startEndPoint = zqDataProcessing.get_one_cardiac(oneLeadData, pd_avgObj, 1)
        if (startEndPoint == -1) return -1
        const pointsJson = zqDataProcessing.getPointsPosition(oneLeadData, pd_avgObj)
        cs2.painter_12_avgWave(
          svg_ecg,
          svg_elem_id,
          ecgParme,
          out_rect,
          lead_sys,
          leadIndex,
          var_data,
          var_size,
          PR,
          QRS,
          QT,
          QTC,
          startEndPoint,
          out_point,
          200,
          40,
          pd_avgObj,
          pointsJson
        )
        // cs2.painter_12_avgWave(svg_ecg, pix_mm, p_w, p_h, out_rect, lead_sys, lead_index, var_data, var_size, PR, QRS, QT, QTC, startEndPoint, out_point, 200, 40, wave_color, pd_avgObj,pointsJson);
        return 0
      },
      /**
       * @method draw_15_avgWave      15导平均模板数据换算函数
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param ecgJson               波形数据
       * @param leadIndex             导联索引
       * @param pd_avgObj             平均模板对象
       */
      draw_15_avgWave(svg_ecg, svg_elem_id, ecgParme, ecgJson, leadIndex, pd_avgObj) {
        const cs2 = this
        var var_data = ecgJson.data
        const var_size = ecgJson.timeMs * 32
        var { PR } = ecgJson
        var { QRS } = ecgJson
        var { QT } = ecgJson
        var { QTC } = ecgJson
        var rect = {
          left: 20,
          right: 0,
          top: 0,
          bottom: 0
        }
        let lead_sys = ecgJson.systemType
        if (lead_sys == null) {
          lead_sys = 0
        }
        if (ecgJson.type == 8) {
          lead_sys = 1
        }
        var out_rect = zqSvg.painter_rect(svg_ecg, ecgParme.pix_mm, rect, ecgParme.gridWidth, ecgParme.gridHeight)
        zqSvg.painter_grid(svg_ecg, svg_elem_id, ecgParme, out_rect, ecgParme.gridWidth, ecgParme.gridHeight)
        var out_point = {}
        out_point.x = 30
        out_point.y = ecgParme.gridHeight / 2
        var oneLeadData = cs2.get15OneLeadData(leadIndex, var_data, var_size)
        var startEndPoint = zqDataProcessing.get_one_cardiac(oneLeadData, pd_avgObj, 1)
        const pointsJson = zqDataProcessing.getPointsPosition(oneLeadData, pd_avgObj)
        cs2.painter_15_avgWave(
          svg_ecg,
          svg_elem_id,
          ecgParme,
          out_rect,
          lead_sys,
          leadIndex,
          var_data,
          var_size,
          PR,
          QRS,
          QT,
          QTC,
          startEndPoint,
          out_point,
          200,
          40,
          pd_avgObj,
          pointsJson
        )
      },
      /**
       * @method draw_18_avgWave      18导平均模板数据换算函数
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme              除波形之外的其他变量参数
       * @param ecgJson               波形数据
       * @param leadIndex             导联索引
       * @param pd_avgObj             平均模板对象
       */
      draw_18_avgWave(svg_ecg, svg_elem_id, ecgParme, ecgJson, leadIndex, pd_avgObj) {
        const cs2 = this
        var var_data = ecgJson.data
        const var_size = ecgJson.timeMs * 38
        var { PR } = ecgJson
        var { QRS } = ecgJson
        var { QT } = ecgJson
        var { QTC } = ecgJson
        var rect = {
          left: 20,
          right: 0,
          top: 0,
          bottom: 0
        }
        let lead_sys = ecgJson.systemType
        if (lead_sys == null) {
          lead_sys = 0
        }
        var out_rect = zqSvg.painter_rect(svg_ecg, ecgParme.pix_mm, rect, ecgParme.gridWidth, ecgParme.gridHeight)
        zqSvg.painter_grid(svg_ecg, svg_elem_id, ecgParme, out_rect, ecgParme.gridWidth, ecgParme.gridHeight)
        var out_point = {}
        out_point.x = 30
        out_point.y = ecgParme.gridHeight / 2
        var oneLeadData = cs2.get18OneLeadData(leadIndex, var_data, var_size)
        var startEndPoint = zqDataProcessing.get_one_cardiac(oneLeadData, pd_avgObj, 1)
        const pointsJson = zqDataProcessing.getPointsPosition(oneLeadData, pd_avgObj)
        cs2.painter_18_avgWave(
          svg_ecg,
          svg_elem_id,
          ecgParme,
          out_rect,
          lead_sys,
          leadIndex,
          var_data,
          var_size,
          PR,
          QRS,
          QT,
          QTC,
          startEndPoint,
          out_point,
          200,
          40,
          pd_avgObj,
          pointsJson
        )
      },
      /**
       * @method clearEcgWave     svg清除函数
       * @param svg_elem_id
       */
      clearEcgWave(svg_elem_id) {
        // print打印参数
        var print = document.getElementById(`print_${svg_elem_id}`)
        if (print != null) {
          print.parentNode.removeChild(print)
        }
        // wave
        var wave = document.getElementById(`wave_${svg_elem_id}`)
        if (wave != null) {
          wave.parentNode.removeChild(wave)
        }
        // message
        const message = document.getElementById(`message_${svg_elem_id}`)
        if (message != null) {
          message.parentNode.removeChild(message)
        }

        const scaleRule = document.getElementById(`scaleRule_${svg_elem_id}`)
        if (scaleRule != null) {
          scaleRule.parentNode.removeChild(message)
        }
      },
      /**
       * @method  clearEcgWave  svg content清除函数
       * @param svg_elem_id
       */
      clearContentWave(svg_elem_id) {
        // 打印参数
        const content = document.getElementById(`content_${svg_elem_id}`)
        if (content != null) {
          content.parentNode.removeChild(content)
        }
        const grid = document.getElementById(`grid_${svg_elem_id}`)
        if (grid != null) {
          grid.parentNode.removeChild(grid)
        }
        this.createGrid(svg_elem_id)
      },
      /**
       * @method clearGridMessage     清除表格
       * @param svg_elem_id
       */
      clearGrid(svg_elem_id) {
        const grid = document.getElementById(`grid_${svg_elem_id}`)
        if (grid != null) {
          grid.parentNode.removeChild(grid)
        }
      },
      /**
       * @method createGrid     创建表格
       * @param svg_elem_id
       */
      createGrid(svg_elem_id) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'g')
        widget.setAttribute('id', `grid_${svg_elem_id}`)
        const dotPath = document.createElementNS('http://www.w3.org/2000/svg', 'path')
        dotPath.setAttribute('id', `dotGrid_${svg_elem_id}`)
        widget.appendChild(dotPath)
        const linePath = document.createElementNS('http://www.w3.org/2000/svg', 'path')
        linePath.setAttribute('id', `lineGrid_${svg_elem_id}`)
        widget.appendChild(linePath)
        var svg_ecg = document.getElementById(svg_elem_id)
        svg_ecg.appendChild(widget)
      },
      /**
       * @method setDotColor     设置小点颜色
       * @param svg_elem_id
       * @param ecgParme          除波形之外的其他变量参数
       */
      setDotColor(svg_elem_id, ecgParme) {
        var svg_ecg = document.getElementById(svg_elem_id)
        const Hpath = svg_ecg.getElementById(`dotGrid_${svg_elem_id}`)
        if (Hpath != null) {
          Hpath.setAttribute(
            'style',
            `fill:transparent;stroke:${ecgParme.dotColor};stroke-width:${ecgParme.dotLineWidth}`
          )
        }
      },
      /**
       * @method setGridColor     设置网格颜色
       * @param svg_elem_id
       * @param ecgParme          除波形之外的其他变量参数
       */
      setGridColor(svg_elem_id, ecgParme) {
        var svg_ecg = document.getElementById(svg_elem_id)
        const Hpath = svg_ecg.getElementById(`dotGrid_${svg_elem_id}`)
        if (Hpath != null) {
          const B_Hpath = svg_ecg.getElementById(`lineGrid_${svg_elem_id}`)
          if (B_Hpath != null) {
            B_Hpath.setAttribute(
              'style',
              `fill:transparent;stroke:${ecgParme.gridColor};stroke-width:${ecgParme.gridLineWidth}`
            )
          }
        }
      }
    }
    window.zqStandard = window.zqStandard || new zqStandard()
    /**
     * 模块说明
     * @module  zqSpectrum	 频谱封装
     * @rely  zqDataProcessing zqSvg
     */
    function zqSpectrum() {
      this.userInfoParWidth = 20 * 5
      this.scaleFontSize = '12px'
    }
    zqSpectrum.prototype = {
      /**
       * @method  draw_ecg_spectrum  频谱绘制入口函数
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_ecg_spectrum(svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight) {
        this.clearEcgWave(svg_elem_id)
        var svg_ecg = document.getElementById(svg_elem_id)
        this.creatEcgChild(svg_ecg, svg_elem_id)
        svg_ecg.setAttribute('width', gridWidth)
        svg_ecg.setAttribute('height', gridHeight)
        this.draw_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight)
      },
      /**
       * @method  creatEcgChild  svg内部组件创建
       * @param svg_ecg
       * @param svg_elem_id
       */
      creatEcgChild(svg_ecg, svg_elem_id) {
        zqSvg.g(svg_ecg, `print_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `graph_II_V5_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `graph_RF_Qxy_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `graph_Vxy_PIH_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `graph_title_${svg_elem_id}`)
      },
      /**
       * @method  draw_lead  频谱绘制入口函数
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight) {
        var rect = { left: 0, right: 0, top: 0, bottom: 0 }
        if (isPrint == 1) {
          // rect = {"left":15,"right":15,"top": 10 * ecgParme.pix_mm,"bottom":20};
          // rect = {"left":15,"right":15,"top":70,"bottom":20};
          rect = { left: 30, right: 10, top: 60, bottom: 30 }
          // let print = document.getElementById("print_" + svg_elem_id);
          // zqSvg.text(print,"text_" + svg_elem_id,
          //     gridWidth / 2, 20,
          //     "font-size:18px", "middle", "Hanging", "频谱心电报告"
          // )
          this.print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, rect)
        }
        var out_rect = zqSvg.painter_rect(svg_ecg, ecgParme.pix_mm, rect, gridWidth, gridHeight)
        zqSvg.painter_grid(svg_ecg, svg_elem_id, ecgParme, out_rect, gridWidth, gridHeight)
        this.draw_ecg(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, gridWidth, gridHeight)
      },
      /**
       * @method  print  打印内容
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson
       * @param ecgParme
       */
      print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, rect) {
        const print = document.getElementById(`print_${svg_elem_id}`)
        let { title1 } = paramset.report
        if (title1 == '') {
          title1 = ecgJson.checkSection
        }
        const { title2 } = paramset.report
        // if(title2 == ''){
        //     title2 = ecgJson['checkDept']
        // }
        const title1_y = 20
        const title2_y = 40
        let line_y = 7 * ecgParme.pix_mm
        if (ecgParme.previewEcg) {
          line_y = 7 * ecgParme.pix_mm + 10
        }

        zqSvg.text(print, `text_${svg_elem_id}`, gridWidth / 2, title1_y, 'font-size:18px', 'middle', 'Hanging', title1)
        zqSvg.text(print, `text_${svg_elem_id}`, gridWidth / 2, title2_y, 'font-size:14px', 'middle', 'Hanging', title2)
        zqSvg.line(
          print,
          ecgParme.pix_mm * 5,
          gridWidth - ecgParme.pix_mm * 2,
          line_y,
          line_y,
          `line_${svg_elem_id}`,
          'stroke-width:1;stroke:#000000'
        )
        const rightDown = zqSvg.getPrintTimeName(paramset.report.rightDown)
        const rightUp = zqSvg.getPrintTimeName(paramset.report.rightUp)

        if (paramset.report.rightUp == 'reviewTime' || paramset.report.rightUp == 'diagnoseTime') {
          zqCommon.getServerSystemTime(
            'up',
            print,
            svg_elem_id,
            gridWidth,
            rightUp,
            rightDown,
            rect,
            paramset.report.rightUp,
            ecgJson.id
          )
        } else if (ecgJson[paramset.report.rightUp] != null) {
          zqSvg.text(
            print,
            `text_${svg_elem_id}`,
            gridWidth - rect.right,
            title1_y,
            'font-size:14px',
            'end',
            'Hanging',
            rightUp + ecgJson[paramset.report.rightUp]
          )
        }
        if (paramset.report.rightDown == 'reviewTime' || paramset.report.rightDown == 'diagnoseTime') {
          zqCommon.getServerSystemTime(
            'down',
            print,
            svg_elem_id,
            gridWidth,
            rightUp,
            rightDown,
            rect,
            paramset.report.rightDown,
            ecgJson.id
          )
        } else if (ecgJson[paramset.report.rightDown] != null) {
          zqSvg.text(
            print,
            `text_${svg_elem_id}`,
            gridWidth - rect.right,
            title2_y,
            'font-size:14px',
            'end',
            'Hanging',
            rightDown + ecgJson[paramset.report.rightDown]
          )
        }
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          title1_y,
          'font-size:14px',
          'start',
          'Hanging',
          '频谱心电报告'
        )
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          gridHeight - 15,
          'font-size:12px',
          'start',
          'Hanging',
          '此报告仅供临床参考，不做任何证明材料用'
        )
      },
      /**
       * @method  imagReal  获取II 和 V5实部虚部
       * @param vector 波形数据
       * @returns {Array}
       */
      imagReal(vector) {
        const newVector = []
        for (let i = 0; i < vector.length; i++) {
          const data1 = new ComplexArray(vector[i])
          const data2 = fft(data1, false)
          newVector[i] = data2
        }
        return newVector
      },
      /**
       * @method  draw_ecg  频谱绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param out_rect svg波形可绘制区域上下左右距离
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_ecg(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, gridWidth, gridHeight) {
        const graph = {}
        // graph.graphW = Math.floor(out_rect.wGrid / 2 - 4 ) * 5 * ecgParme.pix_mm - this.userInfoParWidth * ecgParme.pix_mm ;
        graph.graphW = (gridWidth - this.userInfoParWidth * ecgParme.pix_mm - out_rect.left - out_rect.right) / 2
        // graph.graphH = Math.floor(out_rect.hGrid / 4 ) * 5 * ecgParme.pix_mm - out_rect["top"] - out_rect["bottom"];
        graph.graphH = (gridHeight - out_rect.top - out_rect.bottom) / 4
        graph.offsetLeft = out_rect.left + ecgParme.pix_mm * 10
        graph.offsetTop = out_rect.top + ecgParme.pix_mm * 10
        graph.average = (gridWidth - this.userInfoParWidth * ecgParme.pix_mm) / 1.8
        graph.averageH = (gridHeight - out_rect.top - out_rect.bottom - ecgParme.pix_mm * 30) / 3
        graph.perWAverage = graph.graphW / 5
        graph.perHAverage = graph.graphH / 5
        // 获取II 和 V5 zqecg数据
        const vector = [ecgJson.data[1].slice(0, 8192), ecgJson.data[10].slice(0, 8192)]

        // 转换II 和 V5实部虚部fft
        const imagRealVector = this.imagReal(vector)

        // II 和V5数据处理
        var vector_II_V5 = this.spectrum_II_V5_data(imagRealVector)

        // II 和V5绘制自动率谱绘制
        this.painter_II_V5(svg_ecg, svg_elem_id, ecgParme, out_rect, vector_II_V5, graph)

        // RF数据处理-原始值
        var vector_RF = this.spectrum_RF_data(vector)
        // Qxy数据处理
        var vector_Qxy = this.spectrum_Qxy_data(imagRealVector)
        // RF Qxy绘制
        this.painter_Qxy_RF(svg_ecg, svg_elem_id, ecgParme, out_rect, vector_Qxy, vector_RF, graph)
        // Vxy数据处理-原始值
        var vector_Vxy = this.spectrum_Vxy_data(vector)
        // PIH数据处理
        var vector_PIH = this.spectrum_PIH_data(vector_Qxy)
        // Vxy PIH绘制
        this.painter_Vxy_PIH(svg_ecg, svg_elem_id, ecgParme, out_rect, vector_PIH, vector_Vxy, graph)
        // 坐标系title
        const titleArr = [
          ['II自功率谱', 'X:3mm/Hz', 'Y:30mm/mv'],
          ['V5自功率谱', 'X:3mm/Hz', 'Y:30mm/mv'],
          ['RF相干函数', 'X:3mm/Hz', 'Y:1 == 20mm'],
          ['Qxy相移函数', 'X:3mm/Hz', 'Y:180deg == 20mm'],
          ['Vxy 互相关函数', 'X:45ms/mm', ''],
          ['PIH脉冲响应', 'X:45ms/mm', '']
        ]
        this.painter_title(svg_ecg, svg_elem_id, ecgParme, out_rect, graph, titleArr)
        const userInfo = [
          [ecgJson.pid, '编号：'],
          [ecgJson.name != null ? ecgJson.name : '', '姓名：'],
          [zqCommon.switchSex(ecgJson.sex), '性别：'],
          [ecgJson.age + zqCommon.switchAgeUnit(ecgJson.ageUnit), '年龄：'],
          [ecgJson.checkDept, '科室：'],
          [ecgJson.bedNum != null ? ecgJson.bedNum : '', '床号：']
        ]
        this.painter_userInfo(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, gridWidth, gridHeight, userInfo, 0)
      },
      /**
       * @method  painter_userInfo  参数绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param out_rect svg波形可绘制区域上下左右距离
       * @param gridWidth svg宽
       * @param gridHeight svg高
       * @param userInfo 参数
       * @param topY 距离顶部距离
       */
      painter_userInfo(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, gridWidth, gridHeight, userInfo, topY) {
        const printTitle = document.getElementById(`print_${svg_elem_id}`)
        var widget = document.createElementNS('http://www.w3.org/2000/svg', 'text')
        widget.setAttribute('x', gridWidth)
        widget.setAttribute('y', out_rect.top)
        widget.setAttribute('text-anchor', 'end')
        widget.setAttribute('dominant-baseline', 'auto')
        widget.setAttribute('style', 'font-size:12px;')
        printTitle.appendChild(widget)
        for (let i = 0; i < userInfo.length; i++) {
          const offsetPer = gridWidth / userInfo.length
          for (let j = 0; j < userInfo[i].length; j++) {
            var tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan')
            const offsetX = j == 0 ? 0 : 120
            const offsetY = out_rect.top + i * 30
            tspan.setAttribute('x', gridWidth - offsetX - 10 * ecgParme.pix_mm - out_rect.left)
            tspan.setAttribute('y', offsetY + 5 * ecgParme.pix_mm + topY)
            var textString = document.createTextNode(userInfo[i][j])
            tspan.appendChild(textString)
            widget.appendChild(tspan)
          }
        }
      },
      /**
       * @method  painter_title  坐标系title绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme
       * @param out_rect out_rect svg波形可绘制区域上下左右距离
       * @param graph 坐标系参数
       * @param titleArr 坐标系title
       */
      painter_title(svg_ecg, svg_elem_id, ecgParme, out_rect, graph, titleArr) {
        const graph_title = document.getElementById(`graph_title_${svg_elem_id}`)
        for (let idx = 0; idx < titleArr.length; idx++) {
          const average = idx % 2 == 0 ? 0 : graph.average
          zqSvg.text(
            graph_title,
            `text_${svg_elem_id}`,
            graph.offsetLeft + average + ecgParme.pix_mm,
            graph.offsetTop + ecgParme.pix_mm * 10 * Math.floor(idx / 2) + graph.graphH * Math.floor(idx / 2),
            `font-size:${this.scaleFontSize}`,
            'start',
            'Middle',
            titleArr[idx][0]
          )
          zqSvg.text(
            graph_title,
            `text_${svg_elem_id}`,
            graph.offsetLeft + graph.graphW + average,
            graph.offsetTop + ecgParme.pix_mm * 10 * Math.floor(idx / 2) + graph.graphH * Math.floor(idx / 2),
            `font-size:${this.scaleFontSize}`,
            'end',
            'Middle',
            titleArr[idx][1]
          )
          zqSvg.text(
            graph_title,
            `text_${svg_elem_id}`,
            graph.offsetLeft + graph.graphW + average,
            graph.offsetTop + ecgParme.pix_mm * 10 * Math.floor(idx / 2) + graph.graphH * Math.floor(idx / 2) + 15,
            `font-size:${this.scaleFontSize}`,
            'end',
            'Middle',
            titleArr[idx][2]
          )
        }
      },
      /**
       * @method  painter_title  II 和V5 自动率谱数据换算
       * @param data 波形数据
       * @returns {Array}
       */
      spectrum_II_V5_data(data) {
        const vector = []
        for (let i = 0; i < data.length; i++) {
          vector[i] = []
          const sum = []
          const len = data[i].imag.length
          for (let index = 0; index < len; index++) {
            const imag = Math.pow(data[i].imag[index], 2) // Math.pow(2,4)计算2的4次方
            const real = Math.pow(data[i].real[index], 2)
            const result = (imag + real) / len
            sum.push(result)
          }
          vector[i] = sum
        }
        return vector
      },
      /**
       * @method  spectrum_RF_data  RF数据换算
       * @param data 波形数据
       * @returns {boolean}
       */
      spectrum_RF_data(data) {
        const newVector = this.spectrumData4096(data)
        const vector = this.getRF_data(newVector)
        const II = this.imagReal(vector[0])
        const V5 = this.imagReal(vector[1])
        const len = II[0].imag.length
        const three = []
        const four = []
        const five = []
        for (let index = 0; index < II.length; index++) {
          ;(three[index] = []), (four[index] = []), (five[index] = [])
          for (let i = 0; i < II[index].imag.length; i++) {
            const one = II[index].real[i] * V5[index].real[i] + II[index].imag[i] * V5[index].imag[i]
            const two = II[index].imag[i] * V5[index].real[i] - II[index].real[i] * V5[index].imag[i]
            three[index].push(Math.sqrt(Math.pow(one, 2) + Math.pow(two, 2)))
            four[index].push(Math.pow(II[index].real[i], 2) + Math.pow(II[index].imag[i], 2))
            five[index].push(Math.pow(V5[index].real[i], 2) + Math.pow(V5[index].imag[i], 2))
          }
        }
        const newthree = three[0].map((val, index) => {
          return val + three[1][index] + three[2][index]
        })
        const newfour = three[0].map((val, index) => {
          return val + four[1][index] + four[2][index]
        })
        const newfive = three[0].map((val, index) => {
          return val + five[1][index] + five[2][index]
        })
        const vectors = newthree.map((val, index) => {
          if (newfour[index] * newfive[index] == 0) {
            return 0
          } else {
            return val / Math.sqrt(newfour[index] * newfive[index])
          }
        })
        return vectors
      },
      /**
       * @method  spectrumData4096  数据换算
       * @param data 波形数据
       * @returns {Array}
       */
      spectrumData4096(data) {
        const newVector = []
        const countLen = 4096
        const countLenPer = countLen / 2
        let len = Math.floor(data[0].length / countLenPer)
        for (let idx = 0; idx < data.length; idx++) {
          len = Math.floor(data[idx].length / countLenPer)
          newVector[idx] = []
          for (let idy = 0; idy < len - 1; idy++) {
            const fir = idy == 0 ? 0 : idy * countLenPer
            const end = idy == 0 ? countLen : idy * countLenPer + countLen
            if (end <= data[idx].length) {
              const arr = data[idx].slice(fir, end)
              newVector[idx].push(arr)
            }
          }
        }
        return newVector
      },
      /**
       * @method  getRF_data  RF数据换算
       * @param data 波形数据
       * @returns {array}
       */
      getRF_data(data) {
        const len = 4096 / 2
        data = data.map((val1, index1) => {
          return val1.map((val2, index2) => {
            return val2.map((val3, index3) => {
              const count = (2 * Math.PI * index3) / len
              const wn = 0.54 - 0.46 * Math.cos(count)
              return val3 * wn
            })
          })
        })
        return data
      },
      /**
       * @method  spectrum_Qxy_data  Qxy数据换算
       * @param data
       * @returns {Array}
       */
      spectrum_Qxy_data(data) {
        const sum = []
        const len = data[0].imag.length
        for (let index = 0; index < len; index++) {
          let result
          if (Math.pow(data[0].imag[index], 2) + Math.pow(data[0].real[index], 2) == 0) {
            result = 0
          } else {
            const real =
              (data[1].real[index] * data[0].real[index] + data[1].imag[index] * data[0].imag[index]) /
              (Math.pow(data[0].imag[index], 2) + Math.pow(data[0].real[index], 2))
            const imag =
              (data[0].real[index] * data[1].imag[index] - data[1].real[index] * data[0].imag[index]) /
              (Math.pow(data[0].imag[index], 2) + Math.pow(data[0].real[index], 2))
            result = (Math.atan(imag / real) * 180) / Math.PI
          }
          sum.push(result)
        }
        return sum
      },
      /**
       * @method  spectrum_Vxy_data  Vxy数据换算
       * @param data
       * @returns {Array}
       */
      spectrum_Vxy_data(data) {
        const vector = []
        const len = data[0].length
        for (let index = 0; index < len; index++) {
          const sendLen = len - 1 - index
          const count = this.getVxy_data(data, index, sendLen)
          const result = (1 / len) * count
          vector.push(result)
        }
        return vector
      },
      /**
       * @method  getVxy_data  Vxy数据换算
       * @param data
       * @returns {Array}
       */
      getVxy_data(data, idx, len) {
        let count = 0
        for (let index = 0; index < len; index++) {
          count += data[0][index] * data[1][index + idx]
        }
        return count
      },
      /**
       * @method  spectrum_PIH_data  PIH数据换算
       * @param data
       * @returns {Array}
       */
      spectrum_PIH_data(data) {
        const data1 = new ComplexArray(data)
        const data2 = fft(data1, false)
        const data3 = fft(data2, true)
        return data3.real
      },
      /**
       *  @method  painter_II_V5  II_V5绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme
       * @param out_rect
       * @param data
       * @param graph
       */
      painter_II_V5(svg_ecg, svg_elem_id, ecgParme, out_rect, data, graph) {
        const graph_II_V5 = document.getElementById(`graph_II_V5_${svg_elem_id}`)
        zqSvg.line(
          graph_II_V5,
          graph.offsetLeft,
          graph.offsetLeft + graph.graphW,
          graph.graphH + graph.offsetTop,
          graph.graphH + graph.offsetTop,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        zqSvg.line(
          graph_II_V5,
          graph.offsetLeft,
          graph.offsetLeft,
          graph.offsetTop,
          graph.graphH + graph.offsetTop,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        this.painter_scale(graph_II_V5, ecgParme, graph, out_rect, `line_${svg_elem_id}`, 'stroke:black', 0)
        this.painter_wave(graph_II_V5, ecgParme, data[0], graph, 0)
        zqSvg.line(
          graph_II_V5,
          graph.offsetLeft + graph.average,
          graph.offsetLeft + graph.average + graph.graphW,
          graph.graphH + graph.offsetTop,
          graph.graphH + graph.offsetTop,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        zqSvg.line(
          graph_II_V5,
          graph.offsetLeft + graph.average,
          graph.offsetLeft + graph.average,
          graph.offsetTop,
          graph.graphH + graph.offsetTop,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        this.painter_scale(graph_II_V5, ecgParme, graph, out_rect, `line_${svg_elem_id}`, 'stroke:black', graph.average)
        this.painter_wave(graph_II_V5, ecgParme, data[1], graph, graph.average)
      },
      /**
       *  @method  painter_Qxy_RF  Qxy_RF绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme
       * @param out_rect
       * @param vector_Qxy
       * @param vector_RF
       * @param graph
       */
      painter_Qxy_RF(svg_ecg, svg_elem_id, ecgParme, out_rect, vector_Qxy, vector_RF, graph) {
        const graph_RF_Qxy = document.getElementById(`graph_RF_Qxy_${svg_elem_id}`)
        const offsetTop = graph.offsetTop * 2
        const offsetTopAvg = graph.averageH * 2 + graph.offsetTop - graph.graphH / 2
        zqSvg.line(
          graph_RF_Qxy,
          graph.offsetLeft,
          graph.offsetLeft + graph.graphW,
          graph.averageH * 2 + graph.offsetTop,
          graph.averageH * 2 + graph.offsetTop,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        zqSvg.line(
          graph_RF_Qxy,
          graph.offsetLeft,
          graph.offsetLeft,
          graph.averageH * 2 + graph.offsetTop - graph.graphH,
          graph.averageH * 2 + graph.offsetTop,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        zqSvg.line(
          graph_RF_Qxy,
          graph.offsetLeft,
          graph.offsetLeft + graph.graphW,
          offsetTopAvg,
          offsetTopAvg,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        this.painter_scale_Qxy(
          graph_RF_Qxy,
          ecgParme,
          graph,
          out_rect,
          `line_${svg_elem_id}`,
          'stroke:black',
          0,
          graph.averageH * 2 + graph.offsetTop
        )
        this.painter_wave_RF(graph_RF_Qxy, ecgParme, vector_RF, graph, offsetTopAvg)
        zqSvg.line(
          graph_RF_Qxy,
          graph.offsetLeft + graph.average,
          graph.offsetLeft + graph.graphW + graph.average,
          offsetTopAvg,
          offsetTopAvg,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        zqSvg.line(
          graph_RF_Qxy,
          graph.offsetLeft + graph.average,
          graph.offsetLeft + graph.average,
          graph.averageH * 2 + graph.offsetTop - graph.graphH,
          graph.averageH * 2 + graph.offsetTop,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        this.painter_scale_Qxy(
          graph_RF_Qxy,
          ecgParme,
          graph,
          out_rect,
          `line_${svg_elem_id}`,
          'stroke:black',
          graph.average,
          offsetTopAvg
        )
        const content = [-180, -120, -60, 0, 60, 120, 180]
        const perH = graph.graphH / 6
        for (let i = 0; i < 7; i++) {
          zqSvg.text(
            graph_RF_Qxy,
            `text_${svg_elem_id}`,
            graph.offsetLeft + graph.average,
            graph.averageH * 2 + graph.offsetTop - graph.graphH + i * perH,
            'font-size:10px',
            'end',
            'Middle',
            content[i]
          )
        }
        this.painter_wave_Qxy(graph_RF_Qxy, ecgParme, vector_Qxy, graph, graph.average, offsetTopAvg)
      },
      /**
       *  @method  painter_Vxy_PIH  Vxy_PIH绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme
       * @param out_rect
       * @param vector_PIH
       * @param vector_Vxy
       * @param graph
       */
      painter_Vxy_PIH(svg_ecg, svg_elem_id, ecgParme, out_rect, vector_PIH, vector_Vxy, graph) {
        const graph_Vxy_PIH = document.getElementById(`graph_Vxy_PIH_${svg_elem_id}`)
        zqSvg.line(
          graph_Vxy_PIH,
          graph.offsetLeft,
          graph.offsetLeft + graph.graphW,
          graph.averageH * 3 + graph.offsetTop,
          graph.averageH * 3 + graph.offsetTop,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        zqSvg.line(
          graph_Vxy_PIH,
          graph.offsetLeft + graph.graphW / 2,
          graph.offsetLeft + graph.graphW / 2,
          graph.averageH * 3 + graph.offsetTop - graph.graphH,
          graph.averageH * 3 + graph.offsetTop,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        this.painter_scale_Vxy_PIH(graph_Vxy_PIH, ecgParme, graph, out_rect, `line_${svg_elem_id}`, 'stroke:black', 0)
        this.painter_wave_Vxy(graph_Vxy_PIH, ecgParme, vector_Vxy, graph)
        zqSvg.line(
          graph_Vxy_PIH,
          graph.offsetLeft + graph.average,
          graph.offsetLeft + graph.graphW + graph.average,
          graph.averageH * 3 + graph.offsetTop,
          graph.averageH * 3 + graph.offsetTop,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        zqSvg.line(
          graph_Vxy_PIH,
          graph.offsetLeft + graph.average + graph.graphW / 2,
          graph.offsetLeft + graph.average + graph.graphW / 2,
          graph.averageH * 3 + graph.offsetTop - graph.graphH,
          graph.averageH * 3 + graph.offsetTop,
          `line_${svg_elem_id}`,
          'stroke:black'
        )
        this.painter_scale_Vxy_PIH(
          graph_Vxy_PIH,
          ecgParme,
          graph,
          out_rect,
          `line_${svg_elem_id}`,
          'stroke:black',
          graph.average
        )
        this.painter_wave_PIH(graph_Vxy_PIH, ecgParme, vector_PIH, graph)
      },
      /**
       *  @method  painter_wave  II_V5波形绘制
       * @param svg_ecg
       * @param ecgParme
       * @param data
       * @param graph
       * @param average
       */
      painter_wave(svg_ecg, ecgParme, data, graph, average) {
        let vector = []
        const len = 200
        const datas = data.slice(0, len)
        var max = datas.reduce((a, b) => {
          return b > a ? b : a
        })
        const per = graph.graphW / len
        let perY
        if (max == 0) {
          perY = 0
        } else {
          perY = graph.graphH / Math.ceil(max)
        }
        for (let i = 0; i < len; i++) {
          var p_x, p_y
          p_x = i * per
          p_x = p_x + graph.offsetLeft + average
          p_y = graph.graphH + graph.offsetTop - data[i] * perY
          vector.push(p_x)
          vector.push(p_y)
        }
        zqSvg.polyline(svg_ecg, 'polyline', vector, `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:1`)
        vector = []
      },
      /**
       *  @method  painter_wave_Qxy  Qxy波形绘制
       * @param svg_ecg
       * @param ecgParme
       * @param data
       * @param graph
       * @param average
       * @param offsetTopAvg
       */
      painter_wave_Qxy(svg_ecg, ecgParme, data, graph, average, offsetTopAvg) {
        let vector = []
        const len = 200
        const per_x = graph.graphW / len
        const per_y = graph.graphH / 180
        for (let i = 0; i < len; i++) {
          var p_x, p_y
          p_x = i * per_x
          p_x = p_x + graph.offsetLeft + average
          p_y = offsetTopAvg - data[i] * per_y
          vector.push(p_x)
          vector.push(p_y)
        }
        zqSvg.polyline(svg_ecg, 'polyline', vector, `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:1`)
        vector = []
      },
      /**
       *  @method  painter_wave_RF  RF波形绘制
       * @param svg_ecg
       * @param ecgParme
       * @param data
       * @param graph
       * @param offsetTopAvg
       */
      painter_wave_RF(svg_ecg, ecgParme, data, graph, offsetTopAvg) {
        let vector = []
        const len = 50
        const perX = graph.graphW / len
        // let perY = 	ecgParme.leftOffseH * 2 / 2
        for (let i = 0; i < len; i++) {
          var p_x, p_y
          p_x = i * perX
          p_x += graph.offsetLeft
          // p_y = -1 * data[i] * ecgParme.gain * ecgParme.pix_mm / 1000 + top;
          p_y = offsetTopAvg - data[i] * 3 * 5 * ecgParme.pix_mm + 12.5 * ecgParme.pix_mm
          vector.push(p_x)
          vector.push(p_y)
        }
        zqSvg.polyline(svg_ecg, 'polyline', vector, `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:1`)
        vector = []
      },
      /**
       * @method  painter_wave_PIH  PIH波形绘制
       * @param svg_ecg
       * @param ecgParme
       * @param data
       * @param graph
       */
      painter_wave_PIH(svg_ecg, ecgParme, data, graph) {
        let vector = []
        let twoVector = []
        const len = 800
        const datas = data.slice(0, len)
        var max = datas.reduce((a, b) => {
          return b > a ? b : a
        })
        let perY
        const perX = graph.graphW / 2 / len
        if (max == 0) {
          perY = 0
        } else {
          perY = (5 * ecgParme.pix_mm) / Math.ceil(max)
        }
        for (let i = 0; i < len; i++) {
          var p_x, p_y
          p_x = i * perX
          p_x = p_x + graph.offsetLeft + graph.graphW / 2 + graph.average
          p_y = graph.averageH * 3 + graph.offsetTop - datas[i] * perY

          vector.push(p_x)
          vector.push(p_y)
        }
        zqSvg.polyline(svg_ecg, 'polyline', vector, `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:1`)
        for (let i = len - 1; i > 0; i--) {
          var p_x, p_y
          p_x = (len - i) * perX
          p_x = p_x + graph.offsetLeft + graph.average
          p_y = graph.averageH * 3 + graph.offsetTop - datas[i] * perY
          twoVector.push(p_x)
          twoVector.push(p_y)
        }
        zqSvg.polyline(svg_ecg, 'polyline', twoVector, `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:1`)
        vector = []
        twoVector = []
      },
      /**
       *  @method  painter_wave_Vxy  Vxy波形绘制
       * @param svg_ecg
       * @param ecgParme
       * @param data
       * @param graph
       */
      painter_wave_Vxy(svg_ecg, ecgParme, data, graph) {
        let vector = []
        let twoVector = []
        const len = 3000
        const datas = data.slice(0, len)
        var max = datas.reduce((a, b) => {
          return b > a ? b : a
        })
        const perX = (graph.graphW - 20 * ecgParme.pix_mm) / 2 / len
        let perY
        if (max == 0) {
          perY = 0
        } else {
          perY = graph.averageH / Math.ceil(max)
        }
        for (let i = 0; i < len; i++) {
          var p_x, p_y
          p_x = i * perX
          p_x = p_x + graph.offsetLeft + graph.graphW / 2
          p_y = graph.averageH * 3 + graph.offsetTop - datas[i] * perY

          vector.push(p_x)
          vector.push(p_y)
        }
        zqSvg.polyline(svg_ecg, 'polyline', vector, `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:1`)
        for (let i = len - 1; i > 0; i--) {
          var p_x, p_y
          p_x = (len - i) * perX
          p_x = p_x + graph.offsetLeft + 2 * 5 * ecgParme.pix_mm
          p_y = graph.averageH * 3 + graph.offsetTop - datas[i] * perY
          twoVector.push(p_x)
          twoVector.push(p_y)
        }
        zqSvg.polyline(svg_ecg, 'polyline', twoVector, `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:1`)
        vector = []
        twoVector = []
      },
      /**
       *  @method  painter_scale  II_V5刻度绘制
       * @param gID
       * @param ecgParme
       * @param graph
       * @param out_rect
       * @param className
       * @param styleName
       * @param average
       */
      painter_scale(gID, ecgParme, graph, out_rect, className, styleName, average) {
        for (let i = 0; i < 6; i++) {
          const content = i == 5 ? 'Hz' : i * 5
          zqSvg.line(
            gID,
            graph.offsetLeft + i * graph.perWAverage + average,
            graph.offsetLeft + i * graph.perWAverage + average,
            graph.graphH + graph.offsetTop - 6,
            graph.graphH + graph.offsetTop,
            className,
            styleName
          )
          zqSvg.text(
            gID,
            className,
            graph.offsetLeft + i * graph.perWAverage + average,
            graph.graphH + graph.offsetTop + ecgParme.pix_mm * 3,
            `font-size:${this.scaleFontSize}`,
            'start',
            'Hanging',
            content
          )
          zqSvg.line(
            gID,
            graph.offsetLeft + average,
            graph.offsetLeft + average + 6,
            graph.offsetTop + i * graph.perHAverage,
            graph.offsetTop + i * graph.perHAverage,
            className,
            styleName
          )
        }
      },
      /**
       *  @method  painter_scale  Qxy刻度绘制
       * @param gID
       * @param ecgParme
       * @param graph
       * @param out_rect
       * @param className
       * @param styleName
       * @param average
       * @param offsetTopAvg
       */
      painter_scale_Qxy(gID, ecgParme, graph, out_rect, className, styleName, average, offsetTopAvg) {
        for (let i = 0; i < 6; i++) {
          const content = i == 5 ? 'Hz' : i * 5
          zqSvg.line(
            gID,
            graph.offsetLeft + i * graph.perWAverage + average,
            graph.offsetLeft + i * graph.perWAverage + average,
            offsetTopAvg - 6,
            offsetTopAvg,
            className,
            styleName
          )
          zqSvg.text(
            gID,
            className,
            graph.offsetLeft + i * graph.perWAverage + average,
            offsetTopAvg + ecgParme.pix_mm * 3,
            `font-size:${this.scaleFontSize}`,
            'start',
            'Hanging',
            content
          )
        }
      },
      /**
       *  @method  painter_scale_Vxy_PIH  Vxy_PIH刻度绘制
       * @param gID
       * @param ecgParme
       * @param graph
       * @param out_rect
       * @param className
       * @param styleName
       * @param average
       */
      painter_scale_Vxy_PIH(gID, ecgParme, graph, out_rect, className, styleName, average) {
        const content = ['', -1.5, -1, -0.5, 0, 0.5, 1, 1.5, 's']
        const perW = graph.graphW / 8
        for (let i = 0; i < 8; i++) {
          // svg_ecg, x1, x2, y1, y2, className, styleName
          zqSvg.line(
            gID,
            graph.offsetLeft + i * perW + average,
            graph.offsetLeft + i * perW + average,
            graph.averageH * 3 + graph.offsetTop - 6,
            graph.averageH * 3 + graph.offsetTop,
            className,
            styleName
          )
          zqSvg.text(
            gID,
            className,
            graph.offsetLeft + i * perW + average,
            graph.averageH * 3 + graph.offsetTop + ecgParme.pix_mm * 5,
            `font-size:${this.scaleFontSize}`,
            'Middle',
            'Hanging',
            content[i]
          )
        }
      },
      /**
       * @method  clearEcgWave  svg清除函数
       * @param svg_elem_id
       */
      clearEcgWave(svg_elem_id) {
        // print打印参数
        var print = document.getElementById(`print_${svg_elem_id}`)
        if (print != null) {
          print.parentNode.removeChild(print)
        }
        // II_V5
        var graph_II_V5 = document.getElementById(`graph_II_V5_${svg_elem_id}`)
        if (graph_II_V5 != null) {
          graph_II_V5.parentNode.removeChild(graph_II_V5)
        }
        // RF_Qxy
        const graph_RF_Qxy = document.getElementById(`graph_RF_Qxy_${svg_elem_id}`)
        if (graph_RF_Qxy != null) {
          graph_RF_Qxy.parentNode.removeChild(graph_RF_Qxy)
        }
        // Vxy_PIH
        var graph_Vxy_PIH = document.getElementById(`graph_Vxy_PIH_${svg_elem_id}`)
        if (graph_Vxy_PIH != null) {
          graph_Vxy_PIH.parentNode.removeChild(graph_Vxy_PIH)
        }
        // 坐标title
        var graph_title = document.getElementById(`graph_title_${svg_elem_id}`)
        if (graph_title != null) {
          graph_title.parentNode.removeChild(graph_title)
        }
      }
    }
    window.zqSpectrum = window.zqSpectrum || new zqSpectrum()
    /**
     * 模块说明
     * @module  zqQt	 QT离散度封装
     * @rely  zqDataProcessing zqSvg zqCommon
     */
    function zqQt() {
      this.userInfoPar = [
        ['', 'RR：'],
        ['', 'QTmax：'],
        ['', 'QTmin：'],
        ['', 'QTd：'],
        ['', 'QTcmax：'],
        ['', 'QTcmin：'],
        ['', 'QTcd：']
        // ["", "QTdr：" ],["", "AdQTd：" ],
        // ["", "SDQT：" ],["", "CQTd：" ],
        // ["", "SDQTc：" ],["", "CQTcd：" ]
      ]
      this.userInfoParWidth = 15 * 5
      this.xinboIndex = 2
      this.leadLength = 12
    }
    zqQt.prototype = {
      /**
       * @method  draw_ecg_qt  QT绘制入口函数
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_ecg_qt(svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight) {
        this.clearEcgWave(svg_elem_id)
        var svg_ecg = document.getElementById(svg_elem_id)
        this.creatEcgChild(svg_ecg, svg_elem_id)
        svg_ecg.setAttribute('width', gridWidth)
        svg_ecg.setAttribute('height', gridHeight)
        this.draw_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight)
      },
      /**
       * @method  creatEcgChild  svg内部组件创建
       * @param svg_ecg
       * @param svg_elem_id
       */
      creatEcgChild(svg_ecg, svg_elem_id) {
        zqSvg.g(svg_ecg, `print_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `wave_${svg_elem_id}`)
      },
      /**
       * @method  draw_lead  QT绘制入口函数
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight) {
        var rect = { left: 0, right: 0, top: 0, bottom: 0 }
        if (isPrint == 1) {
          // rect = {"left":20,"right":20,"top": 5 * ecgParme.pix_mm,"bottom":20};
          // let print = document.getElementById("print_" + svg_elem_id);
          // zqSvg.text(print,"text_" + svg_elem_id,
          //     gridWidth / 2, 20,
          //     "font-size:18px", "middle", "Hanging", "QT离散度心电报告"
          // )
          rect = { left: 30, right: 10, top: 60, bottom: 40 }
          this.print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, rect)
        }
        var out_rect = zqSvg.painter_rect(svg_ecg, ecgParme.pix_mm, rect, gridWidth, gridHeight)
        zqSvg.painter_grid(svg_ecg, svg_elem_id, ecgParme, out_rect, gridWidth, gridHeight)
        this.draw_ecg(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, isPrint, gridWidth, gridHeight)
      },
      /**
       * @method  print  打印内容
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson
       * @param ecgParme
       */
      print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, rect) {
        const print = document.getElementById(`print_${svg_elem_id}`)
        let { title1 } = paramset.report
        if (title1 == '') {
          title1 = ecgJson.checkSection
        }
        const { title2 } = paramset.report
        // if(title2 == ''){
        //     title2 = ecgJson['checkDept']
        // }

        const title1_y = 20
        const title2_y = 40
        let line_y = 7 * ecgParme.pix_mm
        if (ecgParme.previewEcg) {
          line_y = 7 * ecgParme.pix_mm + 10
        }

        zqSvg.text(print, `text_${svg_elem_id}`, gridWidth / 2, title1_y, 'font-size:18px', 'middle', 'Hanging', title1)
        zqSvg.text(print, `text_${svg_elem_id}`, gridWidth / 2, title2_y, 'font-size:14px', 'middle', 'Hanging', title2)
        zqSvg.line(
          print,
          ecgParme.pix_mm * 5,
          gridWidth - ecgParme.pix_mm * 2,
          line_y,
          line_y,
          `line_${svg_elem_id}`,
          'stroke-width:1;stroke:#000000'
        )
        const rightDown = zqSvg.getPrintTimeName(paramset.report.rightDown)
        const rightUp = zqSvg.getPrintTimeName(paramset.report.rightUp)

        if (paramset.report.rightUp == 'reviewTime' || paramset.report.rightUp == 'diagnoseTime') {
          zqCommon.getServerSystemTime(
            'up',
            print,
            svg_elem_id,
            gridWidth,
            rightUp,
            rightDown,
            rect,
            paramset.report.rightUp,
            ecgJson.id
          )
        } else if (ecgJson[paramset.report.rightUp] != null) {
          zqSvg.text(
            print,
            `text_${svg_elem_id}`,
            gridWidth - rect.right,
            title1_y,
            'font-size:14px',
            'end',
            'Hanging',
            rightUp + ecgJson[paramset.report.rightUp]
          )
        }
        if (paramset.report.rightDown == 'reviewTime' || paramset.report.rightDown == 'diagnoseTime') {
          zqCommon.getServerSystemTime(
            'down',
            print,
            svg_elem_id,
            gridWidth,
            rightUp,
            rightDown,
            rect,
            paramset.report.rightDown,
            ecgJson.id
          )
        } else if (ecgJson[paramset.report.rightDown] != null) {
          zqSvg.text(
            print,
            `text_${svg_elem_id}`,
            gridWidth - rect.right,
            title2_y,
            'font-size:14px',
            'end',
            'Hanging',
            rightDown + ecgJson[paramset.report.rightDown]
          )
        }
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          title1_y,
          'font-size:14px',
          'start',
          'Hanging',
          'QT离散度心电报告'
        )
        const speedGain = `${ecgParme.speed}mm/s ${ecgParme.gain}mm/mv`

        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          gridHeight - 30,
          'font-size:12px',
          'start',
          'Hanging',
          speedGain
        )
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          gridHeight - 15,
          'font-size:12px',
          'start',
          'Hanging',
          '此报告仅供临床参考，不做任何证明材料用'
        )
      },
      /**
       * @method  draw_ecg  QT绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param out_rect svg波形可绘制区域上下左右距离
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_ecg(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, isPrint, gridWidth, gridHeight) {
        const widget = document.getElementById(`wave_${svg_elem_id}`)
        this.painter_gain(widget, svg_elem_id, ecgParme, out_rect.left, out_rect.top + (gridHeight - out_rect.top) / 2)
        const lead_name = zqSvg.get_12_lead_system_name(ecgJson.systemType)
        const out_point = this.get_12_points(gridWidth, gridHeight, out_rect, ecgParme.pix_mm, ecgParme.speed)
        this.painter_12_wave(widget, svg_elem_id, out_rect, ecgParme, ecgJson, out_point, lead_name, gridWidth)

        this.painter_12_qt(widget, svg_elem_id, out_rect, ecgParme, ecgJson, out_point, gridHeight)
        // if(isPrint == 1)
        // {
        //     this.painter_12_qt(widget, svg_elem_id,out_rect,ecgParme,ecgJson,out_point,gridHeight - 40);
        // }else{
        //     this.painter_12_qt(widget, svg_elem_id, out_rect, ecgParme, ecgJson, out_point, gridHeight);
        //     zqSvg.text(widget, "speed_gain_" + svg_elem_id, out_rect["left"], out_rect["top"],
        //         ";font-size:14px",
        //         "start", "Hanging", ecgParme.speed + "mm/s     " + ecgParme.gain + "mm/mv")
        // }
        const userInfo = [
          [ecgJson.pid, '编号：'],
          [ecgJson.name != null ? ecgJson.name : '', '姓名：'],
          [zqCommon.switchSex(ecgJson.sex), '性别：'],
          [ecgJson.age + zqCommon.switchAgeUnit(ecgJson.ageUnit), '年龄：'],
          [ecgJson.checkDept, '科室：'],
          [ecgJson.bedNum != null ? ecgJson.bedNum : '', '床号：']
        ]
        this.painter_userInfo(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, gridWidth, gridHeight, userInfo, 0)
        this.painter_userInfo(
          svg_ecg,
          svg_elem_id,
          ecgJson,
          ecgParme,
          out_rect,
          gridWidth,
          gridHeight,
          this.userInfoPar,
          10 * 5 * ecgParme.pix_mm
        )

        if (isPrint) {
          out_rect.offsetLeft = out_point[0].x + 10 * ecgParme.pix_mm
          zqSvg.drawScaleRule(svg_ecg, svg_elem_id, ecgParme, out_rect)
        }
      },
      /**
       * @method  painter_userInfo  参数绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param out_rect svg波形可绘制区域上下左右距离
       * @param gridWidth svg宽
       * @param gridHeight svg高
       * @param userInfo 参数
       * @param topY 距离顶部距离
       */
      painter_userInfo(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, gridWidth, gridHeight, userInfo, topY) {
        const printTitle = document.getElementById(`print_${svg_elem_id}`)
        var widget = document.createElementNS('http://www.w3.org/2000/svg', 'text')
        widget.setAttribute('x', gridWidth)
        widget.setAttribute('y', out_rect.top)
        widget.setAttribute('text-anchor', 'end')
        widget.setAttribute('dominant-baseline', 'auto')
        widget.setAttribute('style', 'font-size:12px;')
        printTitle.appendChild(widget)
        for (let i = 0; i < userInfo.length; i++) {
          const offsetPer = gridWidth / userInfo.length
          for (let j = 0; j < userInfo[i].length; j++) {
            var tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan')
            const offsetX = j == 0 ? 0 : 120
            const offsetY = out_rect.top + i * 30
            tspan.setAttribute('x', gridWidth - offsetX - 10 * ecgParme.pix_mm - out_rect.left)
            tspan.setAttribute('y', offsetY + 5 * ecgParme.pix_mm + topY)
            var textString = document.createTextNode(userInfo[i][j])
            tspan.appendChild(textString)
            widget.appendChild(tspan)
          }
        }
      },
      /**
       * @method  painter_gain  定标电压绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme
       * @param px 定标电压x坐标
       * @param py 定标电压y坐标
       */
      painter_gain(svg_ecg, svg_elem_id, ecgParme, px, py) {
        var gainpolylineDate = zqSvg.set_gain_point(ecgParme.pix_mm, px, py, ecgParme.gain, ecgParme.speed)
        zqSvg.polyline(
          svg_ecg,
          `gainpolyline_${svg_elem_id}`,
          gainpolylineDate,
          `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
        )
      },
      /**
       * @method  getPar  参数转换
       * @param r_data_array
       * @param ecgJson
       * @param pix_mm
       * @returns {{}}
       */
      getPar(r_data_array, ecgJson, pix_mm) {
        const par = {}
        par.rCount = r_data_array.length
        par.rMax = Math.max(...r_data_array)
        par.rMin = Math.min(...r_data_array)
        par.rAverage = parseInt(r_data_array.reduce(this.rAverage) / par.rCount)
        par.rVariation = (((par.rMax - par.rMin) * pix_mm) / (5 * pix_mm * 10)).toFixed(3)
        const RV5 = ecgJson.rv5_ * 1.0
        const SV1 = ecgJson.sv1 * 1.0
        par.stanhr = `${RV5}/${SV1}`
        return par
      },
      /**
       * @method  get_12_points  波形xy开始坐标
       * @param gridWidth
       * @param gridHeight
       * @param rect
       * @param pix_mm
       * @param speed
       * @returns {Array}
       */
      get_12_points(gridWidth, gridHeight, rect, pix_mm, speed) {
        const out_point = []
        const len = this.leadLength
        const per = (gridHeight - rect.top - rect.bottom) / (len + 1)
        for (let i = 0; i < len; i++) {
          out_point[i] = {}
          out_point[i].x = rect.left + (speed / 2) * pix_mm
          out_point[i].y = per * (i + 1) + rect.top
        }
        return out_point
      },
      /**
       * @method  painter_12_wave  波形绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param rect
       * @param ecgParme
       * @param ecgJson
       * @param out_point xy定位
       * @param lead_name 导联名称
       */
      painter_12_wave(svg_ecg, svg_elem_id, rect, ecgParme, ecgJson, out_point, lead_name, gridWidth) {
        var vector = this.painter_12_data(rect, ecgParme, ecgJson.data, out_point, gridWidth)
        rect.offsetWidth = ((vector[0].length / 2) * ecgParme.speed * ecgParme.pix_mm) / 1000
        for (let lead_idx = 0; lead_idx < this.leadLength; lead_idx++) {
          zqSvg.polyline(
            svg_ecg,
            `polyline_${svg_elem_id}`,
            vector[lead_idx],
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          zqSvg.text(
            svg_ecg,
            `text_${svg_elem_id}`,
            out_point[lead_idx].x,
            out_point[lead_idx].y,
            'font-size:12px',
            'start',
            'Middle',
            lead_name[lead_idx]
          )
        }
        vector = []
      },
      /**
       * @method  painter_12_wave  波形数据换算
       * @param rect
       * @param ecgParme
       * @param data 波形原始数据
       * @param out_point
       * @returns {Array}
       */
      painter_12_data(rect, ecgParme, data, out_point, gridWidth) {
        var vector = []
        for (let idx = 0; idx < this.leadLength; idx++) {
          vector[idx] = []
          for (let lead_index = 0; lead_index < data[idx].length - ecgParme.begin_ms; lead_index++) {
            if (ecgParme.begin_ms >= data[idx].length) {
              break
            }
            var p_x, p_y
            p_x = (lead_index * ecgParme.speed * ecgParme.pix_mm) / 1000
            p_x = p_x + out_point[idx].x + 10 * ecgParme.pix_mm
            if (p_x > gridWidth - this.userInfoParWidth * ecgParme.pix_mm) {
              break
            }
            p_y = (-1 * data[idx][lead_index + ecgParme.begin_ms] * ecgParme.gain * ecgParme.pix_mm) / 1000
            p_y += out_point[idx].y
            vector[idx].push(p_x)
            vector[idx].push(p_y)
          }
        }
        return vector
      },
      /**
       * @method  QTPosition  QT对象申明
       * @returns {{}}
       * @constructor
       */
      QTPosition() {
        var QTPosition = {}
        QTPosition.LeadStartPoints = new Array(18)
        QTPosition.LeadEndPoints = new Array(18)
        QTPosition.RR_pos = 0
        QTPosition.RR_pos_arr = new Array()
        QTPosition.qBegin_pos_arr = new Array()
        QTPosition.tEnd_pos_arr = new Array()
        return QTPosition
      },
      /**
       * @method  painter_12_qt  QT绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param rect
       * @param ecgParme
       * @param ecgJson
       * @param out_point
       * @param gridHeight
       * @returns {number}
       */
      painter_12_qt(svg_ecg, svg_elem_id, rect, ecgParme, ecgJson, out_point, gridHeight) {
        const QTPosition = this.QTPosition()
        // 第二导Q,T,S,P,R在第七个波峰起始位置范围
        var Q_2_Point = zqDataProcessing.get_one_cardiac(ecgJson.data[1], QTPosition, this.xinboIndex)
        if (Q_2_Point == -1) return -1
        // 获取第二导Q波在第七个波峰位置
        var Q_2_Position = zqDataProcessing.get_one_Q_pos(ecgJson.data[1], this.xinboIndex)
        // 获取第二导R波位置存入数组
        var r_pos_array = zqDataProcessing.get_r_pos_array(ecgJson.data[1])
        const RR = zqDataProcessing.get_rr_average(r_pos_array)
        this.userInfoPar[0][0] = `${RR}ms`

        QTPosition.RR_pos_arr = r_pos_array
        zqDataProcessing.get_QT_pos_array(ecgJson.data[1], QTPosition)
        const QT_dif = QTPosition.tEnd_pos_arr.map((item, index) => {
          return item - QTPosition.qBegin_pos_arr[index]
        })
        const QTmax = QT_dif.reduce(zqCommon.getMax, 0)
        const QTmin = QT_dif.reduce(zqCommon.getMin, QT_dif[0])
        const QTd = QTmax - QTmin
        this.userInfoPar[1][0] = `${QTmax}ms`
        this.userInfoPar[2][0] = `${QTmin}ms`
        this.userInfoPar[3][0] = `${QTd}ms`
        const QTcmax = parseInt(QTmax / Math.sqrt(RR / 1000))
        const QTcmin = parseInt(QTmin / Math.sqrt(RR / 1000))
        const QTcd = parseInt(QTd / Math.sqrt(RR / 1000))
        this.userInfoPar[4][0] = `${QTcmax}ms`
        this.userInfoPar[5][0] = `${QTcmin}ms`
        this.userInfoPar[6][0] = `${QTcd}ms`
        // T_end即T2波绘制
        var T_all_Point = zqDataProcessing.get_all_cardiac(ecgJson.data, this.xinboIndex)
        var T_all_Position = zqDataProcessing.getT_all_Position(ecgJson.data, this.xinboIndex)
        const polyline_svg_dom = document.getElementsByClassName(`.polyline_${svg_elem_id}`)
        if (polyline_svg_dom && polyline_svg_dom.length > 2) {
          if (Q_2_Position < polyline_svg_dom[2].animatedPoints.length) {
            // Q绘制
            this.painter_Q_line(svg_ecg, svg_elem_id, ecgParme, rect, out_point, Q_2_Point, Q_2_Position, gridHeight)
            // T2绘制
            T_all_Point != -1
              ? this.painter_T_line(svg_ecg, svg_elem_id, ecgParme, rect, out_point, T_all_Point, T_all_Position)
              : ''
          }
        }
      },
      /**
       * @method  painter_Q_line  Q绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme
       * @param rect
       * @param out_point
       * @param Q_2_Point 某一导波形某一个心博区间
       * @param Q_2_Position Q波在某一导波形某一个心博定位
       * @param gridHeight
       */
      painter_Q_line(svg_ecg, svg_elem_id, ecgParme, rect, out_point, Q_2_Point, Q_2_Position, gridHeight) {
        var QPoint = {}
        for (let array_idx = Q_2_Point[0]; array_idx < Q_2_Point[1]; array_idx++) {
          var p_x
          p_x = ((array_idx - ecgParme.begin_ms) * ecgParme.speed * ecgParme.pix_mm) / 1000
          p_x = p_x + out_point[1].x + 10 * ecgParme.pix_mm
          if (array_idx == Q_2_Position) {
            QPoint.QPointx = p_x
          }
        }
        if (QPoint.QPointx) {
          zqSvg.line(
            svg_ecg,
            QPoint.QPointx,
            QPoint.QPointx,
            rect.top,
            gridHeight - rect.bottom,
            `line_${svg_elem_id}`,
            `stroke-width:${ecgParme.waveLineWidth};stroke:${ecgParme.waveColor}`
          )
          zqSvg.text(
            svg_ecg,
            `text_${svg_elem_id}`,
            QPoint.QPointx + 10,
            rect.top + 10,
            'font-size:12px',
            'start',
            'Middle',
            'Q'
          )
        }
      },
      /**
       * @method  painter_Q_line  T绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme
       * @param rect
       * @param out_point
       * @param T_all_Point 所有波形某一导心博区间
       * @param T_all_Position 所有波形某一个心博T位置
       */
      painter_T_line(svg_ecg, svg_elem_id, ecgParme, rect, out_point, T_all_Point, T_all_Position) {
        var TPoint = []
        for (let lead_index = 0; lead_index < 12; lead_index++) {
          TPoint[lead_index] = {}
          for (let array_idx = T_all_Point[lead_index][0]; array_idx < T_all_Point[lead_index][1]; array_idx++) {
            var p_x
            var p_y
            p_x = ((array_idx - ecgParme.begin_ms) * ecgParme.speed * ecgParme.pix_mm) / 1000
            p_x = p_x + out_point[lead_index].x + 10 * ecgParme.pix_mm
            p_y = out_point[lead_index].y
            if (array_idx == T_all_Position[lead_index]) {
              TPoint[lead_index].TPointx = p_x
              TPoint[lead_index].TPointy = p_y
            }
          }
          if (TPoint[lead_index].TPointx) {
            zqSvg.line(
              svg_ecg,
              TPoint[lead_index].TPointx,
              TPoint[lead_index].TPointx,
              TPoint[lead_index].TPointy - 15,
              TPoint[lead_index].TPointy + 15,
              `line_${svg_elem_id}`,
              `stroke-width:${ecgParme.waveLineWidth};stroke:${ecgParme.waveColor}`
            )
            zqSvg.text(
              svg_ecg,
              `text_${svg_elem_id}`,
              TPoint[lead_index].TPointx + 5,
              TPoint[lead_index].TPointy - 10,
              'font-size:12px',
              'start',
              'Middle',
              'T2'
            )
          }
        }
      },
      /**
       * @method  clearEcgWave  svg清除函数
       * @param svg_elem_id
       */
      clearEcgWave(svg_elem_id) {
        // print打印参数
        var print = document.getElementById(`print_${svg_elem_id}`)
        if (print != null) {
          print.parentNode.removeChild(print)
        }
        // wave
        var wave = document.getElementById(`wave_${svg_elem_id}`)
        if (wave != null) {
          wave.parentNode.removeChild(wave)
        }
      }
    }
    window.zqQt = window.zqQt || new zqQt()
    /**
     * 模块说明
     * @module  zqTimeVector	时间向量封装
     * @rely  zqSvg
     */
    function zqTimeVector() {
      this.userInfoParWidth = 15 * 5
    }
    zqTimeVector.prototype = {
      /**
       * @method  draw_ecg_timeVector  时间向量绘制入口函数
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_ecg_timeVector(svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight) {
        this.clearEcgWave(svg_elem_id)
        const data = ecgJson.data.filter((val, index) => {
          return index != 2 && index != 3 && index != 4 && index != 5
        })
        // ecgJson["data"] = ecgJson["data"].filter((val,index)=>{
        //     return index != 2 && index != 3 && index != 4 && index != 5
        // })
        var svg_ecg = document.getElementById(svg_elem_id)
        this.creatEcgChild(svg_ecg, svg_elem_id)
        svg_ecg.setAttribute('width', gridWidth)
        svg_ecg.setAttribute('height', gridHeight)
        this.draw_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight, data)
      },
      /**
       * @method  creatEcgChild  svg内部组件创建
       * @param svg_ecg
       * @param svg_elem_id
       */
      creatEcgChild(svg_ecg, svg_elem_id) {
        zqSvg.g(svg_ecg, `print_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `wave_${svg_elem_id}`)
      },
      /**
       * @method  draw_lead  时间向量绘制入口函数
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_lead(svg_ecg, svg_elem_id, ecgJson, ecgParme, isPrint, gridWidth, gridHeight, data) {
        const printTitle = document.getElementById(`print_${svg_elem_id}`)
        var rect = { left: 0, right: 0, top: 0, bottom: 0 }
        if (isPrint == 1) {
          // rect = {"left":15,"right":15,"top": 5 * ecgParme.pix_mm,"bottom":20};
          // let print = document.getElementById("print_" + svg_elem_id);
          // zqSvg.text(print,"text_" + svg_elem_id,
          //     gridWidth / 2, 20,
          //     "font-size:18px", "middle", "Hanging", "时间向量心电报告"
          // )
          rect = { left: 30, right: 10, top: 60, bottom: 40 }
          this.print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, rect)
        }
        var out_rect = zqSvg.painter_rect(svg_ecg, ecgParme.pix_mm, rect, gridWidth, gridHeight)
        zqSvg.painter_grid(svg_ecg, svg_elem_id, ecgParme, out_rect, gridWidth, gridHeight)
        this.draw_ecg(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, isPrint, gridWidth, gridHeight, data)
      },
      /**
       * @method  print  打印内容
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson
       * @param ecgParme
       */
      print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, rect) {
        const print = document.getElementById(`print_${svg_elem_id}`)
        let { title1 } = paramset.report
        if (title1 == '') {
          title1 = ecgJson.checkSection
        }
        const { title2 } = paramset.report
        // if(title2 == ''){
        //     title2 = ecgJson['checkDept']
        // }

        const title1_y = 20
        const title2_y = 40
        let line_y = 7 * ecgParme.pix_mm
        if (ecgParme.previewEcg) {
          line_y = 7 * ecgParme.pix_mm + 10
        }

        zqSvg.text(print, `text_${svg_elem_id}`, gridWidth / 2, title1_y, 'font-size:18px', 'middle', 'Hanging', title1)
        zqSvg.text(print, `text_${svg_elem_id}`, gridWidth / 2, title2_y, 'font-size:14px', 'middle', 'Hanging', title2)
        zqSvg.line(
          print,
          ecgParme.pix_mm * 5,
          gridWidth - ecgParme.pix_mm * 2,
          line_y,
          line_y,
          `line_${svg_elem_id}`,
          'stroke-width:1;stroke:#000000'
        )
        const rightDown = zqSvg.getPrintTimeName(paramset.report.rightDown)
        const rightUp = zqSvg.getPrintTimeName(paramset.report.rightUp)

        if (paramset.report.rightUp == 'reviewTime' || paramset.report.rightUp == 'diagnoseTime') {
          zqCommon.getServerSystemTime(
            'up',
            print,
            svg_elem_id,
            gridWidth,
            rightUp,
            rightDown,
            rect,
            paramset.report.rightUp,
            ecgJson.id
          )
        } else if (ecgJson[paramset.report.rightUp] != null) {
          zqSvg.text(
            print,
            `text_${svg_elem_id}`,
            gridWidth - rect.right,
            title1_y,
            'font-size:14px',
            'end',
            'Hanging',
            rightUp + ecgJson[paramset.report.rightUp]
          )
        }
        if (paramset.report.rightDown == 'reviewTime' || paramset.report.rightDown == 'diagnoseTime') {
          zqCommon.getServerSystemTime(
            'down',
            print,
            svg_elem_id,
            gridWidth,
            rightUp,
            rightDown,
            rect,
            paramset.report.rightDown,
            ecgJson.id
          )
        } else if (ecgJson[paramset.report.rightDown] != null) {
          zqSvg.text(
            print,
            `text_${svg_elem_id}`,
            gridWidth - rect.right,
            title2_y,
            'font-size:14px',
            'end',
            'Hanging',
            rightDown + ecgJson[paramset.report.rightDown]
          )
        }
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          title1_y,
          'font-size:14px',
          'start',
          'Hanging',
          '时间向量心电报告'
        )
        const speedGain = `${ecgParme.speed}mm/s ${ecgParme.gain}mm/mv`

        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          gridHeight - 30,
          'font-size:12px',
          'start',
          'Hanging',
          speedGain
        )
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          gridHeight - 15,
          'font-size:12px',
          'start',
          'Hanging',
          '此报告仅供临床参考，不做任何证明材料用'
        )
      },
      /**
       * @method  draw_ecg  时间向量绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param out_rect svg波形可绘制区域上下左右距离
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_ecg(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, isPrint, gridWidth, gridHeight, data) {
        const widget = document.getElementById(`wave_${svg_elem_id}`)
        this.painter_gain(widget, svg_elem_id, ecgParme, out_rect.left, out_rect.top + (gridHeight - out_rect.top) / 2)
        const lead_name = ['X', 'Y', 'Z', 'X-Y', 'X-Z', 'Z-Y']
        const out_point = this.get_6_points(gridWidth, gridHeight, out_rect, ecgParme.pix_mm, ecgParme.speed)
        if (ecgJson.systemType == 0 || ecgJson.systemType == 6 || ecgJson.systemType == 9) {
          this.painter_start_wave3(
            widget,
            svg_elem_id,
            out_rect,
            ecgParme,
            ecgJson,
            out_point,
            lead_name,
            data,
            gridWidth
          )
          this.painter_end_wave3(
            widget,
            svg_elem_id,
            out_rect,
            ecgParme,
            ecgJson,
            out_point,
            lead_name,
            data,
            gridWidth
          )
        }
        if (isPrint == 0) {
          zqSvg.text(
            widget,
            `speed_gain_${svg_elem_id}`,
            out_rect.left,
            out_rect.top,
            ';font-size:14px',
            'start',
            'Hanging',
            `${ecgParme.speed}mm/s     ${ecgParme.gain}mm/mv`
          )
        }
        const userInfo = [
          [ecgJson.pid, '编号：'],
          [ecgJson.name != null ? ecgJson.name : '', '姓名：'],
          [zqCommon.switchSex(ecgJson.sex), '性别：'],
          [ecgJson.age + zqCommon.switchAgeUnit(ecgJson.ageUnit), '年龄：'],
          [ecgJson.checkDept, '科室：'],
          [ecgJson.bedNum != null ? ecgJson.bedNum : '', '床号：']
        ]
        const userInfoPar = [
          [`${ecgJson.hr}bpm`, 'HR：'],
          [`${ecgJson.pr}ms`, 'PR：'],
          [`${ecgJson.qrs}ms`, 'QRS：'],
          [`${ecgJson.qt}/${ecgJson.qtc}ms`, 'QT/QTC：'],
          [`${ecgJson.p}/${ecgJson.qrs}/${ecgJson.t}°`, 'P/QRS/T：'],
          [`${ecgJson.rv5}/${ecgJson.sv1}mv`, 'RV5/SV1：'],
          [`${(Number(ecgJson.rv5) + Number(ecgJson.sv1)).toFixed(3)}mv`, 'RV5+SV1：']
        ]
        this.painter_userInfo(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, gridWidth, gridHeight, userInfo, 0)
        this.painter_userInfo(
          svg_ecg,
          svg_elem_id,
          ecgJson,
          ecgParme,
          out_rect,
          gridWidth,
          gridHeight,
          userInfoPar,
          10 * 5 * ecgParme.pix_mm
        )

        if (isPrint) {
          out_rect.offsetLeft = out_point[0].x + 10 * ecgParme.pix_mm
          zqSvg.drawScaleRule(svg_ecg, svg_elem_id, ecgParme, out_rect)
        }
      },
      /**
       * @method  painter_userInfo  参数绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param out_rect svg波形可绘制区域上下左右距离
       * @param gridWidth svg宽
       * @param gridHeight svg高
       * @param userInfo 参数
       * @param topY 距离顶部距离
       */
      painter_userInfo(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, gridWidth, gridHeight, userInfo, topY) {
        const printTitle = document.getElementById(`print_${svg_elem_id}`)
        var widget = document.createElementNS('http://www.w3.org/2000/svg', 'text')
        widget.setAttribute('x', gridWidth)
        widget.setAttribute('y', out_rect.top)
        widget.setAttribute('text-anchor', 'end')
        widget.setAttribute('dominant-baseline', 'auto')
        widget.setAttribute('style', 'font-size:12px;')
        printTitle.appendChild(widget)
        for (let i = 0; i < userInfo.length; i++) {
          const offsetPer = gridWidth / userInfo.length
          for (let j = 0; j < userInfo[i].length; j++) {
            var tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan')
            const offsetX = j == 0 ? 0 : 120
            // if(topY == 0){
            //     offsetX = (j == 0) ? 0 : 120
            // }
            const offsetY = out_rect.top + i * 30
            tspan.setAttribute('x', gridWidth - offsetX - 10 * ecgParme.pix_mm - out_rect.left)
            tspan.setAttribute('y', offsetY + 5 * ecgParme.pix_mm + topY)
            var textString = document.createTextNode(userInfo[i][j])
            tspan.appendChild(textString)
            widget.appendChild(tspan)
          }
        }
      },
      /**
       * @method  painter_gain  定标电压绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme
       * @param px 定标电压x坐标
       * @param py 定标电压y坐标
       */
      painter_gain(svg_ecg, svg_elem_id, ecgParme, px, py) {
        var gainpolylineDate = zqSvg.set_gain_point(ecgParme.pix_mm, px, py, ecgParme.gain, ecgParme.speed)
        zqSvg.polyline(
          svg_ecg,
          `gainpolyline_${svg_elem_id}`,
          gainpolylineDate,
          `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
        )
      },
      /**
       * @method  getPar  参数转换
       * @param r_data_array
       * @param ecgJson
       * @param pix_mm
       * @returns {{}}
       */
      getPar(r_data_array, ecgJson, pix_mm) {
        const par = {}
        par.rCount = r_data_array.length
        par.rMax = Math.max(...r_data_array)
        par.rMin = Math.min(...r_data_array)
        par.rAverage = parseInt(r_data_array.reduce(this.rAverage) / par.rCount)
        par.rVariation = (((par.rMax - par.rMin) * pix_mm) / (5 * pix_mm * 10)).toFixed(3)
        const RV5 = ecgJson.rv5_ * 1.0
        const SV1 = ecgJson.sv1 * 1.0
        par.stanhr = `${RV5}/${SV1}`
        return par
      },
      // 前3导数据
      /**
       *  @method  get_start_data3  前三导第一次数据转换
       * @param data
       * @param systemType
       * @returns {Array}
       */
      get_start_data3(data, systemType) {
        var datas = this.get_matrix_data(data)
        var matrix_data = this.matrix_parame(systemType)
        var vector = []
        for (let lead_index = 0; lead_index < 3; lead_index++) {
          vector[lead_index] = []
          vector[lead_index] = datas.map((array, index) => {
            return array.reduce((total, val, key) => {
              return total + val * matrix_data[lead_index][key]
            })
          })
        }
        return vector
      },
      /**
       * @method  painter_start_wave3  前三导第二次数据转换
       * @param svg_ecg
       * @param svg_elem_id
       * @param rect
       * @param ecgParme
       * @param ecgJson
       * @param out_point
       * @param lead_name
       * @param data
       */
      painter_start_wave3(svg_ecg, svg_elem_id, rect, ecgParme, ecgJson, out_point, lead_name, data, gridWidth) {
        var matrixVector_start3 = this.get_start_data3(data, ecgJson.systemType)
        var vector = this.painter_start_data3(rect, ecgParme, matrixVector_start3, out_point, gridWidth)
        rect.offsetWidth = ((vector[0].length / 2) * ecgParme.speed * ecgParme.pix_mm) / 1000
        for (let lead_idx = 0; lead_idx < vector.length; lead_idx++) {
          zqSvg.polyline(
            svg_ecg,
            `polyline_${svg_elem_id}`,
            vector[lead_idx],
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          zqSvg.text(
            svg_ecg,
            `text_${svg_elem_id}`,
            out_point[lead_idx].x,
            out_point[lead_idx].y,
            'font-size:12px',
            'start',
            'Middle',
            lead_name[lead_idx]
          )
        }
        vector = []
      },
      /**
       *  @method  painter_start_wave3  前三导数据第二次转换
       * @param rect
       * @param ecgParme
       * @param data
       * @param out_point
       * @returns {Array}
       */
      painter_start_data3(rect, ecgParme, data, out_point, gridWidth) {
        var vector = []
        for (let idx = 0; idx < data.length; idx++) {
          vector[idx] = []
          for (let lead_index = 0; lead_index < data[idx].length - ecgParme.begin_ms; lead_index++) {
            if (ecgParme.begin_ms >= data[idx].length) {
              break
            }
            var p_x, p_y
            p_x = (lead_index * ecgParme.speed * ecgParme.pix_mm) / 1000
            p_x = p_x + out_point[idx].x + 10 * ecgParme.pix_mm
            if (p_x > gridWidth - this.userInfoParWidth * ecgParme.pix_mm) {
              break
            }
            p_y = (-1 * data[idx][lead_index + ecgParme.begin_ms] * ecgParme.gain * ecgParme.pix_mm) / 1000
            p_y += out_point[idx].y
            vector[idx].push(p_x)
            vector[idx].push(p_y)
          }
        }
        return vector
      },
      /**
       * @method  painter_end_wave3  后3导第一次转换数据
       * @param data
       * @returns {Array}
       */
      get_end_data3(data) {
        var Rxy = []
        var Qxy = []
        var Rxz = []
        var Qxz = []
        var Ryz = []
        var Qyz = []
        var Xxy1 = []
        var Yxy1 = []
        var Xxy2 = []
        var Yxy2 = []
        var Xxy3 = []
        var Yxy3 = []
        var vectorX = []
        var vectorY = []
        var vector = []
        for (let array_idx = 0; array_idx < data[0].length; array_idx++) {
          Rxy.push(Math.sqrt(Math.pow(data[0][array_idx], 2) + Math.pow(data[1][array_idx], 2)))
          Qxy.push(Math.atan(data[0][array_idx] / data[1][array_idx]))
          Rxz.push(Math.sqrt(Math.pow(data[0][array_idx], 2) + Math.pow(data[2][array_idx], 2)))
          Qxz.push(Math.atan(data[0][array_idx] / data[2][array_idx]))
          Ryz.push(Math.sqrt(Math.pow(data[1][array_idx], 2) + Math.pow(data[2][array_idx], 2)))
          Qyz.push(Math.atan(data[1][array_idx] / data[2][array_idx]))
          // Qyz.push(Math.atan(data[2][array_idx] / data[1][array_idx]));
        }

        for (let array_idx = 0; array_idx < data[0].length; array_idx++) {
          Xxy1.push(isNaN(Rxy[array_idx] * Math.cos(Qxy[array_idx])) ? 0 : Rxy[array_idx] * Math.cos(Qxy[array_idx]))
          Yxy1.push(isNaN(Rxy[array_idx] * Math.sin(Qxy[array_idx])) ? 0 : Rxy[array_idx] * Math.sin(Qxy[array_idx]))
          Xxy2.push(isNaN(Rxz[array_idx] * Math.cos(Qxz[array_idx])) ? 0 : Rxz[array_idx] * Math.cos(Qxz[array_idx]))
          Yxy2.push(isNaN(Rxz[array_idx] * Math.sin(Qxz[array_idx])) ? 0 : Rxz[array_idx] * Math.sin(Qxz[array_idx]))
          Xxy3.push(isNaN(Ryz[array_idx] * Math.cos(Qyz[array_idx])) ? 0 : Ryz[array_idx] * Math.cos(Qyz[array_idx]))
          Yxy3.push(isNaN(Ryz[array_idx] * Math.sin(Qyz[array_idx])) ? 0 : Ryz[array_idx] * Math.sin(Qyz[array_idx]))
        }
        vectorX[0] = Xxy1
        vectorY[0] = Yxy1
        vectorX[1] = Xxy2
        vectorY[1] = Yxy2
        vectorX[2] = Xxy3
        vectorY[2] = Yxy3
        vector[0] = vectorX
        vector[1] = vectorY
        return vector
      },
      /**
       *  @method  painter_end_wave3  后三导第二次数据转换
       * @param svg_ecg
       * @param svg_elem_id
       * @param rect
       * @param ecgParme
       * @param ecgJson
       * @param out_point
       * @param lead_name
       * @param data
       */
      painter_end_wave3(svg_ecg, svg_elem_id, rect, ecgParme, ecgJson, out_point, lead_name, data, gridWidth) {
        var matrixVector_start3 = this.get_start_data3(data)
        var matrixVector_end3 = this.get_end_data3(matrixVector_start3)
        var vector = this.painter_end_data3(rect, ecgParme, matrixVector_end3, out_point, gridWidth)
        var ecg = document.getElementById(`ecg_${svg_elem_id}`)
        let idx = 3
        for (let lead_idx = 0; lead_idx < vector.length; lead_idx++) {
          zqSvg.polyline(
            svg_ecg,
            `polyline_${svg_elem_id}`,
            vector[lead_idx],
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          zqSvg.text(
            svg_ecg,
            `text_${svg_elem_id}`,
            out_point[idx].x,
            out_point[idx].y,
            'font-size:12px',
            'start',
            'Middle',
            lead_name[idx]
          )
          idx++
        }
        vector = []
      },
      /**
       *  @method  painter_end_wave3  后三导第三次数据转换
       * @param rect
       * @param ecgParme
       * @param data
       * @param out_point
       * @returns {Array}
       */
      painter_end_data3(rect, ecgParme, data, out_point, gridWidth) {
        var vector = []
        let indx = 3
        var x_gain = 1000.0 * (1.0 / ecgParme.speed)
        for (let idx = 0; idx < 3; idx++) {
          vector[idx] = []
          for (let lead_index = 0; lead_index < data[0][idx].length - ecgParme.begin_ms; lead_index++) {
            // if (ecgParme.begin_ms >= data[idx].length) {
            //     break
            // }
            var p_x, p_y
            p_x = (lead_index * ecgParme.speed * ecgParme.pix_mm) / 1000
            p_x = (data[0][idx][lead_index + ecgParme.begin_ms] * ecgParme.gain * x_gain) / 1000 + p_x
            p_x = p_x + out_point[indx].x + 10 * ecgParme.pix_mm
            if (p_x > gridWidth - this.userInfoParWidth * ecgParme.pix_mm) {
              break
            }
            p_y = (-1 * data[1][idx][lead_index + ecgParme.begin_ms] * ecgParme.gain * ecgParme.pix_mm) / 1000
            p_y += out_point[indx].y
            vector[idx].push(p_x)
            vector[idx].push(p_y)
          }
          indx++
        }
        return vector
      },
      /**
       *  @method  get_6_points  波形xy开始坐标
       * @param gridWidth
       * @param gridHeight
       * @param rect
       * @param pix_mm
       * @param speed
       * @returns {Array}
       */
      get_6_points(gridWidth, gridHeight, rect, pix_mm, speed) {
        const out_point = []
        const len = 6
        const per = (gridHeight - rect.top) / (len + 1)
        for (let i = 0; i < len; i++) {
          out_point[i] = {}
          out_point[i].x = rect.left + (speed / 2) * pix_mm
          out_point[i].y = per * (i + 1) + rect.top
          // [out_point[i]["x"],out_point[i]["y"]] = [rect["left"] + speed / 2 * pix_mm,per * (i+1) + rect["top"]]
        }
        return out_point
      },
      // 矩阵
      /**
       *  @method  matrix_parame  矩阵
       * @param systemType
       * @returns {Array}
       */
      matrix_parame(systemType) {
        var matrix_data = []
        var ln1 = []
        var ln2 = []
        var ln3 = []
        // 8*3矩阵
        // 标准导联体系，18导联体系
        // I II V1 V2 V3 V4 V5 v6 12导标准导联体系 -12
        if (systemType == 0 || systemType == 9) {
          ln1 = [0.156, -0.01, -0.172, -0.074, 0.122, 0.231, 0.239, 0.194]
          ln2 = [-0.227, 0.887, 0.057, -0.019, -0.106, -0.022, 0.041, 0.048]
          ln3 = [0.022, 0.102, -0.229, -0.31, -0.246, -0.063, 0.055, 0.108]
        } else {
          // FRANK导联体系
          // ln1 = [0,0,-0.781,0,0.171,0.610,0,0];
          // ln2 = [-1,2,0,0,0,0,0.345,-1];
          // ln3 = [0,0,-0.264,-0.374,-0.231,0.133,0.736,0];
          ln1 = [0, 0, -0.781, 0, 0.171, 0.61, 0, 0]
          ln2 = [-0.218, 0.437, -0, 0, 0, 0, 0.345, -1]
          ln3 = [0, 0, -0.264, -0.374, -0.231, 0.133, 0.736, 0]
        }
        matrix_data = [ln1, ln2, ln3]
        return matrix_data
      },
      /**
       *  @method  get_matrix_data  8导数据矩阵-8导原始数据换成20000个数组长度为8
       * @param data
       * @returns {Array}
       */
      get_matrix_data(data) {
        var vector = []
        for (let lead_index = 0; lead_index < data[0].length; lead_index++) {
          vector[lead_index] = []
          for (let array_idx = 0; array_idx < 8; array_idx++) {
            vector[lead_index].push(data[array_idx][lead_index])
          }
        }
        return vector
      },
      /**
       * @method  clearEcgWave  svg清除函数
       * @param svg_elem_id
       */
      clearEcgWave(svg_elem_id) {
        // gain
        var gainpolyline = document.getElementById(`${svg_elem_id}_gainpolyline`)
        if (gainpolyline != null) {
          gainpolyline.parentNode.removeChild(gainpolyline)
        }
        // 导联名
        const leadName = document.getElementsByClassName(`leadName_${svg_elem_id}`)
        for (let i = leadName.length - 1; i > -1; i--) {
          if (leadName[i] != null) {
            leadName[i].parentNode.removeChild(leadName[i])
          }
        }
        // 打印
        var printTitle = document.getElementById(`print_${svg_elem_id}`)
        if (printTitle != null) {
          printTitle.innerHTML = ''
        }
      }
    }
    window.zqTimeVector = window.zqTimeVector || new zqTimeVector()
    /**
     * 模块说明
     * @module  zqVector	向量封装
     * @rely  zqSvg
     */
    function zqVector() {
      this.qrsGain = 60
      this.tGain = 80
      this.speed = 50
      this.gain = 20
    }
    zqVector.prototype = {
      /**
       * @method  draw_ecg_vector  向量制入口函数
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_ecg_vector(svg_elem_id, ecgJson, vectorJson, ecgParme, isPrint, gridWidth, gridHeight) {
        this.clearEcgChild(svg_elem_id)
        var svg_ecg = document.getElementById(svg_elem_id)
        this.creatEcgChild(svg_ecg, svg_elem_id)
        svg_ecg.setAttribute('width', gridWidth)
        svg_ecg.setAttribute('height', gridHeight)
        this.draw_lead(svg_ecg, svg_elem_id, ecgJson, vectorJson, ecgParme, isPrint, gridWidth, gridHeight)
      },
      /**
       * @method  creatEcgChild  svg内部组件创建
       * @param svg_ecg
       * @param svg_elem_id
       */
      creatEcgChild(svg_ecg, svg_elem_id) {
        zqSvg.g(svg_ecg, `single_heart_beat_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `sectional_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `sectional_measure_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `print_${svg_elem_id}`)
      },
      /**
       * @method  draw_lead  心率变异性绘制入口函数
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_lead(svg_ecg, svg_elem_id, ecgJson, vectorJson, ecgParme, isPrint, gridWidth, gridHeight) {
        // let printTitle = document.getElementById("print_"+svg_elem_id);
        // var rect = {"left":0,"right":0,"top":0,"bottom":0};

        var rect = { left: 0, right: 0, top: 0, bottom: 0 }
        if (isPrint == 1) {
          // rect = {"left":15,"right":15,"top": 5 * ecgParme.pix_mm,"bottom":20};
          // let print = document.getElementById("print_" + svg_elem_id);
          // zqSvg.text(print,"text_" + svg_elem_id,
          //     gridWidth / 2, 20,
          //     "font-size:18px", "middle", "Hanging", "向量心电报告"
          // )
          rect = { left: 30, right: 10, top: 60, bottom: 30 }
          this.print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, rect)
        }
        var out_rect = zqSvg.painter_rect(svg_ecg, ecgParme.pix_mm, rect, gridWidth, gridHeight)
        zqSvg.painter_grid(svg_ecg, svg_elem_id, ecgParme, out_rect, gridWidth, gridHeight)
        this.draw_ecg(svg_ecg, svg_elem_id, ecgJson, vectorJson, ecgParme, out_rect, gridWidth, gridHeight)
        // Common.painter_gain(svg_ecg, svg_elem_id, ecgParme.pix_mm, 1, out_rect["left"], out_rect["top"] + (ecgParme.gridHeight - out_rect["top"])/2,ecgParme.gain,ecgParme.speed);
      },
      /**
       * @method  print  打印内容
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson
       * @param ecgParme
       */
      print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, rect) {
        const print = document.getElementById(`print_${svg_elem_id}`)
        let { title1 } = paramset.report
        if (title1 == '') {
          title1 = ecgJson.checkSection
        }
        const { title2 } = paramset.report
        // if(title2 == ''){
        //     title2 = ecgJson['checkDept']
        // }
        const title1_y = 20
        const title2_y = 40
        let line_y = 7 * ecgParme.pix_mm
        if (ecgParme.previewEcg) {
          line_y = 7 * ecgParme.pix_mm + 10
        }

        zqSvg.text(print, `text_${svg_elem_id}`, gridWidth / 2, title1_y, 'font-size:18px', 'middle', 'Hanging', title1)
        zqSvg.text(print, `text_${svg_elem_id}`, gridWidth / 2, title2_y, 'font-size:14px', 'middle', 'Hanging', title2)
        zqSvg.line(
          print,
          ecgParme.pix_mm * 5,
          gridWidth - ecgParme.pix_mm * 2,
          line_y,
          line_y,
          `line_${svg_elem_id}`,
          'stroke-width:1;stroke:#000000'
        )
        const rightDown = zqSvg.getPrintTimeName(paramset.report.rightDown)
        const rightUp = zqSvg.getPrintTimeName(paramset.report.rightUp)

        if (paramset.report.rightUp == 'reviewTime' || paramset.report.rightUp == 'diagnoseTime') {
          zqCommon.getServerSystemTime(
            'up',
            print,
            svg_elem_id,
            gridWidth,
            rightUp,
            rightDown,
            rect,
            paramset.report.rightUp,
            ecgJson.id
          )
        } else if (ecgJson[paramset.report.rightUp] != null) {
          zqSvg.text(
            print,
            `text_${svg_elem_id}`,
            gridWidth - rect.right,
            title1_y,
            'font-size:14px',
            'end',
            'Hanging',
            rightUp + ecgJson[paramset.report.rightUp]
          )
        }
        if (paramset.report.rightDown == 'reviewTime' || paramset.report.rightDown == 'diagnoseTime') {
          zqCommon.getServerSystemTime(
            'down',
            print,
            svg_elem_id,
            gridWidth,
            rightUp,
            rightDown,
            rect,
            paramset.report.rightDown,
            ecgJson.id
          )
        } else if (ecgJson[paramset.report.rightDown] != null) {
          zqSvg.text(
            print,
            `text_${svg_elem_id}`,
            gridWidth - rect.right,
            title2_y,
            'font-size:14px',
            'end',
            'Hanging',
            rightDown + ecgJson[paramset.report.rightDown]
          )
        }
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          title1_y,
          'font-size:14px',
          'start',
          'Hanging',
          '向量心电报告'
        )
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          gridHeight - 15,
          'font-size:12px',
          'start',
          'Hanging',
          '此报告仅供临床参考，不做任何证明材料用'
        )
      },
      /**
       * @method  draw_ecg  向量绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param out_rect svg波形可绘制区域上下左右距离
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_ecg(svg_ecg, svg_elem_id, ecgJson, vectorJson, ecgParme, out_rect, gridWidth, gridHeight) {
        const axisesName = ['X-Y', 'X-Z', 'Z-Y']
        const vector_ori = this.draw_ecg_sectional_data_ori(vectorJson, ecgParme, axisesName)
        this.draw_ecg_single_heart_beat(
          svg_ecg,
          svg_elem_id,
          ecgJson,
          vectorJson,
          ecgParme,
          gridWidth,
          gridHeight,
          out_rect
        )
        this.draw_ecg_sectional(svg_ecg, svg_elem_id, ecgJson, vectorJson, ecgParme, out_rect, vector_ori)
        this.draw_ecg_sectional_measure(
          svg_ecg,
          svg_elem_id,
          ecgJson,
          vectorJson,
          ecgParme,
          out_rect,
          vector_ori,
          axisesName,
          gridHeight
        )
      },
      /**
       * @method draw_ecg_single_heart_beat XYZ单心拍绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson
       * @param ecgParme
       * @param gridWidth
       * @param gridHeight
       * @param out_rect
       */
      draw_ecg_single_heart_beat(svg_ecg, svg_elem_id, ecgJson, vectorJson, ecgParme, gridWidth, gridHeight, out_rect) {
        const single_heart_beat = document.getElementById(`single_heart_beat_${svg_elem_id}`)
        const len = 3
        const per = (gridHeight - out_rect.top - out_rect.bottom) / len
        const axisesName = ['X', 'Y', 'Z']
        const lineName = ['p_begin', 'q_begin', 's_end', 't_begin', 't_end']
        zqSvg.text(
          single_heart_beat,
          `text_${svg_elem_id}`,
          out_rect.left + (this.speed / 5) * ecgParme.pix_mm + ecgParme.pix_mm * 2,
          out_rect.top + 3 * ecgParme.pix_mm,
          '',
          'start',
          'Hanging',
          `${this.speed}mm/s ${this.gain}mm/mv`
        )
        for (let idx = 0; idx < len; idx++) {
          const widget = document.createElementNS('http://www.w3.org/2000/svg', 'g')
          single_heart_beat.appendChild(widget)
          const gain = {}
          gain.gain_left = out_rect.left
          gain.gain_top = out_rect.top + per * (idx + 1) - ecgParme.pix_mm * 50
          gain.gain_right = out_rect.left + (this.speed / 5) * ecgParme.pix_mm + ecgParme.pix_mm * 2
          gain.gain_bottom = gain.gain_top + (this.speed / 5) * ecgParme.pix_mm + ecgParme.pix_mm
          // 定标电压绘制
          this.painter_gain(
            svg_ecg,
            svg_elem_id,
            ecgParme,
            out_rect.left,
            out_rect.top + (gridHeight - out_rect.top) / 2
          )
          // Common.painter_gain_class(widget, svg_elem_id, ecgParme.pix_mm, 1, gain.gain_left, gain.gain_top,ecgParme.gain,ecgParme.speed);
          const { vector, line } = this.single_heart_beat_data(
            widget,
            svg_elem_id,
            vectorJson,
            ecgParme,
            out_rect,
            gain,
            axisesName,
            lineName
          )
          // 平均模板绘制
          zqSvg.polyline(
            widget,
            `polyline_${svg_elem_id}`,
            vector[idx],
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:1`
          )
          // 虚竖线绘制
          zqSvg.line(
            widget,
            gain.gain_right,
            gain.gain_right + 1,
            gain.gain_bottom - ecgParme.pix_mm * 5,
            gain.gain_bottom + ecgParme.pix_mm * 5,
            `line_${svg_elem_id}`,
            `stroke:${ecgParme.waveColor}`,
            '2 2'
          )
          // 导联名称绘制
          zqSvg.text(
            widget,
            `text_${svg_elem_id}`,
            gain.gain_right,
            gain.gain_bottom - ecgParme.pix_mm * 8,
            '',
            'start',
            'Middle',
            axisesName[idx]
          )
          // 实竖线绘制
          for (let idxs = 0; idxs < lineName.length; idxs++) {
            if (line[idx][lineName[idxs]]) {
              zqSvg.line(
                widget,
                line[idx][lineName[idxs]][0],
                line[idx][lineName[idxs]][0],
                line[idx][lineName[idxs]][1] - ecgParme.pix_mm * 5,
                line[idx][lineName[idxs]][1] + ecgParme.pix_mm * 5,
                `line_${svg_elem_id}`,
                `stroke:${ecgParme.waveColor}`,
                ''
              )
            }
          }
        }
      },
      /**
       * @method  painter_gain  定标电压绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme
       * @param px 定标电压x坐标
       * @param py 定标电压y坐标
       */
      painter_gain(svg_ecg, svg_elem_id, ecgParme, px, py) {
        var gainpolylineDate = zqSvg.set_gain_point(ecgParme.pix_mm, px, py, this.gain, this.speed)
        zqSvg.polyline(
          svg_ecg,
          `gainpolyline_${svg_elem_id}`,
          gainpolylineDate,
          `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:1`
        )
      },
      /**
       * @method single_heart_beat_data 单心拍数据获取
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson
       * @param ecgParme
       * @param out_rect
       * @param gain
       * @param axisesName
       * @param lineName
       * @returns {{vector: Array, line: Array}}
       */
      single_heart_beat_data(svg_ecg, svg_elem_id, ecgJson, ecgParme, out_rect, gain, axisesName, lineName) {
        const vector = []
        const line = []
        for (let idx = 0; idx < axisesName.length; idx++) {
          ;(vector[idx] = []), (line[idx] = {})
          const axises = ecgJson.axises[axisesName[idx]]
          for (let array_idx = 0; array_idx < axises.length; array_idx++) {
            let p_x = (array_idx * this.speed * ecgParme.pix_mm) / ecgJson.fs
            let p_y = axises[array_idx]
            p_y = (p_y * this.gain * ecgParme.pix_mm) / ecgJson.fs
            p_x += gain.gain_right
            p_y += gain.gain_bottom
            vector[idx].push(p_x)
            vector[idx].push(p_y)
            for (let idxs = 0; idxs < lineName.length; idxs++) {
              const name = lineName[idxs]
              // 原始传递值为位置索引，通过循环索引等值判断p_begin等竖线的绘制定位
              if (ecgJson[name] == array_idx) {
                line[idx][name] = []
                line[idx][name][0] = p_x
                line[idx][name][1] = p_y
              }
            }
          }
        }
        return { vector, line }
      },
      /**
       * @method  draw_ecg_sectional 极点图绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson
       * @param ecgParme
       * @param out_rect
       * @param vector_ori
       */
      draw_ecg_sectional(svg_ecg, svg_elem_id, ecgJson, vectorJson, ecgParme, out_rect, vector_ori) {
        const sectional = document.getElementById(`sectional_${svg_elem_id}`)
        const len = 3
        const perHeight = Math.floor((out_rect.hGrid - 5) / len)
        const perWidth = Math.floor(out_rect.wGrid / 2 - this.speed / 5 - 1)
        const sectionalLeft =
          out_rect.left +
          (this.speed / 5) * ecgParme.pix_mm +
          ecgParme.pix_mm * 2 +
          this.speed * ecgParme.pix_mm +
          ecgParme.pix_mm * 10

        const axisesName = ['X-Y', 'X-Z', 'Z-Y']
        const axisesTitle = ['额面F(X-Y)', '横面H(X-Z)', '右侧面S(Z-Y)']
        const vector = this.draw_ecg_sectional_data(vectorJson, vector_ori)
        for (let idx = 0; idx < len; idx++) {
          const widget = document.createElementNS('http://www.w3.org/2000/svg', 'g')
          sectional.appendChild(widget)
          const qrs_rect = {}
          qrs_rect.width = (perWidth - Math.floor(perWidth / len)) * 5 * ecgParme.pix_mm
          qrs_rect.height = perHeight * 5 * ecgParme.pix_mm
          qrs_rect.left = sectionalLeft
          qrs_rect.top = perHeight * 5 * ecgParme.pix_mm * idx + out_rect.top
          qrs_rect.X = qrs_rect.left + qrs_rect.width / 2
          qrs_rect.Y = qrs_rect.top + qrs_rect.height / 2
          // qrs_rect.X = qrs_rect.left + (Math.abs(vector["xVectorMax"][idx][0]) +  Math.abs(vector["xVectorMin"][idx][0])) / 2
          // qrs_rect.Y = qrs_rect.top + (Math.abs(vector["yVectorMax"][idx][0]) +  Math.abs(vector["yVectorMin"][idx][0])) / 2
          qrs_rect.circleCount = Math.ceil(
            Math.sqrt(Math.pow(qrs_rect.width, 2) + Math.pow(qrs_rect.height, 2)) / (5 * ecgParme.pix_mm)
          )
          const p_rect = {}
          p_rect.width = Math.floor(perWidth / len) * 5 * ecgParme.pix_mm
          p_rect.height = (perHeight / 2) * 5 * ecgParme.pix_mm
          p_rect.left = sectionalLeft + qrs_rect.width
          p_rect.top = perHeight * 5 * ecgParme.pix_mm * idx + out_rect.top
          p_rect.X = p_rect.left + p_rect.width / 2
          p_rect.Y = p_rect.top + p_rect.height / 2
          // p_rect.X = p_rect.left + (Math.abs(vector["xVectorMax"][idx][1]) +  Math.abs(vector["xVectorMin"][idx][1])) / 2
          // p_rect.Y = p_rect.top + (Math.abs(vector["yVectorMax"][idx][1]) +  Math.abs(vector["yVectorMin"][idx][1])) / 2
          p_rect.circleCount = Math.ceil(
            Math.sqrt(Math.pow(p_rect.width, 2) + Math.pow(p_rect.height, 2)) / (5 * ecgParme.pix_mm)
          )
          const t_rect = {}
          t_rect.width = Math.floor(perWidth / len) * 5 * ecgParme.pix_mm
          t_rect.height = (perHeight / 2) * 5 * ecgParme.pix_mm
          t_rect.left = sectionalLeft + qrs_rect.width
          t_rect.top = perHeight * 5 * ecgParme.pix_mm * idx + t_rect.height + out_rect.top
          t_rect.X = t_rect.left + t_rect.width / 2
          t_rect.Y = t_rect.top + t_rect.height / 2
          // t_rect.X = t_rect.left + (Math.abs(vector["xVectorMax"][idx][2]) +  Math.abs(vector["xVectorMin"][idx][2])) / 2
          // t_rect.Y = t_rect.top + (Math.abs(vector["yVectorMax"][idx][2]) +  Math.abs(vector["yVectorMin"][idx][2])) / 2
          t_rect.circleCount = Math.ceil(
            Math.sqrt(Math.pow(t_rect.width, 2) + Math.pow(t_rect.height, 2)) / (5 * ecgParme.pix_mm)
          )
          // 申明区域裁切
          zqSvg.clipPath(
            widget,
            `clipPath_qrs_${svg_elem_id}${idx}`,
            qrs_rect.left,
            qrs_rect.top,
            qrs_rect.width,
            qrs_rect.height
          )
          zqSvg.clipPath(
            widget,
            `clipPath_p_${svg_elem_id}${idx}`,
            p_rect.left,
            p_rect.top,
            p_rect.width,
            p_rect.height
          )
          zqSvg.clipPath(
            widget,
            `clipPath_t_${svg_elem_id}${idx}`,
            t_rect.left,
            t_rect.top,
            t_rect.width,
            t_rect.height
          )
          // 边框绘制
          zqSvg.rect(
            widget,
            `rect_${svg_elem_id}`,
            qrs_rect.left,
            qrs_rect.top,
            qrs_rect.width + p_rect.width,
            qrs_rect.height,
            `stroke:${ecgParme.waveColor};fill:#fff;stroke-width:1`,
            '3 5'
          )
          zqSvg.line(
            widget,
            p_rect.left,
            p_rect.left,
            p_rect.top,
            p_rect.top + qrs_rect.height,
            `line_${svg_elem_id}`,
            `stroke:${ecgParme.waveColor}`,
            '3 5'
          )
          zqSvg.line(
            widget,
            p_rect.left,
            p_rect.left + p_rect.width,
            t_rect.top,
            t_rect.top,
            `line_${svg_elem_id}`,
            `stroke:${ecgParme.waveColor}`,
            '3 5'
          )
          // 圆弧绘制
          this.draw_ecg_sectional_circle(
            widget,
            svg_elem_id,
            ecgParme,
            qrs_rect,
            `url(#clipPath_qrs_${svg_elem_id}${idx})`
          )
          this.draw_ecg_sectional_circle(widget, svg_elem_id, ecgParme, p_rect, `url(#clipPath_p_${svg_elem_id}${idx})`)
          this.draw_ecg_sectional_circle(widget, svg_elem_id, ecgParme, t_rect, `url(#clipPath_t_${svg_elem_id}${idx})`)
          // 直径绘制
          this.draw_ecg_sectional_line(
            widget,
            svg_elem_id,
            ecgParme,
            qrs_rect,
            `url(#clipPath_qrs_${svg_elem_id}${idx})`
          )
          this.draw_ecg_sectional_line(widget, svg_elem_id, ecgParme, p_rect, `url(#clipPath_p_${svg_elem_id}${idx})`)
          this.draw_ecg_sectional_line(widget, svg_elem_id, ecgParme, t_rect, `url(#clipPath_t_${svg_elem_id}${idx})`)
          // 环图绘制
          this.draw_ecg_sectional_ecg(
            widget,
            svg_elem_id,
            vector.xVector[idx][0],
            vector.yVector[idx][0],
            ecgParme,
            qrs_rect,
            axisesName,
            `url(#clipPath_qrs_${svg_elem_id}${idx})`
          )
          this.draw_ecg_sectional_ecg(
            widget,
            svg_elem_id,
            vector.xVector[idx][1],
            vector.yVector[idx][1],
            ecgParme,
            p_rect,
            axisesName,
            `url(#clipPath_p_${svg_elem_id}${idx})`
          )
          this.draw_ecg_sectional_ecg(
            widget,
            svg_elem_id,
            vector.xVector[idx][2],
            vector.yVector[idx][2],
            ecgParme,
            t_rect,
            axisesName,
            `url(#clipPath_t_${svg_elem_id}${idx})`
          )

          // ,'rotate('+ -90 +' '+ qrs_rect.left +' '+ (qrs_rect.top + qrs_rect.width / 3) +')'
          zqSvg.text(
            widget,
            `text_${svg_elem_id}`,
            qrs_rect.left,
            qrs_rect.top + qrs_rect.height / 2,
            '',
            'middle',
            'auto',
            axisesTitle[idx],
            `rotate(${-90} ${qrs_rect.left} ${qrs_rect.top + qrs_rect.height / 2})`
          )

          zqSvg.text(
            widget,
            `text_${svg_elem_id}`,
            qrs_rect.left,
            qrs_rect.top + 3 * ecgParme.pix_mm,
            '',
            'start',
            'Hanging',
            'QRS-T'
          )
          zqSvg.text(
            widget,
            `text_${svg_elem_id}`,
            qrs_rect.left + qrs_rect.width,
            qrs_rect.top + qrs_rect.height,
            '',
            'end',
            'auto',
            `${this.qrsGain}mm/mv`
          )

          zqSvg.text(
            widget,
            `text_${svg_elem_id}`,
            p_rect.left,
            p_rect.top + 3 * ecgParme.pix_mm,
            '',
            'start',
            'Hanging',
            'P'
          )
          zqSvg.text(
            widget,
            `text_${svg_elem_id}`,
            p_rect.left + p_rect.width,
            p_rect.top + p_rect.height,
            '',
            'end',
            'auto',
            `${this.tGain}mm/mv`
          )

          zqSvg.text(
            widget,
            `text_${svg_elem_id}`,
            t_rect.left,
            t_rect.top + 3 * ecgParme.pix_mm,
            '',
            'start',
            'Hanging',
            'T'
          )
          zqSvg.text(
            widget,
            `text_${svg_elem_id}`,
            t_rect.left + t_rect.width,
            t_rect.top + t_rect.height,
            '',
            'end',
            'auto',
            `${this.tGain}mm/mv`
          )
        }
      },
      /**
       * @method draw_ecg_sectional_data_ori 原始数据长度为5进行截取并存放
       * @param ecgJson
       * @param ecgParme
       * @param axisesName
       * @returns {{xVector: Array, yVector: Array, rVector: Array, thetaVector: Array, areaVector: Array}}
       */
      draw_ecg_sectional_data_ori(ecgJson, ecgParme, axisesName) {
        const vector = {
          xVector: [],
          yVector: [],
          rVector: [],
          thetaVector: [],
          areaVector: []
        }
        const vector_ori = []
        const gain = ['qrsGain', 'tGain', 'tGain']
        // x轴坐标, y轴坐标, 距离,  角度, 面积
        for (let idxs = 0; idxs < axisesName.length; idxs++) {
          const plans = ecgJson.plans[axisesName[idxs]]
          ;(vector_ori[idxs] = []),
            (vector.xVector[idxs] = []),
            (vector.yVector[idxs] = []),
            (vector.rVector[idxs] = []),
            (vector.thetaVector[idxs] = []),
            (vector.areaVector[idxs] = [])
          console.log(`plansplansplansplans`, plans)
          for (let idx = 0; idx < ecgJson.xyzEachLen; idx++) {
            vector_ori[idxs].push(plans.slice(idx * 5, (idx + 1) * 5))
          }
          for (let idx = 0; idx < ecgJson.xyzEachLen; idx++) {
            if (vector_ori[idxs][idx].length >= 5) {
              vector.xVector[idxs].push((vector_ori[idxs][idx][0] * this[gain[idxs]] * ecgParme.pix_mm) / ecgJson.fs)
              vector.yVector[idxs].push((vector_ori[idxs][idx][1] * this[gain[idxs]] * ecgParme.pix_mm) / ecgJson.fs)
              vector.rVector[idxs].push(vector_ori[idxs][idx][2])
              vector.thetaVector[idxs].push(vector_ori[idxs][idx][3])
              vector.areaVector[idxs].push(vector_ori[idxs][idx][4])
            }
          }
        }
        console.log(`draw_ecg_sectional_data_ori vector`, vector)
        return vector
      },
      // P QRS-T T环图数据获取，最大最小值获取
      /*
        P环图使用相应平面(plans)的p_begin p_end之间的xy坐标画图；
        QRS-T环图使用相应平面(plans)的q_begin t_end之间的xy坐标画图；
        T环图使用相应平面(plans)的s_end t_end之间的xy坐标画图；
        */
      /**
       * @method draw_ecg_sectional_data P QRS-T T环图数据获取，最大最小值获取
       * @param ecgJson
       * @param vector_ori
       * @returns {{xVector: Array, yVector: Array, xVectorMax: Array, xVectorMin: Array, yVectorMax: Array, yVectorMin: Array}}
       */
      draw_ecg_sectional_data(ecgJson, vector_ori) {
        const vector = {
          xVector: [],
          yVector: [],
          xVectorMax: [],
          xVectorMin: [],
          yVectorMax: [],
          yVectorMin: []
        }
        for (let idx = 0; idx < vector_ori.xVector.length; idx++) {
          ;(vector.xVector[idx] = []), (vector.yVector[idx] = [])

          vector.xVector[idx][1] = vector_ori.xVector[idx].filter((item, index) => {
            return index >= ecgJson.p_begin && index <= ecgJson.p_end
          })
          vector.xVector[idx][0] = vector_ori.xVector[idx].filter((item, index) => {
            return index >= ecgJson.q_begin && index <= ecgJson.t_end
          })
          vector.xVector[idx][2] = vector_ori.xVector[idx].filter((item, index) => {
            return index >= ecgJson.s_end && index <= ecgJson.t_end
          })

          vector.yVector[idx][1] = vector_ori.yVector[idx].filter((item, index) => {
            return index >= ecgJson.p_begin && index <= ecgJson.p_end
          })
          vector.yVector[idx][0] = vector_ori.yVector[idx].filter((item, index) => {
            return index >= ecgJson.q_begin && index <= ecgJson.t_end
          })
          vector.yVector[idx][2] = vector_ori.yVector[idx].filter((item, index) => {
            return index >= ecgJson.s_end && index <= ecgJson.t_end
          })
        }
        for (let idx = 0; idx < vector.xVector.length; idx++) {
          ;(vector.xVectorMax[idx] = []),
            (vector.xVectorMin[idx] = []),
            (vector.yVectorMax[idx] = []),
            (vector.yVectorMin[idx] = [])
          for (let idxs = 0; idxs < 3; idxs++) {
            vector.xVectorMax[idx][idxs] = vector.xVector[idx][idxs].reduce(zqCommon.getMax, 0)
            vector.xVectorMin[idx][idxs] = vector.xVector[idx][idxs].reduce(zqCommon.getMin, 0)
            vector.yVectorMax[idx][idxs] = vector.yVector[idx][idxs].reduce(zqCommon.getMax, 0)
            vector.yVectorMin[idx][idxs] = vector.yVector[idx][idxs].reduce(zqCommon.getMin, 0)
          }
        }
        return vector
      },
      /**
       * @method draw_ecg_sectional_circle 背景绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme
       * @param rect
       * @param clipPath_id
       */
      draw_ecg_sectional_circle(svg_ecg, svg_elem_id, ecgParme, rect, clipPath_id) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'g')
        widget.setAttribute('clip-path', clipPath_id)
        svg_ecg.appendChild(widget)
        for (let idx = 0; idx < rect.circleCount; idx++) {
          zqSvg.circle(
            widget,
            rect.X,
            rect.Y,
            5 * ecgParme.pix_mm * idx,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:0.4`,
            `circle_${svg_elem_id}`,
            '2 4'
          )
        }
      },
      /**
       * @method  draw_ecg_sectional_line 坐标绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme
       * @param rect
       * @param clipPath_id
       */
      draw_ecg_sectional_line(svg_ecg, svg_elem_id, ecgParme, rect, clipPath_id) {
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'g')
        widget.setAttribute('clip-path', clipPath_id)
        svg_ecg.appendChild(widget)
        for (let idx = 0; idx < 12; idx++) {
          // transform='rotate(90 250 250)'绕着(250,250)点旋转 90°
          zqSvg.line(
            widget,
            rect.X,
            rect.X,
            rect.Y - rect.circleCount * 10 * ecgParme.pix_mm,
            rect.Y + rect.circleCount * 10 * ecgParme.pix_mm,
            `line_${svg_elem_id}`,
            'stroke:gray;stroke-width:0.1;fill:#FFFFFF',
            '2 8',
            `rotate(${idx * 30} ${rect.X} ${rect.Y})`
          )
        }
      },
      /**
       * @method draw_ecg_sectional_ecg 切割区域申明
       * @param svg_ecg
       * @param svg_elem_id
       * @param xVector
       * @param yVector
       * @param ecgParme
       * @param rect
       * @param axisesName
       * @param clipPath_id
       */
      draw_ecg_sectional_ecg(svg_ecg, svg_elem_id, xVector, yVector, ecgParme, rect, axisesName, clipPath_id) {
        xVector = xVector.map((item, idx) => {
          return rect.X + item
        })
        yVector = yVector.map((item, idx) => {
          return rect.Y + item
        })
        const widget = document.createElementNS('http://www.w3.org/2000/svg', 'g')
        svg_ecg.appendChild(widget)
        widget.setAttribute('clip-path', clipPath_id)
        for (let idx = 0; idx < xVector.length; idx++) {
          zqSvg.ellipse(
            widget,
            xVector[idx],
            yVector[idx],
            0.5,
            1,
            `fill:${ecgParme.waveColor};`,
            `ellipse_${svg_elem_id}`
          )
          // Common.line(widget,xVector[idx], xVector[idx], yVector[idx], yVector[idx] + 1, "line_" + svg_elem_id, "stroke:"+ecgParme.wave_color)
        }
      },
      /**
       * @method  draw_ecg_sectional_measure 坐标文字绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson
       * @param ecgParme
       * @param out_rect
       * @param vector_ori
       * @param axisesName
       * @param gridHeight
       */
      draw_ecg_sectional_measure(
        svg_ecg,
        svg_elem_id,
        ecgJson,
        vectorJson,
        ecgParme,
        out_rect,
        vector_ori,
        axisesName,
        gridHeight
      ) {
        const sectional_measure = document.getElementById(`sectional_measure_${svg_elem_id}`)
        const len = 3
        const measureLeft =
          out_rect.left +
          (out_rect.wGrid / 2) * 5 * ecgParme.pix_mm +
          (this.speed / 5) * ecgParme.pix_mm +
          ecgParme.pix_mm * 10
        const measureTop = out_rect.top + 3 * ecgParme.pix_mm
        const userInfo1 = [
          ['编号：', ecgJson.pid],
          ['姓名：', ecgJson.name != null ? ecgJson.name : ''],
          ['性别：', zqCommon.switchSex(ecgJson.sex)],
          ['年龄：', ecgJson.age + zqCommon.switchAgeUnit(ecgJson.ageUnit)],
          ['科室：', ecgJson.checkDept],
          ['床号：', ecgJson.bedNum != null ? ecgJson.bedNum : '']
        ]
        const quadrantVector = this.quadrant_measure(vectorJson, ecgParme, out_rect, vector_ori)
        const QRSVector = this.QRS_measure(vectorJson, ecgParme, out_rect, vector_ori)
        const STTVector = this.STT_measure(vectorJson, ecgParme, out_rect, vector_ori)
        const totalVector = this.total_measure(vectorJson, ecgParme, out_rect, vector_ori)
        const sectionalVector = this.sectional_measure(vectorJson, ecgParme, out_rect, vector_ori, axisesName)

        this.draw_ecg_sectional_measure_text(sectional_measure, userInfo1, measureLeft, measureTop, 1)
        this.draw_ecg_sectional_measure_text(
          sectional_measure,
          quadrantVector,
          measureLeft,
          measureTop + 35 * ecgParme.pix_mm,
          1
        )
        this.draw_ecg_sectional_measure_text(
          sectional_measure,
          QRSVector,
          measureLeft,
          measureTop + 58 * ecgParme.pix_mm,
          2
        )
        this.draw_ecg_sectional_measure_text(
          sectional_measure,
          STTVector,
          measureLeft,
          measureTop + 88 * ecgParme.pix_mm,
          3
        )
        this.draw_ecg_sectional_measure_text(
          sectional_measure,
          totalVector,
          measureLeft,
          measureTop + 115 * ecgParme.pix_mm,
          4
        )
        this.draw_ecg_sectional_measure_text(
          sectional_measure,
          sectionalVector,
          out_rect.left,
          gridHeight - 25 * ecgParme.pix_mm,
          5
        )
      },
      /**
       * @method quadrant_measure 参数绘制
       * @param ecgJson
       * @param ecgParme
       * @param out_rect
       * @param vector_ori
       * @returns {*[]}
       */
      quadrant_measure(ecgJson, ecgParme, out_rect, vector_ori) {
        const quadrantVector = [
          ['', '4象限百分比(%)', 'QRS-T夹角(°)', 'STJ向量(mv/°)'],
          ['额面', '', '', ''],
          ['横面', '', '', ''],
          ['右侧面', '', '', '']
        ]
        for (let idxs = 0; idxs < 3; idxs++) {
          // 4象限百分比
          const qBegin_sEnd = vector_ori.thetaVector[idxs].filter((item, index) => {
            return index >= ecgJson.q_begin && index <= ecgJson.s_end
          })
          const qBegin_sEnd090 = []
          let quadrantCount = ''
          for (let idx = 0; idx < 4; idx++) {
            const qBegin_sEnd090 = []
            qBegin_sEnd.filter((item, index) => {
              if (item >= idx * 90 && item < (idx + 1) * 90) {
                qBegin_sEnd090.push(vector_ori.areaVector[0][index + ecgJson.q_begin])
              }
            })
            const count = qBegin_sEnd090.reduce(zqCommon.count, 0).toFixed(2)
            quadrantCount += `${count},`
          }
          quadrantCount = quadrantCount.substr(0, quadrantCount.length - 1)
          quadrantVector[idxs + 1][1] = quadrantCount

          // QRS-T夹角(°)
          const st_t = vector_ori.rVector[idxs].filter((item, index) => {
            return index >= ecgJson.s_end && index <= ecgJson.t_end
          })
          const st_t_max = st_t.reduce(zqCommon.getMax, 0)
          let st_t_theta = ''
          st_t.filter((item, index) => {
            if (item == st_t_max) {
              st_t_theta = vector_ori.thetaVector[[idxs]][index + ecgJson.s_end]
            }
          })
          const qrs = vector_ori.rVector[idxs].filter((item, index) => {
            return index >= ecgJson.q_begin && index <= ecgJson.s_end
          })
          const qrs_max = st_t.reduce(zqCommon.getMax, 0)
          let qrs_theta = ''
          qrs.filter((item, index) => {
            if (item == qrs_max) {
              qrs_theta = vector_ori.thetaVector[[idxs]][index + ecgJson.s_end]
            }
          })
          const arg = Math.abs(st_t_theta - qrs_theta)
          quadrantVector[idxs + 1][2] = arg

          // STJ向量(mv/°)
          const stjr = vector_ori.rVector[idxs].filter((item, index) => {
            return index == ecgJson.s_end
          })
          const stjtheta = vector_ori.thetaVector[idxs].filter((item, index) => {
            return index == ecgJson.s_end
          })
          quadrantVector[idxs + 1][3] = `${stjr}/${stjtheta}`
        }
        return quadrantVector
      },
      /**
       * @method QRS_measure 参数绘制
       * @param ecgJson
       * @param ecgParme
       * @param out_rect
       * @param vector_ori
       * @returns {*[]}
       */
      QRS_measure(ecgJson, ecgParme, out_rect, vector_ori) {
        const QRSVector = [
          ['<<QRS起始40ms向量>>', '', '', ''],
          ['', '10ms(mv/°)', '20ms(mv/°)', '30ms(mv/°)', '40ms(mv/°)'],
          ['额面', '', '', '', ''],
          ['横面', '', '', '', ''],
          ['右侧面', '', '', '', '']
        ]

        for (let idxs = 0; idxs < 3; idxs++) {
          // 10ms(mv/°)
          for (let idx = 0; idx < 4; idx++) {
            const qBeginIndex = ecgJson.q_begin + ((idx + 1) * 10 * ecgJson.fs) / 1000
            const stjr = vector_ori.rVector[idxs].filter((item, index) => {
              return index == qBeginIndex
            })
            const stjtheta = vector_ori.thetaVector[idxs].filter((item, index) => {
              return index == qBeginIndex
            })
            QRSVector[idxs + 2][idx + 1] = `${stjr}/${stjtheta}`
          }
        }
        return QRSVector
      },
      /**
       * @method STT_measure 参数绘制
       * @param ecgJson
       * @param ecgParme
       * @param out_rect
       * @param vector_ori
       * @returns {*[]}
       */
      STT_measure(ecgJson, ecgParme, out_rect, vector_ori) {
        const STTVector = [
          ['<<ST-T起始20,40,60,80ms向量>>', '', '', ''],
          ['', '10ms(mv/°)', '20ms(mv/°)', '30ms(mv/°)', '40ms(mv/°)'],
          ['额面', '', '', '', ''],
          ['横面', '', '', '', ''],
          ['右侧面', '', '', '', '']
        ]

        for (let idxs = 0; idxs < 3; idxs++) {
          // 10ms(mv/°)
          for (let idx = 0; idx < 4; idx++) {
            const sEndIndex = ecgJson.s_end + ((idx + 2) * 10 * ecgJson.fs) / 1000
            const stjr = vector_ori.rVector[idxs].filter((item, index) => {
              return index == sEndIndex
            })
            const stjtheta = vector_ori.thetaVector[idxs].filter((item, index) => {
              return index == sEndIndex
            })
            STTVector[idxs + 2][idx + 1] = `${stjr}/${stjtheta}`
          }
        }
        return STTVector
      },
      /**
       * @method total_measure 参数绘制
       * @param ecgJson
       * @param ecgParme
       * @param out_rect
       * @param vector_ori
       * @returns {*[]}
       */
      total_measure(ecgJson, ecgParme, out_rect, vector_ori) {
        const totalVector = [
          ['<<综合参数>>', '', '', ''],
          ['HR:', '', 'P环最大振幅:', ''],
          ['PR:', '', 'QRS环运行时间:', ''],
          ['QRS:', '', 'QRS环最大振幅:', ''],
          ['QT/QTC:', '', 'ST-T环运行时间:', ''],
          ['P环运行时间:', '', 'ST-T环最大振幅:', '']
        ]
        // P环最大振幅：space.r 在p_begin p_end之间的最大值
        const pBegin_pEnd = vector_ori.rVector[0].filter((item, index) => {
          return index >= ecgJson.p_begin && index <= ecgJson.p_end
        })
        const pBegin_pEnd_max = pBegin_pEnd.reduce(zqCommon.getMax, 0)
        // QRS环最大振幅：space.r 在q_begin s_end之间的最大值
        const qBegin_sEnd = vector_ori.rVector[0].filter((item, index) => {
          return index >= ecgJson.q_begin && index <= ecgJson.s_end
        })
        const qBegin_sEnd_max = qBegin_sEnd.reduce(zqCommon.getMax, 0)
        // ST-T环最大振幅：space.r 在s_end t_end之间的最大值
        const sEnd_tEnd = vector_ori.rVector[0].filter((item, index) => {
          return index >= ecgJson.s_end && index <= ecgJson.t_end
        })
        const sEnd_tEnd_max = sEnd_tEnd.reduce(zqCommon.getMax, 0)
        totalVector[1][1] = `${ecgJson.hr}bmp`
        totalVector[1][3] = `${pBegin_pEnd_max}mv`
        totalVector[2][1] = `${((ecgJson.q_begin - ecgJson.p_begin) * 1000) / ecgJson.fs}ms`
        totalVector[2][3] = `${((ecgJson.s_end - ecgJson.q_begin) * 1000) / ecgJson.fs}ms`
        totalVector[3][1] = `${((ecgJson.s_end - ecgJson.q_begin) * 1000) / ecgJson.fs}ms`
        totalVector[3][3] = `${qBegin_sEnd_max}mv`
        const QT = ((ecgJson.t_end - ecgJson.q_begin) * 1000) / ecgJson.fs
        totalVector[4][1] = `${QT}/${QT + 1.75 * (ecgJson.hr - 60)}ms`
        totalVector[4][3] = `${((ecgJson.t_end - ecgJson.s_end) * 1000) / ecgJson.fs}ms`
        totalVector[5][1] = `${((ecgJson.p_end - ecgJson.p_begin) * 1000) / ecgJson.fs}ms`
        totalVector[5][3] = `${sEnd_tEnd_max}mv`

        return totalVector
      },
      /**
       * @method sectional_measure 参数绘制
       * @param ecgJson
       * @param ecgParme
       * @param out_rect
       * @param vector_ori
       * @returns {*[]}
       */
      sectional_measure(ecgJson, ecgParme, out_rect, vector_ori, axisesName) {
        const sectionalVector = [
          ['', '额面', '', '', '横面', '', '', '右侧面', '', ''],
          ['', 'P环', 'QRS环', 'ST-T环', 'P环', 'QRS环', 'ST-T环', 'P环', 'QRS环', 'ST-T环'],
          ['运动方向:', '8字型', '顺时针:', '逆时针', '8字型', '顺时针:', '逆时针', '8字型', '顺时针:', '逆时针'],
          [
            '最大向量(mv/°):',
            '0.048/-173',
            '0.048/-173',
            '0.048/-173',
            '0.048/-173',
            '0.048/-173',
            '0.048/-173',
            '0.048/-173',
            '0.048/-173',
            '0.048/-173'
          ]
        ]
        const crossesVector = this.draw_ecg_crosses_data_ori(ecgJson, axisesName)
        for (let idxs = 0; idxs < 3; idxs++) {
          // P环
          let moveDirection_p = ''
          const pBegin_pEnd = vector_ori.rVector[0].filter((item, index) => {
            return index >= ecgJson.p_begin && index <= ecgJson.p_end
          })
          // P环运动方向
          const flag_p = crossesVector[idxs].some((item, index) => {
            return item[0] >= ecgJson.p_begin && item[1] <= ecgJson.p_end
          })
          const pBegin_pEnd_count = pBegin_pEnd.reduce(zqCommon.count, 0)
          if (flag_p) {
            moveDirection_p = '8字型'
          } else {
            pBegin_pEnd_count > 0 ? (moveDirection_p = '顺时针') : (moveDirection_p = '逆时针')
          }
          sectionalVector[2][idxs * 3 + 1] = moveDirection_p
          // P环最大向量
          const pBegin_pEnd_max = pBegin_pEnd.reduce(zqCommon.getMax, 0)
          let pBegin_pEnd_theta = ''
          pBegin_pEnd.filter((item, index) => {
            if (item == pBegin_pEnd_max) {
              pBegin_pEnd_theta = vector_ori.thetaVector[[idxs]][index + ecgJson.p_begin]
            }
          })
          sectionalVector[3][idxs * 3 + 1] = `${pBegin_pEnd_max}/${pBegin_pEnd_theta}`

          // QRS环
          let moveDirection_qrs = ''
          const qBegin_sEnd = vector_ori.rVector[0].filter((item, index) => {
            return index >= ecgJson.q_begin && index <= ecgJson.s_end
          })
          // QRS环运动方向
          const flag_qrs = crossesVector[idxs].some((item, index) => {
            return item[0] >= ecgJson.q_begin && item[1] <= ecgJson.s_end
          })
          const qBegin_sEnd_count = qBegin_sEnd.reduce(zqCommon.count, 0)
          if (flag_qrs) {
            moveDirection_qrs = '8字型'
          } else {
            qBegin_sEnd_count > 0 ? (moveDirection_qrs = '顺时针') : (moveDirection_qrs = '逆时针')
          }
          sectionalVector[2][idxs * 3 + 2] = moveDirection_qrs
          // QRS环最大向量
          const qBegin_sEnd_max = qBegin_sEnd.reduce(zqCommon.getMax, 0)
          let qBegin_sEnd_theta = ''
          qBegin_sEnd.filter((item, index) => {
            if (item == qBegin_sEnd_max) {
              qBegin_sEnd_theta = vector_ori.thetaVector[[idxs]][index + ecgJson.q_begin]
            }
          })
          sectionalVector[3][idxs * 3 + 2] = `${qBegin_sEnd_max}/${qBegin_sEnd_theta}`

          // ST-T环
          let moveDirection_stt = ''
          const sEnd_tEnd = vector_ori.rVector[0].filter((item, index) => {
            return index >= ecgJson.s_end && index <= ecgJson.t_end
          })
          // ST-T环运动方向
          const flag_stt = crossesVector[idxs].some((item, index) => {
            return item[0] >= ecgJson.s_end && item[1] <= ecgJson.t_end
          })
          const sEnd_tEnd_count = sEnd_tEnd.reduce(zqCommon.count, 0)
          if (flag_stt) {
            moveDirection_stt = '8字型'
          } else {
            sEnd_tEnd_count > 0 ? (moveDirection_stt = '顺时针') : (moveDirection_stt = '逆时针')
          }
          sectionalVector[2][idxs * 3 + 2] = moveDirection_stt
          // ST-T环最大向量
          const sEnd_tEnd_max = sEnd_tEnd.reduce(zqCommon.getMax, 0)
          let sEnd_tEnd_theta = ''
          sEnd_tEnd.filter((item, index) => {
            if (item == sEnd_tEnd_max) {
              sEnd_tEnd_theta = vector_ori.thetaVector[[idxs]][index + ecgJson.s_end]
            }
          })
          sectionalVector[3][idxs * 3 + 3] = `${sEnd_tEnd_max}/${sEnd_tEnd_theta}`
        }
        return sectionalVector
      },
      /**
       * @method draw_ecg_crosses_data_ori 参数数据转换
       * @param ecgJson
       * @param axisesName
       * @returns {Array}
       */
      draw_ecg_crosses_data_ori(ecgJson, axisesName) {
        const vector_ori = []
        for (let idxs = 0; idxs < axisesName.length; idxs++) {
          const plans = ecgJson.crosses[axisesName[idxs]]
          vector_ori[idxs] = []
          for (let idx = 0; idx < ecgJson.xyzEachLen; idx++) {
            vector_ori[idxs].push(plans.slice(idx * 2, (idx + 1) * 2))
          }
        }
        return vector_ori
      },
      /**
       * @method draw_ecg_sectional_measure_text 文字绘制封装
       * @param svg_ecg
       * @param vector
       * @param measureLeft
       * @param measureTop
       * @param flag
       */
      draw_ecg_sectional_measure_text(svg_ecg, vector, measureLeft, measureTop, flag) {
        var widget = document.createElementNS('http://www.w3.org/2000/svg', 'text')
        widget.setAttribute('x', measureLeft)
        widget.setAttribute('y', measureTop)
        widget.setAttribute('text-anchor', 'start')
        widget.setAttribute('dominant-baseline', 'auto')
        widget.setAttribute('style', 'font-size:10px;')
        svg_ecg.appendChild(widget)

        for (let i = 0; i < vector.length; i++) {
          let offsetX = measureLeft
          let offsetY = measureTop
          for (let j = 0; j < vector[i].length; j++) {
            var tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan')
            if (flag == 1) {
              if (j == 0) {
                offsetX == 0
              } else if (j == 1) {
                offsetX += 50
              } else if (j == 2) {
                offsetX += 150
              } else {
                offsetX += 100
              }
              offsetY = measureTop + i * 20
            } else if (flag == 2) {
              j == 0 ? offsetX == 0 : (offsetX += 70)
              offsetY = measureTop + i * 20
            } else if (flag == 3) {
              j == 0 ? offsetX == 0 : (offsetX += 70)
              offsetY = measureTop + i * 20
            } else if (flag == 4) {
              j == 0 ? offsetX == 0 : (offsetX += 100)
              i == 0 ? (offsetY = measureTop + 5) : (offsetY = measureTop + i * 20)
            } else if (flag == 5) {
              // j == 0 ? offsetX == 0 : offsetX = offsetX  + 48
              if (j == 0) {
                offsetX == 0
              } else if (j == 1) {
                offsetX += 70
              } else {
                offsetX += 46
              }
              i == 0 ? (offsetY = measureTop + 5) : (offsetY = measureTop + i * 20)
            }
            tspan.setAttribute('x', offsetX)
            tspan.setAttribute('y', offsetY)
            var textString = document.createTextNode(vector[i][j])
            tspan.appendChild(textString)
            widget.appendChild(tspan)
          }
        }
      },
      /**
       * @method  clearEcgWave  svg清除函数
       * @param svg_elem_id
       */
      clearEcgChild(svg_elem_id) {
        // 单心拍图
        var single_heart_beat = document.getElementById(`single_heart_beat_${svg_elem_id}`)
        if (single_heart_beat != null) {
          single_heart_beat.parentNode.removeChild(single_heart_beat)
        }
        // QRS-T T P环图
        const sectional = document.getElementById(`sectional_${svg_elem_id}`)
        if (sectional != null) {
          sectional.parentNode.removeChild(sectional)
        }
        // 测量值
        var sectional_measure = document.getElementById(`sectional_measure_${svg_elem_id}`)
        if (sectional_measure != null) {
          sectional_measure.parentNode.removeChild(sectional_measure)
        }
      }
    }
    window.zqVector = window.zqVector || new zqVector()
    /**
     * 模块说明
     * @module  zqVlp	 心室晚电位封装
     * @rely  zqSvg zqCommon
     */
    function zqVlp() {
      this.topY = 0
      this.waveTopY = 0
      this.division = 4
      this.out_rect = null
      this.userInfoSpace = 20

      this.topHeight = 0
      this.sampling = 1000
      this.time_domain_const = 82
      this.frequency_domain_length = 250
      this.stepLength = 2
      this.stepCount = 25
      this.waveGain = 20
      this.waveSpeed = 50
      this.timeDomainGain = 200
      this.timeDomainSpeed = 100

      // 拖拽
      this.mousedown = false
      this.mouseX = 0
      this.offset_x = 0

      this.scaleFontSize = '12px'
    }
    zqVlp.prototype = {
      /**
       * @method  draw_ecg_rhythm  心室晚电位绘制入口函数
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_ecg_vlp(svg_elem_id, ecgJson, vlpJson, ecgParme, isPrint, gridWidth, gridHeight) {
        this.topHeight = gridHeight / 2
        this.clearEcgWave(svg_elem_id)
        var svg_ecg = document.getElementById(svg_elem_id)
        this.creatEcgChild(svg_ecg, svg_elem_id)
        svg_ecg.setAttribute('width', gridWidth)
        svg_ecg.setAttribute('height', gridHeight)
        this.draw_lead(svg_ecg, svg_elem_id, ecgJson, vlpJson, ecgParme, isPrint, gridWidth, gridHeight)
      },
      /**
       * @method  creatEcgChild  svg内部组件创建
       * @param svg_ecg
       * @param svg_elem_id
       */
      creatEcgChild(svg_ecg, svg_elem_id) {
        zqSvg.g(svg_ecg, `print_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `userInfo_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `wave_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `time_domain_${svg_elem_id}`)
        zqSvg.g(svg_ecg, `frequency_domain_${svg_elem_id}`)
      },
      /**
       * @method  draw_lead  心室晚电位绘制入口函数
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_lead(svg_ecg, svg_elem_id, ecgJson, vlpJson, ecgParme, isPrint, gridWidth, gridHeight) {
        var rect = { left: 0, right: 0, top: 0, bottom: 0 }
        if (isPrint == 1) {
          // rect = {"left":15,"right":15,"top": 5 * ecgParme.pix_mm,"bottom":20};
          // let print = document.getElementById("print_" + svg_elem_id);
          // zqSvg.text(print, "text_" + svg_elem_id,
          //     gridWidth / 2, 20,
          //     "font-size:18px", "middle", "Hanging", "心室晚电位心电报告"
          // )
          rect = { left: 30, right: 10, top: 60, bottom: 30 }
          this.print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, rect)
        }
        this.out_rect = zqSvg.painter_rect(svg_ecg, ecgParme.pix_mm, rect, gridWidth, gridHeight)
        zqSvg.painter_grid(svg_ecg, svg_elem_id, ecgParme, this.out_rect, gridWidth, gridHeight)
        this.draw_ecg(svg_ecg, svg_elem_id, ecgJson, vlpJson, ecgParme, this.out_rect, gridWidth, gridHeight)
      },
      /**
       * @method  print  打印内容
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson
       * @param ecgParme
       */
      print(svg_ecg, svg_elem_id, ecgJson, ecgParme, gridWidth, gridHeight, rect) {
        const print = document.getElementById(`print_${svg_elem_id}`)
        let { title1 } = paramset.report
        if (title1 == '') {
          title1 = ecgJson.checkSection
        }
        const { title2 } = paramset.report
        // if(title2 == ''){
        //     title2 = ecgJson['checkDept']
        // }

        const title1_y = 20
        const title2_y = 40
        let line_y = 7 * ecgParme.pix_mm
        if (ecgParme.previewEcg) {
          line_y = 7 * ecgParme.pix_mm + 10
        }

        zqSvg.text(print, `text_${svg_elem_id}`, gridWidth / 2, title1_y, 'font-size:18px', 'middle', 'Hanging', title1)
        zqSvg.text(print, `text_${svg_elem_id}`, gridWidth / 2, title2_y, 'font-size:14px', 'middle', 'Hanging', title2)
        zqSvg.line(
          print,
          ecgParme.pix_mm * 5,
          gridWidth - ecgParme.pix_mm * 2,
          line_y,
          line_y,
          `line_${svg_elem_id}`,
          'stroke-width:1;stroke:#000000'
        )
        const rightDown = zqSvg.getPrintTimeName(paramset.report.rightDown)
        const rightUp = zqSvg.getPrintTimeName(paramset.report.rightUp)

        if (paramset.report.rightUp == 'reviewTime' || paramset.report.rightUp == 'diagnoseTime') {
          zqCommon.getServerSystemTime(
            'up',
            print,
            svg_elem_id,
            gridWidth,
            rightUp,
            rightDown,
            rect,
            paramset.report.rightUp,
            ecgJson.id
          )
        } else if (ecgJson[paramset.report.rightUp] != null) {
          zqSvg.text(
            print,
            `text_${svg_elem_id}`,
            gridWidth - rect.right,
            title1_y,
            'font-size:14px',
            'end',
            'Hanging',
            rightUp + ecgJson[paramset.report.rightUp]
          )
        }
        if (paramset.report.rightDown == 'reviewTime' || paramset.report.rightDown == 'diagnoseTime') {
          zqCommon.getServerSystemTime(
            'down',
            print,
            svg_elem_id,
            gridWidth,
            rightUp,
            rightDown,
            rect,
            paramset.report.rightDown,
            ecgJson.id
          )
        } else if (ecgJson[paramset.report.rightDown] != null) {
          zqSvg.text(
            print,
            `text_${svg_elem_id}`,
            gridWidth - rect.right,
            title2_y,
            'font-size:14px',
            'end',
            'Hanging',
            rightDown + ecgJson[paramset.report.rightDown]
          )
        }
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          title1_y,
          'font-size:14px',
          'start',
          'Hanging',
          '心室晚电位心电报告'
        )
        zqSvg.text(
          print,
          `text_${svg_elem_id}`,
          rect.left,
          gridHeight - 15,
          'font-size:12px',
          'start',
          'Hanging',
          '此报告仅供临床参考，不做任何证明材料用'
        )
      },
      /**
       * @method  draw_ecg  心室晚电位波形绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param out_rect svg波形可绘制区域上下左右距离
       * @param isPrint 是否打印
       * @param gridWidth svg宽
       * @param gridHeight svg高
       */
      draw_ecg(svg_ecg, svg_elem_id, ecgJson, vlpJson, ecgParme, out_rect, gridWidth, gridHeight) {
        // 患者信息绘制
        this.painter_userInfo_all(svg_elem_id, ecgJson, vlpJson, ecgParme, gridWidth, gridHeight)

        // 波形绘制
        this.draw_wave(svg_ecg, svg_elem_id, ecgJson, vlpJson, ecgParme, out_rect, gridWidth, gridHeight)
        // 时域绘制
        this.draw_time_domain(svg_ecg, svg_elem_id, ecgJson, vlpJson, ecgParme, out_rect, gridWidth, gridHeight)

        // 频域绘制
        this.draw_frequency_domain(svg_elem_id, vlpJson, ecgParme, out_rect, gridWidth, gridHeight)
      },
      /**
       * @method  painter_userInfo_all  患者信息绘制
       * @param svg_elem_id
       * @param ecgJson
       * @param ecgParme
       * @param gridWidth
       * @param gridHeight
       */
      painter_userInfo_all(svg_elem_id, ecgJson, vlpJson, ecgParme, gridWidth, gridHeight) {
        const userInfo = document.getElementById(`userInfo_${svg_elem_id}`)
        if (userInfo != null) {
          userInfo.parentNode.removeChild(userInfo)
        }
        var svg_ecg = document.getElementById(svg_elem_id)
        zqSvg.g(svg_ecg, `userInfo_${svg_elem_id}`)
        const userInfo1 = [
          ['编号：', ecgJson.pid],
          ['姓名：', ecgJson.name != null ? ecgJson.name : ''],
          ['性别：', zqCommon.switchSex(ecgJson.sex)],
          ['年龄：', ecgJson.age + zqCommon.switchAgeUnit(ecgJson.ageUnit)],
          ['科室：', ecgJson.checkDept],
          ['床号：', ecgJson.bedNum != null ? ecgJson.bedNum : '']
        ]
        const axises = vlpJson.superimpose
        const sEnd = vlpJson.sEnd - this.time_domain_const
        const sStart = vlpJson.sEnd - this.time_domain_const - 40
        const sStart_sEnd = axises.filter((item, index) => {
          return index >= sStart && index <= sEnd
        })
        const sStart_sEnd_count_arr = sStart_sEnd.filter((item, index) => {
          return item < 40
        })
        const sStart_sEnd_max = sStart_sEnd.reduce(zqCommon.getMax, 0)
        const userInfo2 = [
          ['总心博数：', vlpJson.beatCount],
          ['叠加心拍数：', vlpJson.usedBeatCount],
          ['QRS时限：', `${vlpJson.qrsTime}ms`],
          ['滤波后QRS总时限：', `${vlpJson.filterQrsTime}ms`],
          ['低于40uV持续时间：', `${sStart_sEnd_count_arr.length}ms`],
          ['终末40ms内振幅：', `${(sStart_sEnd_max / 1000).toFixed(3)}mv`],
          ['噪声电平：', `${vlpJson.noiseLevel.toFixed(3)}uv`]
        ]
        this.painter_userInfo(svg_elem_id, ecgJson, vlpJson, ecgParme, gridWidth, gridHeight, userInfo1, 0, 0)
        this.painter_userInfo(
          svg_elem_id,
          ecgJson,
          vlpJson,
          ecgParme,
          gridWidth,
          gridHeight,
          userInfo2,
          15 * 5 * ecgParme.pix_mm,
          1
        )
        // this.painter_result(svg_elem_id, ecgJson, vlpJson, ecgParme, gridWidth, gridHeight, 15 * 5 * ecgParme.pix_mm);
      },
      /**
       * @method  painter_userInfo  参数绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param gridWidth svg宽
       * @param gridHeight svg高
       * @param userInfo 参数
       * @param topY 距离顶部距离
       */
      painter_userInfo(svg_elem_id, ecgJson, vlpJson, ecgParme, gridWidth, gridHeight, userInfo, offsetLeft, flag) {
        const userInfoWidget = document.getElementById(`userInfo_${svg_elem_id}`)
        var widget = document.createElementNS('http://www.w3.org/2000/svg', 'text')
        widget.setAttribute('x', gridWidth)
        widget.setAttribute('y', 5 * ecgParme.pix_mm) // this.out_rect.top
        widget.setAttribute('text-anchor', 'start')
        widget.setAttribute('dominant-baseline', 'auto')
        widget.setAttribute('style', 'font-size:12px;')
        userInfoWidget.appendChild(widget)
        for (let i = 0; i < userInfo.length; i++) {
          const offsetPer = gridWidth / userInfo.length
          for (let j = 0; j < userInfo[i].length; j++) {
            var tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan')
            let offsetX = j == 0 ? 0 : 80
            if (flag == 0) {
              offsetX = j == 0 ? 0 : 80
            } else if (flag == 1) {
              offsetX = j == 0 ? 0 : 150
            }

            const offsetY = this.out_rect.top + i * this.userInfoSpace
            tspan.setAttribute('x', offsetX + 10 * ecgParme.pix_mm + this.out_rect.left + offsetLeft)
            tspan.setAttribute('y', offsetY + 5 * ecgParme.pix_mm)
            var textString = document.createTextNode(userInfo[i][j])
            tspan.appendChild(textString)
            widget.appendChild(tspan)
          }
        }
      },
      /**
       * @method  painter_result  诊断结论绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgJson 波形数据
       * @param ecgParme 除波形之外的其他变量参数
       * @param gridWidth svg宽
       * @param gridHeight svg高
       * @param userInfo 参数
       * @param topY 距离顶部距离
       */
      painter_result(svg_elem_id, ecgJson, vlpJson, ecgParme, gridWidth, gridHeight, topX) {
        ecgJson.advice = 'testtest'
        const userInfoWidget = document.getElementById(`userInfo_${svg_elem_id}`)
        var widget = document.createElementNS('http://www.w3.org/2000/svg', 'text')
        widget.setAttribute('x', topX)
        widget.setAttribute('y', this.out_rect.top + 8 * 5 * ecgParme.pix_mm)
        widget.setAttribute('text-anchor', 'start')
        widget.setAttribute('dominant-baseline', 'Hanging')
        widget.setAttribute('style', 'font-size:12px;')
        userInfoWidget.appendChild(widget)
        var advice = ecgJson.advice.split('\n')
        advice.unshift('诊断结论:')
        for (let idx = 0; idx < advice.length; idx++) {
          var tspan = document.createElementNS('http://www.w3.org/2000/svg', 'tspan')
          const offsetX = idx == 0 ? topX : 150
          const offsetY = this.out_rect.top + 8 * 5 * ecgParme.pix_mm + idx * 30
          tspan.setAttribute('x', topX + 10 * ecgParme.pix_mm + this.out_rect.left)
          tspan.setAttribute('y', offsetY + 5 * ecgParme.pix_mm)
          var textString = document.createTextNode(advice[idx])
          tspan.appendChild(textString)
          widget.appendChild(tspan)
        }
      },
      /**
       * @method  painter_gain  定标电压绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param ecgParme
       * @param px 定标电压x坐标
       * @param py 定标电压y坐标
       */
      painter_gain(svg_ecg, svg_elem_id, ecgParme, px, py) {
        var gainpolylineDate = zqSvg.set_gain_point(ecgParme.pix_mm, px, py, this.waveGain, this.waveSpeed)
        zqSvg.polyline(
          svg_ecg,
          `gainpolyline_${svg_elem_id}`,
          gainpolylineDate,
          `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
        )
      },
      /**
       * @method  draw_wave  波形绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param rect
       * @param ecgParme
       * @param ecgJson
       * @param gridWidth
       * @param gridHeight
       */
      draw_wave(svg_ecg, svg_elem_id, ecgJson, vlpJson, ecgParme, out_rect, gridWidth, gridHeight) {
        const widget = document.getElementById(`wave_${svg_elem_id}`)
        const len = 3
        const per = (ecgParme.gridHeight - out_rect.top - out_rect.bottom - this.topHeight) / len
        const axisesName = ['X', 'Y', 'Z']
        // Common.text(widget,"text_" + svg_elem_id, out_rect["left"] + ecgParme.speed / 5 * ecgParme.pix_mm + ecgParme.pix_mm * 2, out_rect["top"], "", "start", "Hanging", ecgParme.waveSpeed+"mm/s "+ecgParme.waveGain+"mm/mv")
        for (let idx = 0; idx < len; idx++) {
          // let widget = document.createElementNS( "http://www.w3.org/2000/svg", "g" );
          // widget.appendChild(widget)
          const gain = {}
          gain.gain_left = out_rect.left
          gain.gain_top = out_rect.top + per * idx + this.topHeight
          gain.gain_right = out_rect.left + (this.waveSpeed / 5) * ecgParme.pix_mm + ecgParme.pix_mm * 2
          gain.gain_bottom = gain.gain_top + (this.waveSpeed / 5) * ecgParme.pix_mm + ecgParme.pix_mm
          // 定标电压绘制
          if (idx == 1) this.painter_gain(svg_ecg, svg_elem_id, ecgParme, gain.gain_left, gain.gain_top)
          // 定标电压绘制
          // Common.painter_gain_class(widget, svg_elem_id, ecgParme.pix_mm, 1, gain.gain_left, gain.gain_top,ecgParme.waveGain,ecgParme.waveSpeed);
          const vector = this.wave_data(widget, svg_elem_id, vlpJson, ecgParme, out_rect, gain)
          // 单心拍绘制
          zqSvg.polyline(
            widget,
            `polyline_${svg_elem_id}`,
            vector[idx],
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
          // 虚竖线绘制
          zqSvg.line(
            widget,
            gain.gain_right,
            gain.gain_right + 1,
            gain.gain_bottom - ecgParme.pix_mm * 5,
            gain.gain_bottom + ecgParme.pix_mm * 5,
            `line_${svg_elem_id}`,
            `stroke:${ecgParme.waveColor}`,
            '2 2'
          )
          // 导联名称绘制
          zqSvg.text(
            widget,
            `text_${svg_elem_id}`,
            gain.gain_right,
            gain.gain_bottom - ecgParme.pix_mm * 8,
            '',
            'start',
            'Middle',
            axisesName[idx]
          )
        }
        zqSvg.text(
          widget,
          `text_${svg_elem_id}`,
          out_rect.left + (this.waveSpeed / 5) * ecgParme.pix_mm + ecgParme.pix_mm * 2,
          gridHeight - out_rect.bottom - 5 * ecgParme.pix_mm,
          'font-size:10px',
          'start',
          'auto',
          `0Hz-500Hz ${this.waveSpeed}mm/s ${this.waveGain}mm/mv`
        )
      },
      /**
       * @method  painter_12_dataRh  波形数据转换
       * @param rect
       * @param ecgParme
       * @param data
       * @param gridWidth
       * @param gridHeight
       * @returns {Array}
       */
      wave_data(svg_ecg, svg_elem_id, vlpJson, ecgParme, out_rect, gain) {
        const waveAxisesVector = [vlpJson.averageBeatX, vlpJson.averageBeatY, vlpJson.averageBeatZ]
        const vector = []
        for (let idx = 0; idx < waveAxisesVector.length; idx++) {
          vector[idx] = []
          const axises = waveAxisesVector[idx]
          for (let array_idx = 0; array_idx < axises.length; array_idx++) {
            let p_x = (array_idx * this.waveSpeed * ecgParme.pix_mm) / this.sampling
            let p_y = axises[array_idx]
            p_y = (-1 * p_y * this.waveGain * ecgParme.pix_mm) / this.sampling
            p_x += gain.gain_right
            p_y += gain.gain_bottom
            vector[idx].push(p_x)
            vector[idx].push(p_y)
          }
        }
        return vector
      },
      /**
       * @method  draw_time_domain  时域绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param rect
       * @param ecgParme
       * @param ecgJson
       * @param gridWidth
       * @param gridHeight
       */
      draw_time_domain(svg_ecg, svg_elem_id, ecgJson, vlpJson, ecgParme, out_rect, gridWidth, gridHeight) {
        const widget = document.getElementById(`time_domain_${svg_elem_id}`)
        const _this = this
        const len = 3
        const per = (ecgParme.gridHeight - out_rect.top - out_rect.bottom - this.topHeight) / len
        const gain = {}
        gain.gain_left = out_rect.left + gridWidth / 4
        gain.gain_top = out_rect.top + per * 2 + this.topHeight
        gain.gain_right = out_rect.left + gridWidth / 4
        gain.gain_bottom = gain.gain_top + (this.waveSpeed / 5) * ecgParme.pix_mm + ecgParme.pix_mm
        const vector = this.draw_time_data(vlpJson, ecgParme, gain)
        const fillVector = this.draw_time_fill_data(vlpJson, ecgParme, gain)
        zqSvg.polyline(
          widget,
          `polyline_${svg_elem_id}`,
          vector,
          `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
        )
        zqSvg.polyline(
          widget,
          `polyline_${svg_elem_id}`,
          fillVector,
          `stroke:transparent;fill:${ecgParme.waveColor}`,
          `sEnd_polyline_${svg_elem_id}`
        )
        zqSvg.line(
          widget,
          fillVector[fillVector.length - 2],
          fillVector[fillVector.length - 2],
          gain.gain_bottom - 10 * ecgParme.pix_mm,
          gain.gain_bottom + 5 * ecgParme.pix_mm,
          `line_${svg_elem_id}`,
          `stroke-width:1;stroke:${ecgParme.waveColor}`,
          '',
          '',
          `sEnd_line_${svg_elem_id}`
        )
        zqSvg.text(
          widget,
          `text_${svg_elem_id}`,
          fillVector[fillVector.length - 2],
          gain.gain_bottom + 5 * ecgParme.pix_mm,
          'cursor:pointer',
          'middle',
          'Hanging',
          'sEnd',
          '',
          `sEnd_text_${svg_elem_id}`
        )
        var sEnd_text_element = document.getElementById(`sEnd_text_${svg_elem_id}`)
        var sEnd_line_element = document.getElementById(`sEnd_line_${svg_elem_id}`)
        sEnd_text_element.onmousedown = function (e) {
          var e = e || event
          _this.vlpMousedown(e, sEnd_text_element, sEnd_line_element)
        }
        sEnd_text_element.onmousemove = function (e) {
          var e = e || event
          _this.vlpMousemove(e, sEnd_text_element, sEnd_line_element)
        }
        sEnd_text_element.onmouseup = function (e) {
          var e = e || event
          _this.vlpMouseup(
            e,
            sEnd_text_element,
            sEnd_line_element,
            svg_elem_id,
            widget,
            vlpJson,
            ecgParme,
            gain,
            svg_ecg,
            out_rect,
            gridWidth,
            gridHeight
          )
        }
        // 名称绘制
        zqSvg.text(
          widget,
          `text_${svg_elem_id}`,
          gridWidth / 2,
          gain.gain_bottom - 20 * ecgParme.pix_mm,
          'font-size:12px',
          'start',
          'Hanging',
          '晚电位时域分析图'
        )
        zqSvg.text(widget, `text_${svg_elem_id}`, gridWidth / 2, gain.gain_bottom, '', 'start', 'Hanging', '___0uV')
        zqSvg.text(
          widget,
          `text_${svg_elem_id}`,
          gridWidth / 2,
          gain.gain_bottom - 8 * ecgParme.pix_mm,
          '',
          'start',
          'Hanging',
          '___40uV'
        )
        zqSvg.text(
          widget,
          `text_${svg_elem_id}`,
          gain.gain_left,
          gridHeight - out_rect.bottom - 5 * ecgParme.pix_mm,
          'font-size:10px',
          'start',
          'auto',
          `40Hz-500Hz ${this.timeDomainSpeed}mm/s ${this.timeDomainGain}mm/mv`
        )
      },
      /**
       * @method  draw_time_data  时域数据转换
       * @param vlpJson
       * @param ecgParme
       * @param gain
       * @returns {{vector: Array, fillVector: Array}}
       */
      draw_time_data(vlpJson, ecgParme, gain) {
        const vector = []
        const axises = vlpJson.superimpose
        for (let array_idx = 0; array_idx < axises.length; array_idx++) {
          let p_x = (array_idx * this.timeDomainSpeed * ecgParme.pix_mm) / this.sampling
          let p_y = axises[array_idx]
          p_y = (-1 * p_y * this.timeDomainGain * ecgParme.pix_mm) / this.sampling
          p_x += gain.gain_right
          p_y += gain.gain_bottom
          vector.push(p_x)
          vector.push(p_y)
        }
        return vector
      },
      /**
       * @method  draw_time_fill_data  时域数据转换
       * @param vlpJson
       * @param ecgParme
       * @param out_rect
       * @param gain
       * @returns {{vector: Array, fillVector: Array}}
       */
      draw_time_fill_data(vlpJson, ecgParme, gain) {
        const fillVector = []
        const sEnd = vlpJson.sEnd - this.time_domain_const - this.mouseX
        const sStart = vlpJson.sEnd - this.time_domain_const - 40 - this.mouseX
        const axises = vlpJson.superimpose
        fillVector.push((sStart * this.timeDomainSpeed * ecgParme.pix_mm) / this.sampling + gain.gain_right)
        fillVector.push(gain.gain_bottom)
        for (let array_idx = 0; array_idx < axises.length; array_idx++) {
          if (array_idx >= sStart && array_idx <= sEnd) {
            let p_x_fill = (array_idx * this.timeDomainSpeed * ecgParme.pix_mm) / this.sampling
            let p_y_fill = axises[array_idx]
            p_y_fill = (-1 * p_y_fill * this.timeDomainGain * ecgParme.pix_mm) / this.sampling
            p_x_fill += gain.gain_right
            p_y_fill += gain.gain_bottom
            fillVector.push(p_x_fill)
            fillVector.push(p_y_fill)
          }
        }
        fillVector.push((sEnd * this.timeDomainSpeed * ecgParme.pix_mm) / this.sampling + gain.gain_right)
        fillVector.push(gain.gain_bottom)
        return fillVector
      },
      /**
       * @method  draw_frequency_domain  频域绘制
       * @param svg_ecg
       * @param svg_elem_id
       * @param vlpJson
       * @param ecgParme
       * @param out_rect
       * @param gridWidth
       * @param gridHeight
       */
      draw_frequency_domain(svg_elem_id, vlpJson, ecgParme, out_rect, gridWidth, gridHeight) {
        const widget = document.getElementById(`frequency_domain_${svg_elem_id}`)
        const position = {}
        position.diff = 4 * 5 * ecgParme.pix_mm
        position.horizontalScale = 25
        position.verticalScale = 45
        position.obliqueScale = (this.stepCount * this.stepLength) / 10
        position.offsetLeft = gridWidth / 2 + 35 * ecgParme.pix_mm
        position.offsetTop = out_rect.top
        position.width = ((gridWidth - position.offsetLeft) / 3) * 2
        // position.obliqueWidth = (gridWidth - position.offsetLeft ) / 3;
        position.height = (gridHeight - out_rect.top - out_rect.bottom - position.diff * 3) / 3.3
        position.obliqueWidth = position.height
        position.obliqueSpectrumScale = position.obliqueWidth / this.stepCount
        const title = ['初始窗起点:QRS终点前0ms', `步数:${this.stepCount}`, `步长:${this.stepLength}ms`]
        const caption = ['X导联晚电位三维频谱标测图', 'Y导联晚电位三维频谱标测图', 'Z导联晚电位三维频谱标测图']

        for (let idx = 0; idx < title.length; idx++) {
          // zqSvg.text(widget, "text_" + svg_elem_id, position.offsetLeft, position.offsetTop + idx * 5 * ecgParme.pix_mm + 5 *  ecgParme.pix_mm, "font-size:12px", "start", "Hanging", title[idx])
          zqSvg.text(
            widget,
            `text_${svg_elem_id}`,
            gridWidth - out_rect.right,
            position.offsetTop + idx * 5 * ecgParme.pix_mm + 3 * ecgParme.pix_mm,
            `font-size:${this.scaleFontSize}`,
            'end',
            'Hanging',
            title[idx]
          )

          // 横轴
          zqSvg.line(
            widget,
            position.offsetLeft,
            position.offsetLeft + position.width,
            (position.height + position.diff) * (idx + 1) + position.offsetTop,
            (position.height + position.diff) * (idx + 1) + position.offsetTop,
            `line_${svg_elem_id}`,
            `stroke-width:1;stroke:${ecgParme.waveColor}`
          )

          // 纵轴
          zqSvg.line(
            widget,
            position.offsetLeft,
            position.offsetLeft,
            position.diff * (idx + 1) + position.offsetTop + position.height * idx,
            position.diff * (idx + 1) + position.offsetTop + position.height * (idx + 1),
            `line_${svg_elem_id}`,
            `stroke-width:1;stroke:${ecgParme.waveColor}`
          )

          // 斜轴
          zqSvg.line(
            widget,
            position.offsetLeft,
            position.offsetLeft + position.obliqueWidth,
            (position.height + position.diff) * (idx + 1) + position.offsetTop,
            (position.height + position.diff) * (idx + 1) + position.offsetTop,
            `line_${svg_elem_id}`,
            `stroke-width:1;stroke:${ecgParme.waveColor}`,
            '',
            `rotate(${-45} ${position.offsetLeft} ${
              (position.height + position.diff) * (idx + 1) + position.offsetTop
            })`
          )
          const gWidget = document.createElementNS('http://www.w3.org/2000/svg', 'g')
          widget.appendChild(gWidget)
          gWidget.setAttribute(
            'transform',
            `rotate(${45} ${position.offsetLeft + position.width} ${
              (position.height + position.diff) * (idx + 1) + position.offsetTop
            })`
          )
          zqSvg.line(
            gWidget,
            position.offsetLeft + position.width,
            position.offsetLeft + position.width,
            (position.height + position.diff) * (idx + 1) + position.offsetTop - position.obliqueWidth,
            (position.height + position.diff) * (idx + 1) + position.offsetTop,
            `line_${svg_elem_id}`,
            `stroke-width:1;stroke:${ecgParme.waveColor}`
          )
          let obliqueX = ''
          for (let i = 0; i <= position.obliqueScale; i++) {
            const scale = i == 0 ? `${i * 10}(ms)` : i * 10
            obliqueX += `M${position.offsetLeft + position.width} ${
              (position.height + position.diff) * (idx + 1) +
              position.offsetTop -
              position.obliqueWidth +
              (position.obliqueWidth / position.obliqueScale) * i
            } h ${ecgParme.pix_mm * 2} `
            zqSvg.text(
              gWidget,
              `text_${svg_elem_id}`,
              position.offsetLeft + position.width,
              (position.height + position.diff) * (idx + 1) +
                position.offsetTop -
                position.obliqueWidth +
                (position.obliqueWidth / position.obliqueScale) * i,
              `font-size:${this.scaleFontSize}`,
              'start',
              'auto',
              scale
            )
          }
          zqSvg.path(
            gWidget,
            `path_${svg_elem_id}`,
            obliqueX,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:1`
          )

          // 横轴刻度
          let horizontalX = ''
          let verticalX = ''
          for (let i = 0; i <= position.horizontalScale; i++) {
            if (i % 5 == 0) {
              const scale = i == position.horizontalScale ? `${i * 10}(Hz)` : i * 10
              horizontalX += `M${position.offsetLeft + (position.width / position.horizontalScale) * i} ${
                position.diff * (idx + 1) + position.offsetTop + position.height * (idx + 1)
              } v ${ecgParme.pix_mm * 2} `
              zqSvg.text(
                widget,
                `text_${svg_elem_id}`,
                position.offsetLeft + (position.width / position.horizontalScale) * i,
                position.diff * (idx + 1) + position.offsetTop + position.height * (idx + 1) + ecgParme.pix_mm * 4,
                `font-size:${this.scaleFontSize}`,
                'middle',
                'Hanging',
                scale
              )
            } else {
              horizontalX += `M${position.offsetLeft + (position.width / position.horizontalScale) * i} ${
                position.diff * (idx + 1) + position.offsetTop + position.height * (idx + 1)
              } v ${ecgParme.pix_mm} `
            }
          }
          zqSvg.path(
            widget,
            `path_${svg_elem_id}`,
            horizontalX,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:1`
          )

          // 纵轴刻度
          for (let i = 0; i <= position.verticalScale; i++) {
            if (i % 5 == 0) {
              verticalX += `M${position.offsetLeft - ecgParme.pix_mm * 2} ${
                position.diff * (idx + 1) +
                position.offsetTop +
                position.height * idx +
                (position.height / position.verticalScale) * i
              } h ${ecgParme.pix_mm * 2} `
              zqSvg.text(
                widget,
                `text_${svg_elem_id}`,
                position.offsetLeft - ecgParme.pix_mm * 2,
                position.diff * (idx + 1) +
                  position.offsetTop +
                  position.height * idx +
                  (position.height / position.verticalScale) * i,
                'font-size:12px',
                'end',
                'middle',
                450 - i * 10
              )
            } else {
              verticalX += `M${position.offsetLeft - ecgParme.pix_mm} ${
                position.diff * (idx + 1) +
                position.offsetTop +
                position.height * idx +
                (position.height / position.verticalScale) * i
              } h ${ecgParme.pix_mm} `
            }
          }
          zqSvg.path(
            widget,
            `path_${svg_elem_id}`,
            verticalX,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:1`
          )

          zqSvg.text(
            widget,
            `text_${svg_elem_id}`,
            gridWidth - out_rect.right,
            position.diff * (idx + 1) + position.offsetTop + position.height * idx,
            'font-size:12px',
            'end',
            'auto',
            caption[idx]
          )
        }
        this.draw_frequency(widget, svg_elem_id, vlpJson, ecgParme, out_rect, gridWidth, gridHeight, position)
      },
      /**
       * @method  draw_frequency  频域内容绘制
       * @param widget
       * @param svg_elem_id
       * @param vlpJson
       * @param ecgParme
       * @param out_rect
       * @param gridWidth
       * @param gridHeight
       * @param position
       */
      draw_frequency(widget, svg_elem_id, vlpJson, ecgParme, out_rect, gridWidth, gridHeight, position) {
        const insertArr = new Array(480).fill(0)
        const vcgName = ['vcgX', 'vcgY', 'vcgZ']
        // let ecgJsonSEnd = ecgJson["sEnd"] - this.time_domain_const
        const ecgJsonSEnd = vlpJson.sEnd - this.mouseX
        for (let idxs = 0; idxs < 3; idxs++) {
          const vcgX = []
          for (let idx = 0; idx < this.stepCount; idx++) {
            const sEnd = ecgJsonSEnd - idx * this.stepLength
            // position.obliqueSpectrumScale
            vcgX[idx] = []
            vcgX[idx] = vlpJson[vcgName[idxs]].filter((item, index) => {
              return index >= sEnd - 32 && index < sEnd + 32
            })
            vcgX[idx] = [...insertArr, ...vcgX[idx], ...insertArr]
          }

          // 转换实部虚部fft
          const imagRealVcgX = this.imagReal(vcgX)
          // 实部虚部数据处理-FFT值
          const imagRealDealVcgX = this.imagRealDeal(imagRealVcgX)

          // x导联平铺X轴绘制
          this.drawVcgX(
            widget,
            svg_elem_id,
            vlpJson,
            ecgParme,
            out_rect,
            gridWidth,
            gridHeight,
            position,
            imagRealDealVcgX,
            (position.height + position.diff) * (idxs + 1)
          )
        }
        // y导联平铺X轴绘制
        // this.drawVcgX(widget, svg_elem_id, ecgJson, ecgParme, out_rect, gridWidth, gridHeight, position, imagRealDealVcgX)
        // z导联平铺X轴绘制
        // this.drawVcgX(widget, svg_elem_id, ecgJson, ecgParme, out_rect, gridWidth, gridHeight, position, imagRealDealVcgX)

        // let gWidget = document.createElementNS( "http://www.w3.org/2000/svg", "g" );
        // widget.appendChild(gWidget)
        // gWidget.setAttribute("transform", 'rotate('+ (45) +' '+ (position.offsetLeft + position.width) +' '+ ((position.height + position.diff) *(idx + 1)  + position.offsetTop) +')')
        // gWidget.setAttribute("transform", 'rotate('+ (45) +' '+ (position.offsetLeft + position.width) +' '+ (position.height + position.diff  + position.offsetTop) +')')
        // Common.polyline(gWidget, "polyline_" + svg_elem_id, vector, "transparent", ecgParme.wave_color, 1)
      },
      /**
       * @method  imagReal  数据实现实部虚部转换
       * @param vector 波形数据
       * @returns {Array}
       */
      imagReal(vector) {
        const newVector = []
        for (let i = 0; i < vector.length; i++) {
          const data1 = new ComplexArray(vector[i])
          const data2 = fft(data1, false)
          newVector[i] = data2
        }
        return newVector
      },
      /**
       * @method  imagRealDeal  实部虚部第二字转换
       * @param vector 波形数据
       * @returns {Array}
       */
      imagRealDeal(data) {
        const vector = []
        for (let i = 0; i < data.length; i++) {
          const sum = []
          const len = data[i].imag.length
          for (let index = 0; index < len; index++) {
            const imag = Math.pow(data[i].imag[index], 2) // Math.pow(2,4)计算2的4次方
            const real = Math.pow(data[i].real[index], 2)
            const result = Math.sqrt(imag + real)
            // let result = (imag + real) / len;
            sum.push(result)
          }
          vector[i] = sum
        }
        return vector
      },
      /**
       * @method  drawVcgX  频域内容绘制
       * @param widget
       * @param svg_elem_id
       * @param vlpJson
       * @param ecgParme
       * @param out_rect
       * @param gridWidth
       * @param gridHeight
       * @param position
       * @param imagRealDealVcgX
       * @param offsetY
       */
      drawVcgX(
        widget,
        svg_elem_id,
        vlpJson,
        ecgParme,
        out_rect,
        gridWidth,
        gridHeight,
        position,
        imagRealDealVcgX,
        offsetY
      ) {
        const per = position.width / this.frequency_domain_length
        const vector_y = []
        for (let idx = 0; idx < this.stepCount; idx++) {
          const vector = []
          vector_y[idx] = []
          const width_diff = Math.sqrt(Math.pow(position.obliqueWidth - position.obliqueSpectrumScale * idx, 2) / 2)
          const height_diff = position.obliqueWidth - width_diff
          for (let array_idx = 0; array_idx < this.frequency_domain_length; array_idx++) {
            let p_x = array_idx * per
            let p_y = -1 * imagRealDealVcgX[idx][array_idx]
            p_x = p_x + position.offsetLeft + width_diff
            p_y = p_y + offsetY + position.offsetTop - position.obliqueWidth + height_diff
            // p_y = p_y + position.height + position.diff  + position.offsetTop - position.obliqueWidth + height_diff;
            vector.push(p_x)
            vector.push(p_y)
            vector_y[idx].push(p_x)
            vector_y[idx].push(p_y)
          }
          zqSvg.polyline(
            widget,
            `polyline_${svg_elem_id}`,
            vector,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
        }

        for (let idx = 0; idx < vector_y[0].length / 4; idx++) {
          const vector_y_new = []
          for (let array_idx = 0; array_idx < vector_y.length; array_idx++) {
            const splice = vector_y[array_idx].slice(idx * 4, idx * 4 + 2)
            vector_y_new.push(splice)
          }
          zqSvg.polyline(
            widget,
            `polyline_${svg_elem_id}`,
            vector_y_new,
            `fill:transparent;stroke:${ecgParme.waveColor};stroke-width:${ecgParme.waveLineWidth}`
          )
        }
      },
      /**
       * @method  clearEcgWave  svg清除函数
       * @param svg_elem_id
       */
      clearEcgWave(svg_elem_id) {
        // 打印参数
        const print_parameters = document.getElementById(`print_${svg_elem_id}`)
        if (print_parameters != null) {
          print_parameters.parentNode.removeChild(print_parameters)
        }
        // 患者信息
        const userInfo = document.getElementById(`userInfo_${svg_elem_id}`)
        if (userInfo != null) {
          userInfo.parentNode.removeChild(userInfo)
        }
        // 波形
        var wave = document.getElementById(`wave_${svg_elem_id}`)
        if (wave != null) {
          wave.parentNode.removeChild(wave)
        }
        // 时域
        var time_domain = document.getElementById(`time_domain_${svg_elem_id}`)
        if (time_domain != null) {
          time_domain.parentNode.removeChild(time_domain)
        }
        // 频域
        var frequency_domain = document.getElementById(`frequency_domain_${svg_elem_id}`)
        if (frequency_domain != null) {
          frequency_domain.parentNode.removeChild(frequency_domain)
        }
      },
      /**
       * @method  init  拖拽初始化
       */
      init() {
        this.mousedown = false
        this.offset_x = 0
        this.mouseX = 0
      },
      /**
       * @method    vlpMousedown send鼠标事件
       * @param e
       * @param sEnd_text_element
       * @param sEnd_line_element
       */
      vlpMousedown(e, sEnd_text_element, sEnd_line_element) {
        const _this = this
        var mouseEvent = e || event
        if (!_this.mousedown) {
          _this.mouseX = mouseEvent.offsetX
          _this.offset_x = mouseEvent.offsetX - sEnd_line_element.getBBox().x
        }
        _this.mousedown = true
      },
      /**
       * @method    vlpMousemove send鼠标事件
       * @param e
       * @param sEnd_text_element
       * @param sEnd_line_element
       */
      vlpMousemove(e, sEnd_text_element, sEnd_line_element) {
        const _this = this
        var mouseEvent = e || event
        if (_this.mousedown) {
          const x = mouseEvent.offsetX - _this.offset_x

          sEnd_text_element.setAttribute('x', x)
          sEnd_line_element.setAttribute('x1', x)
          sEnd_line_element.setAttribute('x2', x)
        }
      },
      /**
       * @method    vlpMouseup send鼠标事件
       * @param e
       * @param sEnd_text_element
       * @param sEnd_line_element
       * @param svg_elem_id
       * @param vlpJson
       * @param ecgParme
       * @param gain
       */
      vlpMouseup(
        e,
        sEnd_text_element,
        sEnd_line_element,
        svg_elem_id,
        widget,
        vlpJson,
        ecgParme,
        gain,
        svg_ecg,
        out_rect,
        gridWidth,
        gridHeight
      ) {
        const _this = this
        var mouseEvent = e || event
        if (_this.mousedown) {
          // let x = mouseEvent.offsetX - _this.offset_x;
          // sEnd_text_element.setAttribute("x", x);
          // sEnd_line_element.setAttribute("x1", x);
          // sEnd_line_element.setAttribute("x2", x);
          // 偏移量
          _this.mouseX -= mouseEvent.offsetX
          _this.mouseX = Math.round((_this.mouseX / _this.timeDomainSpeed / ecgParme.pix_mm) * _this.sampling)

          // 时域填充波形绘制
          const fillVector = this.draw_time_fill_data(vlpJson, ecgParme, gain)
          const sEnd_polyline = document.getElementById(`sEnd_polyline_${svg_elem_id}`)
          if (sEnd_polyline != null) {
            sEnd_polyline.parentNode.removeChild(sEnd_polyline)
          }
          zqSvg.polyline(
            widget,
            `polyline_${svg_elem_id}`,
            fillVector,
            `stroke:transparent;fill:${ecgParme.waveColor}`,
            `sEnd_polyline_${svg_elem_id}`
          )

          // line/text偏移
          sEnd_text_element.setAttribute('x', fillVector[fillVector.length - 2])
          sEnd_line_element.setAttribute('x1', fillVector[fillVector.length - 2])
          sEnd_line_element.setAttribute('x2', fillVector[fillVector.length - 2])

          // 频域绘制
          var frequency_domain = document.getElementById(`frequency_domain_${svg_elem_id}`)
          if (frequency_domain != null) {
            frequency_domain.parentNode.removeChild(frequency_domain)
          }
          zqSvg.g(svg_ecg, `frequency_domain_${svg_elem_id}`)
          _this.draw_frequency_domain(svg_elem_id, vlpJson, ecgParme, out_rect, gridWidth, gridHeight)
        }
        _this.init()
      }
    }
    window.zqVlp = window.zqVlp || new zqVlp()
    /**
     * 模块说明
     * @module zqMeasureWave	     测量
     * @rely  zqCommon zqSvg	 依赖zq.common.js zq.svg.js
     */
    function zqMeasureWave() {
      this.measure = null
      this.ecgParme = null
      this.distanceWidth = 70
      this.height = 270
      this.leftLinePosX = 0
      this.rightLinePosX = 0
      this.outPoint = {}
      this.line_child_svg = null
      this.leftmousedown = false
      this.rightmousedown = false
      this.movemousedown = false
      this.movemousePos = null
      this.movemouseLeftPos = null
      this.movemouseRightPos = null
      this.measure_svg = null
      this.measure_child_svg = null
      this.measuremousedown = false
      this.measuremousemovedown = false
      this.measurePosX = null
      this.measurePosY = null
      this.distanceX = 0
      this.distanceY = 0
      this.measuremovemousePosX = 0
      this.measuremovemousePosY = 0
      this.measurechangedown = false
      this.measurechangePosX = 0
      this.measurechangePosY = 0
    }
    zqMeasureWave.prototype = {
      initDesignation(measure, ecgParme, width, height) {
        const _this = this
        _this.measure = measure
        _this.ecgParme = ecgParme
        _this.distanceWidth = width
        _this.height = height

        _this.outPoint = {
          x: 6 * _this.ecgParme.pix_mm,
          y: 0
        }
        const totalWidth = _this.ecgParme.gridWidth - 6 * _this.ecgParme.pix_mm
        const posX = totalWidth / 2 - _this.distanceWidth / 2
        _this.leftLinePosX = posX
        _this.rightLinePosX = posX + _this.distanceWidth
      },
      initMeasure(measure, ecgParme, height) {
        const _this = this
        _this.measure = measure
        _this.ecgParme = ecgParme
        _this.height = height

        _this.outPoint = {
          x: 6 * _this.ecgParme.pix_mm,
          y: 0
        }
      },
      paintMeasure() {
        const _this = this
        if (_this.ecgParme.isMeasure == 0) {
          return
        }
        _this.clearSvg('measure_svg')
        _this.createSvg('measure_svg', _this.measure, _this.ecgParme.gridWidth, _this.ecgParme.gridHeight)
        _this.measure_svg = document.getElementById('measure_svg')
        _this.measurePainter()
      },
      measurePainter() {
        const _this = this
        document.onmousedown = function (e) {
          if (e.button == 0) {
            if (_this.measure_child_svg != null) {
              if (
                e.offsetX > _this.measurePosX + 5 &&
                e.offsetX < _this.measurePosX + _this.distanceX - 5 &&
                e.offsetY > _this.measurePosY + 5 &&
                e.offsetY < _this.measurePosY + _this.distanceY - 5
              ) {
                _this.measuremousemovedown = true
                _this.measuremovemousePosX = e.offsetX
                _this.measuremovemousePosY = e.offsetY
              } else if (
                e.offsetX > _this.measurePosX + _this.distanceX - 10 &&
                e.offsetX < _this.measurePosX + _this.distanceX + 10 &&
                e.offsetY > _this.measurePosY + _this.distanceY - 10 &&
                e.offsetY < _this.measurePosY + _this.distanceY + 10
              ) {
                _this.measurechangedown = true
                _this.measurechangePosX = e.offsetX
                _this.measurechangePosY = e.offsetY
              } else {
                setTimeout(() => {
                  _this.measuremousedown = true
                  _this.measurePosX = e.offsetX
                  _this.measurePosY = e.offsetY

                  _this.clearSvg('measure_child_svg')
                  _this.clearSvg('measure_info_svg')
                  _this.measure_child_svg = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
                  _this.measure_child_svg.setAttribute('id', 'measure_child_svg')
                  _this.measure_child_svg.setAttribute('style', 'fill:transparent;stroke:#0000CD;stroke-width:1')
                  _this.measure_child_svg.setAttribute('x', _this.measurePosX)
                  _this.measure_child_svg.setAttribute('y', _this.measurePosY)

                  _this.measure_svg.appendChild(_this.measure_child_svg)
                }, 50)
              }
            } else {
              _this.measuremousedown = true
              _this.measurePosX = e.offsetX
              _this.measurePosY = e.offsetY

              _this.measure_child_svg = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
              _this.measure_child_svg.setAttribute('id', 'measure_child_svg')
              _this.measure_child_svg.setAttribute('style', 'fill:transparent;stroke:#0000CD;stroke-width:1')
              _this.measure_child_svg.setAttribute('x', _this.measurePosX)
              _this.measure_child_svg.setAttribute('y', _this.measurePosY)

              _this.measure_svg.appendChild(_this.measure_child_svg)
            }
          }
        }

        document.onmousemove = function (e) {
          if (e.button == 0) {
            if (document.getElementById('measure_svg') != null) {
              if (
                e.offsetX > _this.measurePosX + 5 &&
                e.offsetX < _this.measurePosX + _this.distanceX - 5 &&
                e.offsetY > _this.measurePosY + 5 &&
                e.offsetY < _this.measurePosY + _this.distanceY - 5
              ) {
                document.getElementById('measure_svg').style.cursor = 'pointer'
              } else if (
                e.offsetX > _this.measurePosX + _this.distanceX - 10 &&
                e.offsetX < _this.measurePosX + _this.distanceX + 10 &&
                e.offsetY > _this.measurePosY + _this.distanceY - 10 &&
                e.offsetY < _this.measurePosY + _this.distanceY + 10
              ) {
                document.getElementById('measure_svg').style.cursor = 'se-resize'
              } else {
                document.getElementById('measure_svg').style.cursor = 'default'
              }
            }

            if (_this.measuremousedown == true) {
              const pos = {
                x: e.offsetX,
                y: e.offsetY
              }

              if (e.offsetX < _this.measurePosX || e.offsetY < _this.measurePosY) {
                return
              }
              if (e.offsetX < _this.outPoint.x) {
                pos.x = _this.outPoint.x
              }
              if (e.offsetX > _this.ecgParme.gridWidth - 10) {
                pos.x = _this.ecgParme.gridWidth - 10
              }
              if (e.offsetY < _this.outPoint.y) {
                pos.y = _this.outPoint.y
              }
              if (e.offsetY > _this.ecgParme.gridHeight - 5) {
                pos.y = _this.ecgParme.gridHeight - 5
              }

              _this.handleMeasureMouseMove(pos)
            }
            if (_this.measuremousemovedown == true) {
              const moveX = e.offsetX - _this.measuremovemousePosX
              const moveY = e.offsetY - _this.measuremovemousePosY
              let rectX = _this.measurePosX + moveX
              let rectY = _this.measurePosY + moveY
              if (rectX < _this.outPoint.x) {
                rectX = _this.outPoint.x
              }
              if (rectX + _this.distanceX > _this.ecgParme.gridWidth) {
                rectX = _this.ecgParme.gridWidth - _this.distanceX
              }
              if (rectY < _this.outPoint.y) {
                rectY = _this.outPoint.y
              }
              if (rectY + _this.distanceY > _this.ecgParme.gridHeight) {
                rectY = _this.ecgParme.gridHeight - _this.distanceY
              }
              _this.measure_child_svg.setAttribute('x', rectX)
              _this.measure_child_svg.setAttribute('y', rectY)
              _this.paintMeasureInfoText(rectX, rectY, _this.distanceX, _this.distanceY)
            }
            if (_this.measurechangedown == true) {
              let moveX = e.offsetX - _this.measurechangePosX
              let moveY = e.offsetY - _this.measurechangePosY

              if (e.offsetX > _this.ecgParme.gridWidth - 10) {
                moveX = _this.ecgParme.gridWidth - _this.measurechangePosX - 10
              }

              if (e.offsetY > _this.ecgParme.gridHeight - 10) {
                moveY = _this.ecgParme.gridHeight - _this.measurechangePosY - 10
              }

              let width = _this.distanceX + moveX
              let height = _this.distanceY + moveY

              if (e.offsetX < _this.measurePosX + 15) {
                width = 15
              }
              if (e.offsetY < _this.measurePosY + 15) {
                height = 15
              }
              _this.measure_child_svg.setAttribute('width', width)
              _this.measure_child_svg.setAttribute('height', height)
              _this.paintMeasureInfoText(_this.measurePosX, _this.measurePosY, width, height)
            }
          }
        }

        document.onmouseup = function (e) {
          _this.measuremousedown = false
          if (_this.measuremousemovedown == true) {
            _this.measuremousemovedown = false

            const moveX = e.offsetX - _this.measuremovemousePosX
            const moveY = e.offsetY - _this.measuremovemousePosY
            _this.measurePosX += moveX
            _this.measurePosY += moveY
          }
          if (_this.measurechangedown == true) {
            _this.measurechangedown = false

            const moveX = e.offsetX - _this.measurechangePosX
            const moveY = e.offsetY - _this.measurechangePosY
            _this.distanceX += moveX
            _this.distanceY += moveY
          }
        }
      },
      handleMeasureMouseMove(pos) {
        const _this = this
        _this.distanceX = pos.x - _this.measurePosX
        _this.distanceY = pos.y - _this.measurePosY
        _this.measure_child_svg.setAttribute('width', _this.distanceX)
        _this.measure_child_svg.setAttribute('height', _this.distanceY)

        _this.paintMeasureInfoText(_this.measurePosX, _this.measurePosY, _this.distanceX, _this.distanceY)
      },
      paintMeasureInfoText(measurePosX, measurePosY, width, height) {
        const _this = this
        _this.clearSvg('measure_info_svg')
        _this.createSvg('measure_info_svg', _this.measure, _this.ecgParme.gridWidth, _this.ecgParme.gridHeight)
        const measure_info_svg = document.getElementById('measure_info_svg')
        const jq_value = parseInt((width / _this.ecgParme.pix_mm / _this.ecgParme.speed) * 1000)
        const hr_value = parseInt(60 / (width / _this.ecgParme.pix_mm / _this.ecgParme.speed))
        const measureSvgHeight = document.getElementById('standard_ecg').clientHeight

        let msPosY = measurePosY + height + 5
        if (msPosY > measureSvgHeight - 10) {
          msPosY = measurePosY + height - 15
        }
        zqSvg.text(
          measure_info_svg,
          'text',
          measurePosX + width / 2,
          msPosY,
          'font-size:12px;fill:#000000;user-select: none;',
          'Middle',
          'Hanging',
          `${jq_value}ms`
        )

        let bpmPosY = measurePosY - 15
        if (bpmPosY < 10) {
          bpmPosY = measurePosY + 5
        }
        zqSvg.text(
          measure_info_svg,
          'text',
          measurePosX + width / 2,
          bpmPosY,
          'font-size:12px;fill:#000000;user-select: none;',
          'Middle',
          'Hanging',
          `${hr_value}bpm`
        )
        const mv_value = parseFloat(height / _this.ecgParme.pix_mm / _this.ecgParme.gain).toFixed(3)

        if (measurePosX < 45) {
          const mvPosX = measurePosX + 5
          zqSvg.text(
            measure_info_svg,
            'text',
            mvPosX,
            measurePosY + height / 2,
            'font-size:12px;fill:#000000;user-select: none;',
            'start',
            'middle',
            `${mv_value}mv`
          )
        } else {
          const mvPosX = measurePosX - 10
          zqSvg.text(
            measure_info_svg,
            'text',
            mvPosX,
            measurePosY + height / 2,
            'font-size:12px;fill:#000000;user-select: none;',
            'end',
            'middle',
            `${mv_value}mv`
          )
        }

        _this.measure_svg.appendChild(measure_info_svg)
      },
      paintDesignation() {
        const _this = this
        if (_this.ecgParme.isDesignation == 0) {
          return
        }
        _this.clearSvg('line_child')
        _this.createSvg('line_child', _this.measure, _this.ecgParme.gridWidth, _this.ecgParme.gridHeight)
        _this.line_child_svg = document.getElementById('line_child')
        _this.paintMoveLine()
        _this.paintLeftAllLine()
        _this.paintRightAllLine()
        _this.paintInfoText()
        _this.painter()
      },
      painter() {
        const _this = this
        document.onmousedown = function (e) {
          if (e.button == 0) {
            if (e.offsetX > _this.leftLinePosX - 5 && e.offsetX < _this.leftLinePosX + 5) {
              _this.leftmousedown = true
              _this.movemousedown = false
              // document.getElementById("left_line").style.cursor = "e-resize"
            }
            if (e.offsetX > _this.rightLinePosX - 5 && e.offsetX < _this.rightLinePosX + 5) {
              _this.rightmousedown = true
              _this.movemousedown = false
              // document.getElementById("right_line").style.cursor = "e-resize"
            }
            if (
              e.offsetX < _this.leftLinePosX - 5 ||
              e.offsetX > _this.rightLinePosX + 5 ||
              (e.offsetX > _this.leftLinePosX + 5 && e.offsetX < _this.rightLinePosX - 5)
            ) {
              _this.movemousedown = true
              _this.movemousePos = e.offsetX
              _this.movemouseLeftPos = _this.leftLinePosX
              _this.movemouseRightPos = _this.rightLinePosX
            }
          }
        }

        document.onmousemove = function (e) {
          if (e.button == 0) {
            if (
              document.getElementById('left_line') != null &&
              e.offsetX > _this.leftLinePosX - 5 &&
              e.offsetX < _this.leftLinePosX + 5
            ) {
              document.getElementById('line_child').style.cursor = 'e-resize'
            }
            if (
              document.getElementById('right_line') != null &&
              e.offsetX > _this.rightLinePosX - 5 &&
              e.offsetX < _this.rightLinePosX + 5
            ) {
              document.getElementById('line_child').style.cursor = 'e-resize'
            }
            if (_this.leftmousedown == true) {
              _this.leftLinePosX = e.offsetX
              _this.handleMouseMove()
            }
            if (_this.rightmousedown == true) {
              _this.rightLinePosX = e.offsetX
              _this.handleMouseMove()
            }
            if (_this.movemousedown == true) {
              _this.leftLinePosX = e.offsetX - _this.movemousePos + _this.movemouseLeftPos
              _this.rightLinePosX = e.offsetX - _this.movemousePos + _this.movemouseRightPos
              if (_this.leftLinePosX < _this.outPoint.x) {
                return
              }
              if (_this.rightLinePosX > _this.ecgParme.gridWidth) {
                return
              }
              _this.handleMouseMove()
            }
          }
        }

        document.onmouseup = function (e) {
          _this.leftmousedown = false
          _this.rightmousedown = false
          _this.movemousedown = false
        }
      },
      handleMouseMove() {
        const _this = this
        _this.distanceWidth = _this.rightLinePosX - _this.leftLinePosX

        if (_this.distanceWidth < 20) {
          _this.leftmousedown = false
          _this.rightmousedown = false
          return
        }
        _this.paintDesignation()
      },
      paintMoveLine() {
        const _this = this
        zqSvg.line(
          _this.line_child_svg,
          _this.leftLinePosX,
          _this.leftLinePosX,
          _this.outPoint.y,
          _this.outPoint.y + _this.height,
          'left_line',
          'stroke-width:3;stroke:#0000CD',
          '',
          '',
          'left_line'
        )
        zqSvg.line(
          _this.line_child_svg,
          _this.rightLinePosX,
          _this.rightLinePosX,
          _this.outPoint.y,
          _this.outPoint.y + _this.height,
          'right_line',
          'stroke-width:3;stroke:#0000CD',
          '',
          '',
          'right_line'
        )
      },
      paintLeftAllLine() {
        const _this = this
        const leftWidth = _this.leftLinePosX - _this.outPoint.x
        const posFirstX = leftWidth % _this.distanceWidth
        const lineCount = Math.floor((leftWidth - posFirstX - _this.distanceWidth) / _this.distanceWidth)

        zqSvg.line(
          _this.line_child_svg,
          posFirstX + _this.outPoint.x,
          posFirstX + _this.outPoint.x,
          _this.outPoint.y,
          _this.outPoint.y + _this.height,
          'left_line',
          'stroke-width:1;stroke:#0000CD',
          '',
          '',
          'left_line_child'
        )

        for (let i = 0; i < lineCount; i++) {
          if (posFirstX + _this.distanceWidth * (i + 1) > _this.leftLinePosX) {
            break
          }
          zqSvg.line(
            _this.line_child_svg,
            _this.outPoint.x + posFirstX + _this.distanceWidth * (i + 1),
            _this.outPoint.x + posFirstX + _this.distanceWidth * (i + 1),
            _this.outPoint.y,
            _this.outPoint.y + _this.height,
            'left_line',
            'stroke-width:1;stroke:#0000CD',
            '',
            '',
            'left_line_child'
          )
        }
      },
      paintRightAllLine() {
        const _this = this
        const rightWidth = _this.ecgParme.gridWidth - _this.rightLinePosX
        const posFirstX = rightWidth % _this.distanceWidth
        const lineCount = Math.floor((rightWidth - posFirstX - _this.distanceWidth) / _this.distanceWidth)

        zqSvg.line(
          _this.line_child_svg,
          _this.ecgParme.gridWidth - posFirstX,
          _this.ecgParme.gridWidth - posFirstX,
          _this.outPoint.y,
          _this.outPoint.y + _this.height,
          'right_line',
          'stroke-width:1;stroke:#0000CD',
          '',
          '',
          'right_line_child'
        )

        for (let i = 0; i < lineCount; i++) {
          if (posFirstX + _this.distanceWidth * (i + 1) > _this.ecgParme.gridWidth) {
            break
          }
          zqSvg.line(
            _this.line_child_svg,
            _this.rightLinePosX + _this.distanceWidth * (i + 1),
            _this.rightLinePosX + _this.distanceWidth * (i + 1),
            _this.outPoint.y,
            _this.outPoint.y + _this.height,
            'right_line',
            'stroke-width:1;stroke:#0000CD',
            '',
            '',
            'right_line_child'
          )
        }
      },
      paintInfoText() {
        const _this = this
        const jq_value = parseInt((_this.distanceWidth / _this.ecgParme.pix_mm / _this.ecgParme.speed) * 1000)
        const hr_value = parseInt(60 / (_this.distanceWidth / _this.ecgParme.pix_mm / _this.ecgParme.speed))
        zqSvg.text(
          _this.line_child_svg,
          'text',
          _this.leftLinePosX + 25,
          _this.outPoint.y + _this.height - 15,
          'font-size:12px;fill:#000000;user-select: none;',
          'Middle',
          'Hanging',
          `${jq_value}ms`
        )
        zqSvg.text(
          _this.line_child_svg,
          'text',
          _this.leftLinePosX + 70,
          _this.outPoint.y + _this.height - 15,
          'font-size:12px;fill:#000000;user-select: none;',
          'Middle',
          'Hanging',
          `${hr_value}bpm`
        )
      },
      /**
       * create_svg  svg页面元素创建
       * @param {*} svg_elem_id
       */
      createSvg(svg_elem_id, measure, width, height) {
        const svgElement = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
        svgElement.setAttribute('width', width)
        svgElement.setAttribute('height', height)
        svgElement.setAttribute('style', 'background:#ffffff')
        svgElement.setAttribute('id', svg_elem_id)
        measure.appendChild(svgElement)

        return svgElement
      },

      /**
       * @method  clearEcgWave  svg清除函数
       */
      clearSvg(svg_elem_id) {
        // zqCommon.clearIdChildren(svg_elem_id)
        // svg
        var svg = document.getElementById(svg_elem_id)
        if (svg != null) {
          svg.parentNode.removeChild(svg)
        }
      }
    }
    window.zqMeasureWave = window.zqMeasureWave || new zqMeasureWave()
    /**
     * 锁定服务
     */
    function LockService() {}
    /**
     * 添加锁
     * @param ecgId
     * @param type （ecg，bp，holter）
     */
    LockService.prototype.add = function (ecgId, type) {
      var data = {
        ecgId,
        type
      }
    }
    /**
     *  绑定页面关闭事件，并且更新后台数据
     * @param ecgId
     * @param type
     */
    LockService.prototype.background = function (ecgId, type) {
      var ctx = this
      ctx.add(ecgId, type) // 添加锁
      window.onbeforeunload = function (ev) {
        ctx.remove(ecgId, type)
      }
    }
    /**
     * 是否被锁定
     * @param ecgId
     * @param type （ecg，bp，holter）
     * @return boolean
     */
    LockService.prototype.isLock = function (ecgId, type) {
      var isLock = true

      return isLock
    }
    /**
     * 解除多个
     */
    LockService.prototype.removes = function (ecgIds, type, func) {
      for (var i = 0; i < ecgIds.length; i++) {
        this.remove(ecgIds[i], type)
      }
      if (func) {
        func()
      }
    }
    /**
     * 解除锁
     * @param ecgId
     * @param type （ecg，bp，holter）
     */
    LockService.prototype.remove = function (ecgId, type) {}
    // 锁全局对象
    window.lockService = window.LockService || new LockService()
  }
}

class ComplexArray {
  constructor(other, arrayType = Float32Array) {
    if (other instanceof ComplexArray) {
      // Copy constuctor.
      this.ArrayType = other.ArrayType
      this.real = new this.ArrayType(other.real)
      this.imag = new this.ArrayType(other.imag)
    } else {
      this.ArrayType = arrayType
      // other can be either an array or a number.
      this.real = new this.ArrayType(other)
      this.imag = new this.ArrayType(this.real.length)
    }

    this.length = this.real.length
  }

  toString() {
    const components = []

    this.forEach((value, i) => {
      components.push(`(${value.real.toFixed(2)}, ${value.imag.toFixed(2)})`)
    })

    return `[${components.join(', ')}]`
  }

  forEach(iterator) {
    const n = this.length
    // For gc efficiency, re-use a single object in the iterator.
    const value = Object.seal(
      Object.defineProperties(
        {},
        {
          real: { writable: true },
          imag: { writable: true }
        }
      )
    )

    for (let i = 0; i < n; i++) {
      value.real = this.real[i]
      value.imag = this.imag[i]
      iterator(value, i, n)
    }
  }

  // In-place mapper.
  map(mapper) {
    this.forEach((value, i, n) => {
      mapper(value, i, n)
      this.real[i] = value.real
      this.imag[i] = value.imag
    })

    return this
  }

  conjugate() {
    return new ComplexArray(this).map(value => {
      value.imag *= -1
    })
  }

  magnitude() {
    const mags = new this.ArrayType(this.length)

    this.forEach((value, i) => {
      mags[i] = Math.sqrt(value.real * value.real + value.imag * value.imag)
    })

    return mags
  }
}

// import baseComplexArray from './complex_array.js';

// Math constants and functions we need.
const { PI } = Math
const { SQRT1_2 } = Math

function FFT(input) {
  return ensureComplexArray(input).FFT()
}

function InvFFT(input) {
  return ensureComplexArray(input).InvFFT()
}

function frequencyMap(input, filterer) {
  return ensureComplexArray(input).frequencyMap(filterer)
}

/* export class ComplexArray extends baseComplexArray {
  FFT() {
    return fft(this, false);
  }

  InvFFT() {
    return fft(this, true);
  }

  // Applies a frequency-space filter to input, and returns the real-space
  // filtered input.
  // filterer accepts freq, i, n and modifies freq.real and freq.imag.
  frequencyMap(filterer) {
    return this.FFT().map(filterer).InvFFT();
  }
}*/

/* export class ComplexArray extends baseComplexArray {
  FFT() {
    return fft(this, false);
  }

  InvFFT() {
    return fft(this, true);
  }

  // Applies a frequency-space filter to input, and returns the real-space
  // filtered input.
  // filterer accepts freq, i, n and modifies freq.real and freq.imag.
  frequencyMap(filterer) {
    return this.FFT().map(filterer).InvFFT();
  }
}*/

function ensureComplexArray(input) {
  return (input instanceof ComplexArray && input) || new ComplexArray(input)
}

function fft(input, inverse) {
  const n = input.length

  if (n & (n - 1)) {
    return FFT_Recursive(input, inverse)
  } else {
    return FFT_2_Iterative(input, inverse)
  }
}

function FFT_Recursive(input, inverse) {
  const n = input.length

  if (n === 1) {
    return input
  }

  const output = new ComplexArray(n, input.ArrayType)

  // Use the lowest odd factor, so we are able to use FFT_2_Iterative in the
  // recursive transforms optimally.
  const p = LowestOddFactor(n)
  const m = n / p
  const normalisation = 1 / Math.sqrt(p)
  let recursive_result = new ComplexArray(m, input.ArrayType)

  // Loops go like O(n Σ p_i), where p_i are the prime factors of n.
  // for a power of a prime, p, this reduces to O(n p log_p n)
  for (let j = 0; j < p; j++) {
    for (let i = 0; i < m; i++) {
      recursive_result.real[i] = input.real[i * p + j]
      recursive_result.imag[i] = input.imag[i * p + j]
    }
    // Don't go deeper unless necessary to save allocs.
    if (m > 1) {
      recursive_result = fft(recursive_result, inverse)
    }

    const del_f_r = Math.cos((2 * PI * j) / n)
    const del_f_i = (inverse ? -1 : 1) * Math.sin((2 * PI * j) / n)
    let f_r = 1
    let f_i = 0

    for (let i = 0; i < n; i++) {
      const _real = recursive_result.real[i % m]
      const _imag = recursive_result.imag[i % m]

      output.real[i] += f_r * _real - f_i * _imag
      output.imag[i] += f_r * _imag + f_i * _real
      ;[f_r, f_i] = [f_r * del_f_r - f_i * del_f_i, (f_i = f_r * del_f_i + f_i * del_f_r)]
    }
  }

  // Copy back to input to match FFT_2_Iterative in-placeness
  // TODO: faster way of making this in-place?
  for (let i = 0; i < n; i++) {
    input.real[i] = normalisation * output.real[i]
    input.imag[i] = normalisation * output.imag[i]
  }

  return input
}

function FFT_2_Iterative(input, inverse) {
  const n = input.length

  const output = BitReverseComplexArray(input)
  const output_r = output.real
  const output_i = output.imag
  // Loops go like O(n log n):
  //   width ~ log n; i,j ~ n
  let width = 1
  while (width < n) {
    const del_f_r = Math.cos(PI / width)
    const del_f_i = (inverse ? -1 : 1) * Math.sin(PI / width)
    for (let i = 0; i < n / (2 * width); i++) {
      let f_r = 1
      let f_i = 0
      for (let j = 0; j < width; j++) {
        const l_index = 2 * i * width + j
        const r_index = l_index + width

        const left_r = isNaN(output_r[l_index]) ? 0 : output_r[l_index]
        const left_i = isNaN(output_i[l_index]) ? 0 : output_i[l_index]
        const right_r = f_r * output_r[r_index] - f_i * output_i[r_index]
        const right_i = f_i * output_r[r_index] + f_r * output_i[r_index]

        output_r[l_index] = SQRT1_2 * (left_r + right_r)
        output_i[l_index] = SQRT1_2 * (left_i + right_i)
        output_r[r_index] = SQRT1_2 * (left_r - right_r)
        output_i[r_index] = SQRT1_2 * (left_i - right_i)
        ;[f_r, f_i] = [f_r * del_f_r - f_i * del_f_i, f_r * del_f_i + f_i * del_f_r]
      }
    }
    width <<= 1
  }

  return output
}

function BitReverseIndex(index, n) {
  let bitreversed_index = 0

  while (n > 1) {
    bitreversed_index <<= 1
    bitreversed_index += index & 1
    index >>= 1
    n >>= 1
  }
  return bitreversed_index
}

function BitReverseComplexArray(array) {
  const n = array.length
  const flips = new Set()

  for (let i = 0; i < n; i++) {
    const r_i = BitReverseIndex(i, n)

    if (flips.has(i)) continue
    ;[array.real[i], array.real[r_i]] = [array.real[r_i], array.real[i]]
    ;[array.imag[i], array.imag[r_i]] = [array.imag[r_i], array.imag[i]]

    flips.add(r_i)
  }

  return array
}

function LowestOddFactor(n) {
  const sqrt_n = Math.sqrt(n)
  let factor = 3

  while (factor <= sqrt_n) {
    if (n % factor === 0) return factor
    factor += 2
  }
  return n
}
var a = 1
