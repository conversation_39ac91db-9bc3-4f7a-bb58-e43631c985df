import request from '@/utils/request'

// 筛查统计
export const getScreeningStatistics = (data) => {
  return request({
    url: '/cspapi/backend/screening/count/overview',
    method: 'post',
    data
  })
}

// 规范管理统计
export const getStandardizeManageStatistics = (data) => {
  return request({
    url: '/cspapi/backend/standard/count/overview',
    method: 'post',
    data
  })
}

// 人群普筛统计
export const getPopulationScreeningStatistics = (data) => {
  return request({
    url: '/cspapi/backend/app/count/overview',
    method: 'post',
    data
  })
}

// 人群普筛工作量统计
export const getPopulationScreeningWorkLoadStatistics = (data) => {
  return request({
    url: '/cspapi/backend/app/count/doctor',
    method: 'post',
    data
  })
}

// 服务包签订统计
export const getServicePackageStatistics = (data) => {
  return request({
    url: '/cspapi/backend/sign/count/overview',
    method: 'post',
    data
  })
}
