// base color
$blue: #324157;
$light-blue: #3a71a8;
$red: #c03639;
$pink: #e65d6e;
$green: #30b08f;
$tiffany: #4ab7bd;
$yellow: #fec171;
$panGreen: #30b08f;

// sidebar
$menuText: #5e6e82;
$menuActiveText: #0a86c8;
$subMenuActiveText: #0a86c8; // https://github.com/ElemeFE/element/issues/12951

// $menuBg: #EBF7F5;
$menuBg: #dff2ef;
$menuHover: #232e3c;

$subMenuBg: #fff;
$subMenuHover: #232e3c;

$sideBarWidth: 0;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
