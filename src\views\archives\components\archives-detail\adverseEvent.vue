<!-- 不良事件 -->
<template>
  <div class="adverse-event">
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      row-key="id"
      :columns="columns"
      :show-pagination="showPagination"
    >
      <template #operation="{ row }">
        <el-button type="text" @click="handleView(row)">查看</el-button>
      </template>
    </base-table>

    <ProDialog ref="proDialog" title="查看" :visible.sync="addDialogVisible" width="650px">
      <el-form ref="formRef" :model="form" label-width="90px">
        <el-form-item label="医疗机构" prop="depart">
          <el-input v-model="form.departName" :disabled="disableSubmit" />
        </el-form-item>
        <el-form-item label="选择人员">
          <el-input v-model="form.patientName" :disabled="disableSubmit" />
        </el-form-item>
        <CheckboxGroupField v-model="form.badEvent" :disabled="disableSubmit" :item="badEventList" :form-data="form" />
        <el-form-item label="发生日期">
          <el-date-picker
            v-model="form.happenDate"
            :disabled="disableSubmit"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="选择日期"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">关闭</el-button>
      </span>
    </ProDialog>
  </div>
</template>

<script>
import { getAdverseEventHistoryList } from '@/api/adverseEvent'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ProDialog from '@/components/ProDialog/index.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'

export default {
  name: 'AdverseEvent',
  components: {
    BaseTable,
    ProDialog,
    CheckboxGroupField
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        patientId: this.$route.query.id
      },
      form: {
        badEvent: []
      },
      badEventList: {
        label: '不良事件：',
        type: 'checkbox',
        prop: 'badEvent',
        belong: 'all',
        required: false,
        remark: true, // 是否需要备注
        options: [
          { label: '出血事件', value: '1', badEventRemark1: '', prop: 'badEventBloodLevel', type: 'select' },
          { label: '心衰', value: '2', badEventRemark2: '' },
          { label: 'TIA', value: '3', badEventRemark2: '' },
          { label: '痴呆', value: '4', badEventRemark3: '' },
          { label: '卒中', value: '5', badEventRemark4: '' },
          { label: '冠心病', value: '6', badEventRemark5: '' },
          { label: '失明', value: '7', badEventRemark6: '' },
          { label: '肾功能不全', value: '8', badEventRemark7: '' },
          { label: '周围血管闭塞', value: '9', badEventRemark8: '' },
          { label: '主动脉夹层', value: '10', badEventRemark9: '' },
          { label: '呼吸衰竭', value: '11', badEventRemark10: '' },
          { label: '肺源性心脏病', value: '12', badEventRemark11: '' },
          { label: '肺性脑病', value: '13', badEventRemark12: '' }
        ],
        extraOptions: [
          { label: '少量出血', value: 1 },
          { label: '中等出血', value: 2 },
          { label: '大出血或致命性出血', value: 3 }
        ]
      },
      columns: [
        { label: '不良事件', prop: 'badEventDesc' },
        { label: '机构名称', prop: 'departName' },
        { label: '发生时间', prop: 'happenDate' },
        { label: '操作', prop: 'operation', slot: 'operation' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      return await getAdverseEventHistoryList(params)
    },
    handleView(row) {
      this.form = row
      this.disableSubmit = true
      this.addDialogVisible = true
    }
  }
}
</script>
