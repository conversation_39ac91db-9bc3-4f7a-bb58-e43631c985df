<template>
  <div class="massScreening-detail">
    <el-card>
      <patient-info :patient-info="detail" :reg-id="$route.query.id" from-type="hide" />
    </el-card>
    <el-card class="massScreening-detail-card">
      <div class="massScreening-detail-content-result">
        <flag-component title="结果判断" />
        <div class="result-table">
          <el-descriptions :column="3" border>
            <el-descriptions-item label-class-name="my-label">
              <template slot="label">糖尿病风险值</template>
              {{ detail.pValue }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label">
              <template slot="label">糖尿病普查结果判定</template>
              {{ detail.csTnbResult }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label">
              <template slot="label">高血压普查结果判定</template>
              {{ detail.csGxyResult }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label">
              <template slot="label">慢阻肺普查结果判定</template>
              {{ detail.csCopdResult }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label">
              <template slot="label">房颤普查结果判定</template>
              {{ detail.csFcResult }}
            </el-descriptions-item>
            <el-descriptions-item label-class-name="my-label">
              <template slot="label" />
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <el-tabs v-model="activeName">
        <el-tab-pane label="问卷调查" name="questionnaire" />
        <el-tab-pane label="体征测量" name="form" />
      </el-tabs>

      <div v-if="activeName === 'questionnaire'">
        <div class="massScreening-detail-content-questionnaire">
          <div class="questionnaire-content">
            <Questionnaire :detail="detail" />
          </div>
        </div>
      </div>

      <div v-if="activeName === 'form'">
        <div class="massScreening-detail-content-form">
          <el-form :model="detail" label-width="100px">
            <el-row>
              <el-col :span="6">
                <el-form-item label="身高">
                  <el-input v-model="detail.height">
                    <template slot="append">cm</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="体重">
                  <el-input v-model="detail.weight">
                    <template slot="append">kg</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="BMI">
                  <el-input v-model="detail.bmi">
                    <template slot="append">kg/m²</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="腰围">
                  <el-input v-model="detail.waistline">
                    <template slot="append">cm</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="收缩压">
                  <el-input v-model="detail.sp">
                    <template slot="append">mmHg</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="舒张压">
                  <el-input v-model="detail.dp">
                    <template slot="append">mmHg</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import PatientInfo from '@/components/patientInfo/index.vue'
import FlagComponent from '@/components/flagComponent/index.vue'
import Questionnaire from './component/questionnaire.vue'
import { getMassScreeningDetail } from '@/api/screenList'

export default {
  name: 'ScreenListDetail',
  components: {
    PatientInfo,
    FlagComponent,
    Questionnaire
  },
  data() {
    return {
      detail: {},
      questionnaireId: '',
      activeName: 'questionnaire'
    }
  },
  created() {
    this.getMassScreeningDetailFn()
  },
  methods: {
    async getMassScreeningDetailFn() {
      const res = await getMassScreeningDetail({ id: this.$route.query.id })
      if (res.code === 200) {
        // 慢病按照这个排序['糖尿病', '高血压', '慢阻肺', '房颤']
        const diseaseListTemp = res.data.disease.split(',')
        const diseaseList = [
          {
            name: '糖尿病',
            id: 'tnb'
          },
          {
            name: '高血压',
            id: 'gxy'
          },
          {
            name: '慢阻肺',
            id: 'COPD'
          },
          {
            name: '房颤',
            id: 'fangchan'
          }
        ]
        const diseaseListIndex = diseaseList.filter((item) => diseaseListTemp.includes(item.id))
        res.data.diseaseList = diseaseListIndex
        this.detail = {
          ...res.data,
          tnbFamilyHistory: res.data.tnbFamilyHistory ? res.data.tnbFamilyHistory.split(',') : [],
          gxyFamilyHistory: res.data.gxyFamilyHistory ? res.data.gxyFamilyHistory.split(',') : [],
          copdFamilyHistory: res.data.copdFamilyHistory ? res.data.copdFamilyHistory.split(',') : [],
          fcFamilyHistory: res.data.fcFamilyHistory ? res.data.fcFamilyHistory.split(',') : []
        }
        this.questionnaireId = res.data.diseaseList[0].id
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.massScreening-detail {
  .el-card {
    margin: 16px;
  }
  .massScreening-detail-card {
    padding: 16px;
    .massScreening-detail-content-result {
      .result-table {
        margin: 16px 0;
        ::v-deep .my-label {
          background: #e6f9ff !important;
          width: 180px;
        }
      }
    }
    .massScreening-detail-content-questionnaire {
      .questionnaire-content {
        margin-top: 16px;
      }
    }
    .massScreening-detail-content-form {
      margin-top: 16px;
    }
  }
}
</style>
