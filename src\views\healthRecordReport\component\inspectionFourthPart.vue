<!-- 检查第四部分: 血常规 -->
<template>
  <div class="inspection-fourth-part">
    <div class="content">
      <div class="title">血常规</div>
      <div class="item">
        <el-table :data="bloodData" style="width: 100%" border>
          <el-table-column align="center" prop="name" label="名称" />
          <el-table-column align="center" prop="unit" label="单位" />
          <el-table-column align="center" prop="range" label="参考范围" />
          <el-table-column align="center" prop="value" label="值" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InspectionFourthPart',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    // 血常规数据
    bloodData() {
      return (
        this.reportInfo.itemList.find((item) => item.itemCode === 'BLOOD_ROUTINE') &&
        this.reportInfo.itemList.find((item) => item.itemCode === 'BLOOD_ROUTINE').data.itemList
      )
    }
  }
}
</script>

<style lang="scss">
.inspection-fourth-part {
  padding: 10px;
  .content {
    margin-top: 8px;
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    .el-table td.el-table__cell .cell {
      line-height: 1.6rem !important;
    }
  }
  @media print {
    // 增加选择器权重确保打印样式生效
    .content .el-table td.el-table__cell {
      padding: 0 !important;
    }
    .content .el-table td.el-table__cell .cell {
      line-height: 1.6rem !important;
    }
  }
}
</style>
