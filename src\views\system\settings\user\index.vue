<template>
  <!-- eslint-disable vue/no-use-v-if-with-v-for -->
  <div class="casting">
    <div class="top">
      <el-button
        type="primary"
        icon="el-icon-plus"
        style="width: 150px; height: 40px"
        @click="addRoleShow = true"
      >新增角色
      </el-button>
    </div>
    <div class="conter">
      <div class="one">
        <h2 class="title">角色列表</h2>
        <el-scrollbar style="height: 90%">
          <div
            v-for="item in roleLIst"
            v-if="item.roleId !== 6"
            :key="item.roleId"
            class="roleLIst"
            :class="{ active: roleLIstId == item.roleId }"
            @click="
              roleLIstId = item.roleId
              $refs.tree.setCurrentKey(null)
              permissionMenu = null
            "
          >
            {{ item.roleName }}
            <div v-if="item.allowDel == 1" class="delect el-icon-close" @click="delectRole(item.roleId)" />
          </div>
        </el-scrollbar>
      </div>
      <div class="three">
        <div class="three-content">
          <div class="three-content-header">
            <h2 class="title">用户权限菜单</h2>
            <el-button size="small" @click="expandAllNodes(true)">展开</el-button>
            <el-button size="small" @click="expandAllNodes(false)">收起</el-button>
          </div>
          <el-button
            type="primary"
            size="mini"
            style="position: absolute; top: 10px; right: 20px"
            @click="savePermission"
          >
            保存</el-button>
          <!--  -->
          <el-scrollbar style="height: 90%">
            <el-tree
              ref="tree"
              :data="data"
              show-checkbox
              node-key="id"
              highlight-current
              :props="defaultProps"
              :check-strictly="true"
              default-expand-all
            />
          </el-scrollbar>
        </div>
      </div>
    </div>

    <!-- 新增角色 -->
    <el-dialog
      v-el-drag-dialog
      title="新增角色"
      :visible.sync="addRoleShow"
      width="700px"
      class="el-big-dialog"
      :close-on-click-modal="false"
      :before-close="addRoleClose"
    >
      <span slot="title" class="dialog-title">
        <img src="@/assets/common_images/adduser.png" alt="">
        <span>新增角色</span>
      </span>
      <!-- <p>角色名称</p>
      <el-input v-model="roleName" placeholder="请输入角色名称"></el-input> -->
      <el-form ref="roleForm" :model="addRoleForm" label-width="100px" class="demo-dynamic" label-position="top">
        <el-form-item
          prop="roleName"
          label="角色名称"
          :rules="[{ required: true, message: '请输入角色名称', trigger: 'blur' }]"
        >
          <el-input v-model="addRoleForm.roleName" maxlength="16" show-word-limit />
        </el-form-item>
        <el-form-item
          prop="roleCode"
          label="角色编码"
          :rules="[{ required: true, message: '请输入角色编码', trigger: 'blur' }]"
        >
          <el-input v-model="addRoleForm.roleCode" maxlength="16" show-word-limit />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="danger"
          style="margin-right: 16px; margin-top: 20px; width: 140px"
          @click="addRoleClose"
        >取消
        </el-button>
        <el-button
          type="primary"
          style="margin-right: 30px; margin-top: 20px; width: 140px"
          @click="roleSubmitForm('roleForm')"
        >保存
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getRole, addRole, getdeleteUser, postroleMenuApi } from '@/api/system'
import { getMenuList, getMenuByRole } from '@/api/menu'
import elDragDialog from '@/directive/el-drag-dialog'

export default {
  name: 'User',
  directives: {
    elDragDialog
  },
  data() {
    return {
      addJobShow: false,
      addRoleShow: false,
      roleLIst: [],
      roleLIstId: '',
      userLIst: [],
      roleCheckList: [],
      permissionCheckList: [],
      data: [],
      defaultProps: {
        children: 'children',
        label: 'name',
        value: 'id'
      },
      addRoleForm: { roleName: '', roleCode: '' },
      permissionMenu: [],
      permissionTreeId: null,
      operationPermissions: null
    }
  },
  watch: {
    roleLIstId: {
      handler(newval, oldval) {
        this.getMenuByRoleFn(newval)
      }
    }
  },
  created() {
    this.getData()
  },
  methods: {
    async savePermission() {
      const checkedKeys = this.$refs.tree.getCheckedKeys()
      const params = { menuIds: checkedKeys, operationIds: [], roleId: this.roleLIstId }
      const res = await postroleMenuApi(params)
      this.getMenuByRoleFn(this.roleLIstId)
      if (`${res.code}` === '200') {
        this.$message({
          message: '恭喜你,权限修改成功，请重新登录！',
          type: 'success'
        })
      }
    },
    async getData() {
      const res = await getMenuList()
      const arr = []
      res.data.forEach((e) => {
        if (e.path !== '/system') {
          arr.push(e)
        }
      })
      this.data = arr
      this.getRoleList().then(() => (this.roleLIstId = this.roleLIst[0].roleId))
    },

    async getRoleList() {
      const res = await getRole({ see: 1 })
      this.roleLIst = res.data
    },

    delectRole(id) {
      this.$confirm('此操作将永久删除该角色, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          const res = await getdeleteUser(id)
          if (`${res.code}` === '200') {
            this.getRoleList()
            this.roleLIstId = this.roleLIst[0].roleId
            this.$message({
              type: 'success',
              message: '删除成功'
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },

    async addRoleUser() {
      await addRole({
        roleName: this.addRoleForm.roleName,
        roleCode: this.addRoleForm.roleCode,
        allowDel: 1
      })
      this.getRoleList()
    },

    // 根据角色获取菜单
    async getMenuByRoleFn(id) {
      const res = await getMenuByRole(id)
      const checkedKeys = res.data.checkedKeys || []
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys(checkedKeys)
      })
    },

    roleSubmitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.addRoleUser()
          this.addRoleShow = false
          this.addRoleForm.roleName = ''
          this.addRoleForm.roleCode = ''
        } else {
          return false
        }
      })
    },

    addRoleClose() {
      this.addRoleShow = false
      this.addRoleForm.roleName = ''
      this.addRoleForm.roleCode = ''
      this.$refs.roleForm.resetFields()
    },

    expandAllNodes(expand) {
      const { tree } = this.$refs
      if (!tree) return

      // 遍历所有节点，展开或收起
      tree.store._getAllNodes().forEach((node) => {
        node.expanded = expand
      })
    }
  }
}
</script>
<style scoped lang="scss">
.casting {
  width: 96%;
  // height: 950px;
  background-color: #fff;
  margin: 30px auto;

  .top {
    width: 100%;
    height: 100px;
    line-height: 100px;
    text-align: left;
    padding: 0 30px;
    border-bottom: 1px solid #f0f0f0;
  }

  .conter {
    padding: 15px;
    display: flex;
    // justify-content: space-between;
    gap: 50px;
    color: #333;

    .one,
    .two,
    .four,
    .three {
      // flex-grow: 1;
      width: 25%;
      border: 1px solid #e7e7e7;
      border-radius: 10px;
      margin: 8px;
      height: 700px;
      position: relative;
    }

    .one {
      .roleLIst {
        width: 100%;
        height: 50px;
        padding-left: 25px;
        padding-right: 20px;
        line-height: 50px;
        border-left: 4px solid transparent;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .delect {
          font-size: 0.7rem;
        }
      }

      .active {
        background-color: #ebf7f5;
        border-left: 4px solid #0a86c8;
        color: #0a86c8;
      }
    }

    .two {
      .userLIst {
        width: 100%;
        height: 40px;
        padding-left: 25px;
        line-height: 40px;
        cursor: pointer;
      }

      .active {
        background-color: #ecf2ff;
        border-radius: 10px;
        color: #5a8bed;
      }
    }

    .three {
      // flex-grow: 2;
      width: 45%;
      border: 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      span {
        white-space: nowrap;
      }

      .three-top {
        border: 1px solid #e7e7e7;
        border-radius: 10px;
        height: 6%;
        display: flex;
        align-items: center;

        .active {
          border: 1px solid #2d8cf0 !important;
        }

        .checkbox:nth-of-type(1) {
          border-bottom-left-radius: 5px;
          border-top-left-radius: 5px;
        }

        .checkbox {
          font-size: 0.6rem;
          padding: 5px 20px;
          border: 1px solid #dddee1;
          border-right: 1px solid transparent;
          position: relative;
          cursor: pointer;
          // width: 100px;
          display: inline-block;
          white-space: nowrap;
          // text-overflow: ellipsis;
          // overflow: hidden;

          &:last-child {
            border-bottom-right-radius: 5px;
            border-top-right-radius: 5px;
            border-right: 1px solid #dddee1;
          }

          &:hover .delect {
            display: block;
          }

          .delect {
            position: absolute;
            font-size: 0.5rem;
            padding: 1px;
            background-color: #5a8bed;
            border-radius: 50%;
            color: #fff;
            right: 4px;
            top: 2px;
            text-align: center;
            display: none;

            &:hover {
              background-color: #f56c6c;
            }
          }
        }
      }

      .three-content {
        border: 1px solid #e7e7e7;
        border-radius: 10px;
        height: 100%;
        .three-content-header {
          display: flex;
          align-items: center;
          ::v-deep .el-button {
            height: 30px !important;
            width: 50px !important;
            line-height: 30px !important;
            font-size: 12px !important;
            padding: 0 !important;
          }
        }
      }
    }

    .title {
      font-size: 0.65rem;
      font-weight: 400;
      margin: 0 15px 0 15px;
      line-height: 50px;
    }
  }

  ::v-deep .el-form-item__label {
    font-weight: 400;
    font-size: 1.1em;
  }
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}

::v-deep .my-scrollbar {
  .el-scrollbar__wrap {
    overflow-y: hidden;
    //   overflow-x: auto;
    // height: 100%; //多出来的20px是横向滚动条默认的样式
  }

  .el-scrollbar__wrap .el-scrollbar__view {
    white-space: nowrap;
    display: inline-block;
  }
}
::v-deep .el-tree-node__label {
  font-size: 0.6rem;
}
::v-deep .el-checkbox__label {
  font-size: 0.6rem;
}
</style>
