<template>
  <div class="AudioVisual">
    <i v-if="playing" class="el-icon-video-pause" @click="playMusic" />
    <i v-else class="el-icon-video-play" @click="playMusic" />
    <div ref="waveform" class="waveform" />
    <div class="time">
      {{ getTimeformat(currentTime) }} /
      {{ getTimeformat(duration) }}
    </div>
  </div>
</template>
<script>
import WaveSurfer from 'wavesurfer.js'

export default {
  name: 'AudioVisual',
  //   components: {
  //     AVWaveform
  //   },
  props: {
    recordingData: {
      type: Object,
      default: () => {
        return null
      }
    }
  },
  data() {
    return {
      wavesurfer: null,
      playing: false,
      duration: 0,
      currentTime: 0
    }
  },
  computed: {
    fz() {
      return this.$store.getters.fz
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initAudio()
    })
  },
  methods: {
    initAudio() {
      this.wavesurfer = WaveSurfer.create({
        // 需要的容器盒子
        container: this.$refs.waveform,
        // 是否出现滚动条
        scrollParent: true,
        // 播放进行时线条颜色
        cursorColor: 'red',
        // 播放进行时线条宽度
        cursorWidth: 2,
        // 未播放的波纹颜色
        waveColor: '#DEDEDE',
        // 已播放的波纹颜色
        progressColor: '#5FA6E7',
        // 倍速
        audioRate: 1,
        height: 2.5 * this.fz
      })
      // 加载音频文件
      if (this.recordingData) {
        this.wavesurfer.load(this.recordingData.fileInfo.url)
      }
      this.wavesurfer.on('ready', () => {
        console.log('加载音频文件  ready!!!')
        this.duration = this.wavesurfer.getDuration()
      })
      //   audioprocess–音频播放时连续发射。搜寻时也会触发。
      this.wavesurfer.on('audioprocess', () => {
        this.currentTime = this.wavesurfer.getCurrentTime()
      })
      this.wavesurfer.on('click', () => {
        console.log('click ')
        this.currentTime = this.wavesurfer.getCurrentTime()
      })
      //   interaction –与波形有相互作用时。
      //   this.wavesurfer.on('interaction', () => {
      //     console.log('interaction  ')
      //     this.currentTime = this.wavesurfer.getCurrentTime()
      //   })
      // finish –完成播放时。
      this.wavesurfer.on('finish', () => {
        console.log('finish  ')
        this.playing = false
      })
    },
    playMusic() {
      console.log('wavesurfer', this.wavesurfer)

      this.wavesurfer.playPause.bind(this.wavesurfer)()
      this.playing = !this.playing
    },
    audioStop() {
      this.wavesurfer.pause()
      this.playing = false
    },
    // 秒数转 时分秒
    getTimeformat(time) {
      // 转换为式分秒
      let ms = parseInt(time * 100 - parseInt(time) * 100)
      let h = parseInt((time / 60 / 60) % 24)
      h = h < 10 ? `0${h}` : h
      let m = parseInt((time / 60) % 60)
      m = m < 10 ? `0${m}` : m
      let s = parseInt(time % 60)
      s = s < 10 ? `0${s}` : s
      ms = ms < 10 ? `0${ms}` : ms
      // 作为返回值返回
      // return [h, m, s]
      if (h !== '00') {
        return `${h}:${m}:${s}.${ms}`
      } else {
        return `${m}:${s}.${ms}`
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.AudioVisual {
  width: 100%;
  height: 3rem;
  background: #ffffff;
  box-shadow: 0px 3px 16px 0px rgba(9, 76, 1, 0.1);
  border-radius: 0.3rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.8rem;
  padding: 0 1rem;
  i {
    font-size: 1.5rem;
    background: linear-gradient(to right, #00b2dd, #40e1d3); /*设置渐变的方向从左到右 颜色从ff0000到ffff00*/
    -webkit-background-clip: text; /*将设置的背景颜色限制在文字中*/
    -webkit-text-fill-color: transparent; /*给文字设置成透明*/
    cursor: pointer;
    flex-shrink: 0;
  }
  .waveform {
    flex-grow: 1;
    margin: 0 0.5rem;
  }
  .time {
    width: 8rem;
    font-weight: 400;
    font-size: 0.8rem;
    color: #666666;
    line-height: 22px;
    text-align: right;
    flex-shrink: 0;
  }
}
</style>
