<template>
  <div class="pageContainer">
    <div class="topTabBox">
      <div class="tabItem" :class="{ 'activeTab': `${topActiveTab}` === '0' }" @click="changeTopActiveTab('0')">
        测量日志
      </div>
      <div class="tabItem" :class="{ 'activeTab': `${topActiveTab}` === '1' }" @click="changeTopActiveTab('1')">
        操作日志
      </div>
      <div class="tabItem" :class="{ 'activeTab': `${topActiveTab}` === '2' }" @click="changeTopActiveTab('2')">
        系统日志
      </div>
      <div class="tabItem" :class="{ 'activeTab': `${topActiveTab}` === '3' }" @click="changeTopActiveTab('3')">
        服务日志
      </div>
    </div>
    <div v-show="topActiveTab === '0'" class="mainContentBox">
      <div class="topDateFilter">
        <el-radio-group v-model="dateChangeTab" class="dateFilterRadioGroup" size="mini" @change="getDataSplit">
          <el-radio-button label="all">全部<span class="countBox">2135</span></el-radio-button>
          <el-radio-button label="today">今天<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="lastDay">昨天<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="toWeek">本周<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="lastWeek">上周<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="toMonth">本月<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="lastMonth">上月<span class="countBox">21</span></el-radio-button>
        </el-radio-group>
        <div class="legendBox">
          <span class="lengendItemBox"> <span class="lengendDot dotUpdate" />修改 </span>
          <span class="lengendItemBox"> <span class="lengendDot dotInsert" />添加 </span>
          <span class="lengendItemBox"> <span class="lengendDot dotDelete" />删除 </span>
        </div>
      </div>
      <div class="listContent">
        <div class="searchBox">
          <el-form
            :inline="true"
            :model="searchData"
            class="applicationCustomSearchForm"
            label-width="4vw"
            label-position="left"
          >
            <el-col class="searchFormItem">
              <el-form-item label="操作人">
                <ProInput
                  v-model="searchData.doctorName"
                  placeholder="请输入"
                  class="selectSelf"
                  clearable
                  @debounce="getDataSplit"
                >
                  <i slot="prefix" class="el-input__icon el-icon-search" />
                </ProInput>
              </el-form-item>
            </el-col>
            <el-col class="searchFormItem">
              <el-form-item label="操作类型">
                <el-select
                  v-model="searchData.operation"
                  class="selectSelf"
                  clearable
                  placeholder="请选择"
                  @change="getDataSplit"
                >
                  <el-option
                    v-for="(label, key) of OPERATE_TYPE_ENUM"
                    :key="'enum-' + key"
                    :label="label"
                    :value="key"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col class="searchFormItem">
              <el-form-item label="患者姓名">
                <ProInput
                  v-model="searchData.patientName"
                  placeholder="请输入"
                  class="selectSelf"
                  clearable
                  @debounce="getDataSplit"
                >
                  <i slot="prefix" class="el-input__icon el-icon-search" />
                </ProInput>
              </el-form-item>
            </el-col>
            <el-col class="searchFormItem">
              <el-form-item label="模块">
                <el-select
                  v-model="searchData.module"
                  class="selectSelf"
                  clearable
                  placeholder="请选择"
                  @change="getDataSplit"
                >
                  <el-option
                    v-for="(label, key) of PATIENT_EXAM_FOLLOWUP_MODULE_CODE_ENUM"
                    :key="'enum-' + key"
                    :label="label"
                    :value="key"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col class="searchFormItem searchFormItemLong">
              <el-form-item label-width="4vw" label="时间范围">
                <el-date-picker
                  v-model="searchData.startDate"
                  class="selectSelf mr20"
                  type="date"
                  placeholder="开始时间"
                  format="yyyy/MM/dd"
                  value-format="yyyy-MM-dd"
                  default-time="12:00:00"
                  @change="getDataSplit"
                />
                <el-date-picker
                  v-model="searchData.endDate"
                  class="selectSelf"
                  type="date"
                  placeholder="结束时间"
                  format="yyyy/MM/dd"
                  value-format="yyyy-MM-dd"
                  default-time="12:00:00"
                  @change="getDataSplit"
                />
              </el-form-item>
            </el-col>
            <el-col class="searchFormBtnBox">
              <el-button type="primary" plain @click="handleReset">重置</el-button>
              <el-button type="primary" @click="getDataSplit">搜索</el-button>
            </el-col>
          </el-form>
        </div>
        <div v-loading="measurementLogLoading">
          <div class="listBox">
            <noData v-if="JSON.stringify(listDataMap) === '{}'" />
            <div
              v-for="(itemValue, itemKey) of listDataMap"
              :key="itemKey"
              class="timelineItem"
              :class="{ 'hideItem': itemValue.hide }"
            >
              <div
                v-for="(timelineItem, timelineIndex) in itemValue.list"
                :key="itemKey + '-' + timelineIndex"
                :class="{ 'timelineFirstItem': timelineIndex === 0, 'timelineOtherItem': timelineIndex !== 0 }"
              >
                <div
                  class="leftDateBox"
                  :class="{ 'hasRightConnect': timelineIndex === 0, 'hiddenLeft': timelineIndex !== 0 }"
                  @click="handleToggle(itemKey, timelineIndex)"
                >
                  <span v-if="timelineIndex === 0" class="dateTextBox">{{ itemKey }}</span>
                  <span v-if="timelineIndex === 0" class="toggleIconBox">
                    <i class="el-icon-caret-top" :class="{ rotate180: itemValue.hide }" />
                  </span>
                </div>
                <div v-if="!itemValue.isSys && topActiveTab + '' === '0'" class="rightTimelineBox">
                  <span class="timeTextBox">{{ timelineItem.createTime | date2HI }}</span>
                  <span class="timelineContentBox">
                    医生-{{ timelineItem.createUsername }}在
                    {{ OPERATE_FROM_TYPE_ENUM[timelineItem.platform || JSON.parse(timelineItem.requestParam).from] }}
                    <span :class="'operateBox ' + 'type-' + timelineItem.operation">
                      {{ OPERATE_TYPE_ENUM[timelineItem.operation] }}了
                    </span>
                    患者<span v-if="timelineItem.patientName">“{{ timelineItem.patientName }}”</span>
                    {{ timelineItem.datetime | date2YMDsHI }}的
                    {{ PATIENT_EXAM_FOLLOWUP_MODULE_CODE_ENUM[timelineItem.module] }}
                    <span v-if="timelineItem.itemName">{{ timelineItem.itemName }}</span>
                    <span v-else-if="timelineItem.itemCode">{{ ITEM_CODE_DICT[timelineItem.itemCode] }}</span>
                    数据
                    <span
                      v-if="
                        (ITEM_LIST.includes(timelineItem.itemCode) || ITEMNAME_LIST.includes(timelineItem.itemName)) &&
                          isObjValue(timelineItem.valueData)
                      "
                      v-html="renderLogValueTag(timelineItem.itemCode, timelineItem.valueData, timelineItem.itemName)"
                    />
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-show="topActiveTab === '1'" class="mainContentBox">
      <div class="topDateFilter">
        <el-radio-group v-model="dateChangeTabs" class="dateFilterRadioGroup" size="mini" @change="getDataSplit">
          <el-radio-button label="all">全部<span class="countBox">2135</span></el-radio-button>
          <el-radio-button label="today">今天<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="lastDay">昨天<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="toWeek">本周<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="lastWeek">上周<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="toMonth">本月<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="lastMonth">上月<span class="countBox">21</span></el-radio-button>
        </el-radio-group>
        <div class="legendBox">
          <span class="lengendItemBox"> <span class="lengendDot dotUpdate" />修改 </span>
          <span class="lengendItemBox"> <span class="lengendDot dotInsert" />添加 </span>
          <span class="lengendItemBox"> <span class="lengendDot dotDelete" />删除 </span>
        </div>
      </div>
      <div class="listContent">
        <div class="searchBox">
          <el-form
            :inline="true"
            :model="setData"
            class="applicationCustomSearchForm"
            label-width="4vw"
            label-position="left"
          >
            <el-col class="searchFormItem">
              <el-form-item label="操作人">
                <ProInput
                  v-model="setData.doctorName"
                  placeholder="请输入"
                  class="selectSelf"
                  clearable
                  @debounce="getDataSplit"
                >
                  <i slot="prefix" class="el-input__icon el-icon-search" />
                </ProInput>
              </el-form-item>
            </el-col>
            <el-col class="searchFormItem">
              <el-form-item label="操作类型">
                <el-select
                  v-model="setData.type"
                  class="selectSelf"
                  clearable
                  placeholder="请选择"
                  @change="getDataSplit"
                >
                  <el-option
                    v-for="(label, key) of OPERATE_TYPE_ENUM"
                    :key="'enum-' + key"
                    :label="label"
                    :value="key"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col class="searchFormItem">
              <el-form-item label="患者姓名">
                <ProInput
                  v-model="setData.patientName"
                  placeholder="请输入"
                  class="selectSelf"
                  clearable
                  @debounce="getDataSplit"
                >
                  <i slot="prefix" class="el-input__icon el-icon-search" />
                </ProInput>
              </el-form-item>
            </el-col>
            <el-col class="searchFormItem">
              <el-form-item label="模块">
                <el-select
                  v-model="setData.module"
                  class="selectSelf"
                  clearable
                  placeholder="请选择"
                  @change="getDataSplit"
                >
                  <el-option label="健康档案" value="健康档案" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col class="searchFormItem searchFormItemLong">
              <el-form-item label-width="4vw" label="时间范围">
                <el-date-picker
                  v-model="setData.startDate"
                  class="selectSelf mr20"
                  type="date"
                  placeholder="开始时间"
                  format="yyyy/MM/dd"
                  value-format="yyyy-MM-dd"
                  default-time="12:00:00"
                  @change="getDataSplit"
                />
                <el-date-picker
                  v-model="setData.endDate"
                  class="selectSelf"
                  type="date"
                  placeholder="结束时间"
                  format="yyyy/MM/dd"
                  value-format="yyyy-MM-dd"
                  default-time="12:00:00"
                  @change="getDataSplit"
                />
              </el-form-item>
            </el-col>
            <el-col class="searchFormBtnBox">
              <el-button type="primary" plain @click="handleReset">重置</el-button>
              <el-button type="primary" @click="getDataSplit">搜索</el-button>
            </el-col>
          </el-form>
        </div>
        <div v-loading="operationLogLoading">
          <div class="listBox">
            <noData v-if="JSON.stringify(Operationlogs) === '{}'" />
            <div
              v-for="(item, index) of Operationlogs"
              :key="index"
              class="timelineItem"
              :class="{ 'hideItem': item.hide }"
            >
              <div
                v-for="(timelineItem, timelineIndex) in item.list"
                :key="index + '-' + timelineIndex"
                :class="{ 'timelineFirstItem': timelineIndex === 0, 'timelineOtherItem': timelineIndex !== 0 }"
              >
                <div
                  class="leftDateBox"
                  :class="{ 'hasRightConnect': timelineIndex === 0, 'hiddenLeft': timelineIndex !== 0 }"
                  @click="handleToggleFun(index, timelineIndex)"
                >
                  <span v-if="timelineIndex === 0" class="dateTextBox">{{ index }}</span>
                  <span v-if="timelineIndex === 0" class="toggleIconBox">
                    <i class="el-icon-caret-top" :class="{ rotate180: item.hide }" />
                  </span>
                </div>
                <div v-if="!item.isSys && topActiveTab + '' === '1'" class="rightTimelineBox">
                  <span class="timeTextBox">{{ timelineItem.createTime.substring(11, 16) }}</span>
                  <span class="timelineContentBox">
                    医生-{{ timelineItem.createUsername }}在网页端
                    <span :class="'operateBox ' + 'type-' + timelineItem.type">
                      {{ timelineItem.type === 'INSERT' ? '添加' : timelineItem.type === 'UPDATE' ? '修改' : '删除' }}了
                    </span>
                    患者
                    <span v-if="JSON.parse(timelineItem.requestParam).username">
                      “{{ JSON.parse(timelineItem.requestParam).username }}” </span>的{{ timelineItem.module }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-show="topActiveTab === '2'" class="mainContentBox">
      <div class="topDateFilter">
        <el-radio-group v-model="dateChangeTabsa" class="dateFilterRadioGroup" size="mini" @change="getDataSplit">
          <el-radio-button label="all">全部<span class="countBox">2135</span></el-radio-button>
          <el-radio-button label="today">今天<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="lastDay">昨天<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="toWeek">本周<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="lastWeek">上周<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="toMonth">本月<span class="countBox">21</span></el-radio-button>
          <el-radio-button label="lastMonth">上月<span class="countBox">21</span></el-radio-button>
        </el-radio-group>
        <!-- <div class="legendBox">
          <span class="lengendItemBox"> <span class="lengendDot dotUpdate" />修改 </span>
          <span class="lengendItemBox"> <span class="lengendDot dotInsert" />添加 </span>
          <span class="lengendItemBox"> <span class="lengendDot dotDelete" />删除 </span>
        </div> -->
      </div>
      <div class="listContent">
        <div class="searchBox">
          <el-form
            :inline="true"
            :model="Loginloges"
            class="applicationCustomSearchForm"
            label-width="4vw"
            label-position="left"
          >
            <el-col class="searchFormItem">
              <el-form-item label="操作人">
                <ProInput
                  v-model="Loginloges.doctorName"
                  placeholder="请输入"
                  class="selectSelf"
                  clearable
                  @debounce="getDataSplit"
                >
                  <i slot="prefix" class="el-input__icon el-icon-search" />
                </ProInput>
              </el-form-item>
            </el-col>
            <el-col class="searchFormItem">
              <el-form-item label="操作类型">
                <el-select
                  v-model="Loginloges.module"
                  class="selectSelf"
                  clearable
                  placeholder="请选择"
                  @change="getDataSplit"
                >
                  <el-option label="全部" :value="''" />
                  <el-option
                    v-for="(label, key) of Dropdown"
                    :key="'enum-' + key"
                    :label="label.module"
                    :value="label.module"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col class="searchFormItem searchFormItemLong">
              <el-form-item label-width="4vw" label="时间范围">
                <el-date-picker
                  v-model="Loginloges.startDate"
                  class="selectSelf mr20"
                  type="date"
                  placeholder="开始时间"
                  format="yyyy/MM/dd"
                  value-format="yyyy-MM-dd"
                  default-time="12:00:00"
                  @change="getDataSplit"
                />
                <el-date-picker
                  v-model="Loginloges.endDate"
                  class="selectSelf"
                  type="date"
                  placeholder="结束时间"
                  format="yyyy/MM/dd"
                  value-format="yyyy-MM-dd"
                  default-time="12:00:00"
                  @change="getDataSplit"
                />
              </el-form-item>
            </el-col>
            <el-col class="searchFormBtnBox">
              <el-button type="primary" plain @click="handleReset">重置</el-button>
              <el-button type="primary" @click="getDataSplit">搜索</el-button>
            </el-col>
          </el-form>
        </div>
        <div v-loading="loginLohLoading">
          <div class="listBox">
            <noData v-if="JSON.stringify(logsData) === '{}'" />
            <div v-for="(item, index) of logsData" :key="index" class="timelineItem" :class="{ 'hideItem': item.hide }">
              <div
                v-for="(timelineItem, timelineIndex) in item.list"
                :key="index + '-' + timelineIndex"
                :class="{ 'timelineFirstItem': timelineIndex === 0, 'timelineOtherItem': timelineIndex !== 0 }"
              >
                <div
                  class="leftDateBox"
                  :class="{ 'hasRightConnect': timelineIndex === 0, 'hiddenLeft': timelineIndex !== 0 }"
                  @click="handleToggleFuns(index, timelineIndex)"
                >
                  <span v-if="timelineIndex === 0" class="dateTextBox">{{ index }}</span>
                  <span v-if="timelineIndex === 0" class="toggleIconBox">
                    <i class="el-icon-caret-top" :class="{ rotate180: item.hide }" />
                  </span>
                </div>
                <div v-if="!item.isSys && topActiveTab + '' === '2'" class="rightTimelineBox">
                  <span class="timeTextBox">{{ timelineItem.createTime.substring(11, 16) }}</span>
                  <span class="timelineContentBox">
                    医生-{{ timelineItem.createUsername }}
                    <span :class="'operateBox '">
                      <!--  + 'type-' + timelineItem.type -->
                      {{ timelineItem.module.includes('登录') ? '登录了' : '退出了' }}
                    </span>
                    系统
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-show="topActiveTab === '3'" v-loading="serviceLogLoading" class="mainContentBox">
      <div class="serverSearchBox">
        <el-form :model="serverSearchData" label-width="100px">
          <el-row :gutter="24">
            <el-col :span="6">
              <el-form-item label="请求人：">
                <el-input v-model="serverSearchData.createUsername" placeholder="请输入请求人" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="请求时间：">
                <el-date-picker
                  v-model="serverSearchData.createTime"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  :clearable="false"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="请求地址：">
                <el-input v-model="serverSearchData.url" placeholder="请输入请求地址" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="">
                <el-button type="primary" @click="handleSearch">查询</el-button>
                <el-button @click="handleSrarchReset">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <el-table border :data="tableData" style="width: 100%" class="tableBox" height="65vh">
        <el-table-column align="center" type="index" label="序号" width="80" />
        <el-table-column align="center" prop="createUserid" label="请求人Id" />
        <el-table-column align="center" prop="createUsername" label="请求人" />
        <el-table-column align="center" prop="createTime" label="请求时间" />
        <el-table-column align="center" prop="url" label="请求地址" show-overflow-tooltip />
        <el-table-column align="center" prop="duration" label="请求时间（ms）" />
      </el-table>
      <div v-if="tableDatatotal > 0">
        <el-pagination
          :current-page="serverSearchData.pageNo"
          background
          :page-sizes="tablePageSizes"
          :page-size="serverSearchData.pageSize"
          :layout="tablePaginationLayout"
          :total="tableDatatotal"
          style="text-align: right; margin: 1rem 0 0"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { parseTime, isObjValue } from '@/utils'
import { PATIENT_EXAM_FOLLOWUP_MODULE_CODE_ENUM, OPERATE_FROM_TYPE_ENUM, OPERATE_TYPE_ENUM } from '@/utils/enum'
import { renderLogValueTag } from './libs/render.js'
import { Loginlogs, Getdropdown } from '@/api/dashboard'
import { logPageByModel, getServiceLogApi } from '@/api/system'
import ProInput from '@/components/ProInput/index.vue'
import dayjs from 'dayjs'

export default {
  name: 'LogCombinedBoard',
  components: { ProInput },
  data() {
    const today = parseTime(new Date(), '{y}-{m}-{d}')
    return {
      PATIENT_EXAM_FOLLOWUP_MODULE_CODE_ENUM,
      OPERATE_FROM_TYPE_ENUM,
      OPERATE_TYPE_ENUM,
      renderLogValueTag,

      ITEM_CODE_DICT: {
        lung: '肺功能'
      },
      ITEM_LIST: [
        'uri',
        'd2Polymers',
        'bloodRoutine',
        'bloodPressure',
        'bloodGlucose',
        'lung',
        'HandW',
        'ksData',
        'BNP',
        'bodyTemperature',
        'Arteriosclerosis',
        'hba1c',
        'QDS'
      ],
      ITEMNAME_LIST: ['电解质', '肾功能', '肝功能'],
      NO_ITEM_LIST: ['auscultation', 'xxxx', 'photo', 'recording'],

      // 0-业务日志 1-系统日志
      topActiveTab: '0',
      // 时间筛选变更
      dateChangeTab: 'today',
      dateChangeTabs: 'today',
      dateChangeTabsa: 'today',
      // 筛选条件
      searchData: {
        startDate: today,
        endDate: today,
        // 操作人
        doctorName: '',
        // 模块 就诊:REG 体检:BODY-CHECK 随访:VISIT
        module: '',
        // 操作筛选 修改|添加|删除/结束
        operation: '',
        // 检查项
        itemName: '',
        pageNo: 1,
        pageSize: 9999,
        patientName: ''
      },
      listDataMap: {},
      Operationlogs: {},
      logsData: {},
      ListDataTime: [],
      // 登录
      Operations: [],
      Dropdown: [],
      tableData: [],
      setData: {
        login: '',
        module: '',
        startDate: today,
        endDate: today,
        type: '',
        patientName: '',
        doctorName: '',
        pageNo: 1,
        pageSize: 9999
      },
      Loginloges: {
        login: true,
        module: '',
        startDate: today,
        endDate: today,
        type: '',
        patientName: '',
        doctorName: '',
        pageNo: 1,
        pageSize: 9999
      },
      // loading
      measurementLogLoading: false,
      operationLogLoading: false,
      loginLohLoading: false,
      serviceLogLoading: false,
      tableDatatotal: 0,
      serverSearchData: {
        pageNo: 1,
        pageSize: 20,
        createTime: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        createUsername: '',
        url: ''
      }
    }
  },
  watch: {
    dateChangeTab(val) {
      this.handleDateRangeChange(val)
    },
    dateChangeTabs(val) {
      this.handleDateRangeChanges(val)
    },
    dateChangeTabsa(val) {
      this.handleDateRangeChangesa(val)
    }
  },
  created() {
    this.getDataSplit()
  },
  methods: {
    isObjValue,
    // 顶部模块切换
    changeTopActiveTab(val) {
      this.topActiveTab = val
      if (val === '1') {
        this.setData.login = false
        this.setData.module = ''
        this.setData.type = ''
        this.setData.patientName = ''
        this.setData.doctorName = ''
        this.setData.pageNo = 1
        this.setData.pageSize = 9999
        this.getOperationlogs()
      } else if (val === '2') {
        this.Loginloges.login = true
        this.Loginloges.module = ''
        this.Loginloges.type = ''
        this.Loginloges.patientName = ''
        this.Loginloges.doctorName = ''
        this.Loginloges.pageNo = 1
        this.Loginloges.pageSize = 9999
        this.Getloginlogs()
      } else if (val === '0') {
        this.searchData.module = ''
        this.searchData.operation = ''
        this.searchData.itemName = ''
        this.searchData.patientName = ''
        this.searchData.doctorName = ''
        this.searchData.pageNo = 1
        this.searchData.pageSize = 9999
        this.getList()
      } else if (val === '3') {
        this.getServiceLogFn()
      }
    },

    // 服务日志查询
    async getServiceLogFn() {
      this.serviceLogLoading = true
      const params = {
        pageNo: this.serverSearchData.pageNo,
        pageSize: this.serverSearchData.pageSize,
        startDate: this.serverSearchData.createTime[0],
        endDate: this.serverSearchData.createTime[1],
        username: this.serverSearchData.createUsername,
        url: this.serverSearchData.url
      }
      try {
        const res = await getServiceLogApi(params)
        if (res.code === 200) {
          this.tableData = res.data.list
          this.tableDatatotal = res.data.total || 0
        }
      } catch (error) {
        console.error('服务日志查询失败', error)
      } finally {
        this.serviceLogLoading = false
      }
    },

    handleSizeChange(val) {
      this.serverSearchData.pageSize = val
      this.getServiceLogFn()
    },

    handleCurrentChange(val) {
      this.serverSearchData.pageNo = val
      this.getServiceLogFn()
    },

    handleSearch() {
      this.serverSearchData.pageNo = 1
      this.getServiceLogFn()
    },

    handleSrarchReset() {
      this.serverSearchData = {
        pageNo: 1,
        pageSize: 20,
        createTime: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        createUsername: '',
        url: ''
      }
      this.getServiceLogFn()
    },

    // 时间范围筛选变更
    handleDateRangeChange(val) {
      const nowDate = new Date()
      let firstDate,
        lastDate,
        dayOfWeek,
        totalDateOfMonth,
        currMonth
      switch (val) {
        case 'all':
          this.searchData.startDate = ''
          this.searchData.endDate = ''
          break
        case 'today':
          firstDate = nowDate
          lastDate = nowDate
          break
        case 'lastDay':
          nowDate.setDate(nowDate.getDate() - 1)
          firstDate = nowDate
          lastDate = nowDate
          break
        case 'toWeek':
          // 获取今天是星期几
          dayOfWeek = nowDate.getDay()
          // 获取本周的周一
          firstDate = new Date(nowDate.getTime())
          firstDate.setDate(firstDate.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1))
          // 获取本周的周日
          lastDate = new Date(firstDate)
          lastDate.setDate(lastDate.getDate() + 6)
          break
        case 'lastWeek':
          // 获取今天是星期几
          dayOfWeek = nowDate.getDay()
          // 获取本周的周一
          firstDate = new Date(nowDate.getTime())
          firstDate.setDate(firstDate.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1) - 7)
          // 获取本周的周日
          lastDate = new Date(firstDate)
          lastDate.setDate(lastDate.getDate() + 6)
          break
        case 'toMonth':
          // 获取本月多少天
          totalDateOfMonth = new Date(nowDate.getFullYear(), nowDate.getMonth() + 1, 0).getDate()
          // 获取本月第一天
          firstDate = new Date(nowDate.getTime())
          firstDate.setDate(1)
          // 获取本月的最后一天
          lastDate = new Date(firstDate)
          lastDate.setDate(totalDateOfMonth)
          break
        case 'lastMonth':
          // 获取上个月多少天
          currMonth = nowDate.getMonth()
          totalDateOfMonth = new Date(nowDate.getFullYear(), nowDate.getMonth(), 0).getDate()
          // 获取本月第一天
          firstDate = new Date(nowDate.getTime())
          firstDate.setMonth(currMonth - 1)
          firstDate.setDate(1)
          // 获取本月的最后一天
          lastDate = new Date(firstDate)
          lastDate.setDate(totalDateOfMonth)
          break
        default:
          break
      }
      this.searchData.startDate = parseTime(firstDate, '{y}-{m}-{d}')
      this.searchData.endDate = parseTime(lastDate, '{y}-{m}-{d}')
    },
    handleDateRangeChanges(val) {
      const nowDate = new Date()
      let firstDate,
        lastDate,
        dayOfWeek,
        totalDateOfMonth,
        currMonth
      switch (val) {
        case 'all':
          this.setData.startDate = ''
          this.setData.endDate = ''
          break
        case 'today':
          firstDate = nowDate
          lastDate = nowDate
          break
        case 'lastDay':
          nowDate.setDate(nowDate.getDate() - 1)
          firstDate = nowDate
          lastDate = nowDate
          break
        case 'toWeek':
          // 获取今天是星期几
          dayOfWeek = nowDate.getDay()
          // 获取本周的周一
          firstDate = new Date(nowDate.getTime())
          firstDate.setDate(firstDate.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1))
          // 获取本周的周日
          lastDate = new Date(firstDate)
          lastDate.setDate(lastDate.getDate() + 6)
          break
        case 'lastWeek':
          // 获取今天是星期几
          dayOfWeek = nowDate.getDay()
          // 获取本周的周一
          firstDate = new Date(nowDate.getTime())
          firstDate.setDate(firstDate.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1) - 7)
          // 获取本周的周日
          lastDate = new Date(firstDate)
          lastDate.setDate(lastDate.getDate() + 6)
          break
        case 'toMonth':
          // 获取本月多少天
          totalDateOfMonth = new Date(nowDate.getFullYear(), nowDate.getMonth() + 1, 0).getDate()
          // 获取本月第一天
          firstDate = new Date(nowDate.getTime())
          firstDate.setDate(1)
          // 获取本月的最后一天
          lastDate = new Date(firstDate)
          lastDate.setDate(totalDateOfMonth)
          break
        case 'lastMonth':
          // 获取上个月多少天
          currMonth = nowDate.getMonth()
          totalDateOfMonth = new Date(nowDate.getFullYear(), nowDate.getMonth(), 0).getDate()
          // 获取本月第一天
          firstDate = new Date(nowDate.getTime())
          firstDate.setMonth(currMonth - 1)
          firstDate.setDate(1)
          // 获取本月的最后一天
          lastDate = new Date(firstDate)
          lastDate.setDate(totalDateOfMonth)
          break
        default:
          break
      }
      this.setData.startDate = parseTime(firstDate, '{y}-{m}-{d}')
      this.setData.endDate = parseTime(lastDate, '{y}-{m}-{d}')
    },
    handleDateRangeChangesa(val) {
      const nowDate = new Date()
      let firstDate,
        lastDate,
        dayOfWeek,
        totalDateOfMonth,
        currMonth
      switch (val) {
        case 'all':
          this.Loginloges.startDate = ''
          this.Loginloges.endDate = ''
          break
        case 'today':
          firstDate = nowDate
          lastDate = nowDate
          break
        case 'lastDay':
          nowDate.setDate(nowDate.getDate() - 1)
          firstDate = nowDate
          lastDate = nowDate
          break
        case 'toWeek':
          // 获取今天是星期几
          dayOfWeek = nowDate.getDay()
          // 获取本周的周一
          firstDate = new Date(nowDate.getTime())
          firstDate.setDate(firstDate.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1))
          // 获取本周的周日
          lastDate = new Date(firstDate)
          lastDate.setDate(lastDate.getDate() + 6)
          break
        case 'lastWeek':
          // 获取今天是星期几
          dayOfWeek = nowDate.getDay()
          // 获取本周的周一
          firstDate = new Date(nowDate.getTime())
          firstDate.setDate(firstDate.getDate() - (dayOfWeek === 0 ? 6 : dayOfWeek - 1) - 7)
          // 获取本周的周日
          lastDate = new Date(firstDate)
          lastDate.setDate(lastDate.getDate() + 6)
          break
        case 'toMonth':
          // 获取本月多少天
          totalDateOfMonth = new Date(nowDate.getFullYear(), nowDate.getMonth() + 1, 0).getDate()
          // 获取本月第一天
          firstDate = new Date(nowDate.getTime())
          firstDate.setDate(1)
          // 获取本月的最后一天
          lastDate = new Date(firstDate)
          lastDate.setDate(totalDateOfMonth)
          break
        case 'lastMonth':
          // 获取上个月多少天
          currMonth = nowDate.getMonth()
          totalDateOfMonth = new Date(nowDate.getFullYear(), nowDate.getMonth(), 0).getDate()
          // 获取本月第一天
          firstDate = new Date(nowDate.getTime())
          firstDate.setMonth(currMonth - 1)
          firstDate.setDate(1)
          // 获取本月的最后一天
          lastDate = new Date(firstDate)
          lastDate.setDate(totalDateOfMonth)
          break
        default:
          break
      }
      this.Loginloges.startDate = parseTime(firstDate, '{y}-{m}-{d}')
      this.Loginloges.endDate = parseTime(lastDate, '{y}-{m}-{d}')
    },
    handleReset() {
      if (this.topActiveTab === '0') {
        this.dateChangeTab = 'all'
        this.searchData = {
          startDate: '',
          endDate: '',
          // 操作人
          doctorName: '',
          // 模块 就诊:REG 体检:BODY-CHECK 随访:VISIT
          module: '',
          // 操作筛选 修改|添加|删除/结束
          operation: '',
          // 检查项
          itemName: '',
          pageNo: 1,
          pageSize: 9999
        }
      } else if (this.topActiveTab === '1') {
        this.dateChangeTabs = 'all'
        this.setData = {
          login: false,
          module: '',
          startDate: '',
          endDate: '',
          type: '',
          patientName: '',
          doctorName: '',
          pageNo: 1,
          pageSize: 9999
        }
      } else {
        this.dateChangeTabsa = 'all'
        this.Loginloges = {
          login: true,
          module: '',
          startDate: '',
          endDate: '',
          type: '',
          patientName: '',
          doctorName: '',
          pageNo: 1,
          pageSize: 9999
        }
      }
      this.getDataSplit()
    },
    async getList() {
      this.measurementLogLoading = true
      const logRes = await logPageByModel(this.searchData)
      console.log('测量日志', 1, logRes)
      const listDataOrigin = logRes.data.list
      const listDataShow = {}

      for (let index = 0; index < listDataOrigin.length; index++) {
        const item = listDataOrigin[index]
        item.datetime = JSON.parse(item.requestParamData)
          ? JSON.parse(item.requestParamData).datetime || item.createTime
          : item.createTime
        const parsedCreateTime = parseTime(item.createTime, '{y}/{m}/{d}')
        if (!listDataShow[parsedCreateTime]) {
          listDataShow[parsedCreateTime] = {
            hide: false,
            list: []
          }
        }
        // 各个项目单独处理
        if (item.itemCode) {
          item.valueData = JSON.parse(item.requestParamData)
        }
        if (this.ITEMNAME_LIST.includes(item.itemName)) {
          item.valueData = JSON.parse(item.requestParamData)
        }
        if (item.itemCode || item.itemName) {
          listDataShow[parsedCreateTime].list.push(item)
        }
      }
      this.listDataMap = listDataShow
      this.measurementLogLoading = false
    },
    async getOperationlogs() {
      this.operationLogLoading = true
      const logRes = await Loginlogs(this.setData, this.$route.query.token)
      // logRes.data.list.reverse()
      const listDataOrigin = logRes.data.list.filter((elem) => {
        return elem.module === '健康档案'
      })
      const listDataShow = {}
      for (let index = 0; index < listDataOrigin.length; index++) {
        listDataOrigin[index].createTime = listDataOrigin[index].createTime.replace(/-/g, '/')
        const item = listDataOrigin[index]
        item.datetime = item.requestParamData ? item.requestParamData.datetime || item.createTime : item.createTime
        const parsedCreateTime = parseTime(item.createTime, '{y}/{m}/{d}')
        if (!listDataShow[parsedCreateTime]) {
          listDataShow[parsedCreateTime] = {
            hide: false,
            list: []
          }
        }
        if (item.createTime.indexOf(parsedCreateTime) !== -1) {
          listDataShow[parsedCreateTime].list.push(item)
        }
      }
      this.Operationlogs = listDataShow
      this.operationLogLoading = false
    },
    async Getloginlogs() {
      this.loginLohLoading = true
      const logres = await Loginlogs(this.Loginloges, this.$route.query.token)
      // logres.data.list.reverse()
      Getdropdown({ loginOpera: true }).then((res) => {
        this.Dropdown = res.data
      })
      const listDataOrigin = logres.data.list
      const listDataShow = {}
      for (let index = 0; index < listDataOrigin.length; index++) {
        listDataOrigin[index].createTime = listDataOrigin[index].createTime.replace(/-/g, '/')
        const item = listDataOrigin[index]
        item.datetime = item.requestParamData ? item.requestParamData.datetime || item.createTime : item.createTime
        const parsedCreateTime = parseTime(item.createTime, '{y}/{m}/{d}')
        if (!listDataShow[parsedCreateTime]) {
          listDataShow[parsedCreateTime] = {
            hide: false,
            list: []
          }
        }
        if (item.createTime.indexOf(parsedCreateTime) !== -1) {
          listDataShow[parsedCreateTime].list.push(item)
        }
      }
      this.logsData = listDataShow
      this.loginLohLoading = false
      console.log('登录日志', listDataShow)
    },
    async getlogged() {
      const result = await Loginlogs(this.setData, this.$route.query.token)
      const listDataOrigin = result.data.list
      console.log(result)
      const listDataShow = {}
      for (let index = 0; index < listDataOrigin.length; index++) {
        const item = listDataOrigin[index]
        item.datetime = item.requestParamData ? item.requestParamData.datetime || item.operTime : item.operTime
        const parsedCreateTime = parseTime(item.operTime, '{y}/{m}/{d}')
        if (!listDataShow[parsedCreateTime]) {
          listDataShow[parsedCreateTime] = {
            hide: false,
            list: []
          }
        }
        if (item.operTime.indexOf(parsedCreateTime)) {
          listDataShow[parsedCreateTime].list.push(item)
        }
      }
      this.Operations = listDataShow
    },
    getDataSplit() {
      if (this.topActiveTab === '0') {
        this.getList()
      } else if (this.topActiveTab === '1') {
        this.getOperationlogs()
      } else {
        this.Getloginlogs()
      }
    },
    getSystemTimeData() {
      const logRes = {
        data: {
          list: [
            {
              createTime: new Date(),
              createUsername: this.$store.getters.name,
              operation: '登录'
            }
          ]
        }
      }
      const listDataOrigin = logRes.data.list

      const listDataShow = {}

      for (let index = 0; index < listDataOrigin.length; index++) {
        const item = listDataOrigin[index]
        item.datetime = JSON.parse(item.requestParamData)
          ? JSON.parse(item.requestParamData).datetime || item.createTime
          : item.createTime
        const parsedCreateTime = parseTime(item.createTime, '{y}/{m}/{d}')
        if (!listDataShow[parsedCreateTime]) {
          listDataShow[parsedCreateTime] = {
            isSys: true,
            hide: false,
            list: []
          }
        }
        listDataShow[parsedCreateTime].list.push(item)
      }
      this.listDataMap = listDataShow
    },
    // 收起 / 展开
    handleToggle(itemKey, timelineIndex) {
      if (timelineIndex === 0) {
        this.listDataMap[itemKey].hide = !this.listDataMap[itemKey].hide
      }
    },
    handleToggleFun(itemKey, timelineIndex) {
      if (timelineIndex === 0) {
        this.Operationlogs[itemKey].hide = !this.Operationlogs[itemKey].hide
      }
    },
    handleToggleFuns(itemKey, timelineIndex) {
      if (timelineIndex === 0) {
        this.logsData[itemKey].hide = !this.logsData[itemKey].hide
      }
    }
  }
}
</script>

<style lang="scss">
.changeTagBoxRender {
  background: #c6eefb;
  border-radius: 10px;
  height: 20px;
  line-height: 20px;
  margin-left: 15px;
  .valueBox {
    font-size: 0.6rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #37b4d8;
  }
  .labelBox {
    font-size: 0.5rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #37b4d8;
  }
}
</style>

<style lang="scss" scoped>
.pageContainer {
  .serverSearchBox {
    ::v-deep .el-form-item__label {
      line-height: 1.8rem !important;
      text-align: right;
      text-align-last: right;
    }
    ::v-deep .el-form-item__content {
      width: 100%;
    }
  }
  .topTabBox {
    height: 50px;
    background: #ffffff;
    padding: 0 60px;
    display: flex;
    justify-content: flex-start;
    box-shadow: 0 2px 4px 0px rgba(0, 0, 0, 0.06) inset;

    .tabItem {
      position: relative;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.8rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      cursor: pointer;
      user-select: none;
      & + .tabItem {
        margin-left: 60px;
      }
      &.activeTab {
        font-weight: 600;
        color: #20b39d;
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          width: 78%;
          height: 3px;
          border-radius: 3px;
          left: 50%;
          transform: translateX(-50%);
          background: #0a86c8;
        }
      }
    }
  }
  .mainContentBox {
    padding: 20px 60px;
    .topDateFilter {
      display: flex;
      justify-content: space-between;
      ::v-deep .dateFilterRadioGroup {
        .el-radio-button {
          & + .el-radio-button {
            margin-left: 1px;
          }
        }
        .el-radio-button__inner {
          min-width: 100px;
          height: 30px;
          line-height: 30px;
          text-align: center;
          background: #f0f0f0;
          font-size: 0.7rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;

          .countBox {
            // display: inline-block;
            display: none;
            min-width: 36px;
            margin-left: 6px;
            padding: 0 4px;
            height: 16px;
            line-height: 16px;
            background: #e7e7e7;
            border-radius: 8px;
            font-size: 0.6rem;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
          }
        }
        .is-active {
          .el-radio-button__inner {
            background: #ffffff;
            font-weight: 600;
            color: #0a86c8;
          }

          .countBox {
            font-weight: 600;
            color: #0a86c8;
          }
        }
      }
      .legendBox {
        display: flex;
        justify-content: flex-start;
        .lengendItemBox {
          display: flex;
          align-items: center;
          font-size: 0.6rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #999999;
          flex-wrap: nowrap;
          & + .lengendItemBox {
            margin-left: 15px;
          }
          .lengendDot {
            display: inline-block;
            width: 6px;
            height: 6px;
            border-radius: 6px;
            margin-right: 6px;
            &.dotInsert {
              background: #0a86c8;
            }
            &.dotUpdate {
              background: #37b4d8;
            }
            &.dotDelete {
              background: #e75454;
            }
          }
        }
      }
    }
    .tableBox {
      ::v-deep thead {
        height: 35px;
      }
    }
  }
  .listContent {
    margin-top: 20px;
    padding: 20px 30px;
    background: #ffffff;
    border-radius: 12px;

    .searchBox {
      .applicationCustomSearchForm {
        .searchFormItem {
          width: 15vw;
          margin-right: -0.5vw;
          margin-bottom: 0.5vw;
          &.searchFormItemLong {
            width: 25vw;
            .selectSelf {
              width: 8vw;
              ::v-deep .el-input__inner {
                width: 8vw;
              }
            }
          }

          ::v-deep .el-form-item__label {
            font-size: 0.73vw !important;
          }

          .selectSelf {
            width: 8vw;
            &.mr20 {
              margin-right: 1vw;
            }
            ::v-deep .el-input__inner {
              width: 8vw;
            }
          }
        }
      }
      .searchFormBtnBox {
        margin-bottom: 0.5vw;
        .el-button {
          width: 3.5vw;
          padding: 0;
        }
      }
    }
    .listBox {
      margin-top: 20px;
      .timelineItem {
        &.hideItem {
          max-height: 50px;
          overflow: hidden;
        }
        & + .timelineItem {
          margin-top: 25px;
        }
        .timelineOtherItem,
        .timelineFirstItem {
          display: flex;
          justify-content: flex-start;

          .leftDateBox {
            cursor: pointer;
            width: 250px;
            height: 50px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #dedede;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            user-select: none;
            position: relative;
            z-index: 1;
            &.hasRightConnect::after {
              content: ' ';
              display: block;
              position: absolute;
              right: -21px;
              top: 50%;
              transform: translateY(-50%);
              height: 0px;
              width: 20px;
              border-bottom: 1px solid #dedede;
              z-index: 0;
            }
            &.hiddenLeft {
              cursor: default;
              border: 1px solid transparent;
            }

            .dateTextBox {
              font-size: 0.8rem;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 600;
              color: #666666;
            }
            .toggleIconBox {
              width: 18px;
              height: 18px;
              border-radius: 4px;
              background-color: #dff2ef;
              text-align: center;
              line-height: 18px;
              i {
                color: #0a86c8;
                transition: transform 0.3s ease-in-out;
                &.rotate180 {
                  transform: rotateX(180deg);
                }
              }
            }
          }
          .rightTimelineBox {
            z-index: 1;
            position: relative;
            margin-left: 20px;
            width: calc(100% - 270px);
            height: 50px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #dedede;
            padding: 0 20px;
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .timeTextBox {
              font-size: 0.8rem;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 600;
              color: #666666;
            }
            .timelineContentBox {
              max-height: 50px;
              overflow: auto;
              margin-left: 25px;
              font-size: 0.7rem;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #666666;
              line-height: 25px;

              .operateBox {
                font-weight: 600;
                &.type-INSERT {
                  color: #0a86c8;
                }
                &.type-UPDATE {
                  color: #37b4d8;
                }
                &.type-DELETE {
                  color: #e75454;
                }
              }
            }
          }
        }
        .timelineOtherItem {
          margin-top: 15px;
          .rightTimelineBox {
            position: relative;
          }
          .rightTimelineBox::before {
            content: ' ';
            position: absolute;
            left: 15px;
            top: -16px;
            display: block;
            height: 15px;
            width: 1px;
            background: linear-gradient(to bottom, #dedede 0%, #dedede 50%, transparent 50%, transparent 100%);
            background-size: 8px 6px;
            background-repeat: repeat-y;
          }
        }
      }
    }
  }
}
</style>
