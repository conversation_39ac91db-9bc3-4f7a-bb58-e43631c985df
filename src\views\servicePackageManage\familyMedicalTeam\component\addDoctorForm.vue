<template>
  <div class="add-doctor-form">
    <ProInput
      v-model="queryParams.keyword"
      placeholder="请输入医生姓名"
      style="width: 250px; margin-bottom: 20px"
      @debounce="handleSearch"
    />
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      :height="'360px'"
      row-key="id"
      :columns="columns"
      :total="total"
      :page-info="queryParams"
      @pagination-change="handlePaginationChange"
      @selection-change="handleSelectionChange"
    />
  </div>
</template>
<script>
import { getmanageDoctorApi } from '@/api/system'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ProInput from '@/components/ProInput/index.vue'
import { localCache } from '@/utils/cache'

export default {
  components: {
    BaseTable,
    ProInput
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        departCode: localCache.getCache('userInfo').departCode || '',
        keyword: ''
      },
      columns: [
        { type: 'selection', width: 55, align: 'center' },
        { label: '医生姓名', prop: 'name' },
        { label: '手机号', prop: 'originalPhone' },
        { label: '所属机构', prop: 'departName' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      return await getmanageDoctorApi(params)
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
      this.$emit('selection-change', selection)
    }
  }
}
</script>
<style lang="scss" scoped></style>
