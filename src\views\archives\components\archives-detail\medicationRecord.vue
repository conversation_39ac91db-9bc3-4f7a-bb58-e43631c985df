<!-- 用药记录 -->
<template>
  <div class="medication-record">
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      row-key="id"
      :columns="columns"
      :show-pagination="showPagination"
    />
  </div>
</template>

<script>
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import { getUserDbMedicationRecord } from '@/api/archives'

export default {
  name: 'MedicationRecord',
  components: {
    BaseTable
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        patientId: this.$route.query.id
      },
      columns: [
        { label: '药品名称', prop: 'medicineName' },
        { label: '每日次数', prop: 'dailyTimes' },
        { label: '每日用量', prop: 'dayCount' },
        { label: '单位', prop: 'unit' },
        { label: '备注', prop: 'remark' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      return await getUserDbMedicationRecord(params)
    }
  }
}
</script>
