<!-- 检验检查第七部分: 眼底检查 -->
<template>
  <div class="inspection-seventh-part">
    <div class="content">
      <div class="title">眼底检查</div>
      <div class="item">
        <el-table :data="fundusExaminationData.table" border style="width: 100%; margin-top: 10px">
          <el-table-column prop="diseaseItem" label="疾病检查项" align="center" width="150" />
          <el-table-column label="左眼" align="center">
            <el-table-column prop="leftCount" label="数量（个）" align="center" />
            <el-table-column prop="leftArea" label="总面积（mm2）" align="center" width="130" />
            <el-table-column prop="leftValue" label="结果" align="center">
              <template slot-scope="scope">
                <span>
                  {{ scope.row.leftValue === 1 ? '阳性' : scope.row.leftValue === 0 ? '阴性' : scope.row.leftValue }}
                </span>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="右眼" align="center">
            <el-table-column prop="rightCount" label="数量（个）" align="center" />
            <el-table-column prop="rightArea" label="总面积（mm2）" align="center" width="130" />
            <el-table-column prop="rightValue" label="结果" align="center">
              <template slot-scope="scope">
                <span>
                  {{ scope.row.rightValue === 1 ? '阳性' : scope.row.rightValue === 0 ? '阴性' : scope.row.rightValue }}
                </span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table>

        <el-descriptions :column="1" border style="margin-top: 10px">
          <el-descriptions-item label="左眼">
            {{ fundusExaminationData.form.diseaseLeft }}
          </el-descriptions-item>
          <el-descriptions-item label="右眼">
            {{ fundusExaminationData.form.diseaseRight }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { mapDataToTable } from '@/utils/cspUtils'

export default {
  name: 'InspectionSeventhPart',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      table: [
        {
          diseaseItem: '微血管瘤',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightMaCount: 'rightCount',
          rightMaArea: 'rightArea',
          rightMaValue: 'rightValue',
          leftMaCount: 'leftCount',
          leftMaArea: 'leftArea',
          leftMaValue: 'leftValue'
        },
        {
          diseaseItem: '棉絮斑',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightCwCount: 'rightCount',
          rightCwArea: 'rightArea',
          rightCwValue: 'rightValue',
          leftCwCount: 'leftCount',
          leftCwArea: 'leftArea',
          leftCwValue: 'leftValue'
        },
        {
          diseaseItem: '硬性渗出',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightHeCount: 'rightCount',
          rightHeArea: 'rightArea',
          rightHeValue: 'rightValue',
          leftHeCount: 'leftCount',
          leftHeArea: 'leftArea',
          leftHeValue: 'leftValue'
        },
        {
          diseaseItem: '视网膜内出血',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightHmaCount: 'rightCount',
          rightHmaArea: 'rightArea',
          rightHmaValue: 'rightValue',
          leftHmaCount: 'leftCount',
          leftHmaArea: 'leftArea',
          leftHmaValue: 'leftValue'
        },
        {
          diseaseItem: '视网膜内微血管异常',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightIrmaCount: 'rightCount',
          rightIrmaArea: 'rightArea',
          rightIrmaValue: 'rightValue',
          leftIrmaCount: 'leftCount',
          leftIrmaArea: 'leftArea',
          leftIrmaValue: 'leftValue'
        },
        {
          diseaseItem: '新生血管或增殖膜',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightNvfpCount: 'rightCount',
          rightNvfpArea: 'rightArea',
          rightNvfpValue: 'rightValue',
          leftNvfpCount: 'leftCount',
          leftNvfpArea: 'leftArea',
          leftNvfpValue: 'leftValue'
        },
        {
          diseaseItem: '视网膜前出血或玻璃体出血',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightPrhvhCount: 'rightCount',
          rightPrhvhArea: 'rightArea',
          rightPrhvhValue: 'rightValue',
          leftPrhvhCount: 'leftCount',
          leftPrhvhArea: 'leftArea',
          leftPrhvhValue: 'leftValue'
        },
        {
          diseaseItem: '激光斑',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightLaserCount: 'rightCount',
          rightLaserArea: 'rightArea',
          rightLaserValue: 'rightValue',
          leftLaserCount: 'leftCount',
          leftLaserArea: 'leftArea',
          leftLaserValue: 'leftValue'
        },
        {
          diseaseItem: '豹纹状眼底',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightTafCount: 'rightCount',
          rightTafArea: 'rightArea',
          rightTafValue: 'rightValue',
          leftTafCount: 'leftCount',
          leftTafArea: 'leftArea',
          leftTafValue: 'leftValue'
        },
        {
          diseaseItem: '近视弧形斑',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightMcCount: 'rightCount',
          rightMcArea: 'rightArea',
          rightMcValue: 'rightValue',
          leftMcCount: 'leftCount',
          leftMcArea: 'leftArea',
          leftMcValue: 'leftValue'
        },
        {
          diseaseItem: '玻璃膜疣',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightDrusCount: 'rightCount',
          rightDrusArea: 'rightArea',
          rightDrusValue: 'rightValue',
          leftDrusCount: 'leftCount',
          leftDrusArea: 'leftArea',
          leftDrusValue: 'leftValue'
        },
        {
          diseaseItem: '动静脉交叉压迹',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightAvnCount: 'rightCount',
          rightAvnArea: 'rightArea',
          rightAvnValue: 'rightValue',
          leftAvnCount: 'leftCount',
          leftAvnArea: 'leftArea',
          leftAvnValue: 'leftValue'
        },
        {
          diseaseItem: '动静脉比异常',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightAarCount: 'rightCount',
          rightAarArea: 'rightArea',
          rightAarValue: 'rightValue',
          leftAarCount: 'leftCount',
          leftAarArea: 'leftArea',
          leftAarValue: 'leftValue'
        },
        {
          diseaseItem: '银丝或铜丝征',
          leftCount: '',
          leftArea: '',
          leftValue: '',
          rightCount: '',
          rightArea: '',
          rightValue: '',
          rightScwCount: 'rightCount',
          rightScwArea: 'rightArea',
          rightScwValue: 'rightValue',
          leftScwCount: 'leftCount',
          leftScwArea: 'leftArea',
          leftScwValue: 'leftValue'
        }
      ]
    }
  },
  computed: {
    fundusExaminationData() {
      const { data = {} } = this.reportInfo.itemList.find((item) => item.itemCode === 'EYE_GROUND') || {}
      return {
        table: mapDataToTable(data, cloneDeep(this.table)),
        form: {
          diseaseLeft: data.diseaseLeft,
          diseaseRight: data.diseaseRight
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.inspection-seventh-part {
  .content {
    margin-top: 8px;
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
    }
  }
}
</style>
