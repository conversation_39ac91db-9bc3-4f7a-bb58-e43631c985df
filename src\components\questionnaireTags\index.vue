<template>
  <div class="questionnaire-name-list">
    <div
      v-for="item in questionnaireList"
      :key="item.id"
      :class="['questionnaire-name', { active: questionnaireId === item.id }]"
      @click="handleQuestionnaireNameClick(item)"
    >
      {{ item.name }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuestionnaireTags',
  props: {
    questionnaireList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      questionnaireId: this.questionnaireList[0].id
    }
  },
  methods: {
    handleQuestionnaireNameClick(item) {
      this.questionnaireId = item.id
      this.$emit('click', item)
    }
  }
}
</script>

<style lang="scss" scoped>
.questionnaire-name-list {
  margin-top: 16px;
  display: flex;
  gap: 80px;
  .questionnaire-name {
    background: #f3f3f3;
    padding: 10px 15px;
    border-radius: 20px;
    cursor: pointer;
    color: #666;
  }
  .active {
    background: #e4f4fb;
    color: #4bc0f1;
  }
}
</style>
