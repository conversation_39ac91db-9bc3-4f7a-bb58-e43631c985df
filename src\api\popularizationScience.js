import request from '@/utils/request'

export function deleteArticleInfo(id) {
  return request({
    url: `/cspapi/backend/science/article/${id}`,
    method: 'delete'
  })
}

export function getArticleInfo(id) {
  return request({
    url: `/cspapi/backend/science/article/${id}`,
    method: 'get'
  })
}

export function putArticleUpdate(data) {
  return request({
    url: `/cspapi/backend/science/article/update`,
    method: 'put',
    data
  })
}

export function getArticleList(params) {
  return request({
    url: `/cspapi/backend/science/article/page/see`,
    method: 'get',
    params
  })
}

export function getVideoList(params) {
  return request({
    url: `/cspapi/backend/science/video/page/see`,
    method: 'get',
    params
  })
}

export function getArticlePageList(params) {
  return request({
    url: `/cspapi/backend/science/article/page`,
    method: 'get',
    params
  })
}

export function postArticleSave(data) {
  return request({
    url: `/cspapi/backend/science/article/save`,
    method: 'post',
    data
  })
}

export function deleteVideo(id) {
  return request({
    url: `/cspapi/backend/science/video/${id}`,
    method: 'delete'
  })
}

export function getVideoPageList(params) {
  return request({
    url: `/cspapi/backend/science/video/page`,
    method: 'get',
    params
  })
}

export function postVideoSave(data) {
  return request({
    url: `/cspapi/backend/science/video/save`,
    method: 'post',
    data
  })
}

export function putVideoUpdate(data) {
  return request({
    url: `/cspapi/backend/science/video/update`,
    method: 'put',
    data
  })
}

export function getVideoPlanList(params) {
  return request({
    url: `/cspapi/backend/science/video/plan/page`,
    method: 'get',
    params
  })
}

export function putVideoPlanList(data) {
  return request({
    url: `/cspapi/backend/science/video/plan`,
    method: 'put',
    data
  })
}

export function deleteVideoPlan(id) {
  return request({
    url: `/cspapi/backend/science/video/plan/${id}`,
    method: 'delete'
  })
}

export function postVideoPlanList(data) {
  return request({
    url: '/cspapi/backend/science/video/plan',
    method: 'post',
    data
  })
}

export function getLentivirus(params) {
  return request({
    url: '/cspapi/backend/sys/dictionary/listByModuleCode/lentivirus',
    method: 'get',
    params
  })
}

export function getExaminePage(params) {
  return request({
    url: '/cspapi/backend/science/video/examine/page',
    method: 'get',
    params
  })
}

export function getQuestionPage(params) {
  return request({
    url: '/cspapi/backend/science/video/question/group/page',
    method: 'get',
    params
  })
}

export function getListByQuestionGroupId(params) {
  return request({
    url: '/cspapi/backend/videoScience/question/listByQuestionGroupId',
    method: 'get',
    params
  })
}

export function getVideoId(videoId) {
  return request({
    url: `/cspapi/backend/science/video/${videoId}`,
    method: 'get'
  })
}

export function getVideoSeeId(videoId) {
  return request({
    url: `/cspapi/backend/science/video/see/${videoId}`,
    method: 'get'
  })
}

export function getVideoExamine(params) {
  return request({
    url: '/cspapi/backend/science/video/examine/cunyi',
    method: 'get',
    params
  })
}

export function postVideoExamine(data) {
  return request({
    url: '/cspapi/backend/science/video/examine',
    method: 'post',
    data
  })
}

export function putVideoExamineUpdateByModel(data) {
  return request({
    url: '/cspapi/backend/science/video/examine/updateByModel',
    method: 'put',
    data
  })
}

export function postVideoAnswer(data) {
  return request({
    url: '/cspapi/backend/science/video/answer',
    method: 'post',
    data
  })
}

export function getQuestionListByQuestionGroupId(params) {
  return request({
    url: '/cspapi/backend/videoScience/question/listByQuestionGroupId',
    method: 'get',
    params
  })
}

export function getMinuteArrayByVideoId(params) {
  return request({
    url: '/cspapi/backend/science/video/question/group/getMinuteArrayByVideoId',
    method: 'get',
    params
  })
}

export function getByUserIdAndVideoId(params) {
  return request({
    url: '/cspapi/backend/science/video/examine/getByUserIdAndVideoId',
    method: 'get',
    params
  })
}

export function getPageWithScore(params) {
  return request({
    url: '/cspapi/backend/science/video/plan/pageWithScore',
    method: 'get',
    params
  })
}

export function getServiceStationTree(params) {
  return request({
    url: '/cspapi/backend/serviceStation/tree',
    method: 'get',
    params
  })
}

export function getExamineManager(params) {
  return request({
    url: '/cspapi/backend/science/video/examine/manager',
    method: 'get',
    params
  })
}

export function pageGroupByVideoIdByModel(params) {
  return request({
    url: '/cspapi/backend/science/video/examine/pageGroupByVideoIdByModel',
    method: 'get',
    params
  })
}

export function getByUserIdAndVideoIdWithAnswer(params) {
  return request({
    url: '/cspapi/backend/science/video/examine/getByUserIdAndVideoIdWithAnswer',
    method: 'get',
    params
  })
}

export function postUploadFilePrivate3(data) {
  return request({
    url: '/cspapi/backend/cos/uploadFile/private3',
    method: 'post',
    data,
    config: { 'Content-Type': 'multipart/form-data' } // 这里添加配置
  })
}
