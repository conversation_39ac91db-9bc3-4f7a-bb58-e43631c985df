<!-- 参会医生页面 -->
<template>
  <div class="otherDoctorPage">
    <remote-video
      type="other"
      :room-id="roomId"
      :participating-doctors="participatingDoctors"
      @meeting-ended="handleMeetingEnded"
    />
    <el-button type="primary" style="float: right; margin-top: 10px" @click="handleSave">保存</el-button>
    <!-- 诊治意见 -->
    <div class="diagnosis-opinion">
      <h3>诊治意见</h3>
      <el-input
        v-model="meetingResult"
        type="textarea"
        :rows="4"
        placeholder="请输入诊治意见"
        style="margin-top: 8px"
      />
    </div>

    <!-- 处方 -->
    <div class="prescription">
      <h3>处方</h3>
      <prescription-table v-model="medicalList" style="width: 100%; margin-top: 8px" />
    </div>
  </div>
</template>

<script>
import { saveConsultationRecordApi } from '@/api/remoteConsultation'
import { localCache } from '@/utils/cache'
import { mapState } from 'vuex'
import RemoteVideo from './remoteVideo.vue'
import PrescriptionTable from '@/components/prescriptionTable/index.vue'

export default {
  components: {
    RemoteVideo,
    PrescriptionTable
  },
  computed: {
    ...mapState('drugManagement', ['consultationRecordDetail'])
  },
  data() {
    return {
      roomId: localCache.getCache('roomId'),
      participatingDoctors: localCache.getCache('participatingDoctors'),
      meetingResult: '',
      medicalList: []
    }
  },
  watch: {
    consultationRecordDetail: {
      handler(newVal) {
        this.meetingResult = newVal.remoteRoom ? newVal.remoteRoom.meetingResult : ''
        this.medicalList = newVal.medicalList ? newVal.medicalList : []
      },
      deep: true
    }
  },
  created() {
    if (this.roomId) {
      this.$store.dispatch('drugManagement/getConsultationRecordDetail', {
        roomId: this.roomId
      })
    }
  },
  destroyed() {
    localCache.removeCache('roomId')
    localCache.removeCache('participatingDoctors')
  },
  methods: {
    // 处理会议结束事件
    handleMeetingEnded() {
      // 清除缓存
      localCache.removeCache('roomId')
      localCache.removeCache('participatingDoctors')
      // 通知全局状态关闭远程会诊弹窗
      this.$store.commit('app/SET_REMOTE_CONSULTATION_VISIBLE', false)
    },

    // 保存
    async handleSave() {
      const params = {
        roomId: this.roomId,
        meetingResult: this.meetingResult,
        medicalList: this.medicalList
      }
      const res = await saveConsultationRecordApi(params)
      if (res.code === 200) {
        this.$message.success('保存成功')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.otherDoctorPage {
  .diagnosis-opinion {
    margin-top: 16px;
  }
  .prescription {
    margin-top: 16px;
  }
}
</style>
