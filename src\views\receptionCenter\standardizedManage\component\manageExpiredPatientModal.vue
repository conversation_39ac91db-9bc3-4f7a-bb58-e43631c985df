<template>
  <div class="manage-expired-patient-modal">
    <ProDialog ref="proDialogRef" title="即将过期的患者" :visible.sync="visible" top="8vh" width="1200px">
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        row-key="id"
        :columns="columns"
        :height="420"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #disease="{ row }">
          <ChronicDiseaseType :record="row" />
        </template>

        <template #sex="{ row }">
          <span>{{ genderTransform(row.sex) }}</span>
        </template>

        <template #idCard="{ row }">
          <EncryptionStr :cipher-text="row.idCard" :replace="row.idCardReplace" />
        </template>

        <template #phone="{ row }">
          <EncryptionStr :cipher-text="row.phone" :replace="row.phoneReplace" />
        </template>

        <template #operation="{ row }">
          <el-button type="text" @click="handleManage(row)">管理</el-button>
        </template>
      </base-table>
    </ProDialog>
  </div>
</template>

<script>
import { getStandardizedManageExpiredList } from '@/api/standardizedManage'
import { genderTransform } from '@/utils/cspUtils'
import ProDialog from '@/components/ProDialog/index.vue'
import tableMixin from '@/mixins/tableMixin'
import BaseTable from '@/components/BaseTable/index.vue'
import EncryptionStr from '@/components/encryptionStr/index.vue'
import ChronicDiseaseType from '@/components/chronicDiseaseType/index.vue'

export default {
  components: {
    ProDialog,
    BaseTable,
    EncryptionStr,
    ChronicDiseaseType
  },
  mixins: [tableMixin],
  data() {
    return {
      visible: false,
      queryParams: {},
      columns: [
        { label: '姓名', prop: 'patientName', width: 120 },
        { label: '慢病病种', prop: 'disease', slot: 'disease' },
        { label: '性别', prop: 'sex', slot: 'sex', width: 100 },
        { label: '年龄', prop: 'age' },
        { label: '手机号', prop: 'phone', slot: 'phone' },
        { label: '身份证号', prop: 'idCard', slot: 'idCard' },
        { label: '即将逾期天数', prop: 'beOverdueDayCount', width: 120 },
        { label: '操作', prop: 'operation', width: 100, slot: 'operation' }
      ]
    }
  },
  methods: {
    genderTransform,
    async getTableList(params) {
      return await getStandardizedManageExpiredList(params)
    },
    handleManage(row) {
      this.$store.commit('managePatient/SET_MANAGE_PATIENT_DATA', {})
      this.$router.push({
        path: '/receptionCenter/managePatient',
        query: {
          id: row.id
        }
      })
    }
  }
}
</script>
