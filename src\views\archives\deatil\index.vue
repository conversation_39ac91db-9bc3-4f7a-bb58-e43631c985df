<template>
  <div class="userdb-detail">
    <div class="userdb-detail-left">
      <el-card style="overflow-y: auto">
        <div
          v-for="item in menuList"
          :key="item.code"
          class="userdb-detail-left-menu-item"
          :class="{ active: activeMenu === item.code }"
          @click="handleMenuClick(item.code)"
        >
          <svg-icon :icon-class="item.icon" />
          <span>{{ item.name }}</span>
        </div>
      </el-card>
    </div>
    <div class="userdb-detail-right">
      <div class="userdb-detail-right-header">
        <PatientInfo :patient-info="userInfo" />
      </div>
      <el-card>
        <div class="userdb-detail-right-content">
          <component
            :is="menuList.find(item => item.code === activeMenu).component"
            v-if="activeMenu !== 'patientCreate'"
            :user-info="userInfo"
          />
          <PatientCreate v-if="activeMenu === 'patientCreate'" type="edit" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getUserDbDetail } from '@/api/archives'
import UserSummary from '@/components/UserInfo-new/summary.vue'
import PatientCreate from '@/views/patientCreate/index.vue'
import PatientInfo from '@/components/patientInfo/index.vue'
import Hypertension from '@/views/archives/components/archives-detail/hypertension.vue'
import BloodSugar from '@/views/archives/components/archives-detail/bloodSugar.vue'
import VisitRecord from '@/views/archives/components/archives-detail/visitRecord.vue'
import InspectionRecord from '@/views/archives/components/archives-detail/inspectionRecord.vue'
import StandardManagement from '@/views/archives/components/archives-detail/standardManagement.vue'
import ReferralRecord from '@/views/archives/components/archives-detail/referralRecord.vue'
import ConsultationRecord from '@/views/archives/components/archives-detail/consultationRecord.vue'
import SignContractRecord from '@/views/archives/components/archives-detail/signContractRecord.vue'
import MedicationRecord from '@/views/archives/components/archives-detail/medicationRecord.vue'
import AdverseEvent from '@/views/archives/components/archives-detail/adverseEvent.vue'
import HealthRecordReport from '@/views/healthRecordReport/index.vue'
import Bnp from '@/views/archives/components/archives-detail/Bnp.vue'

export default {
  name: 'ArchivesDetail',
  components: {
    UserSummary,
    PatientCreate,
    PatientInfo,
    Hypertension,
    BloodSugar,
    VisitRecord,
    InspectionRecord,
    StandardManagement,
    ReferralRecord,
    ConsultationRecord,
    SignContractRecord,
    MedicationRecord,
    AdverseEvent,
    HealthRecordReport,
    Bnp
  },
  data() {
    return {
      id: this.$route.query.id,
      activeMenu: 'prsinfo',
      userInfo: {},
      readonlyType: true,
      editType: true,
      menuList: [
        {
          name: '档案预览',
          icon: 'userdb_dayl',
          code: 'prsinfo',
          component: 'UserSummary'
        },
        {
          name: '居民档案',
          icon: 'userdb_juda',
          code: 'patientCreate'
        },
        {
          name: '血压测量记录',
          icon: 'userdb_gxy',
          code: 'hypertension',
          component: 'Hypertension'
        },
        {
          name: '血糖测量记录',
          icon: 'userdb_tnb',
          code: 'bloodSugar',
          component: 'BloodSugar'
        },
        {
          name: 'BNP测量记录',
          icon: 'userdb_bnp',
          code: 'bnp',
          component: 'Bnp'
        },
        {
          name: '接诊记录',
          icon: 'userdb_jz',
          code: 'visitRecord',
          component: 'VisitRecord'
        },
        {
          name: '检查检验记录',
          icon: 'userdb_jy',
          code: 'inspectionRecord',
          component: 'InspectionRecord'
        },
        {
          name: '规范管理记录',
          icon: 'userdb_gf',
          code: 'standardManagement',
          component: 'StandardManagement'
        },

        {
          name: '转诊记录',
          icon: 'userdb_zz',
          code: 'referralRecord',
          component: 'ReferralRecord'
        },
        {
          name: '会诊记录',
          icon: 'userdb_hz',
          code: 'consultationRecord',
          component: 'ConsultationRecord'
        },
        {
          name: '签约记录',
          icon: 'userdb_qy',
          code: 'signContractRecord',
          component: 'SignContractRecord'
        },
        {
          name: '用药记录',
          icon: 'userdb_yy',
          code: 'medicationRecord',
          component: 'MedicationRecord'
        },
        {
          name: '不良事件',
          icon: 'userdb_bl',
          code: 'adverseEvent',
          component: 'AdverseEvent'
        },
        {
          name: '筛查报告',
          icon: 'userdb_sc',
          code: 'healthRecordReport',
          component: 'HealthRecordReport'
        }
      ]
    }
  },
  async created() {
    await this.getDetail()
  },
  methods: {
    handleMenuClick(code) {
      this.activeMenu = code
    },
    async getDetail() {
      const res = await getUserDbDetail({ patientId: this.$route.query.id })
      if (res.code === 200) {
        this.userInfo = res.data
      }
    },
    changeUserName(name) {
      this.userInfo.userName = name
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';

.userdb-detail {
  height: 100%;
  margin-left: 16px;
  padding-top: 8px;
  display: flex;
  gap: 16px;
  .userdb-detail-left {
    width: 180px;
    height: 100%;
    .el-card {
      height: 100%;
      ::v-deep .el-card__body {
        padding: 0;
      }
      .userdb-detail-left-menu-item {
        display: flex;
        align-items: center;
        gap: 6px;
        width: 180px;
        cursor: pointer;
        padding: 12px 12px 12px 22px;
        font-size: 16px;
        &.active {
          background-color: #d8f2fa;
          color: #4bc0f1;
          border-radius: 4px;
        }
      }
    }
  }
  .userdb-detail-right {
    flex: 1;
    .userdb-detail-right-header {
      background-color: #fff;
      height: 72px;
      margin-bottom: 10px;
      padding: 10px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }
    .el-card {
      height: calc(100% - 90px);
      overflow-y: auto;
    }
  }
}
</style>
