<template>
  <div class="death-person-modal">
    <ProDialog ref="proDialogRef" title="死亡人员名单" :visible.sync="visible" top="8vh" width="1200px">
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        row-key="id"
        :columns="columns"
        :height="420"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #disease="{ row }">
          <ChronicDiseaseType :record="row" />
        </template>

        <template #sex="{ row }">
          <span>{{ genderTransform(row.sex) }}</span>
        </template>

        <template #idCard="{ row }">
          <EncryptionStr :cipher-text="row.idCard" :replace="row.idCardReplace" />
        </template>

        <template #phone="{ row }">
          <EncryptionStr :cipher-text="row.phone" :replace="row.phoneReplace" />
        </template>
      </base-table>
    </ProDialog>
  </div>
</template>

<script>
import { getDeathPersonList } from '@/api/archives'
import { genderTransform } from '@/utils/cspUtils'
import { localCache } from '@/utils/cache'
import ProDialog from '@/components/ProDialog/index.vue'
import tableMixin from '@/mixins/tableMixin'
import BaseTable from '@/components/BaseTable/index.vue'
import EncryptionStr from '@/components/encryptionStr/index.vue'
import ChronicDiseaseType from '@/components/chronicDiseaseType/index.vue'

export default {
  components: {
    ProDialog,
    BaseTable,
    EncryptionStr,
    ChronicDiseaseType
  },
  mixins: [tableMixin],
  data() {
    return {
      visible: false,
      queryParams: {
        departCode: localCache.getCache('userInfo').departCode || '',
        keyword: ''
      },
      columns: [
        { label: '姓名', prop: 'name', width: 120 },
        { label: '慢病病种', prop: 'disease', slot: 'disease' },
        { label: '性别', prop: 'sex', slot: 'sex', width: 80 },
        { label: '年龄', prop: 'age', width: 80 },
        { label: '手机号', prop: 'phone', slot: 'phone' },
        { label: '身份证号', prop: 'idCard', slot: 'idCard' },
        { label: '机构名称', prop: 'departName', width: 120 },
        { label: '死亡时间', prop: 'deathDate', width: 120 },
        { label: '死亡原因', prop: 'deathReason', width: 160, showOverflowTooltip: true }
      ]
    }
  },
  methods: {
    genderTransform,
    async getTableList(params) {
      return await getDeathPersonList(params)
    }
  }
}
</script>
