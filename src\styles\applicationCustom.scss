$--color-primary: #0a86c8;
$--color-danger: #f67f7d;
.justFlexStart {
  display: flex !important;
  justify-content: flex-start !important;
}
.justFlexBetween {
  display: flex !important;
  justify-content: space-between !important;
}
.appCustomCapsuleTabBox {
  overflow: auto;
  margin-bottom: 20px;
  .appCustomCapsuleTab {
    margin-bottom: 0;
    margin-bottom: 10px;
  }
}
.justItemsEnd {
  align-items: end;
}
// 客制化 顶部搜索区
.applicationCustomSearchForm {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  .vwInputBox {
    .vwFormItem {
      display: flex;
      flex-wrap: wrap;
      align-items: end;
      &.noDateIcon {
        .el-date-editor {
          .el-input__icon.el-icon-date {
            display: none;
          }
        }
      }
    }
    .el-button {
      margin-top: 1.425vw;
    }
    .labelBox {
      display: inline-block;
      font-size: 0.625vw;
      padding: 0.25vw 0;
      margin: 0;
    }
    .el-date-editor {
      width: 50%;
    }
    .el-input__inner {
      height: 2vw !important;
      border-radius: 0.416vw !important;
      padding: 0 0.625vw;
      border-width: 0.1vw !important;
      line-height: 1.875vw !important;
      font-size: 0.73vw !important;
      font-weight: 300 !important;
      font-family: PingFangSC-Medium, PingFang SC !important;
    }
    .el-input__icon {
      line-height: 2vw;
    }
  }
  .el-form-item__label {
    // text-align: justify;
    // text-align-last: justify;
    /*兼容ie*/
    text-justify: distribute-all-lines;
    font-size: 0.7rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #666666;
    letter-spacing: 1px;
  }
  .searchFormItem {
    width: 292px;
    margin-right: 40px;

    .el-form-item {
      margin-right: 0;
      margin-bottom: 0;
    }
    .el-input__inner {
      width: 220px;
      height: 36px;
      background: #ffffff;
      border-radius: 8px;
      border: 1px solid #dedede;
      font-size: 0.7rem !important;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222222;
    }
    .el-input {
      .el-input__suffix {
        // top: 2px;
        // height: 36px;
        // line-height: 36px;
      }
    }
    .el-select {
      .el-input {
        .el-input__suffix {
          // top: 0;
          // height: 36px;
          // line-height: 36px;
        }
      }
    }
  }
  .searchFormBtnBox {
    flex: 1;
    height: 36px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .el-button {
      padding: 0 40px;
      height: 36px;
      border-radius: 8px;
      font-size: 0.7rem;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
    }
    .el-button + .el-button {
      margin-left: 16px;
    }
  }
  .searchFormBtnBoxVW {
    flex: 1;
    height: 1.875vw;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .el-button {
      padding: 0 2vw;
      height: 1.875vw;
      border-radius: 0.416vw;
      font-size: 0.73vw;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      margin-bottom: 0.5vw;
    }
    .el-button + .el-button {
      margin-left: 0.833vw;
    }
  }

  .wiTreeInputContainer {
    width: 220px;

    &.type-normal {
      .vue-treeselect {
        height: 36px;
        .vue-treeselect__control {
          padding: 0 5px 0 5px;
          background: transparent;
          border-radius: 8px;
          border: 1px solid #dedede;

          .vue-treeselect__value-container {
            .vue-treeselect__single-value {
              height: 36px;
              font-size: 0.7rem !important;
              font-family: PingFangSC-Regular, PingFang SC;
              line-height: 36px;
              padding: 0 10px !important;
              color: #606266;
            }
            .vue-treeselect__placeholder {
              height: 36px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-size: 0.7rem;
              line-height: 36px;
              padding: 0 10px;
            }
            .vue-treeselect__input-container {
              height: 36px;
              padding: 0 10px !important;
              .vue-treeselect__input {
                height: 36px;
              }
            }
            .vue-treeselect__multi-value {
              margin-bottom: 0 !important;
            }
          }
        }
      }
    }
  }

  .searchFormItemVw {
    width: calc((100% - 5vw) / 5);
    margin-right: 1vw;
    &.col4 {
      width: calc((100% - 5vw) / 4);
    }
    .el-form-item__content {
      height: 1.875vw;
      line-height: 1.875vw;
    }
    .el-input__icon {
      line-height: 1.9vw;
    }
    .el-form-item__label {
      height: 1.875vw;
      line-height: 1.875vw;
      font-size: 0.7rem;
      padding-right: 0.5vw;
    }
    .el-input__inner {
      width: 100%;
      height: 1.875vw;
      background: #ffffff;
      border-radius: 0.416vw;
      border: 0.052vw solid #dedede;
      font-size: 0.73vw !important;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222222;
    }

    .wiTreeInputContainer {
      width: 10vw;

      &.type-normal {
        .vue-treeselect {
          height: 1.875vw;
          .vue-treeselect__control {
            padding: 0 0.26vw 0 0.26vw;
            background: transparent;
            border-radius: 0.416vw;
            border: 0.052vw solid #dedede;

            .vue-treeselect__value-container {
              .vue-treeselect__single-value {
                height: 1.875vw;
                font-size: 0.73vw !important;
                font-family: PingFangSC-Regular, PingFang SC;
                line-height: 1.875vw;
                padding: 0 0.52083vw !important;
                color: #606266;
              }
              .vue-treeselect__placeholder {
                height: 1.875vw;
                font-family: PingFangSC-Regular, PingFang SC;
                font-size: 0.73vw;
                line-height: 1.875vw;
                padding: 0 0.52083vw;
              }
              .vue-treeselect__input-container {
                height: 1.875vw;
                padding: 0 0.52083vw !important;
                .vue-treeselect__input {
                  height: 1.875vw;
                }
              }
              .vue-treeselect__multi-value {
                margin-bottom: 0 !important;
              }
            }
          }
        }
      }
    }

    .el-button {
      padding: 0 2vw;
      height: 1.875vw;
      border-radius: 0.416vw;
      font-size: 0.73vw;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      margin-bottom: 0.5vw;
    }
    .el-button + .el-button {
      margin-left: 0.833vw;
    }
  }
}
.appSearchFormVw {
  .el-form-item__label {
    letter-spacing: 0.05208333vw;
    font-size: 0.72916vw;
    padding-right: 0.625vw;
  }
  .searchFormItem {
    width: auto;
    margin-right: 1vw;
    .el-input__inner {
      width: 11.4583vw;
      height: 1.874vw;
      border-radius: 0.4166vw;
      border-width: 0.05208333vw;
      font-size: 0.8rem !important;
    }
  }
  .searchFormBtnBox {
    height: 1.874vw;

    .el-button {
      padding: 0 2.083vw;
      height: 1.874vw;
      border-radius: 0.4166vw;
      font-size: 0.72916vw;
    }
    .el-button + .el-button {
      margin-left: 0.8333vw;
    }
  }
}
.bigScreenCustomSearchForm {
  .el-form-item__label {
    color: #27a8f1;
  }
  .searchFormItem {
    .el-input-number {
      width: 100%;
      line-height: 36px;
      &.yearNumberBox {
        &::after {
          content: '年';
          display: inline-block;
          width: 20px;
          text-align: center;
          position: absolute;
          right: 40px;
          top: 50%;
          transform: translateY(-50%);
          color: #27a8f1;
          font-size: 0.7rem !important;
        }

        .el-input__inner {
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 600;
          color: #27a8f1;
        }
      }
      .el-input-number__increase,
      .el-input-number__decrease {
        width: 36px;
        height: 36px;
        background: rgba(49, 160, 223, 0.15);
        border: 1px solid transparent;
        color: #27a8f1;
        border-width: 1px;
        font-size: 0.7rem !important;

        &.is-disabled {
          opacity: 0.45;
        }
      }
    }
    .el-input__inner {
      padding-left: 40px;
      padding-right: 40px;
      background: rgba(49, 160, 223, 0.15);
      border: 1px solid #27a8f1;
      color: #27a8f1;
      border-radius: 3px;
      &::-webkit-input-placeholder {
        color: #27a8f1;
        font-size: 0.6rem;
      }
    }
    .el-input__prefix {
      width: 14px;
      height: 14px;
      color: #27a8f1;
    }
    .el-input__suffix-inner {
      color: #27a8f1;
      i {
        color: #27a8f1;
      }

      &:hover {
        opacity: 0.777;
      }
    }
  }
}
// 客制化 表格
.applicationCustomPageTable {
  border: none !important;

  &::before {
    height: 0;
    background-color: transparent;
  }

  th.el-table__cell.el-table-column--selection > .cell {
    padding-left: 14px !important;
    padding-right: 14px !important;
  }

  .el-table-column--selection {
    .el-checkbox__inner {
      border-color: #999 !important;
    }

    .is-indeterminate,
    .is-checked {
      .el-checkbox__inner {
        border-color: $--color-primary !important;
      }
    }
  }
  th.el-table__cell {
    font-size: 0.7rem !important;
    font-family: PingFangSC-Regular, PingFang SC !important;
    font-weight: 400 !important;
    color: #999999 !important;
  }
  td.el-table__cell {
    // border-bottom: 1px solid #dedede !important;
    font-size: 0.7rem !important;
    font-family: PingFangSC-Regular, PingFang SC !important;
    font-weight: 400 !important;
    color: #222222 !important;
  }
  .el-table__cell {
    padding: 8px 0 !important;
  }

  /* 设置滚动条的宽度、高度、背景色和边框样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 10px;
    background-color: #f5f5f5;
    border-radius: 5px;
  }

  /* 设置滚动条滑块的背景色和圆角 */
  ::-webkit-scrollbar-thumb {
    background-color: $--color-primary;
    border-radius: 5px;
  }

  /* 设置滚动条滑块在悬停状态下的背景色和圆角 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #0dae96;
    border-radius: 5px;
  }

  /* 设置滚动条轨道的背景色和圆角 */
  ::-webkit-scrollbar-track {
    background-color: #f5f5f5;
    border-radius: 5px;
  }

  /* 设置滚动条轨道在悬停状态下的背景色和圆角 */
  ::-webkit-scrollbar-track:hover {
    background-color: #e4e4e4;
    border-radius: 5px;
  }
}
// 大屏表格样式
.bigScreenCustomTable {
  background-color: transparent;
  border: 1px solid transparent;

  &::before {
    height: 0;
    background-color: transparent;
  }
  .el-table__header-wrapper {
    tr {
      background-color: transparent;
      th.el-table__cell {
        border: 1px solid transparent;
        background: #0f2043;
        height: 20px;
        font-size: 0.6rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #08fffe;
        line-height: 20px;
        padding: 10px 15px;
      }
    }
  }
  .el-table__body-wrapper {
    tr {
      background-color: transparent;
      td.el-table__cell {
        border: 1px solid transparent;
        padding: 10px 15px;
        background: #070525;
        height: 20px;
        font-size: 0.6rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        line-height: 20px;
      }
      &:nth-child(2n) {
        td.el-table__cell {
          background: #0f2043;
        }
      }
      &:hover {
        td.el-table__cell {
          background: #292747;
        }
      }
    }
  }
}

.bigScreenCustomScrollBoard {
  .header-item {
    color: #08fffe;
  }
}
.applicationCustomPageTableVw {
  border: 0.05208333vw solid transparent;
  th.el-table__cell.el-table-column--selection > .cell {
    padding-left: 0.72916vw !important;
    padding-right: 0.72916vw !important;
  }
  th.el-table__cell {
    font-size: 0.8rem !important;
    height: 2vw !important;
    line-height: 2vw !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  td.el-table__cell {
    font-size: 0.8rem !important;
  }
  .el-table__cell {
    padding: 0.41666vw 0 !important;
  }
  .el-table__body-wrapper {
    height: calc(100% - 2vw) !important;
  }
}
.bigScreenCustomDrawer {
  background: radial-gradient(rgba(7, 0, 39, 0.9) 0%, rgba(3, 0, 17, 0.75) 100%);
  box-shadow: inset 0px 0px 27px 0px rgba(158, 191, 229, 0.3);
  border-radius: 3px;
}
// 客制化tab标签页
.applicationCustomTabs {
  border-radius: 0.625vw;
  overflow: hidden;
  box-shadow: none;
  border: none;
  .el-tabs__nav {
    &.is-top {
      width: 100%;
      display: flex;

      .el-tabs__item {
        border: none;
        border-left: 0.1vw solid #f3fffd !important;
        border-right: 0.1vw solid #f3fffd !important;
        font-size: 0.833vw;
        &:last-child {
          border-right: 0 !important;
        }
        &.is-top {
          text-align: center;
          flex: 1;
          height: 3.33vw;
          line-height: 3.33vw;
          background: #ebf7f5;
          color: #666666;
          &.is-active {
            background: #f3fffd;
            color: #0a86c8;
          }
        }
      }
    }
  }
}
.appTableBgBorder {
  border: 0.052083vw solid #e2e2e2 !important;
  border-radius: 0.624996vw;
  &::before {
    height: 0 !important;
  }

  tr {
    .el-table__cell {
      padding-left: 0.5vw !important;
      padding-right: 0.5vw !important;
      .cell {
        padding: 0;
      }
    }
  }
  th.el-table__cell {
    background: #e7e7e7 !important;
    color: #666666 !important;
  }
  .el-table__body {
    tbody {
      .el-table__row:last-child {
        td.el-table__cell {
          border-bottom: 1px solid transparent;
        }
      }
    }
  }
}
.appTableBgBorderNoPadding {
  border: 0.052083vw solid #e2e2e2 !important;
  border-radius: 0.624996vw;
  th.el-table__cell.el-table-column--selection > .cell {
    padding-left: 14px !important;
    padding-right: 14px !important;
  }
  &::before {
    height: 0 !important;
  }
  th.el-table__cell {
    background: #e7e7e7 !important;
    color: #666666 !important;
  }
  .el-table__body {
    tbody {
      .el-table__row:last-child {
        td.el-table__cell {
          border-bottom: 1px solid transparent;
        }
      }
    }
  }
}
// 套用客制化滚动条的模块
.applicationCustomScrollBar {
  /* 设置滚动条的宽度、高度、背景色和边框样式 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 5px;
    background-color: #f5f5f5;
    border-radius: 5px;
  }

  /* 设置滚动条滑块的背景色和圆角 */
  ::-webkit-scrollbar-thumb {
    background-color: $--color-primary;
    border-radius: 5px;
  }

  /* 设置滚动条滑块在悬停状态下的背景色和圆角 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #0dae96;
    border-radius: 5px;
  }

  /* 设置滚动条轨道的背景色和圆角 */
  ::-webkit-scrollbar-track {
    background-color: #f5f5f5;
    border-radius: 5px;
  }

  /* 设置滚动条轨道在悬停状态下的背景色和圆角 */
  ::-webkit-scrollbar-track:hover {
    background-color: #e4e4e4;
    border-radius: 5px;
  }
}
.applicationCustomLittleGreyScroll {
  /* 设置滚动条的宽度、高度、背景色和边框样式 */
  ::-webkit-scrollbar {
    width: 4px;
    height: 3px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  /* 设置滚动条滑块的背景色和圆角 */
  ::-webkit-scrollbar-thumb {
    background-color: #c7c7c7;
    border-radius: 4px;
  }

  /* 设置滚动条滑块在悬停状态下的背景色和圆角 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #c4c4c4;
    border-radius: 4px;
  }

  /* 设置滚动条轨道的背景色和圆角 */
  ::-webkit-scrollbar-track {
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  /* 设置滚动条轨道在悬停状态下的背景色和圆角 */
  ::-webkit-scrollbar-track:hover {
    background-color: #e4e4e4;
    border-radius: 4px;
  }
}
// 客制化 分页组件
.applicationCustomPagePagination {
  text-align: right;
  margin-top: 5px;
}
// 客制化 dialog
.applicationCustomDialog {
  border-radius: 12px !important;
  overflow: visible;
  .el-dialog__header {
    border-radius: 12px 12px 0 0 !important;
    overflow: hidden;
    position: relative;
    padding: 0;
    .el-dialog__headerbtn {
      top: 50%;
      transform: translateY(-50%);
      right: 20px;
      font-size: 1rem;
      color: #999999;
    }
    .customDialogTitle {
      display: inline-block;
      width: 100%;
      height: 50px;
      line-height: 50px;
      background: #f3fffd;
      position: relative;
      text-align: center;
      font-size: 0.8rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222222;

      .titleLeftModule {
        position: absolute;
        left: 32px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .customLeftDialogTitle {
      text-align: left;
      padding: 0 20px;
    }
  }
  .el-dialog__body {
    padding: 20px 32px !important;
    background-color: #ffffff !important;
    border-radius: 0 0 12px 12px !important;

    .applicationCustomPageTable th.el-table__cell {
      background: #f8f8f8 !important;
      color: #666666 !important;
    }
    .darkBg th.el-table__cell {
      background: #e7e7e7 !important;
      color: #666666 !important;
    }
  }
  .el-dialog__footer {
    padding: 0 32px 20px;
    .customDialogFooter {
      .el-button.el-button--success.is-plain {
        padding: 0;
        width: 140px;
        height: 32px;
        line-height: 30px;
        background: #f3fffd;
        border: 2px solid $--color-primary;
        border-radius: 6px;
        font-size: 0.7rem;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: $--color-primary;
        margin-right: 24px;
      }
      .el-button.el-button--success {
        padding: 0;
        width: 140px;
        height: 32px;
        line-height: 30px;
        background: $--color-primary;
        border-radius: 6px;
        font-size: 0.7rem;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }
    }
  }
}
// 客制化dialog - vw单位版
.applicatoinCustomVwDialog {
  border-radius: 0.625vw !important;
  overflow: hidden;
  .el-dialog__header {
    position: relative;
    padding: 0;
    .el-dialog__headerbtn {
      top: 50%;
      transform: translateY(-50%);
      right: 1.041666vw;
      font-size: 1.041666vw;
      color: #999999;
    }
    .customDialogTitle {
      display: inline-block;
      width: 100%;
      height: 2.6041665vw;
      line-height: 2.6041665vw;
      background: #f3fffd;
      position: relative;
      text-align: center;
      font-size: 0.8333vw;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222222;

      .titleLeftModule {
        position: absolute;
        left: 1.6666vw;
        top: 50%;
        transform: translateY(-50%);
      }
    }
    .customLeftDialogTitle {
      text-align: left;
      padding: 0 1.041666vw;
    }
  }
  .el-dialog__body {
    padding: 1.041666vw 1.6666vw !important;
    background-color: #ffffff !important;

    .applicationCustomPageTable th.el-table__cell {
      background: #f8f8f8 !important;
      color: #666666 !important;
    }
  }
}
// 客制化 drawer
.applicationCustomDrawer {
  .customDrawerHeaderNoTitle {
    position: absolute;
    z-index: 2;
    right: 15px;
    top: 15px;
    font-size: 1rem;
    cursor: pointer;

    &:hover {
      opacity: 0.777;
    }
  }
}
// 客制化 缺省页
.applicationNoDataBox {
  .noDataImgBox {
    user-select: none;
    text-align: center;
    img {
      user-select: none;
      -webkit-user-drag: none;
      width: 120px;
      height: 85px;
    }
  }

  .noDataText {
    user-select: none;
    text-align: center;
    margin-top: 12px;
    height: 25px;
    font-size: 0.7rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #999999;
    line-height: 25px;
  }
}
// 客制化 popConfirm
.applicationCustomPopconfirm {
  padding: 0 14px 14px;
}
// 客制化tree
.applicationCustomTree {
  &.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    color: $--color-primary;
  }
}
// 一些按钮样式
.customDangerBtn {
  font-family: PingFangSC-Regular, PingFang SC !important;
  font-weight: 400 !important;
  color: #ee5757 !important;
}

.el-button {
  border-radius: 8px;
  border-width: 1.5px;
}

// button 样式全部重写
.el-button.el-button--medium {
  min-width: 96px;
  font-size: 0.7rem !important;
  font-family: PingFangSC-Medium, PingFang SC !important;
  font-weight: 500 !important;
}
.el-button.el-button--text.el-button--medium {
  min-width: 0;
}
// primary button 样式全部重写
.el-button--primary {
  background-color: $--color-primary !important;
  border-color: $--color-primary !important;
}
// .el-button--primary:focus,
.el-button--primary:hover {
  background: $--color-primary !important;
  border-color: $--color-primary !important;
  opacity: 0.777;
}
.el-button--primary:active,
.el-button--primary.is-active,
.el-button--primary.is-plain:active {
  background: #0a86c8 !important;
  border-color: #0a86c8 !important;
}
.el-button--primary.is-disabled,
.el-button--primary.is-disabled:active,
// .el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:hover {
  background-color: #769aad !important;
  border-color: #769aad !important;
  color: #fff;
}
.el-button--primary.is-plain {
  color: $--color-primary !important;
  background: #f3fffd !important;
}
// .el-button--primary.is-plain:focus,
.el-button--primary.is-plain:hover {
  background: $--color-primary !important;
  border-color: $--color-primary !important;
  color: #fff !important;
}
.el-button--primary.is-plain.is-disabled,
.el-button--primary.is-plain.is-disabled:active,
// .el-button--primary.is-plain.is-disabled:focus,
.el-button--primary.is-plain.is-disabled:hover {
  color: #ddd !important;
  background-color: #fff !important;
  border-color: #daece8 !important;
}
// danger button 样式全部重写
.el-button--danger {
  background-color: $--color-danger !important;
  border-color: $--color-danger !important;
}
// .el-button--danger:focus,
.el-button--danger:hover {
  background: $--color-danger !important;
  border-color: $--color-danger !important;
  opacity: 0.9;
}
.el-button--danger:active,
.el-button--danger.is-active,
.el-button--danger.is-plain:active {
  background: #d45d5b !important;
  border-color: #d45d5b !important;
}
.el-button--danger.is-disabled,
.el-button--danger.is-disabled:active,
// .el-button--danger.is-disabled:focus,
.el-button--danger.is-disabled:hover {
  background-color: #fdefef !important;
  border-color: #fdefef !important;
  color: #fff;
}
.el-button--danger.is-plain {
  color: $--color-danger !important;
  background: #fdefef !important;
}
// .el-button--danger.is-plain:focus,
.el-button--danger.is-plain:hover {
  background: $--color-danger !important;
  border-color: $--color-danger !important;
  color: #fff !important;
}
.el-button--danger.is-plain.is-disabled,
.el-button--danger.is-plain.is-disabled:active,
// .el-button--danger.is-plain.is-disabled:focus,
.el-button--danger.is-plain.is-disabled:hover {
  color: #ddd !important;
  background-color: #fff !important;
  border-color: #fdefef !important;
}

// text 类型按钮 加粗
.el-button.el-button--text {
  font-size: 0.7rem;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
}

.el-tag {
  border-radius: 4px;
  background-color: #e7f9ff;
  border: none;
  color: #3baecf;
}

.el-tag.el-tag--success {
  border-radius: 4px;
  background-color: #e6fbf8;
  border: none;
  color: $--color-primary;
}
.el-tag.el-tag--warning {
  background-color: #ffece3;
  border-color: #ffdbdb;
  color: #f88d55;
}
.el-tag.el-tag--danger {
  background-color: #ffe6e6;
  border-color: #ffdbdb;
  color: #ee5757;
}
// el-radio-button
// .el-radio-group {
//   outline: none !important;
//   .el-radio-button {
//     overflow: hidden;
//     outline: none !important;
//     box-shadow: none !important;
//     user-select: none !important;
//     &:first-child .el-radio-button__inner {
//       border-radius: 8px 0 0 8px;
//     }
//     &:last-child .el-radio-button__inner {
//       border-radius: 0 8px 8px 0;
//     }
//     .el-radio-button__inner {
//       font-size: 0.7rem;
//       font-family: PingFangSC-Medium, PingFang SC;
//       font-weight: 500;
//       padding: 0 14px;
//       height: 36px;
//       line-height: 34px;
//       background: #f3fffd;
//       color: #606266;
//       border: none !important;
//       box-shadow: none;
//     }
//     &.is-active {
//       .el-radio-button__inner {
//         background: #e0fdf8;
//         color: $--color-primary;
//       }
//     }
//   }

//   &.appRadioVw {
//     .el-radio-button {
//       &:first-child .el-radio-button__inner {
//         border-radius: 0.416664vw 0 0 0.416664vw;
//       }
//       &:last-child .el-radio-button__inner {
//         border-radius: 0 0.416664vw 0.416664vw 0;
//       }
//       .el-radio-button__inner {
//         font-size: 0.729162vw;
//         padding: 0 0.729162vw;
//         height: 1.875vw;
//         line-height: 1.875vw;
//       }
//     }
//   }
// }
// viewer 顶顶顶
.viewer-container {
  z-index: 99999999 !important;
}
.el-scrollbar__thumb {
  background-color: #cacdd5;
}
.el-tabs__item {
  outline: none !important;
  box-shadow: none !important;
  user-select: none !important;
}

// ====== table表格横向滚动条样式 Lisuwan 2024.08.29 start======

.el-table__body-wrapper::-webkit-scrollbar {
  width: 0.3rem; /* 滚动条的宽度 */
  height: 0.4rem; /* 滚动条的高度，对水平滚动条有效 */
  background-color: transparent; /* 滚动条的背景颜色 */
}

.el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #cacdd5; /* 滚动条颜色 */
  border-radius: 0.4rem;
}

.el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: transparent; /* 滚动条轨道颜色 */
}

/* 兼容IE的滚动条样式 */
.el-table__body-wrapper {
  -ms-overflow-style: none; /* IE和Edge浏览器中隐藏滚动条*/
}

/* 添加滚动条样式的同时保留滚动功能 */
.el-table__body-wrapper::-webkit-scrollbar {
  -webkit-appearance: none;
  overflow: scroll;
}
// ====== table表格横向滚动条样式 Lisuwan 2024.08.29 end ======

.scale80Percent {
  transform: scale(0.8);
}

.bigScreenCustomScrollBar {
  /* 设置滚动条的宽度、高度、背景色和边框样式 */
  ::-webkit-scrollbar {
    width: 4px;
    height: 6px;
    background-color: #f5f5f5;
    border-radius: 4px;
  }

  /* 设置滚动条滑块的背景色和圆角 */
  ::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, #a8ffff 0%, rgba(168, 255, 255, 0) 100%);
    border-radius: 4px;
  }

  /* 设置滚动条滑块在悬停状态下的背景色和圆角 */
  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, #62ffff 0%, rgba(215, 255, 255, 0) 100%);
    border-radius: 4px;
  }

  /* 设置滚动条轨道的背景色和圆角 */
  ::-webkit-scrollbar-track {
    background-color: #171a3b;
    border-radius: 4px;
  }

  /* 设置滚动条轨道在悬停状态下的背景色和圆角 */
  ::-webkit-scrollbar-track:hover {
    background-color: #273455;
    border-radius: 4px;
  }
}

.bigScreenInput {
  width: 100%;
  .el-input__inner {
    width: 390px;
    height: 40px;
    border-radius: 3px;
    border: 1px solid #08fffe;
    background-color: #060823;
    font-size: 0.6rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #08fffe;
    line-height: 17px;
    &::-webkit-input-placeholder {
      color: #08fffe;
    }
  }
  .el-input__suffix {
    color: #08fffe;
  }
  &:not(.is-disabled):hover .el-input__inner {
    border: 1px solid #08fffe;
    background-color: #060823;
  }
}

.bigScreenInputPopper {
  background: linear-gradient(360deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.3) 100%);
  border: 1px solid rgba(0, 214, 215, 0.5);
  backdrop-filter: blur(5px);

  font-size: 0.729162vw;
  font-family: Arial;
  line-height: 1.249992vw;
  transition-duration: 0 !important;

  &.el-tooltip__popper {
    padding: 0.52083vw;
    &.is-dark {
      background: rgba(50, 50, 50, 0.7);
      border: none !important;

      &.colorECD268 {
        border: 1px solid #ecd268 !important;
      }
      &.color19F4FE {
        border: 1px solid #19f4fe !important;
      }
      &.color5ED89E {
        border: 1px solid #5ed89e !important;
      }
      &.colorCB61F8 {
        border: 1px solid #cb61f8 !important;
      }
      &.colorF46869 {
        border: 1px solid #f46869 !important;
      }
      &.color9B65EF {
        border: 1px solid #9b65ef !important;
      }

      .toolValueBox {
        font-weight: 600;
      }
    }
    &[x-placement^='top'] {
      .popper__arrow {
        border-top-color: rgba(50, 50, 50, 0);
        &::after {
          border-top-color: rgba(50, 50, 50, 0);
        }
      }
    }
  }
  &.el-popper[x-placement^='bottom'] .popper__arrow {
    border-bottom-color: rgba(0, 214, 215, 0.5);
    &::after {
      border-bottom-color: rgba(0, 214, 215, 0.5);
    }
  }
  .el-cascader-menu {
    min-width: 120px;
    border-right: 2px solid transparent;

    &:last-child {
      border-right: none;
    }
  }
  .el-cascader-menu__wrap {
    .el-cascader-menu__list {
      &:before {
        content: none !important;
      }
      list-style: none !important;
      list-style-type: none !important;
    }
    .el-cascader-node {
      height: 24px;
      font-size: 0.6rem;
      list-style: none !important;
      list-style-type: none !important;
      position: relative;
      &:before {
        content: none !important;
      }
      .el-radio {
        width: calc(100% - 40px);
        display: flex;
        align-items: center;
        height: 100%;
        position: absolute;
      }
      .el-cascader-node__label {
        height: 24px;
        line-height: 24px;
        margin-left: 10px;
        font-size: 0.6rem;
        color: #ffffff;
      }
      &.in-active-path,
      &.is-active,
      &:not(.is-disabled):hover,
      &:not(.is-disabled):focus {
        background-color: #046f7f;
      }
      .el-cascader-node__postfix {
        color: #ffffff;
      }
      .el-radio__inner {
        width: 10px;
        height: 10px;
        border: 1px solid #ffffff;
        background: rgba(0, 214, 215, 0);
      }
      &.in-active-path,
      &.is-active {
        .el-radio__input {
          .el-radio__inner {
            width: 10px;
            height: 10px;
            border-color: #08fffe;
            background: rgba(0, 214, 215, 0.1);

            &::after {
              width: 6px;
              height: 6px;
              background-color: #08fffe;
            }
          }
        }
        .el-cascader-node__label {
          color: #08fffe;
        }
        .el-cascader-node__postfix {
          color: #ffffff;
        }
      }
    }
  }
  .el-cascader__suggestion-item {
    color: #ffffff;
    &:hover,
    &:focus {
      background: #046f7f;
    }
  }
}

.bigScreenInputPopperVw {
  border: 0.052083vw solid rgba(0, 214, 215, 0.5);
  backdrop-filter: blur(0.260415vw);

  font-size: 0.729162vw;
  font-family: Arial;
  line-height: 1.249992vw;
  transition-duration: 0 !important;

  &.el-tooltip__popper {
    padding: 0.52083vw;
  }
  .el-cascader-menu {
    min-width: 6.24996vw;
    border-right: 0.104166vw solid transparent;

    &:last-child {
      border-right: none;
    }
  }
  .el-cascader-menu__wrap {
    height: 10.624932vw;
    .el-cascader-node {
      height: 1.249992vw;
      font-size: 0.624996vw;
      padding: 0 1.56249vw 0 1.04166vw;
      .el-radio {
        width: calc(100% - 2.08332vw);
      }
      .el-cascader-node__label {
        height: 1.249992vw;
        line-height: 1.249992vw;
        margin-left: 0.52083vw;
        font-size: 0.624996vw;
      }
      .el-radio__inner {
        width: 0.52083vw;
        height: 0.52083vw;
        border: 0.052083vw solid #ffffff;
      }
      &.in-active-path,
      &.is-active {
        .el-radio__input {
          .el-radio__inner {
            width: 0.52083vw;
            height: 0.52083vw;

            &::after {
              width: 0.312498vw;
              height: 0.312498vw;
            }
          }
        }
      }
    }
  }
}

.teleDialogContainer {
  overflow-y: auto;
  .el-dialog__wrapper {
    overflow: hidden !important;
  }
  .el-dialog__body {
    height: 39vw;
    overflow-y: auto;
    padding: 0 !important;
  }
}
.memberPopover {
  padding: 0 !important;
}
.memberPopContent {
  display: flex;
  justify-content: flex-start;
  width: 15vw;
  flex-wrap: wrap;
  padding: 0.3vw 0;
  .memberPopItem {
    width: 5vw;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding: 0.2083vw 0.833vw;

    .nameText {
      height: 0.885vw;
      font-size: 0.625vw;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #222222;
      line-height: 0.885vw;
      margin-right: 0.312vw;
    }
    .svg-icon {
      width: 0.625vw;
      height: 0.625vw;
    }
  }
}
// 胶囊tab样式
ul.appCustomCapsuleTab {
  list-style-type: none;
  margin: 0;
  margin-bottom: 30px;
  padding: 0;
  display: flex;
  // justify-content: center;

  li {
    text-align: center;
    height: 36px;
    line-height: 36px;
    display: inline-block;
    padding: 0 16px;
    border-radius: 20px;
    background-color: transparent;
    margin-right: 14px;
    box-sizing: content-box;
    cursor: pointer;
    position: relative;
    background: #ebf7f5;
    border-radius: 18px;
    font-size: 0.7rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #0a86c8;

    .badge {
      position: absolute;
      right: 0px;
      top: 0px;
      height: 8px;
      width: 8px;
      background-color: #f56c6c;
      border-radius: 50%;
    }
  }

  li.active {
    background-color: #0a86c8;
    color: #fff;
    font-weight: 500;
  }
}
.appCustomBtnStyle {
  width: 5vw !important;
  height: 2vw !important;
  padding: 0 !important;
  border-radius: 0.416vw !important;
  border-width: 0.1vw !important;
  line-height: 1.875vw !important;
  padding: 0 !important;
  font-size: 0.73vw !important;
  font-weight: 500 !important;
  font-family: PingFangSC-Medium, PingFang SC !important;
  display: flex;
  align-items: center;
  justify-content: center;
  > span {
    width: auto !important;
  }
}
.boldFontBtn {
  font-size: 0.833vw !important;
  font-weight: 600 !important;
}
.appDelBtn {
  color: #ee5757 !important;
}
.primaryBtn {
  color: $--color-primary;
}
.el-message-box__wrapper {
  .el-message-box {
    border-radius: 12px !important;
    .el-message-box__header {
      line-height: 1.5;
      padding-bottom: 10px;
      background-color: #f6f6f6;
      .el-message-box__title {
        line-height: 1.5;
      }
      .el-message-box__title {
        height: 15px;
        font-size: 0.65rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
        // line-height: 15px;
      }
    }
    .el-message-box__content {
      .el-message-box__status {
        font-size: 0.8rem;
      }

      .el-message-box__message {
        font-size: 0.8rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 24px;
      }
    }
    .el-message-box__btns {
      margin-top: 0.7vw !important;

      .el-button {
        width: 126px;
        padding: 0;
        height: 40px;
        border-radius: 8px;
        border-width: 1px;
        border-color: #0a86c8;
        font-size: 0.7rem;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        line-height: 40px;
        color: #0a86c8;
        &.el-button--primary {
          color: #ffffff;
        }
        &:hover {
          background-color: var(--colorBtnHover);
          border-color: var(--colorBtnHover);
          color: #fff;
        }
      }
      .el-button.el-button--text:hover {
        background: transparent;
        border: none;
      }
    }
  }

  &[aria-label='dialog'] {
    .el-message-box__header {
      display: none !important;
    }
    .el-message-box__content {
      padding: 30px 15px;
    }
    .el-message-box__btns {
      margin-top: 0 !important;
    }
  }
}

.el-message {
  min-width: 320px !important;
  padding: 15px 20px !important;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.3) !important;
  border-radius: 6px !important;
  transform: translate(-50%, 40px) !important;
  .el-message__icon {
    font-size: 1rem !important;
  }
  .el-message__content {
    min-height: 20px !important;
    font-size: 0.8rem !important;
    font-family: PingFangSC-Regular, PingFang SC !important;
    font-weight: 400 !important;
    color: #222222 !important;
    line-height: 20px !important;
  }
  &.el-message--warning {
    background-color: #fef7ea !important;

    .el-message__icon {
      color: #fbb010 !important;
    }
  }
  &.el-message--error {
    background-color: #fcece9 !important;

    .el-message__icon {
      color: #ff624a !important;
    }
  }
  &.el-message--success {
    background-color: #ebf7ee !important;

    .el-message__icon {
      color: #1fc94b !important;
    }
  }
}

.el-tooltip__popper {
  max-width: 61.8vw;
}

.appCustomDisableClick {
  pointer-events: none;
}
