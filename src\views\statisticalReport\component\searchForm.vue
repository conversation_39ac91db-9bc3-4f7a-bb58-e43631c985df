<template>
  <div class="search-form">
    <el-form :model="queryParams" label-width="120px">
      <el-row>
        <el-col v-if="queryCriteria.includes('timeRange')" :span="6">
          <el-form-item label="日期查询：">
            <el-date-picker
              v-model="queryParams.timeRange"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>

        <el-col v-if="queryCriteria.includes('disease')" :span="6">
          <el-form-item label="慢病病种：">
            <el-select v-model="queryParams.disease" style="width: 100%">
              <el-option label="糖尿病" value="tnb" />
              <el-option label="高血压" value="gxy" />
              <el-option label="慢阻肺" value="COPD" />
              <el-option label="房颤" value="fangchan" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col v-if="queryCriteria.includes('departCode')" :span="6">
          <el-form-item label="医疗机构：" prop="depart">
            <TreeSelect
              v-model="queryParams.departCode"
              :data="departTree"
              :props="{
                children: 'children',
                label: 'departName',
                value: 'departCode'
              }"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6" style="text-align: center">
          <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
          <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { getOrgTreeByIdApi } from '@/api/system'
import { getUserId } from '@/utils/auth'
import TreeSelect from '@/components/TreeSelect/index.vue'

export default {
  name: 'SearchForm',
  components: {
    TreeSelect
  },
  props: {
    queryParams: {
      type: Object,
      default: () => {}
    },
    queryCriteria: {
      type: Array,
      default: () => ['timeRange', 'disease', 'departCode']
    }
  },
  data() {
    return {
      departTree: []
    }
  },
  created() {
    this.getDepartTree()
  },
  methods: {
    // 机构树
    async getDepartTree() {
      const res = await getOrgTreeByIdApi({
        patientId: getUserId()
      })
      this.departTree = res.data
    },
    handleSearch() {
      this.$emit('search')
    },
    handleReset() {
      this.$emit('reset')
    }
  }
}
</script>
