<!-- 接诊记录 -->
<template>
  <div class="visit-record">
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      row-key="id"
      :columns="columns"
      :show-pagination="showPagination"
    >
      <<template #receptionStatus="{ row }">
        <el-tag v-if="row.receptionStatus === 1" type="primary">接诊中</el-tag>
        <el-tag v-else-if="row.receptionStatus === 5" type="success">已完成</el-tag>
        <el-tag v-else-if="row.receptionStatus === 9" type="danger">已取消</el-tag>
      </template>
      <template #operation="{ row }">
        <el-button type="text" size="small" @click="handleDetail(row)">详情</el-button>
      </template>
    </base-table>
  </div>
</template>

<script>
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import { getReceptionWorkbenchHistoryList } from '@/api/receptionWorkbench'

export default {
  name: 'VisitRecord',
  components: {
    BaseTable
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        patientId: this.$route.query.id
      },
      columns: [
        { label: '接诊日期', prop: 'receptionDate' },
        { label: '接诊医生', prop: 'receptionDoctorName' },
        { label: '取消接诊原因', prop: 'cancelReason' },
        { label: '接诊状态', prop: 'receptionStatus', slot: 'receptionStatus' },
        { label: '操作', slot: 'operation' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      return await getReceptionWorkbenchHistoryList(params)
    },
    handleDetail(row) {
      this.$router.push({
        path: '/receptionCenter/patientReception',
        query: { id: row.id }
      })
    }
  }
}
</script>
