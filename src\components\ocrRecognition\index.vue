<template>
  <div class="ocr-recognition">
    <el-card class="ocr-card">
      <div class="camera-container">
        <div id="CameraCtl" ref="cameraCtl" class="camera-view" />

        <div class="camera-controls-horizontal">
          <div class="option-item">
            <label>分辨率</label>
            <el-select v-model="selectedResolution" placeholder="请选择分辨率" size="small" @change="SelectResolution">
              <el-option v-for="(res, index) in resolutionList" :key="index" :label="res" :value="index" />
            </el-select>
          </div>
          <div class="option-item">
            <label>裁剪模式</label>
            <el-radio-group v-model="cutMode" @change="SetCameraCutMode">
              <el-radio :label="0">无裁剪</el-radio>
              <el-radio :label="2">手动裁剪</el-radio>
            </el-radio-group>
          </div>
        </div>

        <el-progress :percentage="progress" style="width: 50%" />

        <div class="camera-controls-horizontal">
          <div class="option-item">
            <el-button type="primary" size="small" @click="initCamera">打开高拍仪</el-button>
          </div>
          <div class="option-item">
            <el-button type="primary" size="small" @click="closeCamera">关闭高拍仪</el-button>
          </div>
          <div class="option-item">
            <el-button type="primary" size="small" icon="el-icon-camera" @click="TakePhoto">拍照</el-button>
          </div>
          <div class="option-item">
            <el-button type="primary" size="small" @click="getData">数据解析</el-button>
          </div>
        </div>
      </div>

      <div class="result-container">
        <div class="photo-preview">
          <div class="preview-title">拍照预览</div>
          <div class="preview-box">
            <img v-if="cameraPhoto" id="CameraPhoto" :src="cameraPhoto" alt="拍照预览">
            <div v-if="!cameraPhoto" class="no-image">等待拍照...</div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
/* global Cam_ControlInit, Cam_GetDevResolution, Cam_Close, Cam_SetCutMode, Cam_Open, Cam_Photo */
import { getOcrApi, fileUoload } from '@/api/system'
import { convertBase64ToFormData, splitUrl } from '@/utils/cspUtils'
import { mapGetters } from 'vuex'

export default {
  name: 'OcrRecognition',
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      deviceNames: [],
      resolutionList: [],
      selectedDevice: 0,
      selectedResolution: 0,
      cameraPhoto: '',
      textInfo: '',
      cutMode: 0,
      // 保存原始函数的引用
      originalMainCanvasMouseDown: null,
      originalMainCanvasMouseMove: null,
      progress: 0,
      progressInterval: null // 进度条定时器
    }
  },
  computed: {
    ...mapGetters(['departCode', 'deviceConnect'])
  },
  mounted() {
    // 在初始化摄像头前，先保存并覆盖原始的鼠标事件处理函数
    if (window.MainCanvasMouseDown) {
      this.originalMainCanvasMouseDown = window.MainCanvasMouseDown
      window.MainCanvasMouseDown = function(e) {
        window.isMouseDown = true
        // 使用getBoundingClientRect获取准确的坐标
        const rect = window.MainCanvas.getBoundingClientRect()
        window.pALastX = e.clientX - rect.left
        window.pALastY = e.clientY - rect.top
        if (window.MainCamCutMode === 2) {
          window.pACurrentX = window.pALastX
          window.pACurrentY = window.pALastY
        }
      }
    }

    if (window.MainCanvasMouseMove) {
      this.originalMainCanvasMouseMove = window.MainCanvasMouseMove
      window.MainCanvasMouseMove = function(e) {
        if (window.isMouseDown) {
          if (window.MainCamCutMode === 2) {
            // 使用getBoundingClientRect获取准确的坐标
            const rect = window.MainCanvas.getBoundingClientRect()
            window.pACurrentX = e.clientX - rect.left
            window.pACurrentY = e.clientY - rect.top

            // 以下逻辑与原函数保持一致
            let rectx,
              recty,
              rectw,
              recth
            if (window.pALastX > window.pACurrentX) rectx = window.pACurrentX
            else rectx = window.pALastX
            if (window.pALastY > window.pACurrentY) recty = window.pACurrentY
            else recty = window.pALastY
            // eslint-disable-next-line prefer-const
            rectw = Math.abs(window.pACurrentX - window.pALastX)
            // eslint-disable-next-line prefer-const
            recth = Math.abs(window.pACurrentY - window.pALastY)

            window.SetCutRect(rectx, recty, rectw, recth)
          } else {
            // 非裁剪模式的处理逻辑保持不变
            const rect = window.MainCanvas.getBoundingClientRect()
            window.pACurrentX = e.clientX - rect.left
            window.pACurrentY = e.clientY - rect.top
            const dx = window.pACurrentX - window.pALastX
            const dy = window.pACurrentY - window.pALastY
            let xdir = 0
            let ydir = 0
            if (dx < 0) xdir = 0
            else xdir = 1
            if (dy < 0) ydir = 0
            else ydir = 1
            window.pALastX = window.pACurrentX
            window.pALastY = window.pACurrentY
            window.MoveOffsetXY(Math.abs(dx), Math.abs(dy), xdir, ydir)
          }
        }
      }
    }

    // 挂载回调到全局（供 gpyhs.js 调用）
    window.GetDevCountAndNameResultCB = this.GetDevCountAndNameResultCB
    window.GetResolutionResultCB = this.GetResolutionResultCB
    window.GetCameraOnOffStatus = this.GetCameraOnOffStatus
    window.GetCaptrueImgResultCB = this.GetCaptrueImgResultCB
    window.ShowInfo = this.ShowInfo
  },
  beforeDestroy() {
    // 组件销毁前恢复原始的鼠标事件处理函数
    if (this.originalMainCanvasMouseDown && window.MainCanvasMouseDown) {
      window.MainCanvasMouseDown = this.originalMainCanvasMouseDown
    }
    if (this.originalMainCanvasMouseMove && window.MainCanvasMouseMove) {
      window.MainCanvasMouseMove = this.originalMainCanvasMouseMove
    }
  },
  destroyed() {
    this.$store.commit('app/SET_DEVICE_CONNECT', false)
    this.closeCamera()
  },
  methods: {
    initCamera() {
      // 初始化摄像头
      this.$nextTick(() => {
        if (!window.WebSocket) {
          this.$message.error('浏览器不支持HTML5，请更换浏览器')
          return
        }

        if (this.deviceConnect) {
          this.$message.warning('高拍仪已打开')
          return
        }

        Cam_ControlInit(this.$refs.cameraCtl, 0, 0, 600, 400)
      })
    },

    ShowInfo(msg) {
      this.textInfo += `\r\n${msg}`
    },
    formatDate(time) {
      const date = new Date(time)
      const pad = (n) => (n < 10 ? `0${n}` : n)
      return `${date.getFullYear()}${pad(date.getMonth() + 1)}${pad(date.getDate())}${pad(date.getHours())}${pad(
        date.getMinutes()
      )}${pad(date.getSeconds())}`
    },
    sleep(ms) {
      const start = Date.now()
      while (Date.now() < start + ms);
    },
    GetDevCountAndNameResultCB(devCount, devNameArr) {
      if (devCount > 0) {
        this.deviceNames = devNameArr
        this.selectedDevice = 0
        Cam_GetDevResolution(this.selectedDevice)
      } else {
        this.ShowInfo('没有发现合适的设备！')
        this.$message.warning('没有发现合适的设备！')
      }
    },
    GetResolutionResultCB(resCount, resArr) {
      if (resCount > 0) {
        this.resolutionList = resArr
        const defaultIndex = resArr.findIndex((r) => r === '2592*1944')
        this.selectedResolution = defaultIndex >= 0 ? defaultIndex : 0

        Cam_Close()
        this.sleep(100)

        this.toOpenCamera()
      } else {
        this.ShowInfo('获取分辨率信息失败！')
        this.$message.error('获取分辨率信息失败！')
      }
    },
    GetCameraOnOffStatus(status) {
      if (status === 0) {
        this.ShowInfo('设备开启成功')
        this.$message.success('设备开启成功')
        this.$store.commit('app/SET_DEVICE_CONNECT', true)
      } else {
        this.ShowInfo('设备开启失败！')
        this.$message.error('设备开启失败！')
        this.$store.commit('app/SET_DEVICE_CONNECT', false)
      }
    },
    GetCaptrueImgResultCB(flag, path, base64Str) {
      if (flag === 0) {
        this.cameraPhoto = `data:;base64,${base64Str}`
        this.ShowInfo(path ? `拍照成功，图片保存位置：${path}` : '拍照成功')
        this.$message.success('拍照成功')
      } else {
        this.ShowInfo('拍照失败！')
        this.$message.error('拍照失败！')
      }
    },

    // 裁剪
    SetCameraCutMode() {
      Cam_SetCutMode(this.cutMode)
    },

    toOpenCamera() {
      const restr = this.resolutionList[this.selectedResolution]
      if (!restr) {
        this.$message.error('请先选择分辨率')
        return
      }
      const [width, height] = restr.split('*').map(Number)
      Cam_Open(this.selectedDevice, width, height)
    },
    closeCamera() {
      if (this.deviceConnect) {
        const container = this.$refs.cameraCtl
        if (container) {
          while (container.firstChild) {
            container.removeChild(container.firstChild)
          }
        }
        Cam_Close()
        this.$message.info('高拍仪已关闭')
        this.$store.commit('app/SET_DEVICE_CONNECT', false)
      }
    },
    SelectDevice() {
      Cam_GetDevResolution(this.selectedDevice)
    },
    SelectResolution() {
      this.toOpenCamera()
    },
    TakePhoto() {
      if (!this.deviceConnect) {
        this.$message.warning('请先打开高拍仪')
        return
      }

      this.progress = 0
      const name = this.formatDate(new Date().getTime())
      const path = `D:\\OCR\\${name}.jpg` // 你可以加上文件类型选择逻辑
      Cam_Photo(path)
    },

    // 数据解析
    async getData() {
      // 验证是否有图片数据
      if (!this.cameraPhoto) {
        this.$message.warning('请先拍照后再抓取数据')
        return
      }

      this.progressTimer()

      try {
        const res = await getOcrApi({
          departmentId: this.departCode,
          base64Str: this.cameraPhoto,
          ocrType: this.type || 'bt'
        })

        // 解析响应结果
        if (res.code === 200 || res.success) {
          // 将结果保存到textInfo中显示
          this.textInfo = JSON.stringify(res.data, null, 2)
          // 图片要保存到后台
          const formData = convertBase64ToFormData(this.cameraPhoto)
          const uploadRes = await fileUoload(formData)
          let url = ''
          if (uploadRes.code === 200) {
            url = splitUrl(uploadRes.data[0] && uploadRes.data[0].fullFileUrl)
          }
          // 通过事件将识别结果传递给父组件
          this.$emit('get-ocr-data', res.data, url)
          this.progress = 100
          this.$message.success('数据抓取成功')
        } else {
          this.$message.error(res.message || '数据抓取失败')
        }
      } catch (error) {
        console.error('OCR识别出错:', error)
        this.$message.error('OCR识别失败，请重试')
      } finally {
        clearInterval(this.progressInterval)
      }
    },

    // 进度条定时器
    progressTimer() {
      this.progress = 0
      this.progressInterval = setInterval(() => {
        if (this.progress < 90) {
          this.progress += 10
        }
      }, 1000)
    }

    // clearResults() {
    //   this.cameraPhoto = ''
    //   this.textInfo = ''
    //   this.$message.info('已清除识别结果')
    // },
    // saveResults() {
    //   if (!this.cameraPhoto || !this.textInfo) {
    //     this.$message.warning('没有可保存的识别结果')
    //     return
    //   }
    //   this.$message.success('数据保存成功')
    // }
  }
}
</script>

<style lang="scss" scoped>
.ocr-recognition {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;

  .ocr-card {
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .ocr-header {
      display: flex;
      align-items: center;

      .ocr-title {
        font-size: 18px;
        font-weight: bold;
        color: #409eff;
      }
    }

    .camera-container {
      display: flex;
      flex-direction: column;
      margin-bottom: 15px;
      gap: 10px;

      .camera-view {
        width: 600px;
        height: 400px;
        border: 1px solid #ebeef5;
        border-radius: 4px;
        background-color: #f5f7fa;
      }

      .camera-controls-horizontal {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        width: 100%;
        gap: 10px;

        .option-item {
          display: flex;
          flex-direction: column;
          gap: 5px;

          label {
            font-size: 14px;
            color: #606266;
          }

          .el-radio-group {
            margin-top: 5px;
          }
        }
      }

      .camera-controls {
        display: flex;
        flex-direction: column;
        gap: 20px;

        .camera-actions {
          display: flex;
          flex-direction: column;
          gap: 15px;
        }

        .camera-options {
          display: flex;
          flex-direction: column;
          gap: 15px;

          .option-item {
            display: flex;
            flex-direction: column;
            gap: 5px;

            label {
              font-size: 14px;
              color: #606266;
            }

            .el-radio-group {
              margin-top: 5px;
            }
          }
        }
      }
    }

    .result-container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 20px;

      .photo-preview,
      .text-preview {
        flex: 1;
        min-width: 300px;

        .preview-title {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 10px;
        }

        .preview-box {
          height: 220px;
          width: 600px;
          border: 1px dashed #dcdfe6;
          border-radius: 4px;
          display: flex;
          overflow: hidden;
          background-color: #fafafa;

          img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }

          .no-image {
            color: #909399;
            font-size: 14px;
          }

          .crop-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;

            .crop-box {
              position: absolute;
              border: 2px dashed #409eff;
              box-sizing: border-box;
              cursor: move;

              .resize-handle {
                position: absolute;
                width: 10px;
                height: 10px;
                background-color: #fff;
                border: 1px solid #409eff;

                &.tl {
                  top: -5px;
                  left: -5px;
                  cursor: nw-resize;
                }

                &.tr {
                  top: -5px;
                  right: -5px;
                  cursor: ne-resize;
                }

                &.bl {
                  bottom: -5px;
                  left: -5px;
                  cursor: sw-resize;
                }

                &.br {
                  bottom: -5px;
                  right: -5px;
                  cursor: se-resize;
                }
              }
            }
          }
        }

        .crop-actions {
          margin-top: 10px;
          display: flex;
          justify-content: flex-end;
          gap: 10px;
        }
      }
    }

    .cropped-result {
      margin-bottom: 20px;

      .preview-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 10px;
        color: #606266;
      }

      .preview-box {
        height: 220px;
        border: 1px dashed #dcdfe6;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        background-color: #fafafa;

        img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
      }
    }

    .action-footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #ebeef5;
    }
  }
}
</style>
