.topBasicInfoContainer .leftBasicInfo .infoLine.showMax1440 {
  display: none !important;
}
@media screen and (max-width: 1440px) {
  // 随访工作台
  body {
    .topBasicInfoContainer .leftBasicInfo .infoLine.showMin1440 {
      display: none !important;
    }
    .el-input__icon {
      line-height: 1.6rem !important;
    }
    .topBasicInfoContainer .leftBasicInfo .infoLine.showMax1440 {
      display: flex !important;
    }
    .followupsDiv {
      .banner {
        margin-bottom: 0.4rem;
      }
      .el-date-editor .el-range__icon,
      .el-date-editor .el-range-separator {
        line-height: 1.4rem !important;
      }

      .el-date-editor .el-range-input {
        font-size: 0.6rem !important;
      }
      .banner .banner-box .box {
        padding: 0.8vh;
      }
      .banner .banner-box .box .box-content img {
        height: 3vw;
      }
      .banner .banner-box .box .box-content .box-info p {
        font-size: 0.64rem;
        line-height: 0.8vw;
        height: 0.8vw;
      }
      .banner .banner-box .box .box-content .box-info strong {
        font-size: 0.8rem;
        line-height: 0.8vw;
        height: 0.8vw;
      }
      .container-left .search-subtitle {
        margin-top: 1vh;
      }
      &.app-container {
        .top-header .todayData {
          height: 1.6rem;
          margin-bottom: 0.4rem;
        }
        .container-main {
          height: calc(100% - 5rem);
          .table-box {
            padding-bottom: 0;
            .btn-list {
              padding: 0.4rem 0.8rem 0.25rem;
            }
          }

          .followTable {
            height: calc(100% - 5.5rem);
          }
          .el-pagination {
            margin-top: 0.5rem !important;
          }
          .el-button.add_btn {
            height: 1.4rem;
          }
          .el-input__inner {
            height: 1.4rem !important;
          }
          .el-input--medium .el-input__icon {
            line-height: 1.4rem !important;
          }
          .table-box .btn-list .right_box .icon_box {
            height: 1.4rem !important;
          }
        }
        .top-header .todayData .label {
          line-height: 1rem;
        }
      }
    }

    .questionnaireContainer .applicationCustomTabsDiv {
      margin-top: 0.5rem !important;
      height: calc(100% - 4vw) !important;
      .applicationCustomTabs {
        .align-horizontal-centers {
          top: 0 !important;
        }
        height: 100%;
        padding-top: 0;
        .tabQuesContainer .mainPart .rightQuestionBox .saveAndBack2TopBox {
          right: 34px;
          bottom: 1rem;
        }
        .el-tabs__content {
          .mainPart {
            .rightQuestionBox {
              height: 100%;
              margin-left: 7vw;
              width: calc(100% - 7vw);
              overflow: hidden !important;
              & > div {
                overflow: hidden !important;
                &.aBitTop {
                  padding-bottom: 0;
                }
              }
              .newQuestPage {
                width: calc(100%);
                height: 100%;
                padding: 0 1rem;
              }
            }
          }
        }
      }
    }
  }
  #app .tabQuesContainer .mainPart .rightQuestionBox .questionItemBox {
    .qCkBox .el-checkbox .el-checkbox__input .el-checkbox__inner::after {
      left: 0.2rem !important;
    }
    .qCkBox .el-checkbox .el-checkbox__label,
    .qTitle {
      font-size: 0.7rem !important;
    }
  }
  // 随访详情
  .questionnaireDiv {
    .container {
      .topBasicInfoContainer {
        height: 3vw;
        .left_text {
          width: 8rem;
        }
        .leftBasicInfo {
          line-height: 0.5;
          .infoLine .infoItemBox {
            .labelBox {
              font-size: 0.75rem;
            }
            .valueBox {
              font-size: 0.7rem;
            }
          }
        }
        .badEventABarCode {
          padding: 0.2vh 0;
        }
        .rightButtonBox .overFollowup {
          margin-top: 0.4vh;
        }
      }
    }
    .newQuestPage .rightBox {
      right: 0 !important;
      top: 0vh !important;
      z-index: 1111;
    }
  }
}

.questionnaireContainer .applicationCustomTabsDiv {
  margin-top: 0.5rem !important;
  .applicationCustomTabs {
    padding-top: 0;
    .el-tabs__nav.is-top .el-tabs__item.is-top {
      height: 5vh;
      line-height: 4.2vh;
      margin-top: 0.5vh;
    }

    @media (max-width: 1280px) {
      .el-tabs__nav.is-top .el-tabs__item.is-top {
        height: 7vh;
        line-height: 6vh;
        margin-top: 0.5vh;
      }
    }

    .el-tabs__nav.is-top .el-tabs__item.is-top.is-active::after {
      width: 24% !important;
      left: 38% !important;
    }
    .tabQuesContainer .mainPart .leftStepBox {
      padding-bottom: 5rem !important;
    }
    .el-tabs__content {
      padding: 0;
      .mainPart {
        padding-top: 1rem;
        .leftQuestionBox {
          width: 6vw !important;
          min-width: 6vw !important;
        }
        .leftStepBox {
          width: 6vw;
          border-radius: 0.625vw 0.625vw 0 0;
          min-width: 6vw;
          height: 100%;
          float: left;
          padding-bottom: 0 !important;
          .cstepContainer .stepBox {
            padding: 1rem 0;
          }
        }
        .questType {
          width: 6vw;
          border-radius: 0 0 0.625vw 0.625vw;
          height: auto;
          padding: 0;
          z-index: 1111;
          left: 0px;
          bottom: 0px;
          line-height: 1.5;
          text-align: center;
          box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
          clear: both;
          .list {
            margin-left: 0;
            margin-right: 0;
            width: 6vw;
            display: block;
          }
          background-color: #fff;
          border-top: 1px dashed #d1d1d1;
          padding-top: 1vh;
        }
        .rightQuestionBox {
          overflow: hidden !important;
          height: 100%;
          width: calc(100% - 155px);
          margin-left: 155px;
          & > div {
            overflow: hidden !important;
            &.aBitTop {
              padding-bottom: 0;
            }
          }
          .newQuestPage {
            width: calc(100%);
            height: 100%;
            padding: 0 1rem;
          }
        }
      }
    }
  }
}

[readonly] {
  pointer-events: none;
}

.readonly {
  pointer-events: none;
}
