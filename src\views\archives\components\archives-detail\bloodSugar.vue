<!-- 血糖记录 -->

<template>
  <div class="diabetes">
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      :height="320"
      row-key="id"
      :columns="columns"
      :show-pagination="showPagination"
    />
    <blood-sugar-echart :table-data="tableData" />
  </div>
</template>

<script>
import { getUserDbBloodSugarRecord } from '@/api/archives'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import BloodSugarEchart from '@/views/receptionCenter/managePatient/echarts/bloodGlucoseEchart.vue'

export default {
  name: 'BloodSugar',
  components: {
    BaseTable,
    BloodSugarEchart
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        patientId: this.$route.query.id
      },
      columns: [
        {
          label: '时间',
          prop: 'measureTime'
        },
        {
          label: '血糖',
          prop: 'sugarValue'
        },
        {
          label: '类型',
          prop: 'sugarName'
        }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      return await getUserDbBloodSugarRecord(params)
    },

    // 我这么麻烦转换是为了和规范管理那边使用同一个图表组件
    async fetchData() {
      try {
        this.loading = true
        const params = {
          ...this.queryParams,
          ...this.sortParams
        }
        const res = await this.getTableList(params)
        const list = this.showPagination ? res.data.list : res.data
        list.forEach((item) => {
          item.measureTime = item.sugarTime
          item.sugarName = item.sugarType
          item.sugarType =
            item.sugarType === '空腹血糖'
              ? 1
              : item.sugarType === '随机血糖'
                ? 2
                : item.sugarType === '餐后2h血糖'
                  ? 3
                  : 4
        })
        this.tableData = list

        this.total = res.data.total || 0
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>
