<!-- 超声心动图 -->
<template>
  <div class="echocardiogram">
    <div class="echocardiogram-header">
      <div class="left">
        <div v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail'" class="audit-status">
          <audit-status v-if="auditInfo.status === 5" status="pass" />

          <audit-status v-if="auditInfo.status === 9" status="reject" :reason="auditInfo.auditResult" />

          <audit-status v-if="auditInfo.status === 1" status="pending" />
        </div>
        <div
          v-if="
            $route.path !== '/qualityControl/ultrasonicQualityControl/detail' &&
              (form.attachmentPhotoUrl || form.attachmentVideoUrl.length > 0) &&
              (auditInfo.status === 5 || auditInfo.status === 9)
          "
          class="btn"
        >
          <a style="color: #0a86c8" @click="handleImageAudit"> 影像质控 </a>
        </div>
      </div>

      <div class="right">
        <div v-if="showDeviceCode($route.path)" class="device-barcode">
          <Barcode :code="`${getDeviceCode($route.path, getSourceData())}1`" />
        </div>
      </div>
    </div>

    <el-form ref="formRef" :model="form" :rules="rules" label-width="180px">
      <div class="echocardiogram-item">
        <el-row :gutter="20">
          <flag-component title="左心房及左心室结构评估" style="margin-bottom: 16px" />
          <el-col v-for="item in echocardiogram.leftHeart" :key="item.label" :span="8">
            <el-form-item :label="item.label" :prop="item.prop">
              <custom-input-number v-model="form[item.prop]">
                <template v-if="item.append" #append>
                  {{ item.append }}
                </template>
              </custom-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <flag-component title="右心房及右心室结构评估" style="margin-bottom: 16px" />
          <el-col v-for="item in echocardiogram.rightHeart" :key="item.label" :span="8">
            <el-form-item :label="item.label" :prop="item.prop">
              <custom-input-number v-model="form[item.prop]">
                <template v-if="item.append" #append>
                  {{ item.append }}
                </template>
              </custom-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <flag-component title="大动脉结构评估" style="margin-bottom: 16px" />
          <el-col v-for="item in echocardiogram.aorta" :key="item.label" :span="8">
            <el-form-item :label="item.label" :prop="item.prop">
              <custom-input-number v-model="form[item.prop]">
                <template v-if="item.append" #append>
                  {{ item.append }}
                </template>
              </custom-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <flag-component title="心功能评估-收缩功能" style="margin-bottom: 16px" />
          <el-col v-for="item in echocardiogram.heartFunctionContract" :key="item.label" :span="8">
            <el-form-item :label="item.label" :prop="item.prop">
              <custom-input-number v-model="form[item.prop]">
                <template v-if="item.append" #append>
                  {{ item.append }}
                </template>
              </custom-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <flag-component title="心功能评估-舒张功能" style="margin-bottom: 16px" />
          <el-col v-for="item in echocardiogram.heartFunctionDiastole" :key="item.label" :span="8">
            <el-form-item :label="item.label" :prop="item.prop">
              <custom-input-number v-model="form[item.prop]">
                <template v-if="item.append" #append>
                  {{ item.append }}
                </template>
              </custom-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <flag-component title="二尖瓣" style="margin-bottom: 16px" />
        <component
          :is="getComponent(item.type)"
          v-for="item in echocardiogram.mitralValve"
          :key="item.id"
          v-model="form[item.prop]"
          :item="item"
          :options="item.options"
        />

        <flag-component title="三尖瓣" style="margin-bottom: 16px" />
        <component
          :is="getComponent(item.type)"
          v-for="item in echocardiogram.tricuspidValve"
          :key="item.id"
          v-model="form[item.prop]"
          :item="item"
          :options="item.options"
        />

        <flag-component title="主动脉瓣" style="margin-bottom: 16px" />
        <component
          :is="getComponent(item.type)"
          v-for="item in echocardiogram.aorticValve"
          :key="item.id"
          v-model="form[item.prop]"
          :item="item"
          :options="item.options"
        />

        <flag-component title="肺动脉瓣" style="margin-bottom: 16px" />
        <component
          :is="getComponent(item.type)"
          v-for="item in echocardiogram.pulmonaryValve"
          :key="item.id"
          v-model="form[item.prop]"
          :item="item"
          :options="item.options"
        />

        <flag-component title="左室流出道" style="margin-bottom: 16px" />
        <component
          :is="getComponent(item.type)"
          v-for="item in echocardiogram.leftVentricularOutflowTract"
          :key="item.id"
          v-model="form[item.prop]"
          :item="item"
          :options="item.options"
          style="width: 28%"
        />

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail'" label="上传图片：">
              <custom-upload v-model="form.attachmentPhotoUrl" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail'" label="上传视频：">
              <UploadVideo v-model="form.attachmentVideoUrl" />
            </el-form-item>
          </el-col>

          <el-col
            v-if="auditInfo.status === 5 && $route.path !== '/qualityControl/ultrasonicQualityControl/detail'"
            :span="24"
          >
            <el-form-item label="质控结论：">
              <el-input v-model="auditInfo.auditResult" type="textarea" :rows="3" :disabled="true" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>

    <div v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail' && auditInfo.status !== 1">
      <span style="margin-right: 130px; font-size: 14px">质控人：{{ auditInfo.auditName }}</span>
      <span style="font-size: 14px">质控时间：{{ auditInfo.auditTime }}</span>
    </div>

    <!-- 影像质控 -->

    <image-quality-control ref="imageQualityControl" :detail="imageQualityControl" :disabled="true" />
  </div>
</template>

<script>
import { echocardiogram } from '@/views/receptionCenter/patientReception/component/complicationsScreening'
import { showDeviceCode, getDeviceCode } from '@/utils/cspUtils'
import RadioGroupField from '@/components/questionnaireElementUi/RadioGroupField.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'
import TextField from '@/components/questionnaireElementUi/TextField.vue'
import FlagComponent from '@/components/flagComponent/index.vue'
import CustomUpload from '@/components/customUpload/index.vue'
import UploadVideo from '@/components/uploadVideo/index.vue'
import AuditStatus from '@/components/auditStatus/index.vue'
import Barcode from '@/components/barcode/barcode.vue'
import ImageQualityControl from '@/components/imageQualityControl/index.vue'

export default {
  name: 'Echocardiogram',
  components: {
    RadioGroupField,
    CheckboxGroupField,
    FlagComponent,
    TextField,
    CustomUpload,
    UploadVideo,
    AuditStatus,
    Barcode,
    ImageQualityControl
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const form = {
      attachmentPhotoUrl: '',
      attachmentVideoUrl: []
    }
    const rules = {}
    Object.entries(echocardiogram).forEach(([key, value]) => {
      Object.values(value).forEach((item) => {
        form[item.prop] = item.type === 'checkbox' ? [] : ''
        if (item.required) {
          rules[item.prop] = [{ required: true, message: `请输入${item.label}` }]
        }
      })
    })
    return {
      echocardiogram,
      form,
      rules,
      imageQualityControl: {},
      auditInfo: {}
    }
  },
  methods: {
    showDeviceCode,
    getDeviceCode,
    getSourceData() {
      return this.$route.path === '/receptionCenter/patientReception'
        ? this.$store.getters.complicationsScreeningData
        : this.$store.state.patientExamination.patientExaminationData
    },
    initData(data) {
      this.imageQualityControl = {
        item: this.itemTemp && this.itemTemp.value,
        data: {
          recordId: data.recordId,
          patientId: data.patientId
        }
      }
      this.auditInfo = {
        status: data && data.status,
        auditResult: data && data.auditResult,
        auditName: data && data.auditName,
        auditTime: data && data.auditTime
      }
      Object.keys(this.form).forEach((key) => {
        if (
          key === 'mitralStructure' ||
          key === 'tricuspidStructure' ||
          key === 'aorticStructure' ||
          key === 'pulmonaryStructure' ||
          key === 'attachmentVideoUrl'
        ) {
          this.form[key] = data[key] ? data[key].split(',') : []
        } else {
          this.form[key] = data[key]
        }
      })
      this.form.id = data.id
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate()
      })
    },
    getComponent(type) {
      switch (type) {
        case 'radio':
          return 'RadioGroupField'
        case 'input':
          return 'TextField'
        default:
          return 'CheckboxGroupField'
      }
    },
    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          ...this.form,
          mitralStructure: this.form.mitralStructure.join(','),
          tricuspidStructure: this.form.tricuspidStructure.join(','),
          aorticStructure: this.form.aorticStructure.join(','),
          pulmonaryStructure: this.form.pulmonaryStructure.join(','),
          attachmentVideoUrl: this.form.attachmentVideoUrl.join(','),
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    },
    async handleImageAudit() {
      const params = {
        recordId: this.imageQualityControl.data.recordId,
        module: 'cs',
        item: this.imageQualityControl.item,
        patientId: this.imageQualityControl.data.patientId
      }
      const res = await this.$refs.imageQualityControl.getImageFileListFn(params)
      if (res.code === 200) {
        this.$refs.imageQualityControl.visible = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.echocardiogram {
  .echocardiogram-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      width: 65%;
      display: flex;
      align-items: center;
      .audit-status {
        width: 60%;
      }
    }
    .right {
      width: 35%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }

  ::v-deep .el-form-item__label {
    line-height: 25px !important;
  }
  ::v-deep .el-form-item__content {
    line-height: 25px !important;
  }
}
</style>
