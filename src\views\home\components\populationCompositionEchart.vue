<template>
  <div class="charts-container">
    <div ref="ageChart" class="chart-item" />
    <div ref="genderChart" class="chart-item" />
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'PopulationCompositionEchart',
  props: {
    populationCompositionData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      ageChart: null,
      genderChart: null
    }
  },
  watch: {
    populationCompositionData() {
      this.initCharts()
    }
  },
  mounted() {
    this.initCharts()
  },
  beforeDestroy() {
    if (this.ageChart) {
      this.ageChart.dispose()
      this.ageChart = null
    }
    if (this.genderChart) {
      this.genderChart.dispose()
      this.genderChart = null
    }
  },
  methods: {
    initCharts() {
      this.initAgeChart()
      this.initGenderChart()
    },

    initAgeChart() {
      if (!this.ageChart) {
        this.ageChart = echarts.init(this.$refs.ageChart)
      }

      // 筛选年龄相关数据
      const ageData = this.populationCompositionData.filter((item) =>
        ['youngCount', 'normalCount', 'oldCount', 'soOldCount'].includes(item.code))

      if (!ageData.length) {
        this.ageChart.clear()
        return
      }

      const chartData = ageData.map((item) => ({
        name: item.label,
        value: item.value
      }))

      const total = chartData.reduce((sum, item) => sum + item.value, 0)

      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          top: 'center',
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 10,
          formatter: (name) => {
            const item = chartData.find((it) => it.name === name)
            return `${name}: ${item ? item.value : 0}`
          }
        },
        color: ['#FF7C7C', '#5AD8A6', '#8B7CF9', '#59C4E6'],
        series: [
          {
            name: '年龄分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'center',
              formatter: `{total|${total}}\n{name|总数}`,
              rich: {
                total: {
                  fontSize: 28,
                  fontWeight: 'bold',
                  lineHeight: 40
                },
                name: {
                  fontSize: 14,
                  lineHeight: 20
                }
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '40',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: chartData
          }
        ]
      }

      this.ageChart.setOption(option, true)
    },

    initGenderChart() {
      if (!this.genderChart) {
        this.genderChart = echarts.init(this.$refs.genderChart)
      }

      // 筛选性别相关数据
      const genderData = this.populationCompositionData.filter((item) => ['maleCount', 'femaleCount'].includes(item.code))

      if (!genderData.length) {
        this.genderChart.clear()
        return
      }

      const chartData = genderData.map((item) => ({
        name: item.label,
        value: item.value
      }))

      const total = chartData.reduce((sum, item) => sum + item.value, 0)

      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: '5%',
          top: 'center',
          itemWidth: 10,
          itemHeight: 10,
          itemGap: 10,
          formatter: (name) => {
            const item = chartData.find((it) => it.name === name)
            return `${name}: ${item ? item.value : 0}`
          }
        },
        color: ['#59C4E6', '#FF9F7F'],
        series: [
          {
            name: '性别分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['30%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: true,
              position: 'center',
              formatter: `{total|${total}}\n{name|总数}`,
              rich: {
                total: {
                  fontSize: 28,
                  fontWeight: 'bold',
                  lineHeight: 40
                },
                name: {
                  fontSize: 14,
                  lineHeight: 20
                }
              }
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '40',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: chartData
          }
        ]
      }

      this.genderChart.setOption(option, true)
    }
  }
}
</script>

<style scoped>
.charts-container {
  width: 100%;
  height: 250px;
  display: flex;
  justify-content: space-between;
}
.chart-item {
  width: 50%;
  height: 100%;
}
</style>
