<template>
  <div class="public_boxShadow bgColor_FFF borderRadius6 public_contentLeft" :class="{ hidden: !containerLeftShow }">
    <slot />
    <div class="aside" @click="shrinkLeftArea">
      <i v-if="containerLeftShow" class="el-icon-caret-left" />
      <i v-else class="el-icon-caret-right" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ProTwoColumnsLayoutLeft',
  components: {},

  props: {},
  data() {
    return {
      containerLeftShow: true // 是否显示左侧区域 true:显示 false:隐藏
    }
  },
  computed: {},
  methods: {
    /**
     * @description: 收缩左侧
     * @author: Li<PERSON>uwan
     * @Date: 2024-08-29 20:54:55
     */
    shrinkLeftArea() {
      this.containerLeftShow = !this.containerLeftShow
      this.$emit('resizeDom')
    }
  }
}
</script>

<style lang="scss">
.public_contentLeft {
  width: 15rem;
  padding: 1.5rem;
  position: relative;
  overflow-y: auto;
  box-sizing: border-box;

  &.hidden {
    width: 0.8rem;
    padding: 0;
    overflow: hidden;

    & > .public_label_list {
      display: none;
    }
  }
}

.aside {
  position: absolute;
  top: calc(50% - 2.5rem);
  right: 0;
  width: 0.7rem;
  height: 5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #eaeaea;
  cursor: pointer;
  border-top-left-radius: 1rem;
  border-bottom-left-radius: 1rem;
  clip-path: polygon(0% 20%, 100% 0%, 100% 100%, 0% 80%);
}
</style>
