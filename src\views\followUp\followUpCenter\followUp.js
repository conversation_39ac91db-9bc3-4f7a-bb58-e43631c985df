// 随访问卷
const followUp = {
  way: [
    {
      label: '随访方式：',
      prop: 'type',
      type: 'select',
      required: true,
      span: 6,
      disabled: (type) => type === 'edit',
      options: [
        { label: '电话随访', value: 1 },
        { label: '上门随访', value: 2 },
        { label: '诊室随访', value: 3 }
      ]
    },
    {
      label: '随访日期：',
      prop: 'realDate',
      type: 'date',
      required: true,
      disabled: (type) => type === 'edit',
      span: 6
    }
  ],

  // 患者主诉
  patientComplaint: [
    {
      label: '心慌：',
      prop: 'nervous',
      type: 'radio',
      required: true,
      span: 4,
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 2 }
      ]
    },
    {
      label: '气喘：',
      prop: 'asthma',
      type: 'radio',
      required: true,
      span: 4,
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 2 }
      ]
    },
    {
      label: '下肢浮肿：',
      prop: 'ccb',
      type: 'radio',
      required: true,
      span: 4,
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 2 }
      ]
    },
    {
      label: '跌倒：',
      prop: 'fall',
      type: 'radio',
      required: true,
      span: 4,
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 2 }
      ]
    },
    {
      label: '其他：',
      prop: 'complainedOther',
      type: 'input',
      span: 24,
      width: '70%'
    }
  ],

  badEvent: [
    {
      label: '不良事件：',
      type: 'checkbox',
      prop: 'badEvent',
      span: 24,
      required: false,
      remark: true, // 是否需要备注
      options: [
        { label: '出血事件', value: '1', badEventRemark1: '', prop: 'badEventBloodLevel', type: 'select' },
        { label: '心衰', value: '2', badEventRemark2: '' },
        { label: 'TIA', value: '3', badEventRemark2: '' },
        { label: '痴呆', value: '4', badEventRemark3: '' },
        { label: '卒中', value: '5', badEventRemark4: '' },
        { label: '冠心病', value: '6', badEventRemark5: '' },
        { label: '失明', value: '7', badEventRemark6: '' },
        { label: '肾功能不全', value: '8', badEventRemark7: '' },
        { label: '周围血管闭塞', value: '9', badEventRemark8: '' },
        { label: '主动脉夹层', value: '10', badEventRemark9: '' },
        { label: '呼吸衰竭', value: '11', badEventRemark10: '' },
        { label: '肺源性心脏病', value: '12', badEventRemark11: '' },
        { label: '肺性脑病', value: '13', badEventRemark12: '' }
      ],
      extraOptions: [
        { label: '少量出血', value: 1 },
        { label: '中等出血', value: 2 },
        { label: '大出血或致命性出血', value: 3 }
      ]
    }
  ],

  // 血压
  bloodPressure: [
    {
      label: '收缩压：',
      prop: 'sp',
      type: 'input',
      required: true,
      span: 6,
      width: '70%',
      append: 'mmHg'
    },
    {
      label: '舒张压：',
      prop: 'dp',
      type: 'input',
      required: true,
      span: 6,
      width: '70%',
      append: 'mmHg'
    },

    {
      label: '心率：',
      prop: 'heartRate',
      type: 'input',
      required: true,
      span: 6,
      width: '70%',
      append: '次/分'
    }
  ],

  // 血糖
  bloodSugar: [
    {
      label: '血糖类型：',
      prop: 'sugarType',
      type: 'radio',
      required: true,
      span: 6,
      options: [
        { label: '空腹血糖', value: 1 },
        { label: '餐后血糖', value: 2 }
      ]
    },
    {
      label: '血糖值：',
      prop: 'sugarValue',
      type: 'input',
      required: true,
      span: 6,
      width: '70%',
      append: 'mmol/L'
    }
  ],

  // 生活方式改善情况
  lifestyleImprovement: [
    {
      label: '戒烟：',
      prop: 'smoke',
      type: 'radio',
      required: false,
      span: 6,
      options: [
        { label: '无抽烟史', value: 1 },
        { label: '有抽烟史', value: 2 },
        { label: '偶尔抽烟', value: 3 }
      ]
    },
    {
      label: '戒酒：',
      prop: 'drink',
      type: 'radio',
      required: false,
      span: 6,
      options: [
        { label: '无饮酒史', value: 1 },
        { label: '有饮酒史', value: 2 },
        { label: '偶尔饮酒', value: 3 }
      ]
    }
  ],

  other: [
    {
      label: '随访期间有无住院或急诊室就诊：',
      prop: 'outPatient',
      type: 'radio',
      required: false,
      span: 12,
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 2 }
      ]
    },
    {
      label: '随访期间有无临床事件（包括新发TIA，缺血性脑卒中，体循环栓塞，大出血，小出血或死亡）：',
      prop: 'clinical',
      type: 'radio',
      required: false,
      span: 24,
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 2 }
      ]
    },
    {
      label: '患者抗凝药是否依从：',
      prop: 'oac',
      type: 'radio',
      required: false,
      span: 12,
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 2 }
      ]
    },
    {
      label: '常规发药：',
      prop: 'dispensing',
      type: 'radio',
      required: true,
      span: 24,
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 2 }
      ]
    },
    {
      label: '用药情况：',
      prop: 'medicalList',
      type: 'MedicationTable',
      required: false,
      span: 24
    },
    {
      label: '服用药物更改情况：',
      prop: 'medical',
      type: 'radio',
      required: false,
      span: 24,
      options: [
        { label: '是', value: 1 },
        { label: '否', value: 2 }
      ]
    },
    {
      label: '发药照片上传（常规发药必填）：',
      prop: 'attachmentPhotoUrl',
      type: 'upload',
      required: false,
      width: '70%',
      span: 24
    },
    {
      label: '本次随访意见：',
      prop: 'suggest',
      required: true,
      type: 'radio',
      span: 24,
      options: [
        { label: '常规随诊', value: 1 },
        { label: '建议转诊', value: 2 }
      ]
    }
  ]
}

const followUpMap = {
  way: '随访方式',
  badEvent: '不良事件',
  patientComplaint: '患者主诉',
  bloodPressure: '血压',
  bloodSugar: '血糖',
  lifestyleImprovement: '生活方式改善情况',
  other: '其他'
}

export { followUp, followUpMap }
