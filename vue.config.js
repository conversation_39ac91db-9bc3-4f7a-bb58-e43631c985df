/* eslint-disable max-len */
'use strict'
const path = require('path')
const defaultSettings = require('./src/settings.js')
const envModule = require('./src/utils/env')

// 判断是否为生产环境
const isProduction = process.env.NODE_ENV === 'production'

const SpeedMeasurePlugin = require('speed-measure-webpack-plugin')

// 导入代码压缩插件
const UglifyJsPlugin = require('uglifyjs-webpack-plugin')

const CompressionWebpackPlugin = require('compression-webpack-plugin') // 引入插件

const smp = new SpeedMeasurePlugin({
  outputFormat: 'human'
})

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = defaultSettings.title || 'CSP' // page title
const favicon = defaultSettings.favicon || 'favicon.ico'

// 根据环境修改dist名称
const ENV_DIST_ENUM = {
  test: 'dist_test',
  prod: 'dist_prod'
}
let distName = 'dist'
if (envModule.getEnv() && ENV_DIST_ENUM[envModule.getEnv()]) distName = ENV_DIST_ENUM[envModule.getEnv()]
// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following method:
// port = 9527 npm run dev OR npm run dev --port = 9527
const port = process.env.port || process.env.npm_config_port || 80 // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  publicPath: '/',
  outputDir: distName,
  assetsDir: 'static',
  lintOnSave: !isProduction,
  productionSourceMap: !isProduction,
  devServer: {
    // host: '0.0.0.0',
    // https:true,
    port,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    // before: require('./mock/mock-server.js')
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: envModule.genUrlEnum().HTTP_ENV,
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          [`^${process.env.VUE_APP_BASE_API}`]: ''
        }
      }
    }
  },
  css: {
    extract: false,
    loaderOptions: {
      sass: {
        sassOptions: {
          outputStyle: 'expanded'
        }
      }
    }
  },
  configureWebpack: smp.wrap({
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    name: `${name}-splitflag-${favicon}`,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    plugins: [
      new CompressionWebpackPlugin({
        filename: '[path].gz[query]',
        algorithm: 'gzip',
        test: /\.(js|css|html|svg)$/, // 增加对 HTML 和 SVG 文件的支持
        threshold: 8192, // 降低阈值以压缩更多小文件
        minRatio: 0.7 // 降低比例要求以确保压缩
      })
    ],
    optimization: {
      splitChunks: {
        chunks: 'all',
        minSize: 300000, // 减小最小分割体积
        maxSize: 500000, // 减小最大分割体积以便更小的分包
        minChunks: 1, // 减少分包的最低复用模块数
        maxAsyncRequests: 25, // 降低异步请求数限制
        maxInitialRequests: 20, // 降低初始请求数限制
        automaticNameDelimiter: '-',
        cacheGroups: {
          defaultVendors: {
            name: 'chunk-defaultVendors',
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
            reuseExistingChunk: true
          },
          common: {
            name: 'chunk-common',
            test: resolve('src/components'),
            minChunks: 1, // 调整最小公共模块数
            priority: 10,
            reuseExistingChunk: true
          }
        }
      },
      minimize: true,
      minimizer: [
        new UglifyJsPlugin({
          test: /\.js(\?.*)?$/i, // 测试匹配文件
          include: /\/includes/, // 包含的文件
          exclude: /\/excludes/, // 排除的文件

          uglifyOptions: {
            compress: {
              drop_debugger: isProduction,
              drop_console: isProduction // 生产环境自动删除console
            },
            output: {
              comments: false // 移除注释
            },
            warnings: false
          },

          // 允许过滤哪些块应该被uglified（默认情况下，所有块都是uglified）。
          // 返回true以uglify块，否则返回false。
          chunkFilter: chunk => {
            // Exclude uglification for the `vendor` chunk
            if (chunk.name === 'vendor') {
              return false
            }
            return true
          },
          cache: true, // 启用/禁用文件缓存（类型可布尔也可是字符串）
          parallel: true, // 行化可以显着加快构建速度，因此强烈建议使用
          sourceMap: !isProduction // 使用源映射将错误消息位置映射到模块（这会减慢编译速度,cheap-source-map选项不适用于此插件)
        })
      ]
    }
    // devtool: isProduction ? false : 'source-map'
  }),
  chainWebpack(config) {
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])
    const fontsRule = config.module.rule('fonts')
    fontsRule.uses.clear()
    fontsRule
      .test(/\.(woff2?|eot|ttf|otf)(\?.*)?$/i)
      .use('file-loader')
      .loader('url-loader')
      .options({
        esModule: false,
        fallback: {
          loader: 'file-loader',
          options: {
            // name: `${ process.env.NODE_ENV === 'production' ? name + '/' : name + '/'}fonts/[name].[hash:8].[ext]`
            name: `${name}'/fonts/[name].[hash:8].[ext]`
          }
        }
      })

    // set svg-sprite-loader
    config.module.rule('svg').exclude.add(resolve('src/icons')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
    config.when(process.env.NODE_ENV !== 'development', config => {
      config.plugin('webpack-bundle-analyzer').use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin)
      config
        .plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [
          {
            // `runtime` must same as runtimeChunk name. default is `runtime`
            inline: /runtime\..*\.js$/
          }
        ])
        .end()
      config.optimization.splitChunks({
        chunks: 'all',
        minSize: 409600, // 超过才独立分包
        maxSize: 614400, // 只是提示，可以被违反，会尽量将chunk分的比maxSize小，当设为0代表能分则分，分不了不会强制
        minChunks: 2, // 某个模块至少被多少代码块引用，才会被提取成新的chunk
        maxAsyncRequests: 30, // 分割后，按需加载的代码块最多允许的并行请求数，在webpack5里默认值变为6
        maxInitialRequests: 30, // 分割后，入口代码块最多允许的并行请求数，在webpack5里默认值变为4
        automaticNameDelimiter: '~', // 代码块命名分割符
        cacheGroups: {
          // elementUI: {
          //   name: 'chunk-elementUI',
          //   priority: 20,
          //   test: /[\\/]node_modules[\\/]_?element-ui(.*)/
          // },
          jiaminghi: {
            name: 'chunk-jiaminghi', // split jiaminghi into a single package
            priority: 80,
            test: /[\\/]node_modules[\\/]_?@jiaminghi(.*)/
          },
          'trtc-js-sdk': {
            name: 'chunk-trtc-js-sdk', // split trtc-js-sdk into a single package
            priority: 70,
            test: /[\\/]node_modules[\\/]_?trtc-js-sdk(.*)/
          },
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 60,
            chunks: 'initial',
            reuseExistingChunk: true
          },
          commons: {
            name: 'chunk-commons',
            test: resolve('src/components'), // can customize your rules
            minChunks: 2, //  minimum common number
            priority: 50,
            reuseExistingChunk: true
          },
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true
          }
        }
      })
      // when there are many pages, it will cause too many meaningless requests
      config.plugins.delete('prefetch')
      // 移除 preload 插件
      config.plugins.delete('preload')
    })
  }
}
