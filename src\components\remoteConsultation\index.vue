<template>
  <div class="remoteConsultation">
    <div class="consultation-trigger" @click="handleClick">
      <svg-icon icon-class="icon_consultation" />
      <span style="margin-left: 5px">申请远程会诊</span>
    </div>

    <ProDialog title="远程会诊" :visible.sync="remoteVisible">
      <MainDoctorPage ref="mainDoctorPage" :module-type="moduleType" :patient-info="patientInfo" />
    </ProDialog>
  </div>
</template>

<script>
import MainDoctorPage from './component/mainDoctorPage.vue'
import ProDialog from '@/components/ProDialog/index.vue'

export default {
  name: 'RemoteConsultation',
  components: {
    MainDoctorPage,
    ProDialog
  },
  props: {
    patientInfo: {
      type: Object,
      default: () => ({})
    },
    moduleType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      remoteVisible: false
    }
  },
  methods: {
    async handleClick() {
      // 获取药品列表
      await this.$store.dispatch('drugManagement/getDrugList')
      this.remoteVisible = true
    }
  }
}
</script>

<style scoped lang="scss">
.remoteConsultation {
  .consultation-trigger {
    font-weight: 600;
    font-size: 14px;
    color: #0a86c8;
    cursor: pointer;
    display: inline-block;
  }
}
</style>
