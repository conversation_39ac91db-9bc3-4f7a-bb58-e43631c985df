import request from '@/utils/request'

// 分页查询项目模板
export const getProjectConfigurationList = (data) => {
  return request({
    url: '/cspapi/backend/measure/template/page',
    method: 'post',
    data
  })
}

// 根据departCode查询项目模板列表
export const getProjectConfigurationListByDepartCode = (data) => {
  return request({
    url: '/cspapi/backend/measure/template/list',
    method: 'post',
    data
  })
}

// 保存项目模板
export const saveProjectConfiguration = (data) => {
  return request({
    url: '/cspapi/backend/measure/template/save',
    method: 'post',
    data
  })
}

// 查询项目模板明细
export const getProjectConfigurationDetail = (data) => {
  return request({
    url: '/cspapi/backend/measure/template/detail',
    method: 'post',
    data
  })
}

// 删除模板
export const deleteProjectConfiguration = (data) => {
  return request({
    url: '/cspapi/backend/measure/template/remove',
    method: 'post',
    data
  })
}
