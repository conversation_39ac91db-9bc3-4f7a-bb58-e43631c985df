import request from '@/utils/request'

// 分页查询家医团队
export function getFamilyMedicalTeamList(data) {
  return request({
    url: '/cspapi/backend/familyDoctor/team/page',
    method: 'post',
    data
  })
}

// 查询家医团队详情
export function getFamilyMedicalTeamDetail(data) {
  return request({
    url: '/cspapi/backend/familyDoctor/team/detail',
    method: 'post',
    data
  })
}

// 保存家医团队信息
export function saveFamilyMedicalTeam(data) {
  return request({
    url: '/cspapi/backend/familyDoctor/team/save',
    method: 'post',
    data
  })
}

// 删除家医团队
export function deleteFamilyMedicalTeam(data) {
  return request({
    url: '/cspapi/backend/familyDoctor/team/remove',
    method: 'post',
    data
  })
}

// 家医团队添加医生
export function addFamilyMedicalTeamDoctor(data) {
  return request({
    url: '/cspapi/backend/familyDoctor/team/doctor/add',
    method: 'post',
    data
  })
}

// 家医团队删除医生
export function deleteFamilyMedicalTeamDoctor(data) {
  return request({
    url: '/cspapi/backend/familyDoctor/team/doctor/remove',
    method: 'post',
    data
  })
}

// 家庭团队任命队长
export function appointFamilyMedicalTeamLeader(data) {
  return request({
    url: '/cspapi/backend/familyDoctor/team/doctor/leader',
    method: 'post',
    data
  })
}

// 家庭团队任命角色
export function appointFamilyMedicalTeamRole(data) {
  return request({
    url: '/cspapi/backend/familyDoctor/team/doctor/role',
    method: 'post',
    data
  })
}
