const biochemistry = {
  bloodFat: [
    {
      label: '总胆固醇[TC]：',
      prop: 'tc',
      type: 'input',
      append: 'mmol/L',
      required: false,
      range: '合适范围：＜5.20mmol/L；边缘升高：5.20 - 6.19mmol/L；升高：≥6.20mmol/L',
      description: '是血脂的重要组成部分，升高时增加动脉粥样硬化、冠心病等心脑血管疾病风险，需结合其他指标评估整体风险'
    },
    {
      label: '低密度胆固醇[LDL-C]：',
      prop: 'ldlc',
      type: 'input',
      append: 'mmol/L',
      required: false,
      range:
        '合适范围：＜3.40mmol/L；边缘升高：3.40 - 4.09mmol/L；升高：≥4.10mmol/L；极高危人群（如冠心病患者）：需控制至＜1.8mmol/L',
      description:
        '“坏胆固醇”，是动脉粥样硬化的主要危险因素，其水平与心脑血管疾病发生风险呈正相关，是降脂治疗的主要靶点'
    },
    {
      label: '甘油三酯[TG]：',
      prop: 'tg',
      type: 'input',
      append: 'mmol/L',
      required: false,
      range: '合适范围：＜1.70mmol/L；边缘升高：1.70 - 2.25mmol/L；升高：≥2.26mmol/L；重度升高：≥5.65mmol/L',
      description: '升高时增加胰腺炎风险，也与动脉粥样硬化相关，需结合 LDL-C 等指标综合管理'
    },
    {
      label: '脂蛋白[a]：',
      prop: 'lip',
      type: 'input',
      append: 'mg/L',
      required: false,
      range: '参考范围：＜300mg/L（不同实验室因检测方法差异，参考范围可能略有不同）',
      description:
        '独立的动脉粥样硬化危险因素，水平升高与冠心病、脑卒中等发病风险增加相关，且目前缺乏特效药物，主要通过控制其他危险因素管理'
    },
    {
      label: '高密度胆固醇[HDL-C]：',
      prop: 'hdlc',
      type: 'input',
      append: 'mmol/L',
      required: false,
      range: '合适范围：男性≥1.04mmol/L，女性≥1.29mmol/L；降低：男性＜1.04mmol/L，女性＜1.29mmol/L',
      description: '“好胆固醇”，可逆向转运胆固醇，具有抗动脉粥样硬化作用，降低时增加心脑血管疾病风险'
    }
  ],
  liverFunction: [
    {
      label: '谷丙转氨酶[ALT]：',
      prop: 'alt',
      type: 'input',
      append: 'U/L',
      required: false,
      range: '男性：9 - 50U/L；女性：7 - 40U/L',
      description:
        '是反映肝细胞损伤最敏感的指标之一，升高常见于病毒性肝炎、药物性肝损伤、脂肪肝、酒精性肝病等，也可能因熬夜、饮酒等生理性因素轻度升高'
    },
    {
      label: '谷草转氨酶[AST]：',
      prop: 'ast',
      type: 'input',
      append: 'U/L',
      required: false,
      range: '男性：15 - 40U/L；女性：13 - 35U/L',
      description:
        '不仅存在于肝脏，还分布于心肌、骨骼肌等。升高常见于病毒性肝炎、肝硬化、心肌梗死等，AST/ALT 比值对疾病诊断有一定帮助，如比值大于 1 可能提示肝硬化或酒精性肝病'
    },
    {
      label: 'R-谷氨酰基转肽酶[GGT]：',
      prop: 'ggt',
      type: 'input',
      append: 'U/L',
      required: false,
      range: '男性：10 - 60U/L；女性：7 - 45U/L',
      description:
        '主要存在于肝细胞和胆管上皮细胞中，升高提示肝胆系统疾病，尤其是酒精性肝病和胆汁淤积性肝病，也可见于药物性肝损伤、胰腺炎等'
    },
    {
      label: '白蛋白[ALB]：',
      prop: 'alb',
      type: 'input',
      append: 'g/L',
      required: false,
      range: '合适范围：35 - 55g/L',
      description:
        '是肝脏合成的主要蛋白质，具有维持血浆胶体渗透压等功能。降低提示肝脏合成功能下降或蛋白质丢失过多，常见于肝硬化、慢性肝炎、肾病综合征、营养不良等'
    },
    {
      label: '肌酸激酶同工酶[CKMB]：',
      prop: 'ckmb',
      type: 'input',
      append: 'U/L',
      required: false,
      range: '一般为 0 - 25U/L（不同检测方法可能略有差异）',
      description:
        '主要用于心肌损伤的诊断，在肝功能检查中一般不作为主要指标，若明显升高，多提示心肌梗死等心肌病变，而非肝脏问题'
    },
    {
      label: '总胆红素：',
      prop: 'tb',
      type: 'input',
      append: 'umol/L',
      required: false,
      range: '合适范围：5.1 - 19μmol/L',
      description:
        '是直接胆红素和间接胆红素的总和，反映肝脏对胆红素的代谢能力。升高提示胆红素代谢异常，可见于肝细胞性黄疸、梗阻性黄疸和溶血性黄疸，常见疾病有病毒性肝炎、肝硬化、胆管结石等'
    },
    {
      label: '总蛋白[TP]：',
      prop: 'tp',
      type: 'input',
      append: 'g/L',
      required: false,
      range: '成人坐立位：64 - 83g/L；成人卧位：60 - 78g/L',
      description:
        '由肝脏合成，包括白蛋白和球蛋白。总蛋白降低常与白蛋白减低一同出现，见于肝脏合成功能受损等；增高常同时伴有球蛋白增高，可能与免疫系统亢进等有关'
    },
    {
      label: '肌酸激酶[CK]：',
      prop: 'ck',
      type: 'input',
      append: 'U/L',
      required: false,
      rang: '男性：50 - 310U/L；女性：40 - 200U/L',
      description:
        '主要存在于骨骼肌、心肌等组织中，在肝功能检查中通常不是关键指标。升高常见于骨骼肌损伤、心肌梗死、进行性肌营养不良等，一般与肝脏疾病关系不大'
    },
    {
      label: '直接胆红素：',
      prop: 'db',
      type: 'input',
      append: 'umol/L',
      required: false,
      range: '合适范围：0 - 6.8μmol/L',
      description: '是经过肝脏处理后的胆红素，升高主要见于胆道梗阻性疾病，如胆囊结石、胆道息肉等，也可见于肝细胞损伤'
    }
  ],
  kidneyFunction: [
    {
      label: '尿素[BUN]：',
      prop: 'bun',
      type: 'input',
      append: 'mmol/L',
      required: false,
      range: '成人：3.2 - 7.1mmol/L；男性：2.89 - 7.85mmol/L；女性：2.78 - 7.32mmol/L；儿童：1.8 - 6.5mmol/L',
      description: `是评估肾小球滤过功能的指标之一，但敏感性较差。升高常见于急慢性肾炎、肾衰竭等肾脏疾病，
        也可见于心衰、脱水等导致肾供血不足的情况，还受蛋白质摄入量和分解代谢影响。降低常见于肝病、蛋白质缺乏等`
    },
    {
      label: '肌酐[Scr]：',
      prop: 'scr',
      type: 'input',
      append: 'umol/L',
      required: false,
      range: '男性：53 - 106μmol/L；女性：44 - 97μmol/L；不同医院因检测方法等差异，参考范围可能略有不同',
      description:
        '是肌肉代谢产物，通过肾脏清除。升高常见于各种肾脏疾病，如急性肾衰竭、慢性肾功能不全等，也可因肉类进食过多、剧烈运动等暂时升高。偏低一般临床意义不大，可见于严重肝病、肌萎缩等'
    },
    {
      label: 'eGFR：',
      prop: 'egfr',
      type: 'input',
      append: 'ml/min/1.73m²',
      required: false,
      range: `一般＞90ml/（min・1.73m²）为正常；60 - 89ml/（min・1.73m²）提示肾功能轻度下降；
        30 - 59ml/（min・1.73m²）为中度下降；15 - 29ml/（min・1.73m²）为重度下降；＜15ml/（min・1.73m²）肾衰竭`,
      description: '是评估肾功能的重要指标，可反映肾小球的滤过功能，其值越低，说明肾功能受损越严重'
    },
    {
      label: '尿酸[UA]：',
      prop: 'ua',
      type: 'input',
      append: 'umol/L',
      required: false,
      range: '酶法：男性 150 - 416μmol/L，女性 89 - 357μmol/L5',
      description:
        '是嘌呤代谢产物，大部分经肾脏排出。升高常见于痛风、急慢性肾小球肾炎等，也可因进食高嘌呤食物短暂升高。偏低可见于遗传性黄嘌呤尿症、长期低嘌呤饮食等'
    },
    {
      label: '钠[NA]：',
      prop: 'na',
      type: 'input',
      append: 'mmol/L',
      required: false,
      range: '酶法分析：136 - 146mmol/L；离子选择电极法：135 - 145mmol/L',
      description:
        '可反映体内钠的水平，对维持细胞外液渗透压、酸碱平衡等有重要作用。血钠增多常见于严重脱水、钠盐摄入过多等，减少常见于呕吐、腹泻、心力衰竭等导致钠丢失或分布异常的情况'
    },
    {
      label: '钾[K]：',
      prop: 'k',
      type: 'input',
      append: 'mmol/L',
      required: false,
      range: '合适范围：3.5 - 5.5mmol/L2',
      description:
        '对维持神经肌肉兴奋性、心肌正常功能等至关重要。高于 5.5mmol/L 为高钾血症，常见于肾衰竭少尿期、摄入过多等；低于 3.5mmol/L 为低钾血症，常见于摄入不足、丢失过多等情况'
    }
  ],
  others: [
    {
      label: '同型半胱氨酸：',
      prop: 'hcy',
      type: 'input',
      append: 'umol/L',
      required: false,
      range: '多数实验室参考范围：5 - 15μmol/L（不同检测方法或实验室可能略有差异，部分将＜10μmol/L 视为理想范围）',
      description: ''
    }
  ]
}

const dynamicBlood = {
  heartRate: [
    { label: '总心搏数：', prop: 'heartCount', type: 'input' },
    { label: '平均心率：', prop: 'avgBpm', type: 'input', append: 'BPM' },
    { label: '最慢心率：', prop: 'minBpm', type: 'input', append: 'BPM' },
    { label: '记录时长：', prop: 'heartDuration', type: 'input' },
    { label: '心动过缓(<45 BPM)：', prop: 'slowBpm', type: 'input' },
    { label: '长RR间期(>2000 ms)：', prop: 'rrTimes', type: 'input', append: '次' },
    { label: '心动过速(>130 BPM)：', prop: 'fastBpm', type: 'input' },
    { label: '最快心率：', prop: 'maxBpm', type: 'input', append: 'BPM' }
  ],
  ventricular: [
    { label: '总数：', prop: 'shiCount', type: 'input' },
    { label: '平均(小时)：', prop: 'shiDuration', type: 'input' },
    { label: '单发：', prop: 'shiOne', type: 'input' },
    { label: '成对：', prop: 'shiTwo', type: 'input' },
    { label: '二联律：', prop: 'shiTwoLaw', type: 'input', append: '阵' },
    { label: '三联律：', prop: 'shiThreeLaw', type: 'input', append: '阵' },
    { label: 'R on T：', prop: 'ront', type: 'input' },
    { label: '室性异搏：', prop: 'shiEscapeBeat', type: 'input' },
    { label: '插入性早搏：', prop: 'shiBeforeBeat', type: 'input' },
    { label: '室速总数：', prop: 'shiSpeedCount', type: 'input' },
    { label: '最长的室速：', prop: 'shiSpeedMax', type: 'input', append: '次' },
    { label: '最快心率的室速：', prop: 'shiMaxFastBpm', type: 'input', append: 'BPM' },
    { label: '最慢心率的室速：', prop: 'shiMaxSlowBpm', type: 'input', append: 'BPM' }
  ],
  atrial: [
    { label: '总数：', prop: 'fangCount', type: 'input' },
    { label: '平均(小时)：', prop: 'fangDuration', type: 'input' },
    { label: '单发：', prop: 'fangOne', type: 'input' },
    { label: '成对：', prop: 'fangTwo', type: 'input' },
    { label: '二联律：', prop: 'fangTwoLaw', type: 'input', append: '阵' },
    { label: '三联律：', prop: 'fangThreeLaw', type: 'input', append: '阵' },
    { label: '房性逸搏：', prop: 'fangEscapeBeat', type: 'input' },
    { label: '房速：', prop: 'fangSpeed', type: 'input' },
    { label: '最长的房速：', prop: 'fangMaxLongSpeed', type: 'input', append: '次' },
    { label: '最快心率的房速：', prop: 'fangMaxFastSpeed', type: 'input', append: 'BPM' },
    { label: '最慢心率的房速：', prop: 'fangMaxSlowSpeed', type: 'input', append: 'BPM' }
  ],
  beat: [
    { label: '交界性早搏：', prop: 'borderBeforeBeat', type: 'input' },
    { label: '交界性逸搏：', prop: 'borderEscapeBeat', type: 'input' }
  ],
  hrv: [
    { label: 'SDNN：', prop: 'sdnn', type: 'input', append: 'ms' },
    { label: 'SDANN：', prop: 'sdann', type: 'input', append: 'ms' },
    { label: 'SDNN ldx：', prop: 'sdnnIdx', type: 'input', append: 'ms' },
    { label: 'pNN50：', prop: 'pnn', type: 'input', append: '%' },
    { label: 'VLF：', prop: 'vlf', type: 'input', append: 'ms²' },
    { label: 'LF：', prop: 'lf', type: 'input', append: 'ms²' },
    { label: 'rMSSD：', prop: 'rmssd', type: 'input', append: 'ms' },
    { label: '三角指数：', prop: 'triangularIndex', type: 'input' },
    { label: 'HF：', prop: 'hf', type: 'input', append: 'ms²' }
  ],
  stSegment: [
    { label: '最大压低：', prop: 'stMaxLow', type: 'input' },
    { label: '最大抬高：', prop: 'stMaxHigh', type: 'input' }
  ],
  pause: [
    { label: '房颤心搏：', prop: 'fcHeartBeat', type: 'input' },
    { label: '房扑心搏：', prop: 'fpHeartBeat', type: 'input' }
  ],
  sympathetic: [
    { label: '起搏心搏：', prop: 'qbHeartBeat', type: 'input' },
    { label: '房性起搏：', prop: 'fxUpBeat', type: 'input' },
    { label: '室性起搏：', prop: 'shiUpBeat', type: 'input' },
    { label: '双腔起搏：', prop: 'sqUpBeat', type: 'input' }
  ]
}

const echocardiography = [
  { label: '室间隔厚度[IVSD]：', prop: 'ivsd', type: 'input', append: 'mm' },
  { label: '左室后壁厚度[LVPWD]：', prop: 'lvpwd', type: 'input', append: 'mm' },
  { label: '左房内径[LAD]：', prop: 'lad', type: 'input', append: 'mm' },
  { label: '左室收缩期内径[LVDS]：', prop: 'lvds', type: 'input', append: 'mm' },
  { label: '左室舒张期内径[LVDD]：', prop: 'lvdd', type: 'input', append: 'mm' },
  { label: '左室射血分数[LVEF]：', prop: 'lvef', type: 'input', append: '%' },
  { label: '左心室质量指数[LVMI]：', prop: 'lvmi', type: 'input', append: 'g/m²' }
]

const bloodRoutine = [
  {
    label: '白细胞：',
    prop: 'wbc',
    type: 'input',
    append: '10^9/L',
    range: '参考范围：（3.5 - 9.5）×10⁹/L',
    description:
      '增多常见于感染（尤其是细菌感染）、严重组织损伤、白血病等；减少可见于病毒感染、再生障碍性贫血、使用某些药物等。'
  },

  {
    label: '中性粒细胞 ：',
    prop: 'neut',
    type: 'input',
    append: '10^9/L',
    range: '参考范围：（1.8 - 6.3）×10⁹/L',
    description:
      '中性粒细胞增多，细菌感染时常见，如肺炎、阑尾炎等；某些白血病、急性大出血、严重的组织损伤等情况也会导致增多。减少见于病毒感染、药物副作用、自身免疫性疾病等。'
  },
  {
    label: '中性粒细胞百分比：',
    prop: 'neutPer',
    type: 'input',
    append: '%',
    range: '参考范围：（40 - 75）%',
    description:
      '中性粒细胞增多，细菌感染时常见，如肺炎、阑尾炎等；某些白血病、急性大出血、严重的组织损伤等情况也会导致增多。减少见于病毒感染、药物副作用、自身免疫性疾病等。'
  },
  {
    label: '淋巴细胞：',
    prop: 'lym',
    type: 'input',
    append: '10^9/L',
    range: '参考范围：（1.1 - 3.2）×10⁹/L',
    description:
      '增多常见于病毒感染，如感冒、传染性单核细胞增多症，以及某些淋巴细胞白血病等；减少可见于长期接触放射线、免疫缺陷病等。'
  },
  {
    label: '淋巴细胞百分比：',
    prop: 'lymPer',
    type: 'input',
    append: '%',
    range: '参考范围：百分比：（20 - 50）%',
    description:
      '增多常见于病毒感染，如感冒、传染性单核细胞增多症，以及某些淋巴细胞白血病等；减少可见于长期接触放射线、免疫缺陷病等。'
  },
  {
    label: '中间细胞：',
    prop: 'mid',
    type: 'input',
    append: '10^9/L',
    range: '参考范围：（0.1 - 0.9）×10⁹/L',
    description:
      '嗜酸性粒细胞增多见于过敏性疾病（如哮喘、过敏性鼻炎）、寄生虫感染、某些皮肤病等；嗜碱性粒细胞增多见于慢性粒细胞白血病、某些过敏反应等；单核细胞增多见于感染性心内膜炎、活动性肺结核、疟疾等。'
  },
  {
    label: '中间细胞百分比：',
    prop: 'midPer',
    type: 'input',
    append: '%',
    range: '参考范围：（3 - 14）%',
    description:
      '嗜酸性粒细胞增多见于过敏性疾病（如哮喘、过敏性鼻炎）、寄生虫感染、某些皮肤病等；嗜碱性粒细胞增多见于慢性粒细胞白血病、某些过敏反应等；单核细胞增多见于感染性心内膜炎、活动性肺结核、疟疾等。'
  },
  {
    label: '红细胞：',
    prop: 'rbc',
    type: 'input',
    append: '10^12/L',
    range: '男性：（4.3 - 5.8）×10¹²/L；女性：（3.8 - 5.1）×10¹²/L',
    description:
      '增多见于真性红细胞增多症、严重脱水等；减少常见于各种贫血，如缺铁性贫血、巨幼细胞贫血、再生障碍性贫血等。'
  },
  {
    label: '血红蛋白：',
    prop: 'hgb',
    type: 'input',
    append: 'g/L',
    range: '男性：（130 - 175）g/L；女性：（115 - 150）g/L',
    description: '与红细胞变化类似，是诊断贫血的重要指标。不同类型的贫血，血红蛋白降低程度和表现有所差异。'
  },
  {
    label: '红细胞压积：',
    prop: 'hct',
    type: 'input',
    append: '%',
    range: '男性：（40 - 50）%；女性：（35 - 45）%',
    description: '增高见于真性红细胞增多症、大面积烧伤等；降低见于贫血、血液稀释等。'
  },
  {
    label: '平均红细胞体积：',
    prop: 'mcv',
    type: 'input',
    append: 'fL',
    range: '参考范围：（82 - 100）fl',
    description: '升高常见于巨幼细胞贫血；降低见于缺铁性贫血等。'
  },
  {
    label: '平均红细胞血红蛋白含量：',
    prop: 'mch',
    type: 'input',
    append: 'pg',
    range: '参考范围：（27 - 34）pg',
    description: '与平均红细胞体积结合，用于贫血的形态学分类，如小细胞低色素性贫血、大细胞性贫血等。'
  },
  {
    label: '平均红细胞血红蛋白浓度：',
    prop: 'mchc',
    type: 'input',
    append: 'g/l',
    range: '参考范围：（316 - 354）g/L',
    description: '与上述两个指标共同用于贫血的分类和鉴别诊断。'
  },
  {
    label: '红细胞分布宽度标准差 ：',
    prop: 'rdwSd',
    type: 'input',
    append: 'fL',
    range: '参考范围：（35 - 56）fl',
    description:
      '反映红细胞体积大小的离散程度。增高常见于早期或混合性营养缺乏性贫血，以及骨髓增生异常综合征等；在缺铁性贫血的诊断和疗效观察中有重要意义。'
  },
  {
    label: '红细胞分布宽度变异系数：',
    prop: 'rdwCv',
    type: 'input',
    append: '%',
    range: '参考范围：（11 - 16）%',
    description:
      '反映红细胞体积大小的离散程度。增高常见于早期或混合性营养缺乏性贫血，以及骨髓增生异常综合征等；在缺铁性贫血的诊断和疗效观察中有重要意义'
  },
  {
    label: '血小板：',
    prop: 'plt',
    type: 'input',
    append: '10^9/L',
    range: '参考范围：125 - 350）×10⁹/L',
    description:
      '增多见于原发性血小板增多症、慢性粒细胞白血病、急性感染等；减少见于血小板减少性紫癜、再生障碍性贫血、脾功能亢进等。'
  },
  {
    label: '血小板压积：',
    prop: 'pct',
    type: 'input',
    append: '%',
    range: '参考范围：（0.11 - 0.23）%',
    description: '增高见于血小板增多症、慢性粒细胞白血病等；降低见于血小板减少症等。'
  },
  {
    label: '平均血小板体积：',
    prop: 'mpv',
    type: 'input',
    append: 'fL',
    range: '参考范围 （6.5 - 12）fl',
    description: '增大见于原发性血小板减少性紫癜、骨髓增生异常综合征等；减小见于脾功能亢进、化疗后等。'
  },
  {
    label: '血小板分布宽度：',
    prop: 'pdw',
    type: 'input',
    append: 'fL',
    range: '参考范围：（9 - 17）fl',
    description: '增高表明血小板体积大小不均，见于原发性血小板增多症、急性白血病等；降低意义相对较小。'
  },
  {
    label: '大血小板数：',
    prop: 'plcc',
    type: 'input',
    append: '10^9/L',
    range: '参考范围：（30 - 90）×10⁹/L',
    description:
      '大血小板增多，可能提示骨髓造血功能活跃，如原发性血小板减少性紫癜患者在恢复期等情况；减少可能与骨髓造血功能受抑制有关。'
  },
  {
    label: '大血小板百分比：',
    prop: 'plcr',
    type: 'input',
    append: '%',
    range: '参考范围：（11 - 45）%',
    description:
      '大血小板增多，可能提示骨髓造血功能活跃，如原发性血小板减少性紫癜患者在恢复期等情况；减少可能与骨髓造血功能受抑制有关。'
  }
]

const echocardiogram = {
  // 左心房及左心室结构评估
  leftHeart: [
    { label: '左房前后径：', prop: 'leftFrontBackLength', type: 'input', append: 'mm', required: true },
    { label: '左心房横径：', prop: 'leftHorizontalLength', type: 'input', append: 'mm', required: true },
    { label: '左心房横长径：', prop: 'leftHorizontalLongLength', type: 'input', append: 'mm', required: true },
    {
      label: '收缩末期室间隔厚度（IVSs）：',
      prop: 'leftShrinkThickness',
      type: 'input',
      append: 'mm',
      required: true
    },
    {
      label: '舒张末期室间隔厚度（IVSd）：',
      prop: 'leftDiastoleThickness',
      type: 'input',
      append: 'mm',
      required: true
    },
    {
      label: '舒张末期左心室后壁厚度（LVPWd）：',
      prop: 'leftDiastolePosteriorThickness',
      type: 'input',
      append: 'mm',
      required: true
    },
    {
      label: '收缩末期左心室后壁厚度（LVPWs）：',
      prop: 'leftShrinkPosteriorThickness',
      type: 'input',
      append: 'mm',
      required: true
    },
    {
      label: '舒张末期左心室内径（LVEDD）：',
      prop: 'leftDiastoleLength',
      type: 'input',
      append: 'mm',
      required: true
    },
    {
      label: '收缩末期左心室内径（LVESD）：',
      prop: 'leftShrinkLength',
      type: 'input',
      append: 'mm',
      required: true
    },
    {
      label: '左心室流出道内径：',
      prop: 'leftOutflowLength',
      type: 'input',
      append: 'mm',
      required: false
    },
    {
      label: '舒张末期左心室容积（LVEDV）：',
      prop: 'leftDiastoleVolume',
      type: 'input',
      append: 'mm',
      required: true
    },
    {
      label: '收缩末期左心室容积（LVESV）：',
      prop: 'leftShrinkVolume',
      type: 'input',
      append: 'mm',
      required: true
    }
  ],

  // 右心房及右心室结构评估
  rightHeart: [
    { label: '右心房长径：', prop: 'rightLongLength', type: 'input', append: 'mm', required: false },
    { label: '右心房横径：', prop: 'rightHorizontalLength', type: 'input', append: 'mm', required: false },
    { label: '右心房前壁厚度及前后径：', prop: 'rightFrontBackLength', type: 'input', append: 'mm', required: false },
    { label: '右心室游离壁的厚度：', prop: 'rightFreeWallThickness', type: 'input', append: 'mm', required: false },
    { label: '右心室流出道内径：', prop: 'rightOutflowLength', type: 'input', append: 'mm', required: false },
    { label: 'TAPSE：', prop: 'rightTapse', type: 'input', append: 'mm', required: true },
    { label: '右室游离壁S波峰速：', prop: 'rightFreeWallSpeed', type: 'input', append: 'cm/s', required: true },
    { label: '右室面积变化分数（FAC）：', prop: 'rightAreaScore', type: 'input', append: '%', required: true }
  ],

  // 大动脉结构评估
  aorta: [
    { label: '主动脉窦部内径：', prop: 'aortaSinusLength', type: 'input', append: 'cm/s', required: true },
    { label: '升主动脉内径：', prop: 'aortaAscendingLength', type: 'input', append: '%', required: true },
    { label: '主动脉弓及降主动脉内径：', prop: 'aortaArchLength', type: 'input', append: 'mm', required: false },
    { label: '主肺动脉内径：', prop: 'aortaMpaLength', type: 'input', append: 'cm/s', required: true },
    { label: '左右肺动脉内径：', prop: 'aortaPaLength', type: 'input', append: '%', required: true }
  ],

  // 心功能评估收缩
  heartFunctionContract: [
    { label: '主左心室射血分数（LVEF）：', prop: 'heartLvef', type: 'input', required: true },
    { label: '心搏量（SV）：', prop: 'heartSv', type: 'input', required: true },
    { label: '二尖瓣环收缩期峰值速度（S’）：', prop: 'heartMas', type: 'input', required: true },
    { label: '左心室短轴缩短率（LVFS）：', prop: 'heartLvfs', type: 'input', required: true }
  ],

  // 心功能评估舒张
  heartFunctionDiastole: [
    { label: '二尖瓣舒张期血流速度(E峰，A峰)E/A值：', prop: 'heartEa', type: 'input', required: true },
    { label: '二尖瓣E峰减速时间（DT）：', prop: 'heartDt', type: 'input', required: true },
    { label: '二尖瓣环侧壁和间隔运动速度（e’），平均E/e’值：', prop: 'heartEe', type: 'input', required: true },
    { label: '左房最大容量指数（LAVI）：', prop: 'heartLavi', type: 'input', required: false },
    { label: '肺静脉血流S波，D波及S/D值：', prop: 'heartSd', type: 'input', required: false }
  ],

  mitralValve: [
    {
      id: 101,
      prop: 'mitralStructure',
      label: '结构：',
      type: 'checkbox',
      required: true,
      mutuallyExclusive: true, // 互斥
      options: [
        { label: '正常', value: '1' },
        { label: '钙化', value: '2' },
        { label: '增厚', value: '3' },
        { label: '畸形', value: '4' }
      ]
    },
    {
      id: 102,
      prop: 'mitralDplBlood',
      label: '多普勒血流：',
      type: 'radio',
      required: true,
      options: [
        { label: '正常', value: 1 },
        { label: '反流', value: 2 }
      ]
    },
    {
      id: 103,
      prop: 'mitralReverse',
      label: '反流：',
      type: 'radio',
      required: true,
      options: [
        { label: '中心性', value: 1 },
        { label: '偏心性沿前叶', value: 2 },
        { label: '偏心性沿后叶', value: 3 }
      ]
    },
    {
      id: 104,
      prop: 'mitralDegree',
      label: '程度：',
      type: 'radio',
      required: true,
      options: [
        { label: '轻度', value: 1 },
        { label: '中度', value: 2 },
        { label: '重度', value: 3 }
      ]
    },
    {
      id: 105,
      prop: 'mitralReverseOne',
      label: '反流Carpentier分型：I型',
      type: 'radio',
      required: true,
      options: [
        { label: '瓣叶穿孔', value: 1 },
        { label: '瓣环对合不良（瓣环扩张）', value: 2 }
      ]
    },
    {
      id: 106,
      prop: 'mitralReverseTwo',
      label: 'II型：',
      type: 'radio',
      required: true,
      options: [
        { label: '瓣叶脱垂', value: 1 },
        { label: '瓣叶假性脱垂', value: 2 },
        { label: '瓣叶连枷', value: 3 },
        { label: '瓣叶脱垂合并连枷', value: 4 }
      ]
    },
    {
      id: 107,
      prop: 'mitralReverseThree',
      label: 'III型：',
      type: 'radio',
      required: true,
      options: [
        { label: '瓣叶开放及关闭受限', value: 1 },
        { label: '瓣叶关闭受限（栓系、缺血性病变）', value: 2 }
      ]
    },
    {
      id: 108,
      prop: 'mitralShoot',
      label: '射流：',
      type: 'radio',
      required: false,
      options: [
        { label: '轻度', value: 1 },
        { label: '中度', value: 2 },
        { label: '重度', value: 3 }
      ]
    }
  ],
  tricuspidValve: [
    {
      id: 109,
      prop: 'tricuspidStructure',
      label: '结构：',
      type: 'checkbox',
      required: true,
      mutuallyExclusive: true, // 互斥
      options: [
        { label: '正常', value: '1' },
        { label: '钙化', value: '2' },
        { label: '增厚', value: '3' },
        { label: '畸形', value: '4' }
      ]
    },
    {
      id: 110,
      prop: 'tricuspidDplBlood',
      label: '多普勒血流：',
      type: 'radio',
      required: true,
      options: [
        { label: '正常', value: 1 },
        { label: '反流', value: 2 }
      ]
    },
    {
      id: 111,
      prop: 'tricuspidDegree',
      label: '程度：',
      type: 'radio',
      required: true,
      options: [
        { label: '轻度', value: 1 },
        { label: '中度', value: 2 },
        { label: '重度', value: 3 }
      ]
    },
    {
      id: 112,
      prop: 'tricuspidShoot',
      label: '射流：',
      type: 'radio',
      required: true,
      options: [
        { label: '轻度', value: 1 },
        { label: '中度', value: 2 },
        { label: '重度', value: 3 }
      ]
    }
  ],

  aorticValve: [
    {
      id: 113,
      prop: 'aorticStructure',
      label: '结构：',
      type: 'checkbox',
      required: true,
      mutuallyExclusive: true, // 互斥
      options: [
        { label: '正常', value: '1' },
        { label: '钙化', value: '2' },
        { label: '增厚', value: '3' },
        { label: '畸形', value: '4' }
      ]
    },
    {
      id: 114,
      prop: 'aorticDplBlood',
      label: '多普勒血流：',
      type: 'radio',
      required: true,
      options: [
        { label: '正常', value: 1 },
        { label: '反流', value: 2 }
      ]
    },
    {
      id: 115,
      prop: 'aorticDegree',
      label: '程度：',
      type: 'radio',
      required: true,
      options: [
        { label: '轻度', value: 1 },
        { label: '中度', value: 2 },
        { label: '重度', value: 3 }
      ]
    },
    {
      id: 116,
      prop: 'aorticShoot',
      label: '射流：',
      type: 'radio',
      required: true,
      options: [
        { label: '轻度', value: 1 },
        { label: '中度', value: 2 },
        { label: '重度', value: 3 }
      ]
    }
  ],

  pulmonaryValve: [
    {
      id: 117,
      prop: 'pulmonaryStructure',
      label: '结构：',
      type: 'checkbox',
      required: true,
      mutuallyExclusive: true, // 互斥
      options: [
        { label: '正常', value: '1' },
        { label: '钙化', value: '2' },
        { label: '增厚', value: '3' },
        { label: '畸形', value: '4' }
      ]
    },
    {
      id: 118,
      prop: 'pulmonaryDplBlood',
      label: '多普勒血流：',
      type: 'radio',
      required: true,
      options: [
        { label: '正常', value: 1 },
        { label: '反流', value: 2 }
      ]
    },
    {
      id: 119,
      prop: 'pulmonaryDegree',
      label: '程度：',
      type: 'radio',
      required: true,
      options: [
        { label: '轻度', value: 1 },
        { label: '中度', value: 2 },
        { label: '重度', value: 3 }
      ]
    },
    {
      id: 120,
      prop: 'pulmonaryShoot',
      label: '射流：',
      type: 'radio',
      required: true,
      options: [
        { label: '轻度', value: 1 },
        { label: '中度', value: 2 },
        { label: '重度', value: 3 }
      ]
    }
  ],

  leftVentricularOutflowTract: [
    {
      id: 121,
      prop: 'leftShiStreamNarrow',
      label: '狭窄：',
      type: 'radio',
      required: true,
      options: [
        { label: '有', value: 1 },
        { label: '无', value: 2 }
      ]
    },
    {
      id: 122,
      prop: 'leftShiStreamMaxSpeed',
      label: '最大流速：',
      type: 'input',
      required: true
    },
    {
      id: 123,
      prop: 'leftShiStreamDiastole',
      label: '左室舒张功能：',
      type: 'radio',
      required: true,
      options: [
        { label: '正常', value: 1 },
        { label: '减退', value: 0 }
      ]
    },
    {
      id: 124,
      prop: 'leftShiStreamPulmonary',
      label: '肺动脉压：',
      type: 'input',
      required: true
    }
  ]
}

const tabList = [
  {
    label: '指尖血糖',
    value: 'FINGER_SUGAR',
    component: 'FingertipBloodGlucose'
  },
  {
    label: '餐后2h尿糖',
    value: 'CH2H_SUGAR',
    component: 'PostprandialTwoSugar'
  },
  {
    label: '空腹静脉血糖',
    value: 'KF_JM_SUGAR',
    component: 'FastingVenousBlood'
  },
  {
    label: '糖化血红蛋白',
    value: 'SUGAR_HEMOGLOBIN',
    component: 'GlycosylatedHemoglobin'
  },
  {
    label: 'OGTT',
    value: 'OGTT',
    component: 'OGTT'
  },
  {
    label: '生化',
    value: 'BIOCHEMISTRY',
    component: 'Biochemistry'
  },
  {
    label: '颈动脉超声检查',
    value: 'CAROTID_ULTRASOUND',
    component: 'CarotidUltrasound'
  },
  {
    label: '尿常规检查',
    value: 'URINE_ROUTINE',
    component: 'Urinalysis'
  },
  {
    label: '肌电图检查',
    value: 'EMG',
    component: 'Emg'
  },
  {
    label: '眼底检查',
    value: 'EYE_GROUND',
    component: 'FundusExamination'
  },
  {
    label: 'ACR检查',
    value: 'ACR',
    component: 'ACR'
  },
  {
    label: '震动阈值检查',
    value: 'VIBRATION_THRESHOLD',
    component: 'VibrationThresholdCheck'
  },
  {
    label: '24小时动态血压',
    value: 'DYNAMICS_BLOOD_PRESSURE',
    component: 'BloodPressure'
  },
  {
    label: '心电图检查',
    value: 'ECG',
    component: 'ECG'
  },
  {
    label: '动脉硬化',
    value: 'ARTERIOSCLEROSIS',
    component: 'Arteriosclerosis'
  },
  {
    label: '动态血压心电图',
    value: 'DYNAMICS_ECG',
    component: 'DynamicBlood'
  },
  // {
  //   label: '心脏彩超',
  //   value: 'ECHOCARDIOGRAPHY',
  //   component: 'Echocardiography'
  // },
  {
    label: '血常规',
    value: 'BLOOD_ROUTINE',
    component: 'BloodRoutine'
  },
  {
    label: '超声心动图',
    value: 'ECHOCARDIOGRAM',
    component: 'Echocardiogram'
  },
  {
    label: 'BNP',
    value: 'BNP',
    component: 'BNP'
  },
  {
    label: '外周动脉',
    value: 'PAO',
    component: 'PeripheralArtery'
  }
]

// 心电图检查
const ecgOptions = [
  { label: '未见明显异常', value: '1' },
  { label: '窦性心动过缓', value: '2' },
  { label: 'ST段压低', value: '3' },
  { label: '窦性心律不齐', value: '4' },
  { label: '房室传导阻塞', value: '5' },
  { label: 'T波改变', value: '6' },
  { label: '左室高电压', value: '7' },
  { label: '左心室肥大', value: '8' },
  { label: '房颤', value: '9' },
  { label: '其他', value: '10' }
]

export { biochemistry, dynamicBlood, echocardiography, bloodRoutine, echocardiogram, tabList, ecgOptions }
