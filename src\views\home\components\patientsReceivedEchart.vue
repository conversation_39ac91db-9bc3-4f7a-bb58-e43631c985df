<template>
  <div ref="chart" style="width: 100%; height: 250px" />
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'PatientsReceivedEchart',
  props: {
    patientsReceivedData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    patientsReceivedData() {
      this.updateChart()
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      this.updateChart()
    },
    updateChart() {
      if (!this.chart) return

      const dates = this.patientsReceivedData.map((item) => item.receptionDate)
      const counts = this.patientsReceivedData.map((item) => item.count)

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates
        },
        yAxis: {
          type: 'value',
          name: '单位：人',
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [
          {
            data: counts,
            type: 'line',
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            },
            lineStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: '#409EFF',
              opacity: 0.1
            }
          }
        ]
      }

      this.chart.setOption(option)
    }
  }
}
</script>
