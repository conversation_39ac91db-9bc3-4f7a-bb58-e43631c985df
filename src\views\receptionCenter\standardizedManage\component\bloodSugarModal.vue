<!-- 血糖管理异常患者 -->
<template>
  <div class="blood-sugar-modal">
    <ProDialog ref="proDialogRef" title="血糖管理异常患者" :visible.sync="visible" top="8vh" width="1200px">
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        row-key="id"
        :columns="columns"
        :height="420"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #disease="{ row }">
          <ChronicDiseaseType :record="row" />
        </template>

        <template #sex="{ row }">
          <span>{{ genderTransform(row.sex) }}</span>
        </template>

        <template #idCard="{ row }">
          <EncryptionStr :cipher-text="row.idCard" :replace="row.idCardReplace" />
        </template>

        <template #phone="{ row }">
          <EncryptionStr :cipher-text="row.phone" :replace="row.phoneReplace" />
        </template>

        <template #sugarType="{ row }">
          <span>{{
            row.sugarType === 1
              ? '空腹血糖'
              : row.sugarType === 3
                ? '餐后2H血糖'
                : row.sugarType === 4
                  ? '糖化血红蛋白'
                  : ''
          }}</span>
        </template>

        <template #sugarValue="{ row }">
          <span style="color: red">{{ row.sugarValue }} </span>
        </template>
      </base-table>
    </ProDialog>
  </div>
</template>

<script>
import { getBloodSugarAbnormalList } from '@/api/standardizedManage'
import { genderTransform } from '@/utils/cspUtils'
import ProDialog from '@/components/ProDialog'
import tableMixin from '@/mixins/tableMixin'
import BaseTable from '@/components/BaseTable/index.vue'
import EncryptionStr from '@/components/encryptionStr/index.vue'
import ChronicDiseaseType from '@/components/chronicDiseaseType/index.vue'

export default {
  components: {
    ProDialog,
    BaseTable,
    EncryptionStr,
    ChronicDiseaseType
  },
  mixins: [tableMixin],
  data() {
    return {
      visible: false,
      queryParams: {},
      columns: [
        { label: '姓名', prop: 'patientName', width: 120 },
        { label: '慢病病种', prop: 'disease', slot: 'disease', width: 160 },
        { label: '性别', prop: 'sex', slot: 'sex', width: 100 },
        { label: '年龄', prop: 'age' },
        { label: '手机号', prop: 'phone', slot: 'phone', width: 140 },
        { label: '身份证号', prop: 'idCard', slot: 'idCard', width: 180 },
        { label: '测量类型', prop: 'sugarType', slot: 'sugarType', width: 160 },
        { label: '监测时间', prop: 'monitorTime', width: 160 },
        { label: '监测值', prop: 'sugarValue', slot: 'sugarValue' }
      ]
    }
  },
  methods: {
    genderTransform,
    async getTableList(params) {
      return await getBloodSugarAbnormalList(params)
    }
  }
}
</script>
