// ====== LiSuwan add start 2024.08.27 ======

* {
  margin: 0rem;
  padding: 0rem;
}

ul,
li,
ol {
  list-style: none;
}

// === 文字省略 start ===
.overflow {
  overflow: hidden;
}

.text_overflow_1 {
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  overflow: hidden;
}

.text_overflow_2,
.text_overflow_3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.text_overflow_2 {
  -webkit-line-clamp: 2;
}

.text_overflow_3 {
  -webkit-line-clamp: 3;
}

// === 文字省略 end ===

// === flex 布局 start ====
.flex_spaceAround {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex_spaceAroundStart {
  display: flex;
  align-items: start;
  justify-content: space-around;
}

.flex_spaceBetween {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex_spaceBetweenStart {
  display: flex;
  align-items: start;
  justify-content: space-between;
}

.flex_spaceBetweenWrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
}

.flex_center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex_end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.flex_baseline {
  display: flex;
  align-items: baseline;
  justify-content: flex-start;
}

.flex_spaceBetweenStart {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.flex_column {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.flex_columnStart {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-direction: column;
}
.flex_columnjustifyContentStart {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
}

.flex_columnEnd {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  flex-direction: column;
}

.flex_columnStartSpaceBetween {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-direction: column;
}

.flex_start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex_startEnd {
  display: flex;
  align-items: end;
  justify-content: flex-start;
}
.flex_starAlignItemsStart {
  display: flex;
  align-items: start;
  justify-content: flex-start;
}

// === flex 布局 end ====

// ====== 优化滚动条样式 start ======
.public_scrollbar::-webkit-scrollbar {
  width: 0.3rem; /* 滚动条的宽度 */
  height: 0.2rem; /* 滚动条的高度，对水平滚动条有效 */
  background-color: transparent; /* 滚动条的背景颜色 */
}

/* 滚动条轨道 */
.public_scrollbar::-webkit-scrollbar-track {
  border-radius: 0.2rem;
  background: transparent; /* 轨道的背景颜色 */
}

/* 滚动条滑块 */
.public_scrollbar::-webkit-scrollbar-thumb {
  border-radius: 0.2rem;
  background-color: #cacdd5; /* 滑块的背景颜色 */
}

/* 滚动条滑块：悬停效果 */
.public_scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #cacdd5; /* 滑块的悬停颜色 */
}

/* 滚动条滑块：激活时的效果 */
.public_scrollbar::-webkit-scrollbar-thumb:active {
  background-color: #cacdd5; /* 滑块的激活颜色 */
}

/* 滚动条按钮（上下箭头） */
.public_scrollbar::-webkit-scrollbar-button {
  display: none; /* 通常情况下不显示滚动条按钮 */
}
// ====== 优化滚动条样式 end ======

// ===== 当前皙无童看权限样式 start ======
.noViewingPermission {
  width: 25rem;
}
.noViewingPermission .el-message-box__content {
  width: 100%;
  height: 10rem;
  background-image: url('~@/assets/404_images/noViewingPermission.png');
  background-position: center bottom;
  background-repeat: no-repeat;
  background-size: contain;
  margin-top: 3rem;
}
@media (max-width: 1280px) {
  .noViewingPermission {
    width: 20rem;
  }
  .noViewingPermission .el-message-box__content {
    height: 8rem;
    margin-top: 2rem;
  }
}
// ===== 当前皙无童看权限样式 end ======

// ====== 底部版本所有（技术实现） start =======
.public_copyright {
  width: 100%;
  margin: 0rem auto;
  color: #30a99445;
  text-align: right;
  height: 1rem;
  line-height: 1rem;
  font-size: 0.6rem;
  margin-bottom: 0rem;
  padding-right: 1rem;
  box-sizing: border-box;
}

// ====== 底部版本所有（技术实现） end =======

#app .public_v2 {
  &.bodyContainer {
    width: 100%;
    height: 100%;
    padding: 0.3rem 0.8rem;
    box-sizing: border-box;
    // height: calc(100vh - 100px);
    background-color: #f9fffb;
  }

  .public_contentLeft,
  .public_contentRight {
    height: 100%;
    box-sizing: border-box;
  }

  .public_contentLeft {
    width: 15rem;
    padding: 1.5rem;
    position: relative;
    overflow-y: auto;
    box-sizing: border-box;

    &.hidden {
      width: 0.8rem;
      padding: 0;
      overflow: hidden;
      .public_label_list {
        display: none;
      }
    }
  }

  .public_contentRight {
    display: flex;
    flex-grow: 1;
    margin-left: 0.4rem;
    width: calc(100% - 20rem);
    height: 100%;
    .ecg_table {
      height: 100%;
      overflow-y: auto;
    }
  }

  .table_box_content.public_boxShadow,
  .public_contentRight.public_boxShadow,
  .public_contentLeft.public_boxShadow {
    overflow-y: auto;
  }

  // ====== 优化滚动条样式 start ======
  ::-webkit-scrollbar {
    width: 0.3rem; /* 滚动条的宽度 */
    height: 0.3rem; /* 滚动条的高度，对水平滚动条有效 */
    background-color: transparent; /* 滚动条的背景颜色 */
  }

  /* 滚动条轨道 */
  ::-webkit-scrollbar-track {
    border-radius: 0.2rem;
    background: transparent; /* 轨道的背景颜色 */
  }

  /* 滚动条滑块 */
  ::-webkit-scrollbar-thumb {
    border-radius: 0.2rem;
    background-color: #cacdd5; /* 滑块的背景颜色 */
  }

  /* 滚动条滑块：悬停效果 */
  ::-webkit-scrollbar-thumb:hover {
    background-color: #cacdd5; /* 滑块的悬停颜色 */
  }

  /* 滚动条滑块：激活时的效果 */
  ::-webkit-scrollbar-thumb:active {
    background-color: #cacdd5; /* 滑块的激活颜色 */
  }

  /* 滚动条按钮（上下箭头） */
  ::-webkit-scrollbar-button {
    display: none; /* 通常情况下不显示滚动条按钮 */
  }
  // ====== 优化滚动条样式 end ======

  // === 字体颜色 start ===
  .color1E1E28 {
    color: #1e1e28;
  }

  .colorFFF {
    color: #fff;
  }

  .color333 {
    color: #333333;
  }

  .color666 {
    color: #666666;
  }
  .color1EBFA7 {
    color: #0a86c8;
  }
  .colorFF0000 {
    color: #ff0000;
  }
  .colorA0A0A0 {
    color: #a0a0a0;
  }
  .colorA2B0C2 {
    color: #a2b0c2;
  }
  .color3C405C {
    color: #3c405c;
  }
  .colorFB961A {
    color: #fb961a;
  }
  .colorFB961A {
    color: #fb961a;
  }
  .color3CA1EB {
    color: #3ca1eb;
  }
  .color16C4AF {
    color: #16c4af;
  }
  .color1C1F2E {
    color: #1c1f2e;
  }
  .color9AA0AA {
    color: #9aa0aa;
  }
  .color222 {
    color: #222;
  }

  // === 字体颜色 end ===

  // === 字号 start====
  .fontSize_36 {
    font-size: 1.8rem;
  }
  .fontSize_30 {
    font-size: 1.5rem;
  }

  .fontSize_24 {
    font-size: 1.2rem;
  }
  .fontSize_20 {
    font-size: 1rem;
  }
  .fontSize_18 {
    font-size: 0.9rem;
  }

  .fontSize_17 {
    font-size: 0.85rem;
  }

  .fontSize_16 {
    font-size: 0.8rem;
  }

  .fontSize_14 {
    font-size: 0.7rem;
  }

  .fontSize_12 {
    font-size: 0.6rem;
  }

  .fontSize_10 {
    font-size: 0.5rem;
  }

  .public_iconfont.iconfont {
    font-size: 1.2rem;
    transform: translateY(0.3rem);
  }
  // === 字号 end====
  .fontWeight600 {
    font-weight: 600;
  }
  // === 背景颜色 start ===

  .bgColor_42C9A300B2DC {
    background: linear-gradient(130deg, #00B2DC 0%, #0A86C8 100%);
  }
  .bgColor_f9fffb {
    background-color: #f9fffb;
  }

  .bgColor_FFF {
    background-color: #fff;
  }


  .bgColor_FFBEA2FE8787 {
    background: linear-gradient(128deg, #ffbea2 0%, #fe8787 100%);
  }

  .bgColor_DCF4E8EBF9F2 {
    background: linear-gradient(180deg, #dcf4e8 0%, #ebf9f2 100%);
  }
  .bgColor_34C5AF {
    background-color: #0A86C8;
  }
  .bgColor_f0f0f0 {
    background-color: #f0f0f0;
  }

  // === 背景颜色 end ===

  // === 倒圆 start ===
  .borderRadius6 {
    border-radius: 0.3rem;
  }

  // === 倒圆 end ===

  // === 外边距margin start ===
  .marginTop4 {
    margin-top: 0.2rem;
  }

  .marginTop8 {
    margin-top: 0.4rem;
  }

  .marginTop10 {
    margin-top: 0.5rem;
  }

  .marginTop12 {
    margin-top: 0.6rem;
  }

  .marginTop16 {
    margin-top: 0.8rem;
  }

  .marginTop18 {
    margin-top: 0.9rem;
  }

  .marginTop24 {
    margin-top: 1.2rem;
  }

  .marginTop30 {
    margin-top: 1rem;
  }
  .marginRight6 {
    margin-right: 0.3rem;
  }

  .marginRight10 {
    margin-right: 0.5rem;
  }

  .marginRight12 {
    margin-right: 0.6rem;
  }
  .marginRight20 {
    margin-right: 1rem;
  }
  .marginRight40 {
    margin-right: 2rem;
  }

  .marginRight100 {
    margin-right: 5rem;
  }

  .marginBottom16 {
    margin-bottom: 0.8rem;
  }

  .marginLeft6 {
    margin-left: 0.3rem;
  }
  .marginLeft8 {
    margin-left: 0.4rem;
  }
  .marginLeft16 {
    margin-left: 0.8rem;
  }

  .marginLeft40 {
    margin-left: 2rem;
  }
  .marginLeft100 {
    margin-left: 5rem;
  }

  .margin16 {
    margin: 0.8rem;
  }

  // === 外边距margin end ===

  // === 内边距padding start ===
  .padding30 {
    padding: 1.5rem;
  }
  .padding26 {
    padding: 1.3rem;
  }

  .padding24 {
    padding: 1.2rem;
  }
  .padding1626 {
    padding: 0.8rem 1.3rem;
  }

  .paddingTop26 {
    padding-top: 1.3rem;
  }

  .paddingTop20 {
    padding-top: 1rem;
  }
  .paddingBottom30 {
    padding-bottom: 1.5rem;
  }
  // === 内边距padding end ===

  // === button按钮 start===
  .public_button {
    min-width: 4.1rem;
    height: 1.5rem;
    padding: 0.4rem 0.8rem;
    color: #fff;
    box-sizing: border-box;
    border-radius: 0.3rem;
    border: none;
  }

  .public_dialogButton2,
  .public_dialogButton {
    min-width: 4.8rem;
    height: 1.8rem;
    padding: 0.2rem;
    border: none;
    border-radius: 0.15rem;
    box-sizing: border-box;
  }

  .public_dialogButton2 {
    border: 1px solid #dcdfe6;
  }

  .public_buttonReset {
    color: rgba(0, 0, 0, 0.85);
    border: 1px solid #ced3db;
  }

  .el-button.public_dialogButton2 > span,
  .el-button.public_dialogButton > span {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-button.public_buttonReset:hover,
  .el-button.public_buttonReset:focus {
    background-color: #fff;
  }



  .el-button + .el-button {
    margin-left: 0.8rem;
  }

  .public_tableBtns {
    width: 1.6rem;
    height: 1.6rem;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #ced3db;
    padding: 0px;
    box-sizing: border-box;
  }

  // === button按钮 end===

  // === 行高 start===
  .public_lineHeight36 {
    line-height: 1.8rem;
  }
  // === 行高 end===
  // === 高度 start====

  .public_height32 {
    height: 1.6rem;
    box-sizing: border-box;
  }

  .public_height34 {
    height: 1.7rem;
    box-sizing: border-box;
  }

  // === 高度 end====

  // === 宽度 start ====
  .public_width100 {
    width: 100%;
    box-sizing: border-box;
  }
  .public_width50 {
    width: 50%;
    box-sizing: border-box;
  }

  // === 宽度 end ====

  // 表单搜索模块的搜索收缩和展示样式 start
  .public_shrink {
    width: 2.4rem;
    height: 0.8rem;
    // background: #FAFBFC;
    color: #fafbfc;
    border-radius: 0px 0px 0.15rem 0.15rem;
    position: absolute;
    bottom: -0.8rem;
    left: 50%;
    margin-left: -1.2rem;
    box-sizing: border-box;
    border: none;
    cursor: pointer;
  }

  // 表单搜索模块的搜索收缩和展示样式 end

  // ====== input的尺寸 start ======

  .public_formItem {
    margin-bottom: 0rem;
    .el-form-item__label,
    .el-form-item__content {
      line-height: 1.5rem;
      font-weight: 500;
    }
    .el-input__icon,
    .el-input__inner {
      height: 1.5rem;
      line-height: 1.5rem;
    }
  }

  .public_inputHeight34 .el-input__inner {
    height: 1.6rem;
  }

  .public_datePickerHeight32,
  .public_inputHeight32 .el-input__inner,
  .public_inputHeight32 .el-input__icon {
    line-height: 1.6rem;
  }

  .public_inputHeight34 .el-input__inner,
  .public_inputHeight34 .el-input__icon {
    line-height: 1.6rem;
  }

  .public_datePickerHeight32 {
    padding: 0rem 0.2rem;
    width: 12rem;

    .el-range-separator {
      line-height: 1.7rem;
      font-size: 0.7rem;
    }
  }

  .public_datePicker.el-date-editor {
    .el-range-input::placeholder,
    .el-range-input {
      font-size: 0.7rem;
    }
  }
  .public_inputPlaceholder {
    .el-input__inner,
    .el-input__inner::placeholder {
      font-size: 0.7rem;
    }
  }
  .el-form-item__label {
    text-align-last: auto;
  }

  // ====== input的尺寸 end ======

  // ====== icon样式 start======
  .svg-icon {
    margin-right: 0.5rem;
  }
  .v2icon_search {
    //搜索
    .svg-icon {
      width: 0.7rem;
      height: 0.7rem;
    }
  }

  .v2icon_shrink {
    //重置
    .svg-icon {
      width: 2.4rem;
      height: 0.8rem;
    }
  }

  .v2icon_add {
    //新增
    .svg-icon {
      width: 0.6rem;
      height: 0.6rem;
      margin-right: 0.5rem;
    }
  }

  .v2icon_delete,
  .v2icon_modify {
    //删除、修改
    .svg-icon {
      width: 0.7rem;
      height: 0.7rem;
      margin-right: 0.2rem;
    }
  }

  .v2icon_rightArrow {
    .svg-icon {
      width: 0.35rem;
      height: 0.65rem;
    }
  }

  .v2icon_field {
    //自定义列
    .svg-icon {
      width: 0.65rem;
      height: 0.65rem;
      margin: 0px auto;
    }
  }

  .v2icon_print {
    //打印
    .svg-icon {
      width: 0.85rem;
      height: 0.8rem;
      margin: 0px auto;
    }
  }

  .v2icon_batchExport {
    //批量导出
    .svg-icon {
      width: 0.75rem;
      height: 0.8rem;
      margin: 0px auto;
    }
    &.el-button:focus {
      background: #ffffff;
      border: 1px solid #dcdfe6;
      border-color: #dcdfe6;
    }
  }

  .v2icon_export {
    &.el-button:focus {
      background: #ffffff;
      border: 1px solid #dcdfe6;
      border-color: #dcdfe6;
    }
    //导出
    .svg-icon {
      width: 0.75rem;
      height: 0.75rem;
      margin: 0px auto;
    }
  }

  .v2icon_arrowLeft {
    //向左箭头
    .svg-icon {
      width: 0.4rem;
      height: 0.7rem;
    }
  }

  // ====== icon样式 end======

  // ====== 阴影效果 start======
  .public_boxShadow {
    box-shadow: 0px 0.15rem 0.8rem 0rem rgba(0, 94, 192, 0.1);
    padding: 0.8rem 1.2rem;
    position: relative;
    overflow: hidden;
  }

  // ====== 阴影效果 start======

  // ====== 右侧input的宽度样式 strt======
  .public_form_input {
    width: 17.6rem;
    margin-right: 5.4rem;
  }

  // ====== 右侧input的宽度样式 end======

  // ===== 类型标签样式 start ======
  .public_label_list {
    .public_label.el-button,
    .public_label {
      height: 1.6rem;
      border-radius: 0.8rem;
      margin-bottom: 0.8rem;
      margin-right: 2.1rem;
      margin-left: 0rem;
      cursor: pointer;
      width: 4.6rem;
      padding: 0rem 0.4rem;
      box-sizing: border-box;
      min-width: auto;
      &:nth-child(2n) {
        margin-right: 0rem;
      }
    }
    .public_labelBig.public_label {
      margin-right: 0.5rem;
      width: 5.8rem;
    }

    .public_labelDefault {
      color: #555;
      background: #e9e9e9;
      border: 1px solid #e9e9e9;
    }

    .public_labelDefault:hover,
    .public_labelActive {
      background: #0a86c8;
      color: #fff;
      border: 1px solid #0a86c8;
    }
    .public_label_customTime {
      width: auto;
      // width: 4rem;
      // font-weight: bold;
    }
  }

  .public_label_list + .public_label_list {
    border-top: 1px solid #ececec;
  }
  .public_label_listBorderTop {
    border-top: 1px solid #ececec;
  }

  // ===== 类型标签样式 end ======

  // 树形样式
  .el-tree .el-tree-node__label {
    font-size: 0.7rem;
  }
  .el-tree .el-tree-node__content > .el-tree-node__expand-icon {
    &::before {
      content: '\e6df';
      display: inline-block;
      transform: rotate(270deg);
    }
  }
  .el-tree .el-tree-node__content > .el-tree-node__expand-icon {
    &::after {
      content: '';
      width: 0.5rem;
      height: 0.5rem;
      display: inline-block;
      background: url(../assets/v2_treeIcon_default.png) no-repeat right center;
      background-size: 0.5rem 0.5rem;

      position: relative;
      top: 0.05rem;
      margin-left: 0.3rem;
    }
  }

  .el-tree .el-tree-node__expand-icon.expanded {
    transform: rotate(0deg);
  }
  .el-tree .el-tree-node__content > .el-tree-node__expand-icon.expanded {
    &::before {
      content: '\e6df';
      display: inline-block;
      transform: rotate(0deg);
    }
  }

  .el-tree .el-tree-node__content > .el-tree-node__expand-icon.expanded {
    &::after {
      display: inline-block;
      content: '';
      width: 0.6rem;
      height: 0.6rem;
      background: url(../assets/v2_treeIcon_open.png) no-repeat right center;
      background-size: 0.6rem 0.6rem;
      position: relative;
      top: 0.1rem;
      margin-left: 0.3rem;
    }
  }

  .el-tree .el-tree-node__content > .el-tree-node__expand-icon.el-icon-caret-right.is-leaf {
    &::after {
      content: '';
      background: none;
      width: 0rem;
      height: 0rem;
    }
  }
  .el-tree .el-tree-node__content > .el-tree-node__expand-icon.el-icon-caret-right.is-leaf {
    border-left: 1px dashed #dfe3ec;
    height: 26px;
    margin-left: -5px;
  }

  .cell .el-tag {
    margin-right: 0.3rem;
    font-size: 0.6rem;
    height: auto;
    padding: 0.2rem 0.4rem;
    line-height: 1rem;
  }

  .public_aside {
    position: absolute;
    top: calc(50% - 2.5rem);
    right: 0;
    width: 0.7rem;
    height: 5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #eaeaea;
    cursor: pointer;
    border-top-left-radius: 1rem;
    border-bottom-left-radius: 1rem;
    clip-path: polygon(0% 20%, 100% 0%, 100% 100%, 0% 80%);
  }

  .el-checkbox-group {
    .el-checkbox__label {
      font-size: 0.7rem;
    }
  }
}

// ====== table样式 start ======

@media (max-width: 1280px) {
  div.public_searchInputParent {
    .public_searchInput {
      width: 10rem;
    }
  }
}
.public_searchInputParent {
  flex: 1;
  text-align: right;
  padding-right: 1rem;
  .public_searchInput {
    width: 15rem;
    margin-left: auto;
  }
}

.public_table {
  border: none;
  border-radius: 0rem;
  .el-button + .el-button {
    margin-left: 0.5rem;
  }
  &.el-table .cell {
    // line-height: 2.4rem;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  &.el-table.analysis_talbe td.el-table__cell .cell {
    -webkit-line-clamp: 1 !important;
    white-space: normal;
  }

  td.el-table__cell {
    border-right: 1px solid #dfe6ec;
  }

  th.el-table__cell,
  td.el-table__cell {
    font-size: 0.8rem;
  }
  th.el-table__cell {
    background: linear-gradient(180deg, #eee 0%, #f0f0f0 100%);
    color: #666666;
    height: 2.4rem;
    line-height: 0.8rem;
    font-weight: 600;
  }

  th.el-table__cell.el-table-column--selection > .cell {
    padding-left: 0.7rem;
    padding-right: 0.7rem;
  }

  .el-table--striped .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: #f9fffb;
  }

  .el-table th.el-table__cell.is-leaf,
  .el-table td.el-table__cell {
    border: none;
  }

  .el-table__body tr.hover-row > td.el-table__cell,
  .el-table__body tr.hover-row.current-row > td.el-table__cell,
  .el-table__body tr.hover-row.selection-row > td.el-table__cell,
  .el-table__body tr.hover-row.el-table__row--striped > td.el-table__cell,
  .el-table__body tr.hover-row.el-table__row--striped.current-row > td.el-table__cell,
  .el-table__body tr.hover-row.el-table__row--striped.selection-row > td.el-table__cell {
    background-color: #ebf9f2 !important;
  }

  .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background-color: #ebf9f2 !important;
  }

  .el-button.el-button--text:hover {
    background: transparent;
    border: none;
    color: #0a86c8;
  }
}

// ====== table样式 end ======

.public_scrollbar .el-scrollbar__wrap {
  overflow: auto;
  margin-bottom: 0px !important;
}

// ====== 弹窗样式 start======
#app {
  .applicationGlobalBigFont .el-dialog .el-dialog__body .el-form-item .el-form-item__content .el-input__inner {
    font-size: 0.7rem !important;
  }

  .el-big-dialog,
  .el-dialog__wrapper {
    .public_v2_dialog {
      .el-dialog__header {
        height: 3.15rem;
        background: #f6fdf8;
        padding: 0.9rem 1.1rem;
        box-sizing: border-box;
        border: none;
      }

      .el-dialog__header span.dialog-title {
        background-color: transparent;
        line-height: normal;
        height: auto;
        display: block;
        width: 100%;
        position: relative;
        .v2icon_amplifyFullScreen {
          position: absolute;
          right: 1.5rem;
          top: 50%;
          -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
        }
        span {
          font-weight: 500;
          font-size: 0.9rem !important;
          background-color: transparent;
          color: #0a86c8;
          line-height: normal;
          height: auto;
        }
      }

      label {
        font-weight: 500;
      }
    }
  }

  .el-input-number__increase,
  .el-input-number__decrease {
    //height: 1.4rem;
    //width: 1.4rem;
    //box-sizing: border-box;
    //padding: 0px;
    //border: none;
    //line-height: 1.4rem;
    //top: 6px;
  }
}

// ====== 弹窗样式 end======

// === 优化Tabs 标签页 start ===

.public_tab {
  .el-tabs__item {
    font-size: 0.8rem;
    &.is-active {
      font-weight: 600;
    }
  }
  .el-tabs__nav-wrap::after {
    background-color: transparent;
  }
}
// === 优化Tabs 标签页 end ===

// ====== 暂无异常数据 start======
.public_noData {
  width: 100%;
  padding: 2rem 4%;
  box-sizing: border-box;
  text-align: center;
  .public_noData_img {
    max-width: 100%;
    height: auto;
  }
}

.public_no_data_content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    width: auto;
    height: 7rem;
  }
}

// ====== 暂无异常数据 end======
// ====== LiSuwan add start 2024.08.27 ======

.public_v2.bodyContainer.statistic {
  height: calc(100vh - 100px);
}
// ====== LiSuwan add end 2024.08.27 ======
// ====== lisuwan add  start ======
#app .el-dialog__wrapper.dialog_common .el-dialog {
  &.statisticAnalysis {
    &.statisticAnalysisEdouard {
      height: 80%;
      overflow: hidden;
      .el-dialog__body {
        height: 90%;
      }
      &.is-fullscreen {
        height: 100%;
        .el-dialog__body {
          height: 100%;
        }
      }
    }
    max-height: 80%;
    overflow: auto;
    &.is-fullscreen {
      max-height: 100%;
      overflow: hidden;
    }
    .el-dialog__body {
      min-height: 30vh;
      max-height: 90%;
      height: auto;
      overflow-y: auto;
    }
  }
}

// ====== lisuwan add  end ======

// ====== 通过媒体查询的方式设置字体的尺寸 start ======
@media (max-width: 1280px) {
  #app {
    .public_v2 {
      .el-tree .el-tree-node__content > .el-tree-node__expand-icon {
        &::after {
          content: '';
          width: 0.6rem;
          height: 0.6rem;
          display: inline-block;
          background: url(../assets/v2_treeIcon_default.png) no-repeat right center;
          background-size: 0.6rem 0.6rem;

          position: relative;
          top: 0.1rem;
          margin-left: 0.3rem;
        }
      }

      .el-tree .el-tree-node__content > .el-tree-node__expand-icon.expanded {
        &::after {
          display: inline-block;
          content: '';
          width: 0.7rem;
          height: 0.7rem;
          background: url(../assets/v2_treeIcon_open.png) no-repeat right center;
          background-size: 0.7rem 0.7rem;
          position: relative;
          top: 0.1rem;
          margin-left: 0.3rem;
        }
      }

      .public_v2.bodyContainer.statistic,
      &.bodyContainer {
        height: 100%;
      }

      .fontSize_36 {
        font-size: 1.2rem;
      }

      .fontSize_30 {
        font-size: 1.1rem;
      }
      .fontSize_24 {
        font-size: 0.9rem;
      }
      .fontSize_20 {
        font-size: 0.8rem;
      }
      .fontSize_16,
      .fontSize_18 {
        font-size: 0.7rem;
      }
      .fontSize_14 {
        font-size: 0.6rem;
      }
      .fontSize_12 {
        font-size: 0.5rem;
      }

      .public_datePicker.el-date-editor {
        .el-range-input::placeholder,
        .el-range-input {
          font-size: 0.6rem;
        }
      }
      .public_inputPlaceholder {
        .el-input__inner,
        .el-input__inner::placeholder {
          font-size: 0.6rem;
        }
      }

      // .public_contentLeft {
      //   width: 12rem;
      //   padding: 0.8rem 0.8rem;

      // }

      // .public_label_list {
      //   .public_label.el-button,
      //   .public_label {
      //     width: 4.4rem;
      //     margin-right: 1.2rem;
      //   }
      // }
      .public_datePickerHeight32 {
        // width: 10.5rem;
        .el-range-separator {
          font-size: 0.6rem;
        }
      }

      .marginRight10 {
        margin-right: 0.3rem;
      }

      .el-checkbox-group {
        .el-checkbox__label {
          font-size: 0.6rem;
        }
      }
    }
  }
}
// ====== 通过媒体查询的方式设置字体的尺寸 end ======

body .public_inputNumber_userdb .el-input {
  height: 1.9rem;
  line-height: 1.9rem;
}

body .public_datePickerhealthPushRecords.el-date-editor .el-range-input {
  height: 100%;
  line-height: 1;
}

body .public_slectMultiple.el-select .el-input,
body .public_slectMultiple.el-select {
  height: auto;
}
