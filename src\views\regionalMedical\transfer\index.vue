<template>
  <ProTwoColumnsLayout>
    <ProTwoColumnsLayoutLeft slot="left">
      <!-- ====== 审核状态 start ======-->
      <div class="public_label_list">
        <span class="fontSize_14 color666 public_label_customTime">转诊状态</span>
        <search-item-check
          v-model="queryParams.referralStatus"
          class="marginTop10"
          :options="referralStatusList"
          :has-all="true"
          @change="handleSearch"
        />
      </div>
      <!-- ====== 审核状态 end ======-->

      <!-- ====== 自定义时间 start ======-->
      <div class="public_label_list paddingTop20 marginTop4 flex_columnStart">
        <span class="fontSize_14 color666 public_label_customTime">申请时间</span>
        <el-date-picker
          v-model="queryParams.timeRange"
          unlink-panels
          class="public_inputHeight32 public_datePickerHeight32 marginTop10 public_datePicker"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          :clearable="false"
          @change="handleSearch"
        />
      </div>
      <!-- ====== 自定义时间 end ======-->
    </ProTwoColumnsLayoutLeft>
    <ProTwoColumnsLayoutRight slot="right">
      <div class="ecgDetail_data public_boxShadow bgColor_FFF borderRadius6 public_width100 ecgDetail_transferNum flex_start">
        <div
          class="flex_spaceBetween transfer-item"
          :class="{ 'active': queryParams.type === 1 }"
          @click="handleTransferClick(1)"
        >
          <img class="transfer-icon" src="@/assets/v2_ecgTransfer_1.png" alt="转入患者">
          <div class="flex_column fontSize_16 colorFB961A">
            <span>转入患者</span>
            <span class="marginTop4"><i class="fontSize_30 fontWeight600">{{ patientCount.inCount }}</i><i class="marginLeft6">人</i></span>
          </div>
        </div>
        <div
          class="flex_spaceBetween transfer-item color3CA1EB"
          :class="{ 'active': queryParams.type === 2 }"
          @click="handleTransferClick(2)"
        >
          <img class="transfer-icon" src="@/assets/v2_ecgTransfer_2.png" alt="转出患者">
          <div class="flex_column fontSize_16">
            <span>转出患者</span>
            <span class="marginTop4"><i class="fontSize_30 fontWeight600">{{ patientCount.outCount }}</i><i class="marginLeft6">人</i></span>
          </div>
        </div>
      </div>

      <div class="public_boxShadow bgColor_FFF public_width100 ecg_table marginTop16 borderRadius6">
        <div class="flex_end public_width100">
          <el-button type="primary" @click="handleAddTransfer">新增患者转诊申请</el-button>
          <div class="public_searchInputParent">
            <ProInput
              v-model="queryParams.keyword"
              class="fontSize_12 public_height32 public_inputHeight32 public_inputPlaceholder public_searchInput"
              style="width: 20vw"
              placeholder="请输入患者姓名搜索"
              @debounce="handleSearch"
            >
              <template #suffix>
                <el-icon class="iconfont public_iconfont icon-sousuo" />
              </template>
            </ProInput>
          </div>
        </div>
        <div class="marginTop16 public_boxShadow bgColor_FFF public_width100 borderRadius6">
          <base-table
            ref="baseTable"
            :table-data="tableData"
            :loading="loading"
            :stripe="true"
            :height="'45vh'"
            row-key="id"
            :columns="columns"
            :total="total"
            :page-info="queryParams"
            @pagination-change="handlePaginationChange"
          >
            <template #referralStatus="{ row }">
              <span v-if="row.referralStatus === 1">待接诊</span>
              <span v-if="row.referralStatus === 5">已接诊</span>
              <span v-if="row.referralStatus === 7">已拒绝</span>
              <span v-if="row.referralStatus === 9">已撤销</span>
            </template>

            <template #referralType="{ row }">
              <span v-if="row.referralType === 1">上转</span>
              <span v-if="row.referralType === 2">下转</span>
            </template>

            <template #treatSuggest="{ row }">
              <span v-if="row.referralType === 1">门诊</span>
              <span v-if="row.referralType === 2">住院</span>
            </template>

            <template #operation="{ row }">
              <el-button type="text" size="small" @click="handleDetail(row)">详情</el-button>
              <el-button
                v-if="queryParams.type === 1 && row.referralStatus === 1"
                type="text"
                size="small"
                @click="handleTreat(row)"
              >
                接诊
              </el-button>
              <el-button
                v-if="queryParams.type === 1 && row.referralStatus === 1"
                type="text"
                size="small"
                @click="handleRefuse(row)"
              >
                拒绝
              </el-button>
              <el-button
                v-if="queryParams.type === 2 && row.referralStatus === 1"
                type="text"
                size="small"
                @click="handleCancel(row)"
              >
                撤销
              </el-button>
            </template>
          </base-table>
        </div>
      </div>
    </ProTwoColumnsLayoutRight>
  </ProTwoColumnsLayout>
</template>

<script>
import searchItemCheck from '@/components/search-item-check.vue'
import ProInput from '@/components/ProInput/index.vue'
import ProTwoColumnsLayout from '@/components/ProTwoColumnsLayout/parent.vue'
import ProTwoColumnsLayoutLeft from '@/components/ProTwoColumnsLayout/left.vue'
import ProTwoColumnsLayoutRight from '@/components/ProTwoColumnsLayout/right.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import {
  getReferralRecord,
  getReferralRecordCount,
  cancelReferralRecord,
  treatReferralRecord,
  refuseReferralRecord
} from '@/api/regionalMedical'

export default {
  name: 'EcgTransfer',
  components: {
    ProTwoColumnsLayoutRight,
    ProTwoColumnsLayoutLeft,
    ProTwoColumnsLayout,
    ProInput,
    searchItemCheck,
    BaseTable
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        referralStatus: null,
        type: 1,
        keyword: '',
        timeRange: []
      },
      patientCount: {
        inCount: 0,
        outCount: 0
      },
      // 转诊状态
      referralStatusList: [
        {
          code: 1,
          value: '待接诊'
        },
        {
          code: 5,
          value: '已接诊'
        },
        {
          code: 7,
          value: '已拒绝'
        },
        {
          code: 9,
          value: '已撤销'
        }
      ],
      columns: [
        { prop: 'patientName', label: '姓名' },
        { prop: 'planDate', label: '申请时间' },
        { prop: 'referralType', label: '转诊申请类型', slot: 'referralType' },
        { prop: 'referralStatus', label: '转诊状态', slot: 'referralStatus' },
        { prop: 'outDepartName', label: '转出单位' },
        { prop: 'inDepartName', label: '转入单位' },
        { prop: 'treatSuggest', label: '转诊建议', slot: 'treatSuggest' },
        { prop: 'operation', label: '操作', slot: 'operation', width: 150 }
      ]
    }
  },
  created() {
    this.getReferralRecordCountFn()
  },
  methods: {
    async getTableList(params) {
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }

      return await getReferralRecord(queryParams)
    },

    async getReferralRecordCountFn() {
      const { timeRange, ...rest } = this.queryParams || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]
      const queryParams = {
        ...rest,
        startDate,
        endDate
      }
      const res = await getReferralRecordCount(queryParams)
      if (res.code === 200) {
        this.patientCount = {
          inCount: res.data.find((it) => it.diseaseCode === 'in').count,
          outCount: res.data.find((it) => it.diseaseCode === 'out').count
        }
      }
    },

    handleTransferClick(type) {
      this.queryParams.type = type
      this.handleSearch()
    },

    handleAddTransfer() {
      this.$router.push({
        path: '/regionalMedical/transfer/transferApply'
      })
    },

    handleDetail(row) {
      this.$router.push({
        path: '/regionalMedical/transfer/transferApply',
        query: { id: row.id }
      })
    },

    handleTreat(row) {
      this.handleConfirmDelete({
        params: { id: row.id },
        deleteApi: treatReferralRecord,
        message: '确定接诊该转诊记录吗？',
        afterSuccess: () => {
          this.handleSearch()
        }
      })
    },

    handleRefuse(row) {
      this.handleConfirmDelete({
        params: { id: row.id },
        deleteApi: refuseReferralRecord,
        message: '确定拒绝接诊该转诊记录吗？',
        afterSuccess: () => {
          this.handleSearch()
        }
      })
    },

    handleCancel(row) {
      this.handleConfirmDelete({
        params: { id: row.id },
        deleteApi: cancelReferralRecord,
        message: '确定撤销该转诊记录吗？',
        afterSuccess: () => {
          this.handleSearch()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.transfer-icon {
  width: 55px; /* 调整图片大小，从40px增加到55px */
  height: 55px;
  object-fit: contain;
}

.transfer-item {
  cursor: pointer;
  position: relative;
  padding: 10px 15px;
  transition: all 0.3s;

  &.active {
    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 0;
      width: 100%;
      height: 3px;
      background-color: #3ca1eb; /* 统一高亮颜色为蓝色 */
    }
  }

  &:hover {
    opacity: 0.9;
  }
}
</style>
