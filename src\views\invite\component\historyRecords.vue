<template>
  <div>
    <ProDialog
      :visible.sync="historyVisible"
      :title="historyType === 'receive' ? '接诊历史' : historyType === 'check' ? '检验检查历史' : '规范管理历史'"
      width="930px"
    >
      <div>
        <el-table :data="tableData" style="width: 100%" border>
          <el-table-column
            align="center"
            :prop="historyType === 'receive' ? 'receptionDate' : historyType === 'check' ? 'inspectDate' : 'manageDate'"
            :label="historyType === 'receive' ? '接诊日期' : historyType === 'check' ? '检查日期' : '管理日期'"
          />
          <el-table-column
            align="center"
            :prop="historyType === 'receive' ? 'receptionDoctorName' : 'doctorName'"
            :label="historyType === 'receive' ? '接诊医生' : historyType === 'check' ? '检查医生' : '管理医生'"
          />
          <el-table-column align="center" :prop="historyType === 'receive' ? 'receptionStatus' : 'status'" label="状态">
            <template slot-scope="{ row }">
              <el-tag
                v-if="historyType === 'receive' ? row.receptionStatus === 1 : row.status === 1"
                type="primary"
              >进行中</el-tag>
              <el-tag
                v-else-if="historyType === 'receive' ? row.receptionStatus === 5 : row.status === 5"
                type="success"
              >已完成</el-tag>
              <el-tag
                v-else-if="historyType === 'receive' ? row.receptionStatus === 9 : row.status === 9"
                type="danger"
              >已取消</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="operate" label="操作">
            <template slot-scope="{ row }">
              <el-button type="text" size="small" @click="handleDetail(row.id)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </ProDialog>
    <ProDialog
      :visible.sync="detailVisible"
      :title="historyType === 'receive' ? '接诊历史' : historyType === 'check' ? '检查历史' : '规范管理历史'"
      width="90%"
      :before-close="handleClose"
    >
      <PatientReception v-if="historyType === 'receive' && id" :key="id" :history-id="id" />
      <ManagePatient v-if="historyType === 'standard' && id" :key="id" :history-id="id" />
      <PatientExamination v-if="historyType === 'check' && id" :key="id" :history-id="id" />
    </ProDialog>
  </div>
</template>

<script>
import { getReceptionWorkbenchHistoryList } from '@/api/receptionWorkbench'
import { getInspectionTestingHistoryList } from '@/api/examination'
import { getStandardizedManageHistoryList } from '@/api/standardizedManage'
import PatientReception from '@/views/receptionCenter/patientReception/index.vue'
import ManagePatient from '@/views/receptionCenter/managePatient/index.vue'
import PatientExamination from '@/views/receptionCenter/patientExamination/index.vue'
import ProDialog from '@/components/ProDialog'

export default {
  name: 'HistoryRecords',
  components: {
    ProDialog,
    PatientReception,
    ManagePatient,
    PatientExamination
  },
  data() {
    return {
      historyVisible: false,
      historyType: '',
      tableData: [],
      id: '',
      detailVisible: false
    }
  },
  methods: {
    getList() {
      const apiMap = {
        receive: getReceptionWorkbenchHistoryList,
        check: getInspectionTestingHistoryList,
        standard: getStandardizedManageHistoryList
      }

      const apiFunc = apiMap[this.historyType]
      if (apiFunc) {
        apiFunc({ patientId: this.$route.query.userId }).then((res) => {
          if (res.code === 200) {
            this.tableData = res.data
          }
        })
      }
    },
    handleDetail(id) {
      this.id = id
      this.detailVisible = true
    },
    handleClose() {
      this.id = ''
      this.detailVisible = false
    }
  }
}
</script>
