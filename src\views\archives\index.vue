<template>
  <ProTwoColumnsLayout>
    <ProTwoColumnsLayoutLeft slot="left">
      <div class="public_label_list flex_columnStart paddingTop20">
        <span class="fontSize_14 color666 public_label_customTime">建档时间</span>
        <el-date-picker
          v-model="queryParams.timeRange"
          unlink-panels
          class="public_inputHeight32 public_datePickerHeight32 marginTop10 public_datePicker"
          type="daterange"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          format="yyyy-MM-dd"
          @change="handleSearch"
        />
      </div>
      <div class="public_label_list paddingTop20 marginTop4 flex_columnStart">
        <span class="fontSize_14 color666 public_label_customTime">糖尿病诊断</span>
        <search-item-check
          v-model="queryParams.tnbResult"
          class="marginTop10"
          :class-name="'check-item2'"
          :options="tnbTypeOptions"
          :has-all="true"
          @change="handleSearch"
        />
      </div>
      <div class="public_label_list paddingTop20 marginTop4 flex_columnStart">
        <span class="fontSize_14 color666 public_label_customTime">高血压诊断</span>
        <search-item-check
          v-model="queryParams.gxyResult"
          class="marginTop10"
          :class-name="'check-item2'"
          :options="gxyTypeOptions"
          :has-all="true"
          @change="handleSearch"
        />
      </div>
      <div class="public_label_list paddingTop20 marginTop4 flex_columnStart">
        <span class="fontSize_14 color666 public_label_customTime">慢阻肺诊断</span>
        <search-item-check
          v-model="queryParams.copdResult"
          class="marginTop10"
          :class-name="'check-item2'"
          :options="mzfTypeOptions"
          :has-all="true"
          @change="handleSearch"
        />
      </div>
      <div class="public_label_list paddingTop20 marginTop4 flex_columnStart">
        <span class="fontSize_14 color666 public_label_customTime">房颤诊断</span>
        <search-item-check
          v-model="queryParams.fcResult"
          class="marginTop10"
          :class-name="'check-item2'"
          :options="fangchanTypeOptions"
          :has-all="true"
          @change="handleSearch"
        />
      </div>
    </ProTwoColumnsLayoutLeft>
    <ProTwoColumnsLayoutRight slot="right">
      <div class="right-content">
        <el-card>
          <el-button type="text" @click="handleDeathList">
            <span style="border-bottom: 1px solid #409eff">死亡人员名单</span>
          </el-button>
          <DiseaseCategory ref="diseaseCategory" :query-params="queryParams" @change="handleDiseaseChange" />
        </el-card>

        <el-card style="margin-top: 16px">
          <div class="search-box">
            <TreeSelect
              v-model="queryParams.departCode"
              :data="departTree"
              :props="{
                children: 'children',
                label: 'departName',
                value: 'departCode'
              }"
              placeholder="请选择机构"
              style="width: 230px; margin: 0 10px 10px 0"
              @change="handleDepartChange"
            />

            <el-select
              v-model="queryParams.manageDoctorId"
              placeholder="请选择责任医生"
              clearable
              filterable
              style="width: 200px; margin: 0 10px 10px 0"
              @change="handleSearch"
            >
              <el-option v-for="item in doctorList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>

            <el-input
              v-model="queryParams.keyword"
              placeholder="请输入姓名/身份证号"
              clearable
              style="width: 200px"
              @change="handleSearch"
            />
          </div>
          <BaseTable
            ref="baseTable"
            :table-data="tableData"
            :loading="loading"
            :stripe="true"
            row-key="id"
            :height="tableHeight"
            :columns="columns"
            :total="total"
            :page-info="queryParams"
            @pagination-change="handlePaginationChange"
          >
            <!-- 慢病病种 -->
            <template #disease="{ row }">
              <ChronicDiseaseType :record="row" />
            </template>

            <template #idCard="{ row }">
              <EncryptionStr :cipher-text="row.idCard" :replace="row.idCardReplace" />
            </template>

            <template #sex="{ row }">
              <span>{{ genderTransform(row.sex) }}</span>
            </template>

            <template #phone="{ row }">
              <EncryptionStr :cipher-text="row.phone" :replace="row.phoneReplace" />
            </template>

            <template #tnbResult="{ row }">
              <span>{{
                row.tnbResult === 1 ? '正常' : row.tnbResult === 2 ? '糖尿病前期' : row.tnbResult === 3 ? '糖尿病' : ''
              }}</span>
            </template>

            <template #tnbFlag="{ row }">
              <span>{{ row.tnbFlag === 0 ? '达标' : row.tnbFlag === 1 ? '异常' : '' }}</span>
            </template>

            <template #gxyResult="{ row }">
              <span>{{
                row.gxyResult === 1 ? '正常' : row.gxyResult === 2 ? '高血压前期' : row.gxyResult === 3 ? '高血压' : ''
              }}</span>
            </template>

            <template #gxyFlag="{ row }">
              <span>{{ row.gxyFlag === 0 ? '达标' : row.gxyFlag === 1 ? '异常' : '' }}</span>
            </template>

            <template #copdResult="{ row }">
              <span>{{ row.copdResult === 1 ? '正常' : row.copdResult === 2 ? '慢阻肺' : '' }}</span>
            </template>

            <template #fcResult="{ row }">
              <span>{{ row.fcResult === 1 ? '正常' : row.fcResult === 2 ? '房颤' : '' }}</span>
            </template>

            <template #fcFlag="{ row }">
              <span>{{ row.fcFlag === 0 ? '达标' : row.fcFlag === 1 ? '异常' : '' }}</span>
            </template>

            <template #gxzFlag="{ row }">
              <span>{{ row.gxzFlag === 0 ? '达标' : row.gxzFlag === 1 ? '异常' : '' }}</span>
            </template>

            <template #threeHighFlag="{ row }">
              <span>{{ row.threeHighFlag === 0 ? '正常' : row.threeHighFlag === 1 ? '异常' : '' }}</span>
            </template>

            <template #threeHighResult="{ row }">
              <span>{{ row.threeHighResult === 0 ? '达标' : row.threeHighResult === 1 ? '异常' : '' }}</span>
            </template>

            <template #operation="{ row }">
              <OperateMenu :row="row" :visible-count="3">
                <template #view>
                  <el-button type="text" size="small" @click="handleView(row)">详情</el-button>
                </template>
                <template v-if="row.disease" #continue>
                  <el-button type="text" size="small" @click="handleFollowJZ(row)">接诊</el-button>
                </template>
                <template #cancel>
                  <el-button type="text" size="small" style="color: red" @click="handleDelete(row)"> 删除 </el-button>
                </template>
              </OperateMenu>
            </template>
          </BaseTable>
        </el-card>
        <DeathPersonModal ref="deathPersonModal" />
      </div>
    </ProTwoColumnsLayoutRight>
  </ProTwoColumnsLayout>
</template>

<script>
import { getmanageDoctorApi, getOrgTreeByIdApi } from '@/api/system'
import { genderTransform } from '@/utils/cspUtils'
import { getUserDbList, jzBypatientIdApi, deleteUserDb } from '@/api/archives'
import { getUserId } from '@/utils/auth'
import ProTwoColumnsLayout from '@/components/ProTwoColumnsLayout/parent.vue'
import ProTwoColumnsLayoutLeft from '@/components/ProTwoColumnsLayout/left.vue'
import ProTwoColumnsLayoutRight from '@/components/ProTwoColumnsLayout/right.vue'
import searchItemCheck from '@/components/search-item-check.vue'
import DiseaseCategory from '@/components/diseaseCategory/index.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ChronicDiseaseType from '@/components/chronicDiseaseType/index.vue'
import EncryptionStr from '@/components/encryptionStr/index.vue'
import OperateMenu from '@/components/operateMenu/index.vue'
import TreeSelect from '@/components/TreeSelect/index.vue'
import DeathPersonModal from './components/deathPersonModal.vue'

export default {
  name: 'Archives',
  components: {
    ProTwoColumnsLayout,
    ProTwoColumnsLayoutLeft,
    ProTwoColumnsLayoutRight,
    searchItemCheck,
    DiseaseCategory,
    BaseTable,
    ChronicDiseaseType,
    EncryptionStr,
    OperateMenu,
    TreeSelect,
    DeathPersonModal
  },
  mixins: [tableMixin],
  data() {
    return {
      departTree: [],
      queryParams: {
        timeRange: [],
        disease: '',
        tnbType: '',
        gxyType: '',
        mzfType: '',
        fangchanType: '',
        manageDoctorId: '',
        departCode: '',
        keyword: ''
      },
      tnbTypeOptions: [
        { value: '正常', code: 1 },
        { value: '糖尿病前期', code: 2 },
        { value: '糖尿病', code: 3 }
      ],
      gxyTypeOptions: [
        { value: '正常', code: 1 },
        { value: '高血压前期', code: 2 },
        { value: '高血压', code: 3 }
      ],
      mzfTypeOptions: [
        { value: '正常', code: 1 },
        { value: '慢阻肺', code: 2 }
      ],
      fangchanTypeOptions: [
        { value: '正常', code: 1 },
        { value: '房颤', code: 2 }
      ],
      columns: [
        { prop: 'name', label: '姓名', width: 100 },
        { prop: 'disease', label: '慢病病种', width: 160, slot: 'disease' },
        { prop: 'idCard', label: '身份证号', width: 190, slot: 'idCard' },
        { prop: 'age', label: '年龄' },
        { prop: 'sex', label: '性别', width: 80, slot: 'sex' },
        { prop: 'phone', label: '手机号', width: 140, slot: 'phone' },
        { prop: 'address', label: '家庭住址', width: 180, showOverflowTooltip: true },
        { prop: 'departName', label: '医疗机构', width: 150, showOverflowTooltip: true },
        { prop: 'manageDoctorName', label: '责任医生' },
        { prop: 'registerDate', label: '建档日期', width: 120 },
        { prop: 'tnbResult', label: '糖尿病', slot: 'tnbResult', width: 100 },
        { prop: 'tnbFlag', label: '糖尿病管理', width: 100, slot: 'tnbFlag' },
        { prop: 'gxyResult', label: '高血压', slot: 'gxyResult', width: 100 },
        { prop: 'gxyFlag', label: '高血压管理', width: 100, slot: 'gxyFlag' },
        { prop: 'copdResult', label: '慢阻肺', slot: 'copdResult' },
        { prop: 'fcResult', label: '房颤', slot: 'fcResult', width: 100 },
        { prop: 'fcFlag', label: '房颤管理', width: 100, slot: 'fcFlag' },
        { prop: 'gxzFlag', label: '高血脂管理', width: 100, slot: 'gxzFlag' },
        { prop: 'tnbCsItem', label: '糖尿病并发症已做项目', width: 180, showOverflowTooltip: true },
        { prop: 'fcCsItem', label: '高血压靶器官已做项目', width: 180, showOverflowTooltip: true },
        { prop: 'threeHighFlag', label: '三高人群', width: 100, slot: 'threeHighFlag' },
        { prop: 'threeHighResult', label: '三高达标', width: 100, slot: 'threeHighResult' },
        { prop: 'teamName', label: '家医团队', width: 150 },
        { prop: 'doctorName', label: '家庭医生' },
        { prop: 'signDate', label: '签约时间', width: 120 },
        { prop: 'serviceStartDate', label: '服务开始时间', width: 120 },
        { prop: 'serviceEndDate', label: '服务结束时间', width: 120 },
        { prop: 'totalMoney', label: '总金额' },
        { prop: 'operation', label: '操作', slot: 'operation', width: 160, fixed: 'right' }
      ],
      doctorList: [],
      tableHeight: 'calc(100vh - 370px)'
    }
  },
  created() {
    this.getDoctorList()
    this.getDepartTree()
  },
  methods: {
    genderTransform,
    async getTableList(params) {
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }

      return await getUserDbList(queryParams)
    },

    // 机构树
    async getDepartTree() {
      const res = await getOrgTreeByIdApi({
        patientId: getUserId()
      })
      this.departTree = res.data
    },

    // 责任医生
    async getDoctorList() {
      const params = {
        pageNo: 1,
        pageSize: 2000,
        departCode: this.$store.getters.departCode
      }
      const res = await getmanageDoctorApi(params)
      if (res.code === 200) {
        this.doctorList = res.data.list || []
      }
    },

    handleDiseaseChange(disease) {
      this.queryParams.disease = disease.diseaseCode === 'all' ? '' : disease.diseaseCode
      this.handleSearch()
    },

    handleView(row) {
      this.$router.push({
        path: '/archives/detail',
        query: {
          id: row.id
        }
      })
    },

    async handleFollowJZ(item) {
      const { data } = await jzBypatientIdApi({ patientId: item.id })
      data.diseaseList = []
      this.$store.commit('receptionWorkbench/SET_RECEPTION_WORKBENCH_DATA', data)
      this.$router.push({ path: '/receptionCenter/patientReception', query: { id: data.id } })
    },

    handleDelete(item) {
      this.handleConfirmDelete({
        params: { id: item.id },
        deleteApi: deleteUserDb,
        message: `确定删除${item.name}的患者档案吗？`
      })
    },

    handleDepartChange(value) {
      this.queryParams.departCode = value
      this.handleSearch()
    },

    handleDeathList() {
      this.$refs.deathPersonModal.visible = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.right-content {
  width: 100%;
  height: 100%;
  .search-box {
    display: flex;
    justify-content: end;
  }
}
</style>
