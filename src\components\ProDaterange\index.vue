<template>
  <el-date-picker
    ref="picker"
    v-model="dateValue"
    style="width: 100%"
    class="public_inputHeight32 public_datePickerHeight32 marginTop10 public_datePicker"
    type="daterange"
    unlink-panels
    range-separator="~"
    start-placeholder="开始日期"
    end-placeholder="结束日期"
    value-format="yyyy-MM-dd"
    format="yyyy/MM/dd"
    :clearable="false"
    :picker-options="pickerOptions"
    @change="handleDate"
  />
</template>

<script>
import dayjs from 'dayjs'
import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import { calcTimeInterval } from '@/utils'
// 扩展 dayjs 功能以支持季度
dayjs.extend(quarterOfYear)

export default {
  name: 'ProDateRange',
  inheritAttrs: false, // 禁止自动绑定 attrs
  data() {
    return {
      dateValue: [],
      presets: {
        今天: 'today',
        本周: 'thisWeek',
        本月: 'thisMonth',
        本季度: 'thisQuarter',
        本年: 'thisYear',
        上周: 'lastWeek',
        上月: 'lastMonth',
        上季度: 'lastQuarter',
        去年: 'lastYear'
      },
      pickerOptions: {
        shortcuts: []
      }
    }
  },
  mounted() {
    this.pickerOptions.shortcuts = Object.keys(this.presets).map((key) => ({
      text: key,
      onClick: () => this.setPreset(this.presets[key])
    }))
  },
  methods: {
    handleDate(date) {
      const timeRange = calcTimeInterval(...date)
      if (timeRange > 12) {
        this.$message.warning('时间区间不能大于12个月')
        this.dateValue = []
        return
      }
      this.$emit('change', date)
    },
    setPreset(preset) {
      const dateRanges = {
        today: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        thisWeek: [dayjs().startOf('week').format('YYYY-MM-DD'), dayjs().endOf('week').format('YYYY-MM-DD')],
        thisMonth: [dayjs().startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')],
        thisQuarter: [dayjs().startOf('quarter').format('YYYY-MM-DD'), dayjs().endOf('quarter').format('YYYY-MM-DD')],
        thisYear: [dayjs().startOf('year').format('YYYY-MM-DD'), dayjs().endOf('year').format('YYYY-MM-DD')],
        lastWeek: [
          dayjs().subtract(1, 'week').startOf('week').format('YYYY-MM-DD'),
          dayjs().subtract(1, 'week').endOf('week').format('YYYY-MM-DD')
        ],
        lastMonth: [
          dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'),
          dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')
        ],
        lastQuarter: [
          dayjs().subtract(1, 'quarter').startOf('quarter').format('YYYY-MM-DD'),
          dayjs().subtract(1, 'quarter').endOf('quarter').format('YYYY-MM-DD')
        ],
        lastYear: [
          dayjs().subtract(1, 'year').startOf('year').format('YYYY-MM-DD'),
          dayjs().subtract(1, 'year').endOf('year').format('YYYY-MM-DD')
        ]
      }
      this.$refs.picker && this.$refs.picker.pickerVisible && (this.$refs.picker.pickerVisible = false)

      this.dateValue = dateRanges[preset]
      // this.$emit('change', this.dateValue)
    }
  }
}
</script>

<style scoped>
.presets {
  margin-top: 10px;
}
</style>
