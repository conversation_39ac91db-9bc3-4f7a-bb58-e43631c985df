<!-- 检验检查第十部分: 超声心动图 -->
<template>
  <div class="inspection-tenth-part">
    <div class="content">
      <div class="title">超声心动图</div>

      <div class="item">
        <div class="item-title">二尖瓣</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in echocardiogram.mitralValve" :key="item.prop" :label="item.label">
            {{
              item.options.find(option => option.value === echocardiogramData[item.prop]) &&
                item.options.find(option => option.value === echocardiogramData[item.prop]).label
            }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">三尖瓣</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in echocardiogram.tricuspidValve" :key="item.prop" :label="item.label">
            {{
              item.options.find(option => option.value === echocardiogramData[item.prop]) &&
                item.options.find(option => option.value === echocardiogramData[item.prop]).label
            }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">主动脉瓣</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in echocardiogram.aorticValve" :key="item.prop" :label="item.label">
            {{
              item.options.find(option => option.value === echocardiogramData[item.prop]) &&
                item.options.find(option => option.value === echocardiogramData[item.prop]).label
            }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">肺动脉瓣</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item v-for="item in echocardiogram.pulmonaryValve" :key="item.prop" :label="item.label">
            {{
              item.options.find(option => option.value === echocardiogramData[item.prop]) &&
                item.options.find(option => option.value === echocardiogramData[item.prop]).label
            }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <div class="item">
        <div class="item-title">左室流出道</div>
        <el-descriptions :column="3" border style="margin-top: 6px">
          <el-descriptions-item
            v-for="item in echocardiogram.leftVentricularOutflowTract"
            :key="item.prop"
            :label="item.label"
          >
            <span v-if="item.type === 'radio'">
              {{
                item.options.find(option => option.value === echocardiogramData[item.prop]) &&
                  item.options.find(option => option.value === echocardiogramData[item.prop]).label
              }}
            </span>
            <span v-else>
              {{ echocardiogramData[item.prop] }}
            </span>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </div>
</template>

<script>
import { echocardiogram } from '@/views/receptionCenter/patientReception/component/complicationsScreening'

export default {
  name: 'InspectionTenthPart',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      echocardiogram
    }
  },
  computed: {
    echocardiogramData() {
      const { data = {} } = this.reportInfo.itemList.find((item) => item.itemCode === 'ECHOCARDIOGRAM') || {}
      return data
    }
  }
}
</script>

<style lang="scss" scoped>
.inspection-tenth-part {
  .content {
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 6px;
    }
    .item {
      margin-bottom: 6px;
      .item-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 8px;
      }
    }
  }
}
</style>
