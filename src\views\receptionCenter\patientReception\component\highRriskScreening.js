const physicalExaminationForm = [
  {
    label: '身高：',
    prop: 'height',
    type: 'input',
    required: true,
    append: 'cm'
  },
  {
    label: '体重：',
    prop: 'weight',
    type: 'input',
    required: true,
    append: 'kg'
  },
  {
    label: 'BMI：',
    prop: 'bmi',
    type: 'input',
    required: false,
    disabled: true,
    append: 'kg/m²'
  },
  {
    label: '腰围：',
    type: 'input',
    required: true,
    prop: 'waistline',
    append: 'cm'
  },
  {
    label: '臀围：',
    type: 'input',
    required: true,
    prop: 'hipLine',
    append: 'cm'
  },
  {
    label: '腰臀比：',
    prop: 'whr',
    type: 'input',
    required: false
  },
  {
    label: '收缩压：',
    prop: 'sp',
    type: 'input',
    required: true,
    append: 'mmHg'
  },
  {
    label: '舒张压：',
    prop: 'dp',
    type: 'input',
    required: true,
    append: 'mmHg'
  }
]
const bodyCompositionForm = [
  {
    label: '人体总水分：',
    prop: 'tbw',
    type: 'input',
    append: 'kg',
    required: false
  },
  {
    label: '蛋白质含量：',
    prop: 'protein',
    type: 'input',
    append: 'kg',
    required: false
  },
  {
    label: '无机盐含量：',
    prop: 'mineral',
    type: 'input',
    append: 'kg',
    required: false
  },
  {
    label: '体脂肪量：',
    prop: 'bfm',
    type: 'input',
    append: 'kg',
    required: false
  },
  {
    label: '体脂肪百分比：',
    prop: 'pbf',
    type: 'input',
    append: '%',
    required: false
  },
  // {
  //   label: '身体质量指数：',
  //   prop: 'bmi',
  //   type: 'input',
  //   append: 'kg/m²',
  //   required: false
  // },
  {
    label: '细胞外水分比率：',
    prop: 'ecwRatio',
    type: 'input',
    append: '%',
    required: false
  },
  {
    label: '去脂体重：',
    prop: 'ffm',
    type: 'input',
    append: 'kg',
    required: false
  },
  {
    label: '肌肉量：',
    prop: 'slm',
    type: 'input',
    append: 'kg',
    required: false
  }
]

export { physicalExaminationForm, bodyCompositionForm }
