<!-- BNP -->
<template>
  <div class="BNP">
    <el-form ref="formRef" :model="form" label-width="120px">
      <el-form-item label="BNP：">
        <custom-input-number v-model="form.bnp" style="width: 28%">
          <template #append>ng/mL</template>
        </custom-input-number>
        <span style="margin-left: 10px">参考范围：< 100 pg/mL</span>
      </el-form-item>

      <el-form-item label="NT-proBNP：">
        <custom-input-number v-model="form.ntProBnp" style="width: 28%">
          <template #append>ng/L</template>
        </custom-input-number>
        <span style="margin-left: 10px">参考范围：< 125 ng/L</span>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'BNP',
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        bnp: '',
        ntProBnp: ''
      }
    }
  },
  methods: {
    initData(data) {
      this.form = {
        bnp: data.bnp,
        ntProBnp: data.ntProBnp,
        id: data.id
      }
    },
    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          ...this.form,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    }
  }
}
</script>
