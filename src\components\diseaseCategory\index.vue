<template>
  <div class="disease-category">
    <div
      v-for="item in diseaseList"
      :key="item.diseaseCode"
      :class="['stat-card', { active: item.diseaseCode === activeCode }]"
      @click="handleClick(item)"
    >
      <img :src="requireShowDisease(item.diseaseCode)" class="stat-card-icon">
      <div class="stat-card-title">{{ item.diseaseName }}</div>
      <div class="stat-card-value">{{ item.count }}</div>
    </div>
  </div>
</template>

<script>
import { getMassScreeningDisease } from '@/api/screenList'
import { getReceptionWorkbenchDisease } from '@/api/receptionWorkbench'
import { getStandardizedManageDisease } from '@/api/standardizedManage'
import { getPhrUserDBCountApi } from '@/api/personageHealth'

export default {
  name: 'DiseaseCategory',
  props: {
    queryParams: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      diseaseList: [],
      activeCode: 'all'
    }
  },

  watch: {
    queryParams: {
      handler() {
        if (this.$route.path === '/archives') {
          this.getDiseaseList()
        }
      },
      deep: true
    }
  },

  created() {
    this.getDiseaseList()
  },

  methods: {
    async getDiseaseList() {
      let res = null
      const { path } = this.$route
      const { timeRange, ...rest } = this.queryParams || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]
      const params = { ...rest, startDate, endDate }
      if (path === '/screenList') {
        res = await getMassScreeningDisease(params)
      } else if (path === '/receptionCenter/receptionWorkbench') {
        res = await getReceptionWorkbenchDisease(params)
      } else if (path === '/receptionCenter/standardizedManage') {
        res = await getStandardizedManageDisease(params)
      } else if (path === '/archives') {
        res = await getPhrUserDBCountApi(params)
      }
      if (res.code === 200) {
        const list = res.data || []
        this.diseaseList = list
      }
    },
    // 慢病图标 是否展示
    requireShowDisease(code) {
      try {
        if (code === 'COPD') {
          code = 'copd'
        }

        if (code === 'fangchan') {
          code = 'fc'
        }
        const data = require(`@/assets/disease/${code}.png`)
        if (data) {
          return data
        } else {
          return false
        }
      } catch (error) {
        return false
      }
    },
    handleClick(item) {
      this.activeCode = item.diseaseCode
      this.$emit('change', item)
    }
  }
}
</script>

<style lang="scss" scoped>
.disease-category {
  display: flex;
  align-items: center;
  height: 45px;
  gap: 10px;
  .stat-card {
    display: flex;
    align-items: center;
    background: #f4f4f4;
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    border: 2px solid transparent;
    min-width: 130px;
    gap: 8px;
    transition: border 0.2s, background 0.2s;
  }
  .stat-card.active {
    background: #0a86c8;
    color: #fff;
    border-color: #0a86c8;
  }
  .stat-card-icon {
    width: 24px;
    height: 24px;
  }
  .stat-card-title {
    font-size: 14px;
  }
  .stat-card-value {
    font-size: 20px;
    font-weight: bold;
  }
}
</style>
