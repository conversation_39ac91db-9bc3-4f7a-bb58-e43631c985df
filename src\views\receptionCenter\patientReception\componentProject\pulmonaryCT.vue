<!-- 肺部CT -->
<template>
  <div class="pulmonary-ct">
    <!-- <div v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail'">
      <audit-status v-if="auditInfo.status === 5" status="pass" style="margin-bottom: 10px; width: 50%" />

      <audit-status
        v-if="auditInfo.status === 9"
        status="reject"
        :reason="auditInfo.auditResult"
        style="margin-bottom: 10px; width: 50%"
      />

      <audit-status v-if="auditInfo.status === 1" status="pending" style="margin-bottom: 10px; width: 50%" />
    </div> -->
    <el-form :model="form" label-width="120px">
      <CheckboxGroupField v-model="form.ctResult" :item="checkboxItem" />
      <el-form-item label="检查所见：">
        <el-input v-model="form.ctFinding" type="textarea" />
      </el-form-item>
      <el-form-item v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail'" label="上传报告图片：">
        <custom-upload v-model="form.attachmentPhotoUrl" />
      </el-form-item>
      <el-form-item v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail'" label="上传视频：">
        <UploadVideo v-model="form.attachmentVideoUrl" />
      </el-form-item>
      <el-form-item
        v-if="auditInfo.status === 5 && $route.path !== '/qualityControl/ultrasonicQualityControl/detail'"
        label="质控结论："
      >
        <el-input v-model="auditInfo.auditResult" type="textarea" :rows="3" :disabled="true" />
      </el-form-item>
    </el-form>
    <!-- <div v-if="$route.path !== '/qualityControl/ultrasonicQualityControl/detail' && auditInfo.status !== 1">
      <span style="margin-right: 130px; font-size: 14px">质控人：{{ auditInfo.auditName }}</span>
      <span style="font-size: 14px">质控时间：{{ auditInfo.auditTime }}</span>
    </div> -->
  </div>
</template>

<script>
import CustomUpload from '@/components/customUpload/index.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'
import UploadVideo from '@/components/uploadVideo/index.vue'
// import AuditStatus from '@/components/auditStatus/index.vue'

export default {
  name: 'PulmonaryCT',
  components: {
    CustomUpload,
    CheckboxGroupField,
    UploadVideo
    // AuditStatus
  },
  data() {
    return {
      form: {
        ctResult: [],
        ctFinding: '',
        attachmentPhotoUrl: '',
        attachmentVideoUrl: []
      },
      auditInfo: {},
      checkboxItem: {
        label: '检查结果：',
        type: 'checkbox',
        required: false,
        mutuallyExclusive: true, // 互斥
        prop: 'ctResult',
        options: [
          { label: '正常', value: '1' },
          { label: '肺结核', value: '2' },
          { label: '肺气肿', value: '3' },
          { label: '肺大泡', value: '4' },
          { label: '支气管扩展', value: '5' },
          { label: '其他', value: '6' }
        ]
      }
    }
  },
  methods: {
    initData({ id, data }) {
      this.auditInfo = {
        status: data && data.status,
        auditResult: data && data.auditResult,
        auditName: data && data.auditName,
        auditTime: data && data.auditTime
      }
      this.form = {
        ctResult: data && data.ctResult ? data.ctResult.split(',') : [],
        ctFinding: data && data.ctFinding,
        attachmentPhotoUrl: data && data.attachmentPhotoUrl,
        attachmentVideoUrl: data && data.attachmentVideoUrl ? data.attachmentVideoUrl.split(',') : [],
        itemId: id,
        itemDetailId: data && data.id
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pulmonary-ct {
  padding: 16px;
}
</style>
