export function getTableOffsetTop() {
  let element = document.getElementById('table-pro')
  let offsetTop = 0
  while (element) {
    offsetTop += element.offsetTop
    element = element.offsetParent
  }
  return offsetTop + 100
}

// export function getListHeight() {
//   return `${window.innerHeight - getTableOffsetTop()}px`
// }

export function getElementTop(element) {
  const rect = element.getBoundingClientRect()
  const scrollTop = window.scrollY || document.documentElement.scrollTop
  return rect.top + scrollTop + 30 // 下面的padding 有个30
}

export function getListHeight(elId = 'table-pro', hasPagination = true) {
  const element = document.getElementById(elId)
  let topDistance = getElementTop(element)
  if (hasPagination) topDistance += 70 // 如果是表格默认把分页的高度加上
  return `${window.innerHeight - Math.floor(topDistance)}px`
}
