<!-- 指尖血糖 -->
<template>
  <div class="fingertip-blood-glucose">
    <div class="fingertip-blood-glucose-data">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="130px">
        <el-form-item label="血糖类型：" prop="fingerSugarType">
          <el-radio-group v-model="form.fingerSugarType">
            <el-radio :label="1">空腹血糖</el-radio>
            <el-radio :label="2">随机血糖</el-radio>
            <el-radio :label="3">餐后2h血糖</el-radio>
            <el-radio :label="4">既往空腹血糖</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item :label="getLabel()" prop="fingerSugarValue">
          <custom-input-number v-model="form.fingerSugarValue" style="width: 30%">
            <template #append>mmol/L</template>
          </custom-input-number>
        </el-form-item>
      </el-form>
    </div>
    <div class="fingertip-blood-glucose-table">
      <static-table :columns="fingertipBloodGlucose.columns" :table-data="fingertipBloodGlucose.staticTableData" />
    </div>
  </div>
</template>

<script>
import { fingertipBloodGlucose } from './staticTableData'
import StaticTable from '@/components/staticTable'

export default {
  name: 'FingertipBloodGlucose',
  components: {
    StaticTable
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        fingerSugarType: '',
        fingerSugarValue: ''
      },
      rules: {
        fingerSugarType: [{ required: true, message: '请选择血糖类型' }],
        fingerSugarValue: [{ required: true, message: '请输入空腹血糖' }]
      },
      fingertipBloodGlucose
    }
  },
  methods: {
    initData(data) {
      this.form = {
        fingerSugarType: data.fingerSugarType,
        fingerSugarValue: data.fingerSugarValue,
        id: data.id
      }
    },

    getLabel() {
      switch (this.form.fingerSugarType) {
        case 1:
          return '空腹血糖：'
        case 2:
          return '随机血糖：'
        case 3:
          return '餐后2h血糖：'
        case 4:
          return '既往空腹血糖：'
        default:
          return '空腹血糖：'
      }
    },

    async handleSave() {
      const result = {
        name: `${this.itemTemp.label}`,
        success: false,
        data: {
          fingerSugarType: this.form.fingerSugarType,
          fingerSugarValue: this.form.fingerSugarValue,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    }
  }
}
</script>

<style lang="scss" scoped></style>
