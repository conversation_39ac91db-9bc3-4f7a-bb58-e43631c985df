<template>
  <div class="base-table">
    <el-table
      ref="baseTable"
      v-loading="loading"
      :data="tableData"
      :border="border"
      :stripe="stripe"
      :height="height"
      :max-height="maxHeight"
      :row-key="rowKey"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <template v-for="(column, index) in columns">
        <!-- 序号 -->
        <el-table-column
          v-if="column.type === 'index'"
          :key="`index-${index}`"
          type="index"
          :width="column.width"
          :align="column.align || 'center'"
          :fixed="column.fixed"
        />
        <!-- 选择 -->
        <el-table-column
          v-else-if="column.type === 'selection'"
          :key="`selection-${index}`"
          type="selection"
          :width="column.width"
          :align="column.align || 'center'"
          :fixed="column.fixed"
        />
        <!-- 判断是否使用插槽 -->
        <el-table-column
          v-else-if="!column.slot"
          :key="`column-${index}`"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :sortable="column.sortable"
          :show-overflow-tooltip="column.showOverflowTooltip"
          :align="column.align || 'center'"
          :fixed="column.fixed"
        />
        <el-table-column
          v-else
          :key="`slot-${index}`"
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :min-width="column.minWidth"
          :sortable="column.sortable"
          :show-overflow-tooltip="column.showOverflowTooltip"
          :align="column.align || 'center'"
          :fixed="column.fixed"
        >
          <template #default="scope">
            <slot :name="column.slot" v-bind="scope" />
          </template>
        </el-table-column>
      </template>
    </el-table>

    <div v-if="showPagination" class="pagination-container">
      <el-pagination
        :current-page="pageInfo.pageNo"
        :page-sizes="pageSizes"
        :page-size="pageInfo.pageSize"
        :layout="paginationLayout"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'BaseTable',
  props: {
    // 表格数据
    tableData: {
      type: Array,
      default: () => []
    },
    // 表格列
    columns: {
      type: Array,
      default: () => []
    },
    // 是否显示边框
    border: {
      type: Boolean,
      default: true
    },
    // 是否显示斑马纹
    stripe: {
      type: Boolean,
      default: true
    },
    // 表格高度
    height: {
      type: [String, Number],
      default: null
    },
    // 表格最大高度
    maxHeight: {
      type: [String, Number],
      default: null
    },
    // 行数据的 Key
    rowKey: {
      type: String,
      default: 'id'
    },
    // 是否显示分页
    showPagination: {
      type: Boolean,
      default: true
    },
    // 分页布局
    paginationLayout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    // 分页信息
    pageInfo: {
      type: Object,
      default: () => ({
        pageNo: 1,
        pageSize: 20
      })
    },
    // 每页显示个数选择器的选项设置
    pageSizes: {
      type: Array,
      default: () => [10, 20, 30, 50]
    },
    // 总条数
    total: {
      type: Number,
      default: 0
    },
    // 加载状态
    loading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // 选择项发生变化
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection)
    },
    // 排序发生变化
    handleSortChange(sort) {
      this.$emit('sort-change', sort)
    },
    // 每页条数改变
    handleSizeChange(val) {
      this.pageInfo.pageSize = val
      this.$emit('pagination-change', {
        pageNo: this.pageInfo.pageNo,
        pageSize: val
      })
    },
    // 当前页改变
    handleCurrentChange(val) {
      this.pageInfo.pageNo = val
      this.$emit('pagination-change', {
        pageNo: val,
        pageSize: this.pageInfo.pageSize
      })
    },
    // 清空选择
    clearSelection() {
      this.$refs.baseTable.clearSelection()
    }
  }
}
</script>

<style lang="scss" scoped>
.base-table {
  height: 100%;
  // overflow: auto;
  ::v-deep thead {
    height: 35px;
  }
  .pagination-container {
    margin-top: 15px;
    text-align: right;
    ::v-deep .number {
      margin: 0 5px;
    }
  }
}
</style>
