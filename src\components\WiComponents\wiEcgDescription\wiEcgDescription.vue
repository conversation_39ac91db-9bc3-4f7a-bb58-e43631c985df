<template>
  <div class="ecgDescriptionContainer">
    <el-descriptions direction="vertical" :column="8" border :class="{ 'preview-sty': !previewShow }">
      <el-descriptions-item label="HR">
        <span :class="{ fadeItem: !isValue(ecgDescriptionData.hr) }">
          {{ ecgDescriptionData.hr | transEmptyToH }}
        </span>
        <span class="unitBox"> bpm</span>
      </el-descriptions-item>
      <el-descriptions-item label="PR">
        <span :class="{ fadeItem: !isValue(ecgDescriptionData.pr) }">
          {{ ecgDescriptionData.pr | transEmptyToH }}
        </span>
        <span class="unitBox"> ms</span>
      </el-descriptions-item>
      <el-descriptions-item label="RV5">
        <span :class="{ fadeItem: !isValue(ecgDescriptionData.rv5) }">
          {{ ecgDescriptionData.rv5 | transEmptyToH }}
        </span>
        <span class="unitBox"> mv</span>
      </el-descriptions-item>
      <el-descriptions-item label="SV1">
        <span :class="{ fadeItem: !isValue(ecgDescriptionData.sv1) }">
          {{ ecgDescriptionData.sv1 | transEmptyToH }}
        </span>
        <span class="unitBox"> mv</span>
      </el-descriptions-item>
      <el-descriptions-item label="QRS">
        <span :class="{ fadeItem: !isValue(ecgDescriptionData.qrs) }">
          {{ ecgDescriptionData.qrs | transEmptyToH }}
        </span>
        <span class="unitBox"> ms</span>
      </el-descriptions-item>
      <el-descriptions-item label="QT/QTC">
        <span :class="{ fadeItem: !isValue(ecgDescriptionData.qt) }">
          {{ ecgDescriptionData.qt | transEmptyToH }} / {{ ecgDescriptionData.qtc | transEmptyToH }}
        </span>
        <span class="unitBox"> ms</span>
      </el-descriptions-item>
      <el-descriptions-item label="P/QRS/T">
        <span :class="{ fadeItem: !isValue(ecgDescriptionData.p) }">
          {{ ecgDescriptionData.p | transEmptyToH }} / {{ ecgDescriptionData.qrs | transEmptyToH }} /
          {{ ecgDescriptionData.t | transEmptyToH }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item v-if="previewShow" label="危急值">
        <span :class="{ fadeItem: !isValue(ecgDescriptionData.dangerNumber) }">
          {{ ecgDescriptionData.dangerNumber | transEmptyToH }}
        </span>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import { isValue } from '@/utils'

export default {
  name: 'WiEcgDescription',
  props: {
    ecgDescriptionData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    previewShow: {
      type: Boolean,
      default: () => {
        return true
      }
    }
  },
  methods: {
    isValue
  }
}
</script>

<style lang="scss" scoped>
.ecgDescriptionContainer {
  ::v-deep .el-descriptions {
    border-radius: 0.625vw;
    border: 1px solid #dedede;
    overflow: hidden;

    .el-descriptions-row {
      .el-descriptions-item__cell {
        border-color: #dedede;
        width: calc(100% / 8);
        &:first-child {
          border-left: none;
        }
        &:last-child {
          border-right: none;
        }
        &.el-descriptions-item__label {
          border-top: none;
          background-color: #f8f8f8;
          padding: 0 1.56vw;
          height: 2.3vw;
          line-height: 2.3vw;
          font-size: 0.73vw;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;

          &:first-child {
            border-radius: 0.625vw 0 0 0;
          }
          &:last-child {
            border-radius: 0 0.625vw 0 0;
          }
        }
        &.el-descriptions-item__content {
          border-bottom: none;
          padding: 0 1.56vw;
          font-size: 0.83vw;
          font-family: PingFangSC-Medium, PingFang SC;
          height: 2.3vw;
          line-height: 2.3vw;
          font-weight: 600;
          color: #222222;
          .fadeItem {
            color: #999;
          }
          &:first-child {
            border-radius: 0 0 0 0.625vw;
          }
          &:last-child {
            border-radius: 0 0 0.625vw 0;
          }

          .unitBox {
            font-size: 0.63vw;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #999999;
          }
        }
      }
    }
  }
}
.preview-sty {
  ::v-deep .el-descriptions-item__label {
    background-color: #dedede !important;
  }
  ::v-deep &.el-descriptions .el-descriptions-row .el-descriptions-item__cell {
    border: 4px solid #fff;
    border-color: #fff;
  }
}
</style>
