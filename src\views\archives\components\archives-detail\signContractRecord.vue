<!-- 签约记录 -->
<template>
  <div class="sign-contract-record">
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      row-key="id"
      :columns="columns"
      :show-pagination="showPagination"
    >
      <template #signStatus="{ row }">
        <el-tag v-if="row.signStatus === 1" type="success">待生效</el-tag>
        <el-tag v-if="row.signStatus === 3" type="warning">履约中</el-tag>
        <el-tag v-if="row.signStatus === 5" type="info">即将到期</el-tag>
        <el-tag v-if="row.signStatus === 7" type="danger">已到期</el-tag>
        <el-tag v-if="row.signStatus === 9" type="info">已解约</el-tag>
      </template>
      <template #operate="{ row }">
        <el-button
          v-if="row.signStatus === 3 || row.signStatus === 5"
          type="text"
          @click="handleRenewal(row)"
        >履约</el-button>
      </template>
    </base-table>

    <Performance ref="performance" />
  </div>
</template>

<script>
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import { getUserDbSignContractRecord } from '@/api/archives'
import Performance from '@/views/servicePackageManage/individualSigning/component/performance.vue'

export default {
  name: 'SignContractRecord',
  components: {
    BaseTable,
    Performance
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        patientId: this.$route.query.id
      },
      columns: [
        { label: '家医团队', prop: 'teamName' },
        { label: '家庭医生', prop: 'doctorName' },
        { label: '签约服务包', prop: 'spNameStr', width: 200, showOverflowTooltip: true },
        { label: '签约金额', prop: 'selfAmt' },
        { label: '签约开始日期', prop: 'cycleStartDate' },
        { label: '签约结束日期', prop: 'cycleEndDate' },
        { label: '状态', prop: 'signStatus', slot: 'signStatus' },
        { label: '操作', prop: 'operate', slot: 'operate' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      return await getUserDbSignContractRecord(params)
    },
    // 履约
    handleRenewal(row) {
      this.$refs.performance.getIndividualSigningDetail(row.id)
    }
  }
}
</script>
