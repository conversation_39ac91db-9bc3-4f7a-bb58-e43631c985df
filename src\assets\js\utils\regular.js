// 正则
// 数组 - 身份证各位权重
const ID_CARD_WEIGHTS = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
// 字符串 - 身份证可包含字符
const IC_CARD_CHARS = '10X98765432'
// 正则 - 手机号正则
const REGEX_MOBILE = /^1[3456789]\d{9}$/
// 正则 - 身份证号正则
const REGEX_IDCARD = /(^\d{15}$)|(^\d{17}(\d|X|x)$)/

// 枚举 - 身份证城市
export const ID_CARD_CITIES = {
  11: '北京',
  12: '天津',
  13: '河北',
  14: '山西',
  15: '内蒙古',
  21: '辽宁',
  22: '吉林',
  23: '黑龙江',
  31: '上海',
  32: '江苏',
  33: '浙江',
  34: '安徽',
  35: '福建',
  36: '江西',
  37: '山东',
  41: '河南',
  42: '湖北',
  43: '湖南',
  44: '广东',
  45: '广西',
  46: '海南',
  50: '重庆',
  51: '四川',
  52: '贵州',
  53: '云南',
  54: '西藏',
  61: '陕西',
  62: '甘肃',
  63: '青海',
  64: '宁夏',
  65: '新疆',
  71: '台湾',
  81: '香港',
  82: '澳门',
  83: '台湾',
  91: '国外'
}

/**
 * 从身份证获取出生年月日 如： 1995/10/24
 * @param {string} idStr 身份证号
 */
export function getYMDFromIdCard(idStr) {
  return `${idStr.substr(6, 4)}-${Number(idStr.substr(10, 2))}-${Number(idStr.substr(12, 2))}`.replace(/-/g, '/')
}
export const rules = {
  idCard: (rule, value, callback) => {
    if (!value) {
      return callback(new Error('身份证号不能为空'))
    }
    if (!REGEX_IDCARD.test(value)) {
      callback(new Error('身份证长度或格式错误'))
    }
    if (!ID_CARD_CITIES[parseInt(value.substr(0, 2))]) {
      callback(new Error('身份证地区非法'))
    }
    // 出生日期验证
    const sBirthday = getYMDFromIdCard(value)
    const d = new Date(sBirthday)
    if (sBirthday !== `${d.getFullYear()}/${d.getMonth() + 1}/${d.getDate()}`) {
      callback(new Error('身份证上的出生日期非法'))
    }

    // 身份证号码校验
    let sum = 0
    const weights = ID_CARD_WEIGHTS
    const codes = IC_CARD_CHARS
    for (let i = 0; i < value.length - 1; i++) {
      sum += value[i] * weights[i]
    }
    const last = codes[sum % 11] // 计算出来的最后一位身份证号码
    if (`${value[value.length - 1]}` !== `${last}`) {
      callback(new Error('输入的身份证号非法'))
    }
    callback()
  },
  phone: (rule, value, callback) => {
    if (value && !REGEX_MOBILE.test(value)) {
      callback(new Error('手机号格式不对'))
    }
    callback()
  }
}
