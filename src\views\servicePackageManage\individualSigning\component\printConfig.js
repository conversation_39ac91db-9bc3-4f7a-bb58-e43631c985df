/**
 * 设置表格边框样式
 * @param {HTMLElement} container - 包含表格的容器元素
 * @param {string} borderStyle - 边框样式，默认为1px实线黑色
 */
export const formatTableBorders = (container, borderStyle = '1px solid #000') => {
  if (!container) return

  const tables = container.querySelectorAll('table')
  if (!tables.length) return

  tables.forEach((table) => {
    // 设置表格属性
    table.setAttribute('border', '1')
    table.style.borderCollapse = 'collapse'

    // 设置单元格边框
    const cells = table.querySelectorAll('th, td')
    cells.forEach((cell) => {
      cell.style.border = borderStyle
    })
  })
}

/**
 * 打印样式配置
 */
const printStyles = `
  <meta charset="UTF-8">
  <style>
    @page {
      size: A4 portrait;
      margin: 0;
    }
    @media print {
      /* 基础页面设置 */
      html, body {
        margin: 0 !important;
        padding: 0 !important;
        width: 100% !important;
        height: auto !important;
        background-color: white !important;
        font-size: 16px !important;
        font-family: "SimSun", "宋体", serif !important;
      }

      /* 打印容器样式 */
      #printContainer {
        position: static !important;
        left: 0 !important;
        visibility: visible !important;
        padding: 20mm !important;
        box-sizing: border-box !important;
        width: 210mm !important;
        min-height: 297mm !important;
      }

      /* 表格样式 */
      table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin: 10px 0 !important;
        border: 1px solid #000 !important;
        color: #000 !important;
      }

      table, th, td {
        border: 1px solid #000 !important;
        border-width: 1px !important;
        padding: 4px !important;
        color: #000 !important;
      }

      /* 强制显示所有表格边框 */
      table tr td {
        border: 1px solid #000 !important;
        border-style: solid !important;
        border-width: 1px !important;
        box-shadow: inset 0 0 0 1px #000 !important;
      }

      /* 表格内容对齐 */
      td {
        text-align: center !important;
      }

      td p {
        margin: 0 !important;
        text-align: center !important;
      }

      /* 文本和元素样式 */
      p {
        margin: 8px 0 !important;
      }

      strong {
        font-weight: bold !important;
      }

      img {
        max-width: 100px !important;
        height: auto !important;
      }
    }
  </style>
`

/**
 * 签约协议打印配置
 */
export const printConfig = {
  id: 'printContainer',
  popTitle: '签约协议',

  // 打印前回调：设置表格边框
  beforeOpenCallback() {
    const printContent = document.querySelector('#printContainer .print-content')
    if (printContent) {
      formatTableBorders(printContent)
    }
  },

  // 打印样式
  extraHead: printStyles
}
