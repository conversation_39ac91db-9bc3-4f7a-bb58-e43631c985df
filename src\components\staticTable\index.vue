<template>
  <el-table :data="tableData" border style="width: 100%">
    <el-table-column
      v-for="col in columns"
      :key="col.prop"
      :prop="col.prop"
      :label="col.label"
      :width="col.width"
      :align="col.align || 'center'"
    />
  </el-table>
</template>

<script>
export default {
  name: 'StaticTable',
  props: {
    columns: {
      type: Array,
      required: true
    },
    tableData: {
      type: Array,
      required: true
    }
  }
}
</script>
