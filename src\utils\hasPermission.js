const hasPermission = {
  install(Vue, options) {
    Vue.directive('has', {
      inserted: (el, binding, vnode) => {
        permissionFilter(el, binding, vnode)
      }
    })
  }
}

const permissionFilter = (el, binding, vnode) => {
  const { value } = binding
  const store = vnode.context.$store
  const permissionList = store.state.user.buttonPermissions
  if (!permissionList.includes(value)) {
    el.parentNode && el.parentNode.removeChild(el)
  }
}

export default hasPermission
