import Vue from 'vue'
import Router from 'vue-router'

/* Layout */
import Layout from '@/layout'

Vue.use(Router)

export const constantRoutes = [
  {
    path: '/ecg',
    component: Layout,
    hidden: true
  },

  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },
  // {
  //   path: '/home',
  //   name: 'Home',
  //   component: () => import('@/views/home/<USER>'),
  //   hidden: true
  // },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error-page/401'),
    hidden: true
  },

  {
    path: '/userInfo',
    component: Layout,
    meta: {
      title: '个人信息'
    },
    hidden: true,
    children: [
      {
        meta: {
          title: '信息维护'
        },
        path: '/userInfo/index',
        component: () => import('@/views/userInfo/index.vue'),
        hidden: true
      }
    ]
  }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = []
const createRouter = () =>
  new Router({
    mode: 'history', // require service support
    // mode:'hash',
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes
  })

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router

router.beforeEach((to, from, next) => {
  // 将from注册到vuex
  router.app.$store.commit('app/SET_FROM_PATH', from.path)
  next()
})
