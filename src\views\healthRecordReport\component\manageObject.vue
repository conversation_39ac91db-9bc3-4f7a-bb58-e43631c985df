<!-- 管理目标 -->
<template>
  <div class="manage-object">
    <h2 style="color: #4bc0f1">管理目标</h2>
    <div class="letter-content">
      <div class="greeting">亲爱的 {{ patientInfo.name }} {{ patientInfo.sex === 1 ? '先生' : '女士' }}：</div>
      <div class="paragraph">您好，作为您的慢病管理伙伴，我们致力于为您提供全方为、个性化的健康管理服务。</div>
      <div class="paragraph">
        慢病的康复需要长期的坚持和努力，除了在医院的治疗之外，您在日常生活中积极的健康管理也是十分重要的环节，这对血压、血糖的控制、并发症预防很有帮助。
      </div>
      <div class="paragraph">
        我们根据您的健康状况量身定制了个性化的体征检测、运动、饮食、用药方案，并设置了阶段性的康复目标，让我们共同努力，维护您的健康。
      </div>
    </div>
    <div class="manage-object-table">
      <el-table :data="tableData" style="width: 100%" border :span-method="mergeClassificationCells">
        <el-table-column align="center" label="分类" prop="classification" />
        <el-table-column align="center" label="检查项目" prop="itemName" width="200px" />
        <el-table-column align="center" label="目前病情" prop="itemValue">
          <template #default="{ row }">
            <span v-if="row.itemValue">{{ row.itemValue }}</span>
            <span v-else>{{ '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="控制目标" prop="itemRange">
          <template #default="{ row }">
            <span v-if="row.itemRange">{{ row.itemRange }}</span>
            <span v-else>{{ '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="manage-object-effect-evaluation">
      <h2 style="color: #4bc0f1">管理效果评估：</h2>
      <div class="manage-object-effect-evaluation-item">
        <el-tag v-if="reportInfo.tnbResult === 1" type="danger">糖尿病未达标</el-tag>
        <el-tag v-else-if="reportInfo.tnbResult === 0" type="success">糖尿病已达标</el-tag>
        <el-tag v-else type="info">糖尿病结果未生成</el-tag>
        <el-tag v-if="reportInfo.gxzResult === 1" type="danger">高血脂未达标</el-tag>
        <el-tag v-else-if="reportInfo.gxzResult === 0" type="success">高血脂已达标</el-tag>
        <el-tag v-else type="info">高血脂结果未生成</el-tag>
        <el-tag v-if="reportInfo.gxyResult === 1" type="danger">高血压未达标</el-tag>
        <el-tag v-else-if="reportInfo.gxyResult === 0" type="success">高血压已达标</el-tag>
        <el-tag v-else type="info">高血压结果未生成</el-tag>
        <el-tag v-if="reportInfo.fcResult === 1" type="danger">房颤未达标</el-tag>
        <el-tag v-else-if="reportInfo.fcResult === 0" type="success">房颤已达标</el-tag>
        <el-tag v-else type="info">房颤结果未生成</el-tag>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'ManageObject',
  props: {
    patientInfo: {
      type: Object,
      default: () => {}
    },
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    tableData() {
      const bodyInfoListTemp = (this.reportInfo.bodyInfoList || []).map((item) => ({
        ...item,
        classification: '一般情况'
      }))

      const sugarListTemp = (this.reportInfo.sugarList || []).map((item) => ({
        ...item,
        classification: '血糖'
      }))

      const pressureListTemp = (this.reportInfo.pressureList || []).map((item) => ({
        ...item,
        classification: '血压'
      }))

      const fatListTemp = (this.reportInfo.fatList || []).map((item) => ({
        ...item,
        classification: '血脂'
      }))

      const heartRateListTemp = (this.reportInfo.heartRateList || []).map((item) => ({
        ...item,
        classification: '心率'
      }))

      return [...bodyInfoListTemp, ...sugarListTemp, ...pressureListTemp, ...fatListTemp, ...heartRateListTemp]
    }
  },
  methods: {
    mergeClassificationCells({ row, column, rowIndex, columnIndex }) {
      // 只对分类列（第一列）进行合并处理
      if (columnIndex === 0) {
        // 计算当前分类在表格中的起始位置和数量
        const currentClassification = row.classification
        const { tableData } = this

        // 找到当前分类第一次出现的位置
        let firstIndex = -1
        let count = 0

        for (let i = 0; i < tableData.length; i++) {
          if (tableData[i].classification === currentClassification) {
            if (firstIndex === -1) {
              firstIndex = i
            }
            count++
          }
        }

        // 如果当前行是该分类的第一行，返回合并的行数
        if (rowIndex === firstIndex) {
          return [count, 1]
        } else {
          // 如果不是第一行，返回[0, 0]表示被合并
          return [0, 0]
        }
      }

      // 其他列正常显示
      return [1, 1]
    }
  }
}
</script>
<style lang="scss" scoped>
.manage-object {
  padding: 10px;

  .letter-content {
    margin-top: 18px;
    line-height: 1.8;

    .greeting {
      margin-bottom: 8px;
      font-weight: 500;
      font-size: 20px;
    }

    .paragraph {
      margin-bottom: 3px;
      line-height: 24px;
      font-size: 16px;
    }
  }

  .manage-object-table {
    margin-top: 16px;
  }

  .manage-object-effect-evaluation {
    margin-top: 16px;
    width: 100%;
    .manage-object-effect-evaluation-item {
      display: flex;
      align-items: center;
      gap: 20px;
      margin-top: 10px;
    }
  }
}
</style>
