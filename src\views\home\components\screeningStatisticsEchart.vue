<template>
  <div ref="chart" style="width: 100%; height: 250px" />
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'ScreeningStatisticsEchart',
  props: {
    chartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      handler() {
        this.initChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    initChart() {
      if (!this.chartData || this.chartData.length === 0) return

      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chart)
      }

      const xAxisData = this.chartData.map((item) => item.diseaseName)
      const seriesData = this.chartData.map((item) => item.count)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData,
          axisTick: {
            alignWithLabel: true
          }
        },
        yAxis: {
          type: 'value',
          name: '单位：人'
        },
        series: [
          {
            name: '筛查统计',
            type: 'bar',
            barWidth: '10%',
            data: seriesData,
            itemStyle: {
              color: '#409EFF'
            }
          }
        ]
      }

      this.chart.setOption(option)
    }
  }
}
</script>
