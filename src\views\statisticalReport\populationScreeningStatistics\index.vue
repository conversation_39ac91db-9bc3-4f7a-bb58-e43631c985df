<!-- 人群普筛统计 -->
<template>
  <div class="population-screening-statistics">
    <el-card class="population-screening-statistics-search">
      <SearchForm
        ref="searchForm"
        :query-params="queryParams"
        :query-criteria="['timeRange', 'departCode']"
        @search="handleSearch"
        @reset="handleReset"
      />
    </el-card>

    <el-card class="population-screening-statistics-table">
      <BaseTable
        ref="baseTable"
        :columns="columns"
        :loading="loading"
        :table-data="tableData"
        :show-pagination="showPagination"
      />
    </el-card>
  </div>
</template>

<script>
import { getPopulationScreeningStatistics } from '@/api/statisticalReport'
import { localCache } from '@/utils/cache'
import SearchForm from '../component/searchForm.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'

export default {
  name: 'PopulationScreeningStatistics',
  components: {
    SearchForm,
    BaseTable
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        timeRange: [],
        departCode: localCache.getCache('userInfo').departCode || ''
      },
      columns: [
        { prop: 'departName', label: '机构名称' },
        { prop: 'count', label: '普筛人数' },
        { prop: 'tnbCount', label: '普筛糖尿病人数' },
        { prop: 'gxyCount', label: '普筛高血压人数' },
        { prop: 'copdCount', label: '普筛慢阻肺人数' },
        { prop: 'fcCount', label: '普筛房颤人数' },
        { prop: 'recepCount', label: '接诊人数' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }
      return await getPopulationScreeningStatistics(queryParams)
    },

    handleReset() {
      this.queryParams.timeRange = []
      this.queryParams.departCode = localCache.getCache('userInfo').departCode || ''
      this.fetchData()
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/form-overrides.scss';

.population-screening-statistics {
  padding: 16px;
  .population-screening-statistics-search {
    margin-bottom: 16px;
  }
}
</style>
