import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/cspapi/gateway/loginByPhone',
    method: 'post',
    data
  })
}

export function getInfo(id) {
  return request({
    url: `/cspapi/backend/user/new/${id}`,
    method: 'get'
  })
}

export function logout() {
  return request({
    url: '/cspapi/gateway/logout',
    method: 'post',
    data: { deviceName: 'chrome,', platform: 'pc' }
  })
}

// 递归查询用户所具有的左侧菜单列表
export function menuTree(id) {
  return request({
    url: `/cspapi/backend/menu/tree/${id}`,
    method: 'get'
  })
}

// pc微信扫码登录获取QRcode
export function getWxQrcodeApi() {
  return request({
    url: `/api/gateway/getWxQrcode`,
    method: 'get'
  })
}
// pc微信扫码登录获取QRcode 状态
export function getWxQrcodeStatusApi(qrcode) {
  return request({
    url: `/api/gateway/getWxQrcodeStatus`,
    method: 'get',
    params: { qrcode }
  })
}
// pc微信扫码登录获取QRcode 状态
export function getLoginPcByWxApi(data) {
  return request({
    url: `/api/gateway/loginPcByWx`,
    method: 'post',
    data
  })
}

// 30.11.1 居民健康数据库
export function getDieDataApi(data) {
  return request({
    url: `/cspapi/backend/base/user/page`,
    method: 'post',
    data
  })
}

export function getRepeatUserDataApi(data) {
  return request({
    url: `/cspapi/backend/phr/userDupDB`,
    method: 'post',
    data
  })
}

export function listAllByPatientId(params) {
  return request({
    url: `/cspapi/backend/visitCheck/visit/listAllByPatientId`,
    method: 'get',
    params
  })
}

// 短信登录获取验证码
export function getSmsCode(params) {
  return request({
    url: `/cspapi/gateway/getSmsCode`,
    method: 'get',
    params
  })
}

// 短信登陆
export function loginBySms(data) {
  return request({
    url: `/cspapi/gateway/loginBySms`,
    method: 'post',
    data
  })
}
