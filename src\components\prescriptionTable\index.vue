<template>
  <el-table :data="localTableData" border style="width: 80%">
    <el-table-column prop="medicineName" label="药品名称" align="center">
      <template slot-scope="scope">
        <el-select
          v-model="scope.row.medicineName"
          allow-create
          filterable
          clearable
          placeholder="请选择"
          style="width: 100%"
          @change="handlemedicalChange(scope.row)"
        >
          <el-option v-for="opt in drugList" :key="opt.id" :label="opt.medicalName" :value="opt.medicalName" />
        </el-select>
      </template>
    </el-table-column>

    <el-table-column prop="dayTimes" label="每日次数" align="center">
      <template slot-scope="scope">
        <el-input-number v-model="scope.row.dayTimes" placeholder="请输入" style="width: 100%" @input="updateParent" />
      </template>
    </el-table-column>

    <el-table-column prop="dayCount" label="每次用量" align="center">
      <template slot-scope="scope">
        <el-input-number v-model="scope.row.dayCount" placeholder="请输入" style="width: 100%" @input="updateParent" />
      </template>
    </el-table-column>

    <el-table-column prop="unit" label="计量单位" align="center">
      <template slot-scope="scope">
        <el-input v-model="scope.row.unit" placeholder="请输入" @input="updateParent" />
      </template>
    </el-table-column>

    <el-table-column prop="remark" label="备注" align="center">
      <template slot-scope="scope">
        <el-input v-model="scope.row.remark" placeholder="请输入" @input="updateParent" />
      </template>
    </el-table-column>

    <el-table-column label="操作" align="center" width="100">
      <template slot-scope="scope">
        <span v-if="scope.$index === localTableData.length - 1">
          <i class="el-icon-plus" style="color: #41a1d4; cursor: pointer" @click="handleAdd" />
        </span>
        <span>
          <i
            class="el-icon-delete"
            style="color: red; margin-left: 16px; cursor: pointer"
            @click="handleDelete(scope.$index)"
          />
        </span>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { mapState } from 'vuex'
import { cloneDeep } from 'lodash'

const defaultRow = {
  medicineName: '',
  dayTimes: undefined,
  dayCount: undefined,
  unit: '',
  remark: ''
}

export default {
  name: 'PrescriptionTable',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      localTableData: []
    }
  },
  computed: {
    ...mapState('drugManagement', ['drugList'])
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        // 监听外部传入的值，确保数据同步
        this.localTableData =
          Array.isArray(newVal) && newVal.length > 0
            ? cloneDeep(newVal).map((item) => ({
              ...item,
              dayTimes: item.dayTimes || undefined,
              dayCount: item.dayCount || undefined
            }))
            : [{ ...defaultRow }]
      }
    }
  },
  methods: {
    handlemedicalChange(row) {
      const medical = this.drugList.find((item) => item.medicalName === row.medicineName)
      if (medical) {
        row.unit = medical.medicalUnit
        this.updateParent()
      }
    },
    handleAdd() {
      this.localTableData.push({ ...defaultRow })
      this.updateParent()
    },
    handleDelete(index) {
      this.localTableData.splice(index, 1)
      if (this.localTableData.length === 0) {
        this.localTableData = [{ ...defaultRow }]
      }
      this.updateParent()
    },
    updateParent() {
      // 确保数据是纯对象数组
      const dataToEmit = this.localTableData.map((item) => ({
        medicineName: item.medicineName || '',
        dayTimes: item.dayTimes || undefined,
        dayCount: item.dayCount || undefined,
        unit: item.unit || '',
        remark: item.remark || ''
      }))
      this.$emit('input', dataToEmit)
    }
  }
}
</script>

<style scoped lang="scss">
.el-table {
  width: 100%;
  ::v-deep .el-input-number .el-input__inner {
    text-align: left;
    padding: 10px;
  }
}
</style>
