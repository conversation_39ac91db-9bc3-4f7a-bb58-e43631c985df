<template>
  <div class="select-table-container">
    <el-select
      ref="selectRef"
      v-model="selectedValue"
      filterable
      remote
      :remote-method="remoteMethod"
      :placeholder="placeholder"
      :disabled="disabled"
      :clearable="clearable"
      :loading="loading"
      style="width: 100%"
      @visible-change="handleVisibleChange"
      @change="handleChange"
    >
      <!-- 没有数据时显示的内容 -->
      <template v-if="options.length === 0 && !loading">
        <el-option value="" disabled label="无数据" />
      </template>

      <!-- 选项列表 -->
      <template v-else>
        <el-option
          v-for="item in options"
          :key="item[optionItem.value]"
          :label="item[optionItem.label]"
          :value="item[optionItem.value]"
        >
          <slot name="option" :item="item">
            {{ item[optionItem.label] }}
            <!-- 身份证后四位 -->
            {{ item.originalIdCard ? `（${item.originalIdCard.slice(-4)}）` : '' }}
          </slot>
        </el-option>
      </template>

      <!-- 分页控件 -->
      <el-option value="" disabled class="pagination-option">
        <div class="pagination-container">
          <el-pagination
            v-if="total > pageSize"
            background
            layout="prev, pager, next"
            :current-page.sync="pageNo"
            :page-size="pageSize"
            :total="total"
            @current-change="handlePageChange"
          />
          <div v-else-if="options.length > 0" class="pagination-info">共 {{ total }} 条记录</div>
        </div>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import request from '@/utils/request'
import { throttle } from 'lodash'

export default {
  name: 'SelectTable',
  props: {
    value: {
      type: [String, Number, Object],
      default: ''
    },
    url: {
      type: String,
      required: true
    },
    method: {
      type: String,
      default: 'get'
    },
    params: {
      type: Object,
      default: () => ({})
    },
    optionItem: {
      type: Object,
      default: () => ({
        label: 'label',
        value: 'value'
      })
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    clearable: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    pageSize: {
      type: Number,
      default: 10
    },
    responseKeys: {
      type: Object,
      default: () => ({
        list: 'list',
        total: 'total'
      })
    },
    pageKeys: {
      type: Object,
      default: () => ({
        pageNo: 'pageNo',
        pageSize: 'pageSize'
      })
    },
    searchKey: {
      type: String,
      default: 'query'
    }
  },
  data() {
    return {
      selectedValue: this.value,
      options: [],
      pageNo: 1,
      total: 0,
      query: '',
      loading: false,
      initialLoaded: false,
      throttledLoadData: null
    }
  },
  watch: {
    value: {
      handler(val) {
        this.selectedValue = val
      },
      immediate: true
    },
    params: {
      handler() {
        if (this.initialLoaded) {
          this.reset()
          this.throttledLoadData()
        }
      },
      deep: true
    }
  },
  created() {
    this.throttledLoadData = throttle(this.loadData, 800)
  },
  beforeDestroy() {
    this.throttledLoadData.cancel && this.throttledLoadData.cancel()
  },
  methods: {
    reset() {
      this.pageNo = 1
    },

    remoteMethod(query) {
      this.query = query
      this.reset()
      this.throttledLoadData()
    },

    handleVisibleChange(visible) {
      if (visible && !this.initialLoaded) {
        this.throttledLoadData()
        this.initialLoaded = true
      }
    },

    handlePageChange(page) {
      this.pageNo = page
      this.throttledLoadData()
    },

    handleChange(val) {
      this.$emit('input', val)
      this.$emit('change', val)

      const selectedItem = this.options.find((item) => item[this.optionItem.value] === val)
      if (selectedItem) {
        this.$emit('select', selectedItem)
      }
    },

    getNestedValue(obj, path) {
      if (!obj || !path) return null
      return path.split('.').reduce((prev, curr) => {
        return prev && prev[curr] !== undefined ? prev[curr] : null
      }, obj)
    },

    searchByValue(keyword) {
      this.query = keyword
      this.pageNo = 1
      this.$nextTick(() => {
        this.$refs.selectRef && this.$refs.selectRef.focus()
      })
    },

    loadData() {
      const params = {
        ...this.params,
        [this.pageKeys.pageNo]: this.pageNo,
        [this.pageKeys.pageSize]: this.pageSize
      }

      if (this.query) {
        params[this.searchKey] = this.query
      }

      this.loading = true

      request({
        url: this.url,
        method: this.method,
        data: this.method.toLowerCase() === 'post' ? params : {}
      })
        .then((response) => {
          const data = response.data || {}
          this.options = this.getNestedValue(data, this.responseKeys.list) || []
          console.log('this.options', this.options)
          this.total = this.getNestedValue(data, this.responseKeys.total) || 0

          console.log('加载数据成功:', {
            选项: this.options,
            总数: this.total,
            响应数据: data
          })
        })
        .catch((error) => {
          console.error('数据加载失败:', error)
          this.$message.error('数据加载失败，请重试')
          this.options = []
          this.total = 0
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style scoped>
.select-table-container {
  width: 100%;
}

.pagination-container {
  text-align: center;
  padding: 10px 0;
  border-top: 1px solid #e4e7ed;
  background-color: #f5f7fa;
  margin-top: 4px;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.05);
}

.pagination-option {
  height: auto !important;
  padding: 0 !important;
  margin-top: 4px;
}

.pagination-info {
  color: #606266;
  font-size: 14px;
  padding: 8px 0;
  font-weight: 500;
}

.el-pagination {
  pointer-events: auto;
  display: flex;
  justify-content: center;
  font-weight: 500;
}

.el-pagination .btn-prev,
.el-pagination .btn-next,
.el-pagination .number {
  min-width: 34px;
  height: 34px;
  line-height: 34px;
  font-size: 14px;
}

.el-pagination .el-pager li.active {
  background-color: #409eff;
  color: white;
  font-weight: 600;
}

.el-pagination .el-pager li:hover:not(.active) {
  color: #409eff;
  font-weight: 500;
}

.pagination-container {
  border-radius: 0 0 4px 4px;
}

.pagination-container::before {
  content: '';
  display: block;
  width: 30px;
  height: 2px;
  background-color: #dcdfe6;
  margin: 0 auto 8px;
}

.el-select-dropdown__list {
  padding: 0 !important;
}
</style>
