import request from '@/utils/request'

// 分页查询服务包
export const getServicePackageList = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/servicePackage/page',
    method: 'post',
    data
  })
}

// 查询服务包详情
export const getServicePackageDetail = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/servicePackage/detail',
    method: 'post',
    data
  })
}

// 保存签约服务包
export const saveServicePackage = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/servicePackage/save',
    method: 'post',
    data
  })
}

// 删除服务包
export const deleteServicePackage = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/servicePackage/remove',
    method: 'post',
    data
  })
}

// 启用/禁用服务包
export const enableServicePackage = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/servicePackage/enabled',
    method: 'post',
    data
  })
}

// 分页查询服务项
export const getServiceItemList = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/serviceItem/page',
    method: 'post',
    data
  })
}

// 查询服务项详情
export const getServiceItemDetail = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/serviceItem/detail',
    method: 'post',
    data
  })
}

// 保存服务项
export const saveServiceItem = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/serviceItem/save',
    method: 'post',
    data
  })
}

// 删除服务项
export const deleteServiceItem = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/serviceItem/remove',
    method: 'post',
    data
  })
}

// 绑定服务包
export const bindServicePackage = (data) => {
  return request({
    url: '/cspapi/backend/familyDoctor/servicePackage/bind',
    method: 'post',
    data
  })
}
