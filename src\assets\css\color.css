body {
    /* 页面基础样式 */
    --noColor: rgba(0, 0, 0, 0);
    --color: #2A2D33;
    --colorSecond: #666666;
    --colorThird: #999999;
    --colorActive: #0A86C8;
    --colorNo:#B3B6BB;
    --colorBJActive: rgba(0,161,151,.3);
    --colorBJActive2: rgba(0, 161, 151, .1);
    --colorBJ: #F9FFFB;
    --bjColor: #fff;
    --colorError: #EB4F3E;
    --colorBJError: rgba(235,79,62,.3);
    --colorBJErrorRgb: #FBDCD8;
    --colorWarn: #FB961A;
    --colorBJWarn: rgba(251,150,26,.3);
    --colorBlue: #3A59F8;
    --colorBJBlue: rgba(58, 89, 248, .3);
    --colorPlaceholder: #c7c7c7;
    --colorGreenActive: #0A86C8;
    --colorGreenBJActive: rgba(56,186,132,.3);
    --colorHintBJColor:#ffe8ca;
    --colorHint:#FB961A;
    --colorBJshallowGreen: #f3fcff;
    /* placeholder字体颜色 */
    --placeholderColor: #919191;
    /* 搜索 */
    --searchBoxColor:rgba(0, 0, 0, .1);
    /*按钮*/
    --btnColor: #fff;
    --btnBorderColor: transparent;
    --btnBJColor: linear-gradient(130deg, #00B2DC 0%, #0A86C8 100%);
    --btnLineBJColor: #DAF3F2;
    --btnWhiteColor: #198BDE;
    --btnWhiteBorderColor: #F3F3F3;
    --btnWhiteBJColor: #F3F3F3;
    --btnGreensBJColor: #DAF3F2;
    --btnGreeyBJColor: #ffffff;
    --btnGreeyColor: #222222;
    --btnGreeyBorderColor: #DEDEDE;
    --btnListBJColor:#EBF7F5;
    --closeColor:#fff;
    --closeBJColor: rgba(0,0,0,0.5);
    --btnCancelColor: #222;
    --btnCancelBorderColor: #dedede;
    --btnCancelBJColor: #fff;
    --btnErrorBorderColor: red;
    --btnErrorBJColor: red;
    --btnRedBJColor: rgb(255, 159, 159);
    --btnRedColor: red;
    --btnRedBorderColor: red;

    --btnBackBJColor: transparent;
    --btnBackColor: #ADADAD;
    --btnBackBorderColor: #E0E2E6;

    --btnLinear:#fff;
    --btnBJLinear:linear-gradient(130deg, #00B2DC 0%, #0A86C8 100%);

    /* 字体图标 */
    --icoBorder: 1px solid #CED3DB;
    /* 面包屑 */
    --crumbColor: #3D404B;
    --crumbActiveColor: #2A2D33;
    --crumbBorderColor: rgba(159, 161, 174, .4);
    --crumbBJColor: rgba(255, 255, 255, .6);

    /* 标题 */
    --titleColor: #2A2D33;
    --titleIcoColor: #0A86C8;

    /* switch */
    --switchBJColor: #f3f3f3;
    --switchActiveBJColor: #FFDDDD;
    --switchActiveColor: #EB4F3E;
    /* radio */
    --radioBorderColor: #DEDEDE;
    --radioActiveColor: #0A86C8;
    --el-color-primary: var(--radioActiveColor);
    /* 输入框 */
    --inputColor: rgba(145, 145, 145, 1);
    --inputBJColor: rgba(26, 27, 29, 1);
    --inputBorderColor: rgba(26, 27, 29, 1);

    /* 滚动轴样式 */
    --scrollbarThumb: rgba(0,0, 0, .25);
    --scrollbarThumbHover: rgba(0, 0, 0, .35);


    /* header */
    --headerBJColor: linear-gradient(133deg, #42c9a3 0%, #00b2dd 100%);
    --headerColor: #ffffff;

    /* 左侧菜单 */
    --navBJColor:#F0F0F0;
    --navSecondBJColor: #F5F5F5;

    --cardBjColorF: #D1F2E3;
    --cardBjColorS:#D0E9FB;
    --cardBjColorT: #E5E8FE;
    --cardColorF: #4BB583;
    --cardColorS: #198BDE;
    --cardColorT: #3550DA;
    --navBJcolor-Green:#65CCCA;


    --lineBJColor:#FFFFFF;
    --lineBorderColor:#DEDEDE;
    --lineGreenColor:#0A86C8;
    /* 日历 */
    --calendarHeaderGJColor: #E5E8FE;
    --todayColor: #fff;

    /* tree树 */
    --treeTitleBJColor: #F3F3F3;
    --treeTitleBorderColor: #DEDEDE;
    --treeActiveBJColor: #D7F0EE;
    --treeTitleActiveBJColor: #DAF3F2;

    /* 表格 */
    --tableTitleBJColor: linear-gradient(180deg, #eee 0%, #f0f0f0 100%);
    --tableEvenTrBJColor: #F9FFFB;
    --el-border-color-lighter:#DCDFE6;
    /* 分页 */
    --paginationActiveColor: #fff;
    --paginationActiveBJColor: linear-gradient(135deg, #3FE1D2 0%, #01B2DD 100%);

    /* 弹窗 */
    --colorBJWhite: rgba(255,255,255,0.5);
    --colorBJBlock: rgba(0, 0, 0, 0.5);
    --colorBlock: #fff;
    --dialogTitleColor:#F6FDF8;

    /* 状态 */
    --typeColor:#fff;
    --typeBJColor:#38BA84;
    --typeBJ2Color: #FB961A;
    --typeBJ3Color: #EB4F3E;

    /* 外阴影 */
    --shadowColor:rgba(9,76,1,0.1);


    /* echarts */
    --optionColor: #A3A3AB;
    --optionNumberColor: #4A4D68;

    /* 上传的按钮 */
    --uploadBtnBorder: #0A86C8;
    --uploadBtnBJ: #F6FDF8;
    --uploadBtnColor: #BEC7C6;
    --uploadBtnBJColor: rgba(0,0,0,0.2);

    /* 富文本编辑器 */

    --editorTxtBJ:#F8F8F8;
}

body .navTyoeCssTop,body .navTyoeCssTopPop {
    /* 左侧菜单 */
    --navColor: #fff;
    --navPopBJColor: #fff;
    --navPopColor: #2A2D33;
    --navActiveColor: linear-gradient(134deg, #43C9A3 0%, #00B2DC 100%);
    --navActiveBJColor: rgba(0, 0, 0, 0.1);
}
