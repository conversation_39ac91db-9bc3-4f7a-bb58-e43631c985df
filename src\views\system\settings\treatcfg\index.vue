<template>
  <div class="treatcfg">
    <div class="title">
      就诊配置
      <el-button
        :loading="loading"
        type="primary"
        size="small"
        @click="saveBtn"
      >{{ '\u2003' }}保存{{ '\u2003' }}</el-button>
    </div>
    <p>
      自动结束就诊功能 {{ '\u3000' }}
      <el-switch v-model="cfgdata.isEnd" active-color="#0A86C8" inactive-color="#D7D7D7" />
    </p>
    <p>
      每日结束就诊时间设置 {{ '\u3000' }}
      <el-time-picker v-model="cfgdata.time" value-format="HH:mm" format="HH:mm" placeholder="请选择时间点" />
    </p>
    <div class="title">高频词汇阈值配置</div>
    <p>
      阈值 {{ '\u3000' }}
      <el-input-number v-model="cfgdata.foler1Num" size="small" />
      <span>患者主诉：</span>
      <el-select v-model="cfgdata.foler1" style="width: 500px" multiple placeholder="请选择">
        <el-option v-for="item in changyongyvList" :key="item.code" :label="item.value" :value="item.value" />
      </el-select>
    </p>
    <p>
      阈值 {{ '\u3000' }}
      <el-input-number v-model="cfgdata.changyongyvNum" size="small" />
      <span>诊断标签：</span>
      <el-select v-model="cfgdata.changyongyv" style="width: 500px" multiple placeholder="请选择">
        <el-option v-for="item in foler1List" :key="item.code" :label="item.value" :value="item.value" />
      </el-select>
    </p>
  </div>
</template>
<script>
import { getDictionaryValApi } from '@/api/dict'

export default {
  name: 'Treatcfg',
  data() {
    return {
      cfgdata: {
        isEnd: true,
        time: '00:00',
        changyongyvNum: 200,
        foler1: [
          '胸闷气短',
          '牙龈出血',
          '头晕恶心',
          '头昏恶心',
          '胸闷，呼吸急促',
          '心慌，心痛',
          '记忆力下降',
          '反映迟钝'
        ],
        foler1Num: 200,
        changyongyv: [
          '手脚麻木',
          '低血糖',
          '冠心病',
          '结核病',
          '高血压',
          '胃溃疡',
          '2型糖尿病',
          '房颤',
          '脑卒中',
          '肝炎'
        ]
      },
      foler1List: [],
      changyongyvList: [],
      loading: true
    }
  },
  created() {
    this.getList()
  },
  methods: {
    async getList() {
      const res = await getDictionaryValApi('foler1')
      if (res.code === 200) {
        this.foler1List = res.data
      }
      const ress = await getDictionaryValApi('changyongyv')
      if (ress.code === 200) {
        this.changyongyvList = ress.data
      }
      this.loading = false
    },
    saveBtn() {
      this.loading = true
      setTimeout(() => {
        this.loading = false
        this.$message.success('保存成功')
      }, 1000)
    }
  }
}
</script>
<style lang="scss" scoped>
.treatcfg {
  width: calc(100% - 60px);
  height: calc(100vh - 120px);
  border-radius: 15px;
  margin: 30px;
  background-color: #fff;
  padding: 30px 40px;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.8rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #222222;
  }
  p {
    padding: 0 10px;
    font-size: 0.8rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #222222;
    line-height: 22px;
    span {
      margin-left: 50px;
    }
  }
}
</style>
