<template>
  <section />
</template>

<script>
import Vue from 'vue'

// 假设 EventBus 已经在 main.js 中创建并导出
// import { EventBus } from '@/main.js'

export default {
  name: 'PrintModule',
  data() {
    return {
      pollingInterval: null,
      g_websocket: null, // WebSocket 实例
      ackJsonData: null, // 响应数据
      websocketConnectStatus: false, // WebSocket 连接状态
      initSdkStatus: false, // SDK 初始化状态
      deviceStatus: false, // 设备状态
      MessageList: {}, // 消息回调列表
      initSdkParam: {
        // 初始化数据
        fontDir: ''
      },
      width: 40,
      height: 20,
      rotate: 0,
      jsonObj: {
        printerImageProcessingInfo: {
          printQuantity: 1
        }
      },
      dataParams: {
        printFrom: {
          paperType: 1,
          density: 3,
          quantityCount: 1, // 打印份数
          printMode: 1,
          startJobExecuted: false
        },
        InitDrawingBoardParam: {
          width: 40,
          height: 20,
          rotate: 0,
          path: 'ZT001.ttf',
          verticalShift: 0,
          HorizontalShift: 0
        }
      },
      reconnectAttempts: 0, // 重连尝试次数
      maxReconnectAttempts: 5, // 最大重连次数
      reconnectDelay: 3000, // 重连间隔时间（毫秒）
      isDestroyed: false // 组件销毁标志
    }
  },
  activated() {
    // 组件挂载后初始化 WebSocket 服务
    this.isDestroyed = false
    this.getInstance()
  },
  deactivated() {
    // 在组件被 keep-alive 缓存时触发
    console.log('缓存时触发')
    this.destroyedFunc()
  },
  beforeDestroy() {
    this.destroyedFunc()
  },
  mounted() {
    // 组件挂载后初始化 WebSocket 服务
    this.getInstance()
  },
  methods: {
    destroyedFunc() {
      // 标记组件已销毁
      this.isDestroyed = true
      // 组件销毁时清除轮询，避免内存泄漏
      this.stopPolling()
      // 关闭 WebSocket 连接
      this.unInitPrintInstance()
    },
    // 轮询控制
    stopPolling() {
      if (this.pollingInterval) {
        clearInterval(this.pollingInterval)
        this.pollingInterval = null
      }
    },
    // 初始化 Nimbot 打印
    initNimbotPrint() {
      this.initSdk()
    },
    // 启动打印测试任务
    startPrintJobTest() {
      if (!this.initSdkStatus || !this.deviceStatus) {
        this.initNimbotPrint()
        setTimeout(() => {
          this.startPrintJobLine()
        }, 1000)
      } else {
        setTimeout(() => {
          this.startPrintJobLine()
        }, 500)
      }
    },
    // WebSocket 相关方法
    sendMsg(msg, callback) {
      console.log('sendMsg', msg.apiName)
      this.MessageList[msg.apiName] = callback

      const data = JSON.stringify(msg)
      const tryTimes = 10

      for (let i = 0; i < tryTimes; i++) {
        if (this.g_websocket.readyState === WebSocket.OPEN) { // OPEN 状态
          this.g_websocket.send(data)
          return
        }
      }
      console.warn(`WebSocket 未处于 OPEN 状态，无法发送消息: ${msg.apiName}`)
    },
    getInstance() {
      // 避免多次调用导致多个 WebSocket 实例和定时器
      if (this.g_websocket && (this.g_websocket.readyState === WebSocket.OPEN || this.g_websocket.readyState === WebSocket.CONNECTING)) {
        console.log('WebSocket 已经在连接或已连接状态。')
        return
      }

      if ('WebSocket' in window) {
        this.g_websocket = this.websocketLifePeriod()
      } else {
        console.log('不支持 WebSocket')
        this.g_websocket = null
        return false
      }

      // 清除之前的轮询，避免多重定时器
      this.stopPolling()

      // 保持连接
      this.pollingInterval = setInterval(() => {
        if (
          this.g_websocket.readyState === WebSocket.CLOSING ||
          this.g_websocket.readyState === WebSocket.CLOSED
        ) {
          if (!this.isDestroyed) { // 仅在组件未销毁时尝试重连
            console.log('WebSocket 连接已关闭，尝试重新连接...')
            this.getInstance()
          }
        }
      }, this.reconnectDelay)

      return true
    },
    websocketLifePeriod() {
      const websocket = new WebSocket('ws://127.0.0.1:37989')
      // websocket.binaryType = 'arraybuffer';
      websocket.onerror = (evt) => {
        console.error('WebSocket 发生错误:', evt)
        // 错误时触发 onclose 以便重连
        websocket.close()
      }

      websocket.binaryType = 'arraybuffer'

      websocket.onopen = this.connectCallback
      websocket.onclose = this.closeCallback
      websocket.onmessage = this.readCallback
      websocket.onerror = this.errorCallback

      return websocket
    },
    connectCallback(e) {
      console.log('WebSocket 连接成功。')
      this.ackJsonData = ''
      this.websocketConnectStatus = true
      this.reconnectAttempts = 0 // 重置重连次数
      this.initSdk()
    },
    closeCallback(e) {
      console.log(`WebSocket 连接关闭，代码: ${e.code}, 原因: ${e.reason}`)
      this.websocketConnectStatus = false
      this.ackJsonData = ''

      // 如果组件已销毁，不再尝试重连
      if (this.isDestroyed) {
        console.log('组件已销毁，停止重连。')
        return
      }

      // 如果关闭原因不是主动关闭，则尝试重连
      if (e.code !== 1000) { // 1000 表示正常关闭
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
          this.reconnectAttempts++
          console.log(`尝试重连第 ${this.reconnectAttempts} 次，等待 ${this.reconnectDelay} 毫秒...`)
          setTimeout(() => {
            this.getInstance()
          }, this.reconnectDelay)
        } else {
          console.warn('达到最大重连次数，停止尝试重连。')
        }
      }
    },
    readCallback(e) {
      const callBackInfo = e.data
      this.ackJsonData = callBackInfo

      if (this.isJSON(this.ackJsonData)) {
        const arrParse = JSON.parse(this.ackJsonData)

        // 接口回调
        if (this.MessageList[arrParse.apiName]) {
          this.MessageList[arrParse.apiName](arrParse)
          delete this.MessageList[arrParse.apiName]
        }

        // 回调分发
        if (arrParse.apiName === 'printStatus') {
          this.deviceStatus = arrParse.resultAck.online === 'online'
        } else if (arrParse.resultAck.callback) {
          const callbackName = arrParse.resultAck.callback.name
          const callbackData = arrParse.resultAck.callback

          switch (callbackName) {
            case 'onConnectSuccess':
              this.onConnectSuccess(callbackData.printerName)
              break
            case 'onDisConnect':
              this.onDisConnect(callbackData.printerName)
              break
            case 'onCoverStatusChange':
              this.onCoverStatusChange(callbackData.coverStatus)
              break
            case 'onElectricityChange':
              this.onElectricityChange(callbackData.powerLever)
              break
            case 'onPaperStatusChange':
              this.onPaperStatusChange(callbackData.paperStatus)
              break
            case 'onPrintPageCompleted':
              this.onPrintPageCompleted()
              break
            case 'onPrintProgress':
              this.onPrintProgress()
              break
            case 'onAbnormalResponse':
              this.onAbnormalResponse()
              break
            default:
              console.log('未知的回调 API！')
          }
        }

        this.ackJsonData = ''
      }
    },
    errorCallback(e) {
      // 处理 WebSocket 错误
      console.error('WebSocket 遇到错误:', e)
      // 错误时触发 onclose 以便重连
      if (this.g_websocket) {
        this.g_websocket.close()
      }
    },
    // SDK 和打印机方法
    initSdk() {
      const json = this.initSdkParam
      const callback = (data) => {
        const arrParse = JSON.parse(JSON.stringify(data))
        if (arrParse.resultAck.result !== 0) {
          console.warn('SDK 初始化失败。')
          return
        }

        this.getAllPrinters((printerData) => {
          const printerParse = JSON.parse(JSON.stringify(printerData))

          if (printerParse.resultAck.result === 0 && this.isJSON(printerParse.resultAck.info)) {
            const allPrinters = JSON.parse(printerParse.resultAck.info)
            const allPrintersName = Object.keys(allPrinters)
            const allPrintersValue = Object.values(allPrinters)

            if (allPrintersName.length > 0 && allPrintersValue.length > 0) {
              this.selectPrinter(
                allPrintersName[0],
                parseInt(allPrintersValue[0]),
                (selectData) => {
                  this.initSdkStatus = true
                }
              )
            }
          }
        })
      }
      this.sendMsg({apiName: 'initSdk', parameter: json}, callback)
    },
    unInitPrintInstance() {
      if (this.g_websocket) {
        this.g_websocket.close()
        this.g_websocket = null
      }
    },
    // 回调处理器
    onConnectSuccess(printerName) {
      console.log('打印机连接成功！')
      // 使用全局 EventBus 代替 new Vue()，避免内存泄漏
      // EventBus.$emit('onPrintOk')
      this.$emit('onPrintOk') // 假设父组件监听该事件
      this.initSdkStatus = true
      this.deviceStatus = true
    },
    onDisConnect(printerName) {
      console.log('打印机断开连接！')
      this.initSdkStatus = false
      this.deviceStatus = false
    },
    onCoverStatusChange(coverStatus) {
      console.log('打印机盒盖状态变化:', coverStatus)
    },
    onElectricityChange(powerLever) {
      console.log('打印机电量变化:', powerLever)
    },
    onPaperStatusChange(paperStatus) {
      console.log('打印机纸张状态变化:', paperStatus)
    },
    onPrintPageCompleted() {
      console.log('打印页面完成！')
    },
    onPrintProgress() {
      console.log('打印进度更新！')
    },
    onAbnormalResponse() {
      console.log('打印机异常响应！')
    },
    // 打印机操作
    getAllPrinters(callbackFunction) {
      this.sendMsg({apiName: 'getAllPrinters'}, callbackFunction)
    },
    picturePrint(base64Data, nPrintCount, bDenoise, callbackFunction) {
      const msg = {
        apiName: 'picturePrint',
        parameter: {
          data: base64Data,
          nPrintCount,
          bDenoise
        }
      }
      this.sendMsg(msg, callbackFunction)
    },
    selectPrinter(printerName, port, callbackFunction) {
      const msg = {
        apiName: 'selectPrinter',
        parameter: {printerName, port}
      }
      this.sendMsg(msg, callbackFunction)
    },
    stopPrint(callbackFunction) {
      this.sendMsg({apiName: 'stopPrint'}, callbackFunction)
    },
    closePrinter(callbackFunction) {
      this.sendMsg({apiName: 'closePrinter'}, callbackFunction)
    },
    setPrintDensity(nDensity, callbackFunction) {
      const msg = {
        apiName: 'setPrintDensity',
        parameter: {nDensity}
      }
      this.sendMsg(msg, callbackFunction)
    },
    setPrintSpeed(nSpeed, callbackFunction) {
      const msg = {
        apiName: 'setPrintSpeed',
        parameter: {nSpeed}
      }
      this.sendMsg(msg, callbackFunction)
    },
    setPrintLabelType(nType, callbackFunction) {
      const msg = {
        apiName: 'setPrintLabelType',
        parameter: {nType}
      }
      this.sendMsg(msg, callbackFunction)
    },
    setPrinterAutoShutDownTime(nType, callbackFunction) {
      const msg = {
        apiName: 'setPrinterAutoShutDownTime',
        parameter: {nType}
      }
      this.sendMsg(msg, callbackFunction)
    },
    setPrinterReset(callbackFunction) {
      this.sendMsg({apiName: 'setPrinterReset'}, callbackFunction)
    },
    setPrintPaperPos(nType, callbackFunction) {
      const msg = {
        apiName: 'setPrintPaper',
        parameter: {nType}
      }
      this.sendMsg(msg, callbackFunction)
    },
    getPrintSpeed(callbackFunction) {
      this.sendMsg({apiName: 'getPrintSpeed'}, callbackFunction)
    },
    getPower(callbackFunction) {
      this.sendMsg({apiName: 'getPower'}, callbackFunction)
    },
    getPrintLabelType(callbackFunction) {
      this.sendMsg({apiName: 'getPrintLabelType'}, callbackFunction)
    },
    getPrintDensity(callbackFunction) {
      this.sendMsg({apiName: 'getPrintDensity'}, callbackFunction)
    },
    getPrinterLanguageType(callbackFunction) {
      this.sendMsg({apiName: 'getPrinterLanguageType'}, callbackFunction)
    },
    getPrinterAutoShutDownTime(callbackFunction) {
      this.sendMsg({apiName: 'getPrinterAutoShutDownTime'}, callbackFunction)
    },
    getPrinterSn(callbackFunction) {
      this.sendMsg({apiName: 'getPrinterSn'}, callbackFunction)
    },
    getPrinterHardwareVersion(callbackFunction) {
      this.sendMsg({apiName: 'getPrinterHardwareVersion'}, callbackFunction)
    },
    getPrinterSoftwareVersion(callbackFunction) {
      this.sendMsg({apiName: 'getPrinterSoftwareVersion'}, callbackFunction)
    },
    setPrinterLanguageType(nType, callbackFunction) {
      const msg = {
        apiName: 'setPrinterLanguageType',
        parameter: {nType}
      }
      this.sendMsg(msg, callbackFunction)
    },
    getSpeedScope(callbackFunction) {
      this.sendMsg({apiName: 'getSpeedScope'}, callbackFunction)
      return this.getResult(5, 'getSpeedScope', '设置打印机语言超时！')
    },
    getConnectPrinter(callbackFunction) {
      this.sendMsg({apiName: 'getConnectPrinter'}, callbackFunction)
      return this.getResult(5, 'getConnectPrinter', '获取连接的打印机超时！')
    },
    getPrinterType(callbackFunction) {
      this.sendMsg({apiName: 'getPrinterType'}, callbackFunction)
    },
    getDensityScope(callbackFunction) {
      this.sendMsg({apiName: 'getDensityScope'}, callbackFunction)
    },
    getPrinterMode(callbackFunction) {
      this.sendMsg({apiName: 'getPrintMode'}, callbackFunction)
    },
    getMacAddress(callbackFunction) {
      this.sendMsg({apiName: 'getMacAddress'}, callbackFunction)
    },
    setPrintMode(nType, callbackFunction) {
      const msg = {
        apiName: 'setPrintMode',
        parameter: {nType}
      }
      this.sendMsg(msg, callbackFunction)
    },
    startJob(printDensity, printLabelType, printMode, count, callbackFunction) {
      const msg = {
        apiName: 'startJob',
        parameter: {
          printDensity,
          printLabelType,
          printMode,
          count
        }
      }
      this.sendMsg(msg, callbackFunction)
    },
    commitJob(printData, printerImageProcessingInfo, callbackFunction) {
      const printDataJson = JSON.parse(printData)
      const printerImageProcessingInfoJson = JSON.parse(printerImageProcessingInfo)
      const msg = {
        apiName: 'commitJob',
        parameter: {
          printData: printDataJson,
          printerImageProcessingInfo: printerImageProcessingInfoJson.printerImageProcessingInfo
        }
      }
      this.sendMsg(msg, callbackFunction)
    },
    endJob(callbackFunction) {
      this.sendMsg({apiName: 'endJob'}, callbackFunction)
    },
    cancleJob(callbackFunction) {
      this.sendMsg({apiName: 'stopPrint'}, callbackFunction)
    },
    InitDrawingBoard(json, callbackFunction) {
      const msg = {
        apiName: 'InitDrawingBoard',
        parameter: json
      }
      this.sendMsg(msg, callbackFunction)
    },
    DrawLableText(json, callbackFunction) {
      const msg = {
        apiName: 'DrawLableText',
        parameter: json
      }
      this.sendMsg(msg, callbackFunction)
    },
    DrawLableBarCode(json, callbackFunction) {
      const msg = {
        apiName: 'DrawLableBarCode',
        parameter: json
      }
      this.sendMsg(msg, callbackFunction)
    },
    DrawLableQrCode(json, callbackFunction) {
      const msg = {
        apiName: 'DrawLableQrCode',
        parameter: json
      }
      this.sendMsg(msg, callbackFunction)
    },
    DrawLableLine(json, callbackFunction) {
      const msg = {
        apiName: 'DrawLableLine',
        parameter: json
      }
      this.sendMsg(msg, callbackFunction)
    },
    DrawLableGraph(json, callbackFunction) {
      const msg = {
        apiName: 'DrawLableGraph',
        parameter: json
      }
      this.sendMsg(msg, callbackFunction)
    },
    DrawLableImage(json, callbackFunction) {
      const msg = {
        apiName: 'DrawLableImage',
        parameter: json
      }
      this.sendMsg(msg, callbackFunction)
    },
    generateImagePreviewImage(displayScale, callbackFunction) {
      const msg = {
        apiName: 'generateImagePreviewImage',
        displayScale
      }
      this.sendMsg(msg, callbackFunction)
    },
    // 工具方法
    getResult(tryTime, apiName, errInfo) {
      let tryTimes = tryTime
      let result = {}

      while (tryTimes--) {
        if (!this.isJSON(this.ackJsonData)) continue

        const arrParse = JSON.parse(this.ackJsonData)
        if (arrParse.apiName === apiName) {
          result = arrParse.resultAck
          break
        }
      }

      if (tryTimes <= 0) {
        result.result = false
        result.errorCode = 0x12
        result.info = errInfo
      }
      return result
    },
    isJSON(str) {
      if (typeof str === 'string') {
        try {
          const obj = JSON.parse(str)
          return typeof obj === 'object' && obj !== null
        } catch (e) {
          return false
        }
      }
      console.log('这不是一个字符串！')
      return false
    },
    // 打印任务方法
    startPrintJobLine() {
      // 检查 WebSocket 连接状态
      if (!this.websocketConnectStatus) {
        console.warn('WebSocket 未连接。')
        return
      }

      // 检查 SDK 初始化状态
      if (!this.initSdkStatus) {
        this.initSdk()
      }

      // 检查设备在线状态
      if (this.initSdkStatus && !this.deviceStatus) {
        console.warn('设备不在线，请连接打印机。')
        return
      }

      const content = JSON.parse(sessionStorage.getItem('printContent'))
      const contentArr = [content]
      console.log(`contentArr`, contentArr)
      this.batchPrintJob(contentArr)
    },
    batchPrintJob(list) {
      console.log(list, 'list')
      if (!list || list.length === 0) {
        return
      }

      const {printQuantity} = this.jsonObj.printerImageProcessingInfo

      this.startJob(
        this.dataParams.printFrom.density,
        this.dataParams.printFrom.paperType,
        this.dataParams.printFrom.printMode,
        list.length * printQuantity,
        (data) => {
          const arrParse = JSON.parse(JSON.stringify(data))
          if (arrParse.resultAck.result !== 0) {
            return
          }

          // 提交打印任务
          list.forEach((_, printIndex) => {
            this.printTag(list, printIndex)
          })
        }
      )
    },
    printTag(list, x) {
      console.log(`x的值:${x}`)
      // 设置画布尺寸
      this.InitDrawingBoard(this.dataParams.InitDrawingBoardParam, () => {
        this.printItem(list, x, list[x], 0)
      })
    },
    printItem(list, x, item, i) {
      if (i < item.length) {
        const currentItem = item[i]
        const callback = () => {
          this.printItem(list, x, item, i + 1)
        }

        switch (currentItem.type) {
          case 'text':
            this.DrawLableText(currentItem.json, callback)
            break
          case 'qrCode':
            this.DrawLableQrCode(currentItem.json, callback)
            break
          case 'barCode':
            this.DrawLableBarCode(currentItem.json, callback)
            break
          case 'line':
            this.DrawLableLine(currentItem.json, callback)
            break
          case 'graph':
            this.DrawLableGraph(currentItem.json, callback)
            break
          default:
            console.warn(`未知的项目类型: ${currentItem.type}`)
            this.printItem(list, x, item, i + 1)
        }
      } else {
        const commitJobCallback = (data) => {
          console.log(`commitJob data`, data)
          const arrParse = JSON.parse(JSON.stringify(data))
          const resultInfo = 'commitJob ok'

          // 处理错误码和缓存状态
          if (
            arrParse.resultAck.errorCode !== 0 &&
            (arrParse.resultAck.cacheStatus === 'pause' ||
              arrParse.resultAck.cacheStatus === 'cancel')
          ) {
            return
          }

          // 检查任务是否完成
          if (
            arrParse.resultAck.printQuantity === this.jsonObj.printerImageProcessingInfo.printQuantity &&
            arrParse.resultAck.onPrintPageCompleted === this.jsonObj.printerImageProcessingInfo.printQuantity
          ) {
            // 结束打印任务
            this.endJob((endData) => {
              const endParse = JSON.parse(JSON.stringify(endData))
              if (String(endParse.resultAck.info).includes('endJob ok')) {
                // 使用全局 EventBus 代替 new Vue()，避免内存泄漏
                // EventBus.$emit('onPrintEnd')
                this.$emit('onPrintEnd') // 假设父组件监听该事件
              }
            })
            return
          }

          // 如果提交成功且还有更多页面需要发送
          if (String(arrParse.resultAck.info).includes(resultInfo) && x < list.length - 1) {
            console.log('发送下一页打印数据...')
            this.printTag(list, x + 1)
          }
        }

        this.commitJob(null, JSON.stringify(this.jsonObj), commitJobCallback)
      }
    }
  }
}
</script>

<style scoped>
/* 在此添加你的样式 */
</style>
