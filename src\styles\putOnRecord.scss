.putOnRecordDiv {
  .putOnRecordTitle {
    height: 3.4rem;
    line-height: 4.4rem;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 1.2rem;
    color: #000000;
    letter-spacing: 0.6rem;
    text-align: center;
    font-style: normal;
  }
  width: 100%;
  max-width: 60rem;
  background: #ffffff;
  box-shadow: 0px 0.15rem 0.8rem 0px rgba(9, 76, 1, 0.1);
  border-radius: 0.3rem;
  margin: auto;
  margin-top: 1rem;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 0.8rem;
  color: #3c405c;

  ::v-deep .el-checkbox {
    margin: 0.25rem;
    float: left;
    line-height: 2rem;
    height: 2rem;
    .el-input {
      line-height: 2rem;
      height: 2rem;
      .el-input__inner {
        line-height: 2rem;
        height: 2rem;
      }
    }
    .putOnRecordElePage {
      min-height: 2rem;
    }
  }
  ::v-deep .el-select {
    width: 100%;
  }
  & > .oh {
    position: relative;
    height: auto;
    margin-top: 1rem;
    overflow: auto;
    margin-bottom: 0.5rem;
    margin-left: 1rem;
    width: calc(100% - 1rem);
    padding-right: 1rem;
    padding-top: 1rem;
    padding-bottom: 1rem;
  }
  .fr {
    float: right;
  }
  label {
    font-weight: normal;
  }
  ::v-deep .el-radio {
    margin-right: 0.15rem;
  }
  ::v-deep .el-date-editor {
    width: 100%;
    .el-input__inner {
      padding-left: 20px;
      font-size: 0.8rem !important;
    }
    .el-input__prefix {
      left: -0.2rem;
      top: 0.15rem;
    }
  }
  ::v-deep .el-input__inner {
    &::placeholder {
      font-size: 0.8rem !important;
    }
    line-height: 1.8rem;
    height: 1.8rem;
    border: 0 none;
    padding: 0;
    border-bottom: 1px solid #000;
    border-radius: 0;
    font-size: 0.8rem !important;
  }
  .tableTopDiv {
    position: relative;
    overflow: hidden;
    line-height: 1.4rem;
    .deathReasonDiv,
    .departIdDiv,
    .deathDateDiv {
      .txt {
        width: 4rem;
      }
      .val {
        width: calc(100% - 5rem);
      }
    }
    ::v-deep .el-cascader {
      width: 100%;
      line-height: 1.2rem;
      .el-input__inner {
        line-height: 1.2rem;
        height: 1.2rem;
      }
      .el-icon-arrow-down {
        line-height: 1;
      }
    }
    .row5 {
      width: 50%;
      float: left;
    }
    .row3 {
      width: 35%;
      float: left;
    }
    .row2 {
      width: 15%;
      float: left;
    }
    .rowHeightDiv {
      line-height: 2.2rem;
      ::v-deep .el-input__inner {
        border-bottom: 0 none;
      }
    }
    .badEvent {
      line-height: 2.2rem;
      margin-left: 14px;
    }
    .departIdDiv {
      .txt {
        width: 4rem;
      }
      ::v-deep .putOnRecordElePage .oh {
        padding-right: 0;
      }
    }
    .txt {
      width: 8rem;
      display: inline-block;
    }
    .val {
      width: calc(100% - 8rem);
      float: right;
      &.nullTxt {
        width: 100%;
      }
    }
    .w6 {
      width: 60%;
    }
    .w7 {
      width: 70%;
    }
    .w4 {
      width: 40%;
    }
    .w3s {
      width: 30%;
    }
    .cb {
      clear: both;
    }
    .fl {
      float: left;
    }
    .fr {
      float: right;
    }
  }
  .tableRowDiv {
    ::v-deep * {
      box-sizing: border-box;
      font-size: 0.8rem;
      &.el-input__icon {
        line-height: 1.7rem;
      }
    }
    line-height: 2.4rem;
    border: 1px solid #000;
    .listBT {
      border-top: 1px solid #000;
      .val {
        border-left: 1px solid #d5d5d5;
      }
    }
    & > .list {
      background-color: #f5f7fa;

      &.workUnitCss {
        .txt.txtS {
          padding: 0 1rem;
        }
      }
      ::v-deep .inputType1 {
        background-color: #fff;
        .el-input__inner {
          border: 0 none;
        }
      }
      position: relative;
      overflow: hidden;
      & + .list {
        & > .moreList {
          border-top: 1px solid #000;
          &.nobt {
            border-top: 0 none;
          }
        }
      }
      &.moreList {
        border-top: 1px solid #000;
        &.nobt {
          border-top: 0 none;
        }
        ::v-deep .el-input {
          display: flex !important;
          flex-direction: row;
          flex-wrap: nowrap;
          justify-content: flex-start;
          align-items: center;
          margin-top: 0.5rem;
        }
        & > .moreList {
          border-top: 0 none;

          ::v-deep &.inPatientCount {
            .el-input-group__append {
              background-color: #fff !important;
              border: 0;
            }
            .el-input-group__prepend {
              color: #606266;
              background-color: #fff !important;
              border: 0;
              padding-left: 0rem;
              width: 15rem;
              display: inline-block !important;
            }
            .el-input__inner {
              width: 5rem;
              border-bottom: 1px solid #000;
            }
          }
        }
        & > .txt {
          width: 6rem;
          left: 0;
          font-weight: normal;
        }
      }
      & > .val {
        width: calc(100% - 6rem);
        background-color: #fff;
        float: right;
      }
      ::v-deep &.professionDoctorSuggest .txt.txtS {
        padding: 0 1rem;
      }
      .txt {
        font-size: 0.8rem;
        position: absolute;
        // 6rem - 1.6rem / 2
        top: 50%;
        transform: translate(0%, -50%);
        text-align: center;
        line-height: 1;
        font-weight: normal;
        padding: 0 0.5rem;
        &.txtS {
          width: 6rem;
          left: 0rem;
          font-weight: normal;
        }
      }
      ::v-deep .moreList {
        position: relative;
        overflow: hidden;
        width: calc(100% - 6rem);
        float: right;
        background-color: #fff;
        border-left: 1px solid #d5d5d5;
        &.nullTxt {
          width: 100%;
          border-left: 0 none;
        }
        & + .moreList {
          border-top: 1px solid #000;
          &.nobt {
            border-top: 0 none;
          }
        }
        background: #f5f7fa;
        .txt {
          line-height: 1;
          width: 6rem;
          top: 50%;
          position: absolute;
          left: 0rem;
          text-align: center;
          font-weight: normal;
          padding: 0 0.5rem;
          transform: translate(0%, -50%);
        }
        .val {
          background: #fff;
          &.nullTxt {
            width: 100%;
            border-left: 0 none;
          }
          width: calc(100% - 6rem);
          float: right;
          border-left: 1px solid #d5d5d5;
          &.dangerActChoose {
            .el-input-group__append {
              background-color: #fff !important;
              border: 0 none !important;
            }
          }
          .rowMore {
            position: relative;
            overflow: hidden;
            &.ybrgx {
              .txt {
                width: 5rem;
                padding: 0.5rem 0;
              }
              .val {
                width: calc(100% - 5rem);
              }
            }
            .txt {
              width: 5rem;
              padding-left: 0;
            }
            .val {
              width: calc(100% - 5rem);
            }
            &.row2 {
              width: calc(50% - 1px);
              float: left;
            }
            &.row3 {
              width: calc(33.33% - 1px);
              float: left;
            }
            &.row4 {
              width: calc(25% - 1px);
              float: left;
            }
            & + .rowMore {
              border-left: 1px solid #d5d5d5;
            }
          }
        }
        &.rowName {
          width: 12.05rem;
          float: left;
        }
        &.noBorderTop {
          border-top: 0 none;
        }
        &.rowSex {
          border-left: 1px solid #d5d5d5;
          width: calc(50% - 6.05rem);
          float: left;
          .txt {
            width: 5.49rem;
          }
          .val {
            width: calc(100% - 5.49rem);
          }
          .el-radio__label {
            padding-left: 0;
            font-size: 0.6rem !important;
          }
          .el-radio {
            margin-right: 0.3rem;
          }
        }
        &.rowBirthday {
          border-left: 1px solid #d5d5d5;
          width: 12rem;
          float: left;
          .txt {
            width: 5rem;
          }
          .val {
            width: calc(100% - 5rem);
          }
        }
        &.rowAge {
          border-left: 1px solid #d5d5d5;
          width: calc(50% - 18rem);
          float: left;
          .txt {
            width: 3rem;
          }
          .val {
            width: calc(100% - 3rem);
          }
        }
        &.rowIdType {
          border-left: 1px solid #d5d5d5;
          width: calc(50% - 6.05rem);
          float: left;
          .txt {
            width: 5.49rem;
          }
          .val {
            width: calc(100% - 5.49rem);
          }
        }
        &.rowIdCard {
          border-left: 1px solid #d5d5d5;
          width: calc(50% - 6.2rem);
          float: left;
          .txt {
            width: 5rem;
          }
          .val {
            width: calc(100% - 5rem);
          }
        }
        &.rowNation {
          border-left: 1px solid #d5d5d5;
          width: calc(70% - 12rem);
          float: left;
        }
        &.rowNativePlace,
        &.rowbirthplace {
          width: 12.05rem;
          float: left;
        }
        &.rowbirthplace {
          width: calc(50% - 6.05rem);
          .txt {
            width: 5.49rem;
          }
          .val {
            width: calc(100% - 5.49rem);
          }
        }
        &.rowNation {
          width: calc(50% - 6.05rem);
          .txt {
            width: 5rem;
          }
          .val {
            width: calc(100% - 5rem);
          }
        }
        &.rowbirthplace,
        &.bl1 {
          border-left: 1px solid #000;
        }
        &.bb1 {
          border-bottom: 1px solid #000;
        }
        &.br1 {
          border-right: 1px solid #000;
        }
        &.row3 {
          width: 30%;
          float: left;
        }
        &.row2 {
          width: 20%;
          float: left;
        }
        &.row25 {
          width: 25%;
          float: left;
        }
        &.row245 {
          width: 24.5%;
          float: left;
        }
        &.row2455 {
          width: 24.55%;
          float: left;
        }
        &.row246 {
          width: 24.6%;
          float: left;
        }
        &.row33 {
          width: calc(100% / 3);
          float: left;
        }
        &.row35 {
          width: 35%;
          float: left;
        }
        &.row7 {
          width: 70%;
          float: left;
        }
        &.row75 {
          width: 75%;
          float: left;
        }
        &.row754 {
          width: 74.4%;
          float: left;
        }
        &.row755 {
          width: 74.5%;
          float: left;
        }
        &.row7545 {
          width: 74.45%;
          float: left;
        }
        &.row8 {
          width: 80%;
          float: left;
        }
        &.jyfwqydm {
          width: 35%;
          float: left;
        }
        &.row5 {
          width: 50%;
          float: left;
        }
        &.row5Row2 {
          width: calc(50% - 3rem);
          float: right;
        }
      }
      ::v-deep .rowSDiv {
        background: red;
      }
      .height2 {
        height: 5.4rem;
        .putOnRecordElePage {
          margin-top: 1rem;
        }
      }
    }
    .updateOnRecordField,
    .putOnRecordField {
      display: none;
    }
    ::v-deep .el-table {
      th.el-table__cell.is-leaf,
      td.el-table__cell {
        padding: 0;
        .cell {
          font-weight: normal;
          color: #000;
          text-align: center;
        }
      }
      th.el-table__cell {
        background: #f5f7fa !important;
        .cell {
          color: #3c405c !important;
          font-weight: 400 !important;
        }
      }
    }
  }
  .requiredRed {
    position: relative;
    padding-left: 0.5rem;
    &::before {
      content: '*';
      color: red;
      font-size: 1.2rem;
    }
  }
  .clickArea {
    width: 1rem;
    height: 1rem;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 4;
  }
  .btnDvi {
    height: 3.6rem;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    overflow: hidden;
    padding: 1.4rem 1rem 0;
    position: fixed;
    right: 1.5rem;
    top: 5rem;
    z-index: 99;
    ::v-deep .clearBtn {
      margin-right: 1rem;
      width: 4.5rem;
      height: 1.6rem;
      border-radius: 0.3rem;
      border: 1px solid #ced3db;
      line-height: 1.6rem;
      padding: 0 !important;
      font-size: 0.8rem;
      color: rgba(0, 0, 0, 0.85);
      span {
        display: block !important;
      }
    }
    ::v-deep .saveBtn {
      width: 4.5rem;
      height: 1.6rem;
      border-radius: 0.3rem;
      border: 1px solid #ced3db;
      line-height: 1.6rem;
      padding: 0 !important;
      font-size: 0.8rem;
      color: #fff;
      span {
        display: block !important;
      }
    }
  }
  ::v-deep .txtOnlyFour {
    & > .txt {
      width: 5rem !important;
      left: 0.5rem !important;
      line-height: 1.5 !important;
    }
  }
  ::v-deep .areaContractPhone {
    & > .txt {
      width: 8rem !important;
    }
    & > .val {
      width: calc(100% - 8rem) !important;
    }
  }
  ::v-deep .permanentAddressDiv {
    .permanentAddressDiv {
      & > .putOnRecordElePage > .oh {
        & > .putOnRecordElePage {
          width: calc(100% - 10rem);
        }
      }
    }
  }
  ::v-deep .moreList:has(.error) {
    position: relative;
    .val::after {
      content: '';
      display: block;
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      border: 2px dashed red;
      pointer-events: none;
    }
  }
  .editBtnDiv {
    position: absolute;
    right: 0;
    top: 0;
    color: #00b2dc;
    cursor: pointer;
    z-index: 11;
  }
  .db {
    display: block;
  }
  ::v-deep .readonlyDiv {
    .tableTopDiv .list {
      filter: saturate(20%); /* 增加饱和度 */
    }
    .tableRowDiv {
      filter: saturate(20%); /* 增加饱和度 */
    }
    * {
      border-color: #d5d5d5 !important;
    }
    // .rowAge .val {
    //   label {
    //     color: #c0c4cc;
    //   }
    // }
    // .el-radio .el-radio__label {
    //   color: #c0c4cc;
    // }
  }
  ::v-deep .deathDateDiv .el-input__icon {
    line-height: 1.6rem;
  }
}

@media (max-width: 1280px) {
  .putOnRecordDiv {
    .tableRowDiv {
      .row2Css {
        line-height: 1.4rem;
      }
    }
  }
}
