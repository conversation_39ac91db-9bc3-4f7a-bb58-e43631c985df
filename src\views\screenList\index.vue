<template>
  <div class="massScreening">
    <el-card class="massScreening-disease">
      <DiseaseCategory ref="diseaseCategory" :query-params="queryParams" @change="handleDiseaseChange" />
    </el-card>

    <el-card class="massScreening-search">
      <el-form :model="queryParams" label-width="120px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="普查时间：">
              <el-date-picker
                v-model="queryParams.timeRange"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="P值：">
              <el-row style="display: flex; align-items: center">
                <el-col :span="11">
                  <el-input v-model="queryParams.pValueMin" placeholder="最小值" />
                </el-col>
                <el-col :span="2" style="text-align: center">
                  <span>~</span>
                </el-col>
                <el-col :span="11">
                  <el-input v-model="queryParams.pValueMax" placeholder="最大值" />
                </el-col>
              </el-row>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="普查状态：">
              <el-select v-model="queryParams.csStatus" placeholder="请选择" style="width: 100%">
                <el-option label="已筛查" :value="1" />
                <el-option label="已转居民档案" :value="5" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button
              icon="el-icon-search"
              type="primary"
              @click="
                () => {
                  handleSearch()
                  $refs.diseaseCategory.getDiseaseList()
                }
              "
            >
              查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="massScreening-table">
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 50px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <!-- 慢病病种 -->
        <template #disease="{ row }">
          <ChronicDiseaseType :record="row" />
        </template>
        <!-- 普查状态 -->
        <template #csStatus="{ row }">
          <span v-if="row.csStatus === 1">已筛查</span>
          <span v-else>已转居民档案</span>
        </template>

        <!-- 操作 -->
        <template #operation="{ row }">
          <el-button v-if="row.csStatus === 1" type="text" size="small" @click="handleReceive(row)">
            转档案并接诊
          </el-button>
          <el-button type="text" size="small" @click="handleDetail(row)">详情</el-button>
          <el-button style="color: red" type="text" size="small" @click="handleDelete(row)">删除</el-button>
        </template>
      </base-table>
    </el-card>
  </div>
</template>

<script>
import { getMassScreeningList, deleteMassScreening } from '@/api/screenList'
import { getReceptionWorkbenchByCsId } from '@/api/receptionWorkbench'
import DiseaseCategory from '@/components/diseaseCategory/index.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import ChronicDiseaseType from '@/components/chronicDiseaseType/index.vue'
import tableMixin from '@/mixins/tableMixin'

export default {
  name: 'ScreenList',
  components: {
    DiseaseCategory,
    BaseTable,
    ChronicDiseaseType
  },
  mixins: [tableMixin],
  data() {
    return {
      columns: [
        { prop: 'name', label: '姓名' },
        { prop: 'disease', label: '慢病病种', width: 160, slot: 'disease' },
        { prop: 'age', label: '年龄' },
        { prop: 'idCard', label: '身份证号', width: 180 },
        { prop: 'phone', label: '手机号码', width: 130 },
        { prop: 'pValue', label: 'P值' },
        { prop: 'csTnbResult', label: '糖尿病普查结果', width: 120 },
        { prop: 'csGxyResult', label: '高血压普查结果', width: 120 },
        { prop: 'csCopdResult', label: '慢阻肺普查结果', width: 120 },
        { prop: 'csFibResult', label: '房颤普查结果', width: 120 },

        { prop: 'csStatus', label: '普查状态', slot: 'csStatus', width: 120 },
        { prop: 'createTime', label: '创建时间', width: 180 },
        {
          prop: 'operation',
          label: '操作',
          slot: 'operation',
          width: 200,
          fixed: window.innerWidth < 1600 ? 'right' : false
        }
      ],
      queryParams: {
        disease: '',
        timeRange: [],
        pValueMin: '',
        pValueMax: '',
        csStatus: ''
      }
    }
  },
  methods: {
    async getTableList(params) {
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }

      return await getMassScreeningList(queryParams)
    },

    handleDiseaseChange(disease) {
      this.queryParams.disease = disease.diseaseCode === 'all' ? '' : disease.diseaseCode
      this.handleSearch()
    },

    async handleReceive(row) {
      this.$confirm('确定转档案并接诊吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          const res = await getReceptionWorkbenchByCsId({
            id: row.id
          })
          if (res.code === 200) {
            this.$router.push({
              path: '/receptionCenter/patientReception',
              query: { id: res.data.id }
            })
          }
        })
        .catch(() => {
          this.$message.info('已取消转档案并接诊')
        })
    },

    handleDetail(row) {
      this.$router.push({
        path: '/screenList/detail',
        query: {
          id: row.id
        }
      })
    },

    handleDelete(row) {
      this.handleConfirmDelete({
        params: {
          id: row.id
        },
        deleteApi: deleteMassScreening,
        message: '确认删除该条数据吗？'
      })
    },

    handleReset() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20
      }
      this.$refs.diseaseCategory.activeCode = 'all'
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.massScreening {
  height: 100%;
  display: flex;
  flex-direction: column;

  .massScreening-disease {
    margin: 16px;
  }

  .massScreening-search {
    margin: 0 16px;
    height: 77px;
  }
  .massScreening-table {
    flex: 1;
    margin: 16px 16px 0 16px;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
}
</style>
