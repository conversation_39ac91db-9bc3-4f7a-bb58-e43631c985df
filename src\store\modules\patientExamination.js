/* eslint-disable no-shadow */
// import { cloneDeep } from 'lodash'
import { getInspectionTestingDetail } from '@/api/examination'

const state = {
  loading: false,
  saveBtnLoading: false,
  patientExaminationData: {} // 患者检查详情
}

const mutations = {
  SET_LOADING(state, flag) {
    state.loading = flag
  },
  SET_SAVE_BTN_LOADING(state, flag) {
    state.saveBtnLoading = flag
  },
  SET_PATIENT_EXAMINATION_DATA(state, data) {
    state.patientExaminationData = data
  }
}

const actions = {
  async getPatientExaminationData({ commit }, params) {
    let res = null
    try {
      res = await getInspectionTestingDetail(params)
      commit('SET_PATIENT_EXAMINATION_DATA', res.data)
    } catch (error) {
      console.error(error)
    }
    return res ? res.data : null
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
