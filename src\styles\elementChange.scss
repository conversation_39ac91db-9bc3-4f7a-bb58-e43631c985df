// ====== 修改element-ui的字体样式 start ======
.el-autocomplete .el-input,
.el-tree-node__expand-icon {
  font-size: 0.6rem;
}

.el-select-dropdown__item,
.el-input,
.el-button.el-button--text,
.el-date-editor .el-range__close-icon,
.el-date-editor .el-range__icon,
.el-date-editor .el-range-input {
  font-size: 0.7rem;
}

.el-pager li,
.el-pagination__sizes .el-input .el-input__inner,
.el-pagination span:not([class*='suffix']),
.el-pagination button {
  font-size: 0.65rem;
}

.el-pagination__editor.el-input .el-input__inner {
  height: 1.4rem;
}
.el-pagination .el-select .el-input__inner {
  height: 1.4rem !important;
  line-height: 1.4rem;
}
.el-pagination .el-select .el-select__caret {
  line-height: 1.4rem;
}

.el-pager li,
.el-pagination span:not([class*='suffix']),
.el-pagination button,
.el-input--mini .el-input__inner {
  height: 1.4rem;
  line-height: 1.4rem;
}

.el-pagination .el-select .el-input {
  width: 5rem;
}

.el-pagination__editor.el-input {
  width: 2.5rem;
}

.el-select-dropdown__item {
  padding: 0rem 1rem;
  height: 1.7rem;
  line-height: 1.7rem;
}

/* ====== 设置switch的样式 start ====== */
.public_switch {
  &.el-switch {
    font-size: 0.7rem;
    line-height: 1rem;
    height: 1rem;
    &.is-checked {
      .el-switch__core {
        &:after {
          margin-left: 0px;
          right: 1px;
          left: auto;
        }
      }
    }
  }
  .el-switch__core {
    width: 2.8rem !important;
    height: 1.2rem;
    border-radius: 0.6rem;
    &:after {
      width: 1rem;
      height: 1rem;
    }
  }

  @media (max-width: 1280px) {
    &.el-switch {
      font-size: 0.6rem;
      line-height: 0.9rem;
      height: 1rem;
      &.is-checked {
        .el-switch__core {
          &:after {
            margin-left: 0px;
            right: 1px;
            left: auto;
          }
        }
      }
    }
    .el-switch__core {
      width: 2rem !important;
      height: 1rem;
      border-radius: 0.6rem;
      &:after {
        width: 0.8rem;
        height: 0.8rem;
      }
    }
  }
}

/* ====== 设置switch的样式 end ====== */

.el-cascader {
  line-height: 1.7rem;
}
// ====== 修改element-ui的字体样式 end ======

/* edouard */

#app {
  /* element radio 单选框 css修改 start */
  .el-radio {
    font-size: 0.7rem;
    .el-radio__inner {
      width: 0.7rem;
      height: 0.7rem;
    }
    .el-radio__label {
      font-size: 0.7rem !important;
    }
  }

  /* element radio 单选框 css修改 end */

  /* element radio 单选框 css修改 start */

  .el-form-item__label,
  .el-input__inner,
  .vue-treeselect__control {
    height: 1.8rem;
  }
  .putOnRecordDiv {
    .el-form-item__label,
    .el-input__inner,
    .vue-treeselect__control {
      height: 1.6rem;
    }
  }
  .vue-treeselect__placeholder {
    line-height: 1.8rem;
  }

  .el-select {
    .el-input {
      .el-select__caret {
        line-height: 1.7rem;
        height: 1.7rem;
      }
    }
  }
  /* element radio 单选框 css修改 end */

  /* element editor 日期选择 css修改 start */
  // .el-date-editor {
  //   .el-range__icon,
  //   .el-range-separator {
  //     line-height: 1.7rem;
  //   }
  // }
  /* element editor 日期选择 css修改 end */

  /* element button 按钮 css修改 start */
  .el-button {
    padding: 1vh 1rem;
    font-size: 0.7rem;
    height: 1.8rem;
    box-sizing: border-box;
    &.el-button--text {
      padding: 0;
    }
  }
  .el-dropdown {
    font-size: 0.7rem;
  }


  /* element button 按钮 css修改 end */

  /* element button 按钮 css修改 start */
  .el-checkbox {
    .el-checkbox__input {
      .el-checkbox__inner {
        width: 0.7rem;
        height: 0.7rem;
      }
      .el-checkbox__inner::after {
        height: 0.35rem;
        left: 0.2rem;
      }
    }
  }
  /* element button 按钮 css修改 end */

  /* element table 表格 css修改 start */
  .el-table {
    th.el-table__cell {
      color: #222 !important;
      .cell {
        font-weight: 700;
      }
      background: linear-gradient(180deg, #eee 0%, #f0f0f0 100%);
    }
    td.el-table__cell {
      color: #666 !important;
      &.noTxtOh {
        .cell {
          line-height: 1rem;
          word-break: unset;
          text-overflow: unset;
          overflow: unset;
          display: block;
          -webkit-box-orient: unset;
        }
      }
      .cell {
        line-height: 2rem;
      }
    }
    .caret-wrapper {
      width: 1rem;
    }
  }
  /* element table 表格 css修改 end */
  .batchAddUserPop .el-dialog__body {
    padding-bottom: 0;
  }
  .el-transfer-panel {
    width: calc(50% - 5rem);
  }
  .el-transfer__buttons {
    padding: 0 1.5rem;
    .el-button {
      width: 3rem;
      text-align: center;
      padding: 1vh 0;
      span {
        justify-content: center;
      }
    }
  }

  .el-form-item__content {
    line-height: 1;
  }
  .searchFormItemVw {
    .el-form-item__label {
      line-height: 1.8rem;
    }
    @media (max-width: 1280px) {
      .el-form-item__label,
      .el-form-item__content {
        padding-right: 0rem;
      }
    }
  }
  .el-form-item__label {
    line-height: 1.2;
    font-weight: 500;
  }

  // .el-form--inline .el-form-item {
  //   margin-right: 5rem;
  // }
}

.toothListTable {
  text-align: center;
  width: 100%;
  border: 1px solid #222;
  border-bottom: 0 solid #222;
  margin-bottom: 1rem;
  .toothListTheader {
    width: 100%;
    overflow: hidden;
    position: relative;
    border-bottom: 1px solid #222;
    .toothListThTd {
      width: 50%;
      float: left;
      line-height: 1.5;
      border-right: 1px solid #222;
      & + .toothListThTd {
        border-right: 0;
      }
    }
  }
  .toothListTbody {
    overflow: hidden;
    position: relative;
    .toothListTbodyTr {
      border-bottom: 1px solid #222;
      width: 100%;
      overflow: hidden;
      position: relative;
      .toothListThTd {
        width: calc(100% / 16);
        float: left;
        line-height: 1.5;
        cursor: pointer;
        &.active {
          background-color: #0a86c8;
          color: #fff;
        }
        &:hover {
          background-color: rgb(30, 191, 167, 0.6);
          color: #fff;
        }
        & + .toothListThTd {
          border-left: 1px solid #222;
        }
      }
    }
  }
}
.toothTitle {
  padding: 0.5rem 0;
}
