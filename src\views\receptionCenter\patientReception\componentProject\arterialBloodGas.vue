<!-- 动脉血气 -->
<template>
  <div class="arterial-blood-gas">
    <el-form :model="form" label-width="120px">
      <CheckboxGroupField v-model="form.arteryVigorResult" :item="checkboxItem" />
      <!-- <el-form-item label="检查结果：">
        <el-checkbox-group v-model="form.arteryVigorResult">
          <el-checkbox label="1">正常</el-checkbox>
          <el-checkbox label="2">低血氧症</el-checkbox>
          <el-checkbox label="3">Ⅰ型呼吸衰竭</el-checkbox>
          <el-checkbox label="4">Ⅱ型呼吸衰竭</el-checkbox>
        </el-checkbox-group>
      </el-form-item> -->
      <el-form-item label="检查所见：">
        <el-input v-model="form.arteryVigorFinding" type="textarea" />
      </el-form-item>
      <el-form-item label="上传报告图片：">
        <custom-upload v-model="form.attachmentPhotoUrl" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import CustomUpload from '@/components/customUpload/index.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'

export default {
  name: 'ArterialBloodGas',
  components: {
    CustomUpload,
    CheckboxGroupField
  },
  data() {
    return {
      form: {
        arteryVigorResult: [],
        arteryVigorFinding: '',
        attachmentPhotoUrl: ''
      },
      checkboxItem: {
        label: '检查结果：',
        type: 'checkbox',
        required: false,
        mutuallyExclusive: true, // 互斥
        prop: 'arteryVigorResult',
        options: [
          { label: '正常', value: '1' },
          { label: '低血氧症', value: '2' },
          { label: 'Ⅰ型呼吸衰竭', value: '3' },
          { label: 'Ⅱ型呼吸衰竭', value: '4' }
        ]
      }
    }
  },
  methods: {
    initData({ id, data }) {
      this.form = {
        arteryVigorResult: data && data.arteryVigorResult ? data.arteryVigorResult.split(',') : [],
        arteryVigorFinding: data && data.arteryVigorFinding,
        attachmentPhotoUrl: data && data.attachmentPhotoUrl,
        itemId: id,
        itemDetailId: data && data.id
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.arterial-blood-gas {
  padding: 16px;
}
</style>
