<!-- 动脉硬化 -->
<template>
  <div class="arteriosclerosis">
    <div v-if="showDeviceCode($route.path)" class="device-barcode">
      <Barcode :code="`${getDeviceCode($route.path, getSourceData())}`" />
    </div>

    <div class="arteriosclerosis-content">
      <div class="arteriosclerosis-content-data">
        <!-- 表格一：血压数据 -->
        <el-table
          :data="pressureData"
          border
          style="width: 100%; margin-bottom: 30px"
          :span-method="pressureSpanMethod"
        >
          <el-table-column label="位置" prop="part" align="center" />
          <el-table-column label="" prop="type" align="center" />
          <el-table-column label="左" prop="left" align="center">
            <template slot-scope="scope">
              <custom-input-number v-model="scope.row.left" />
            </template>
          </el-table-column>
          <el-table-column label="右" prop="right" align="center">
            <template slot-scope="scope">
              <custom-input-number v-model="scope.row.right" />
            </template>
          </el-table-column>
        </el-table>

        <!-- 表格二：检测结果 -->
        <el-table :data="resultData" border style="width: 100%">
          <el-table-column label="检查结果" prop="type" align="center" />
          <el-table-column label="左" prop="left" align="center">
            <template slot-scope="scope">
              <custom-input-number v-model="scope.row.left" />
            </template>
          </el-table-column>
          <el-table-column label="右" prop="right" align="center">
            <template slot-scope="scope">
              <custom-input-number v-model="scope.row.right" />
            </template>
          </el-table-column>
        </el-table>

        <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" style="margin-top: 30px">
          <el-form-item label="检查所见：">
            <el-input v-model="form.arteriosclerosisFinding" type="textarea" :rows="3" style="width: 100%" />
          </el-form-item>

          <el-form-item label="医生意见：">
            <el-input v-model="form.arteriosclerosisSuggest" type="textarea" :rows="3" style="width: 100%" />
          </el-form-item>

          <el-form-item label="检查结果：" prop="arteriosclerosisResult">
            <el-radio-group v-model="form.arteriosclerosisResult">
              <el-radio :label="1">正常</el-radio>
              <el-radio :label="2">异常</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="上传报告图片">
            <custom-upload v-model="form.attachmentPhotoUrl" />
          </el-form-item>
        </el-form>
      </div>

      <div class="arteriosclerosis-content-img">
        <img src="@/assets/cspImg/arteriosclerosis.png" alt="arteriosclerosis">
        <!-- <static-table :columns="arteriosclerosis.columns" :table-data="arteriosclerosis.staticTableData" /> -->
      </div>
    </div>
  </div>
</template>

<script>
import { mapDataToTable, getDeviceCode, showDeviceCode } from '@/utils/cspUtils'
import { cloneDeep } from 'lodash'
// import { arteriosclerosis } from './staticTableData'
import CustomUpload from '@/components/customUpload/index.vue'
import Barcode from '@/components/barcode/barcode.vue'
// import StaticTable from '@/components/staticTable/index.vue'

export default {
  name: 'Arteriosclerosis',
  components: {
    CustomUpload,
    Barcode
    // StaticTable
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // arteriosclerosis,
      form: {
        arteriosclerosisResult: '',
        arteriosclerosisFinding: '',
        arteriosclerosisSuggest: '',
        attachmentPhotoUrl: ''
      },
      rules: {
        arteriosclerosisResult: [{ required: true, message: '请选择检查结果' }]
      },
      pressureData: [
        { part: '上臂', type: 'SBP', left: '', right: '', rightLegSbp: 'right', leftLegSbp: 'left' },
        { part: '上臂', type: 'MBP', left: '', right: '', rightLegMbp: 'right', leftLegMbp: 'left' },
        { part: '上臂', type: 'DBP', left: '', right: '', rightLegDbp: 'right', leftLegDbp: 'left' },
        { part: '上臂', type: 'PP', left: '', right: '', rightLegPp: 'right', leftLegPp: 'left' },
        { part: '脚踝', type: 'SBP', left: '', right: '', rightFootSbp: 'right', leftFootSbp: 'left' },
        { part: '脚踝', type: 'MBP', left: '', right: '', rightFootMbp: 'right', leftFootMbp: 'left' },
        { part: '脚踝', type: 'DBP', left: '', right: '', rightFootDbp: 'right', leftFootDbp: 'left' },
        { part: '脚踝', type: 'PP', left: '', right: '', rightFootPp: 'right', leftFootPp: 'left' }
      ],
      resultData: [
        { type: '踝臂指数ABI', left: '', right: '', rightAbi: 'right', leftAbi: 'left' },
        { type: '臂踝指数BAI', left: '', right: '', rightBai: 'right', leftBai: 'left' },
        { type: '脉搏波传导速度PWV', left: '', right: '', rightPwv: 'right', leftPwv: 'left' }
      ]
    }
  },
  methods: {
    showDeviceCode,
    getDeviceCode,
    getSourceData() {
      return this.$route.path === '/receptionCenter/patientReception'
        ? this.$store.getters.complicationsScreeningData
        : this.$store.state.patientExamination.patientExaminationData
    },
    initData(data) {
      this.pressureData = mapDataToTable(data, cloneDeep(this.pressureData))
      this.resultData = mapDataToTable(data, cloneDeep(this.resultData))
      this.form = {
        arteriosclerosisResult: data.arteriosclerosisResult,
        arteriosclerosisFinding: data.arteriosclerosisFinding,
        arteriosclerosisSuggest: data.arteriosclerosisSuggest,
        attachmentPhotoUrl: data.attachmentPhotoUrl,
        id: data.id
      }
    },
    pressureSpanMethod({ rowIndex, columnIndex }) {
      // 合并“位置”列（第一列）
      if (columnIndex === 0) {
        if (rowIndex === 0) {
          return { rowspan: 4, colspan: 1 } // 上臂占 4 行
        }
        if (rowIndex === 4) {
          return { rowspan: 4, colspan: 1 } // 脚踝占 4 行
        }
        return { rowspan: 0, colspan: 0 } // 其余不显示
      }
    },
    async handleSave() {
      const pressureData = this.extractTableData(this.pressureData).reduce((acc, curr) => {
        acc = Object.assign(acc, curr)
        return acc
      }, {})
      const resultData = this.extractTableData(this.resultData).reduce((acc, curr) => {
        acc = Object.assign(acc, curr)
        return acc
      }, {})
      const result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          ...pressureData,
          ...resultData,
          ...this.form,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    },
    extractTableData(data, mappingFields = ['right', 'left']) {
      return data.map((item) => {
        const result = {}
        for (const key in item) {
          if (key.startsWith('right') && mappingFields.includes(item[key])) {
            result[key] = item[item[key]]
          }
          if (key.startsWith('left') && mappingFields.includes(item[key])) {
            result[key] = item[item[key]]
          }
        }
        return result
      })
    }
  }
}
</script>

<style scoped lang="scss">
.device-barcode {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.arteriosclerosis-content {
  display: flex;
  justify-content: space-between;
  gap: 50px;
}
.arteriosclerosis-content-data {
  width: 60%;
}
.arteriosclerosis-content-img {
  margin-bottom: 16px;
  flex: 1;
}
</style>
