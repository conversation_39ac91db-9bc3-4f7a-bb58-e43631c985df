<template>
  <div class="ProTag">
    <span v-if="tipsArr.length === 0">-</span>
    <div v-if="tipsArr.length <= 3 && tipsArr.length > 0">
      <el-tag v-for="(i, ind) in tipsArr" :key="ind" type="danger">{{ i }} </el-tag>
    </div>
    <el-tooltip v-else effect="dark" :content="tips" placement="top">
      <div class="text_overflow_1">
        <el-tag v-for="(i, ind) in tipsArr" :key="ind" type="danger">{{ i }} </el-tag>
      </div>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'ProTag',
  props: {
    tipsArr: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  computed: {
    tips() {
      return this.tipsArr.toString()
    }
  }
}
</script>
<style scoped lang="scss">
// .ProTag {
//   .text_overflow_1 {
//     white-space: nowrap;
//     text-overflow: unset;
//     -o-text-overflow: unset;
//     overflow: unset;
//   }
// }
</style>
