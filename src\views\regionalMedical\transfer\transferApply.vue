<!-- eslint-disable vue/html-self-closing -->
<!-- eslint-disable max-len -->
<!-- eslint-disable vue/first-attribute-linebreak -->
<!-- eslint-disable vue/order-in-components -->
<!-- eslint-disable vue/order-in-components -->
<!-- eslint-disable vue/order-in-components -->
<template>
  <div class="public_v2 bodyContainer flex_columnjustifyContentStart">
    <div class="ecgDetail_data public_boxShadow bgColor_FFF borderRadius6 public_width100">
      <el-form ref="ruleForm" :model="formData" :rules="rules" label-width="120px">
        <!-- ====== 患者信息 start ====== -->
        <div class="transferDetail_moudle">
          <label class="color1C1F2E fontSize_16">患者信息</label>

          <el-row>
            <el-col :span="6">
              <el-form-item label="患者姓名：" prop="patientId">
                <el-select
                  v-if="!disabledSubmit"
                  v-model="formData.patientId"
                  filterable
                  remote
                  :remote-method="getPatientListFn"
                  placeholder="请输入患者姓名"
                  style="width: 100%"
                  :loading="remoteLoading"
                  clearable
                  :disabled="disabledSubmit"
                  @change="changePatient"
                >
                  <el-option
                    v-for="user of patientList"
                    :key="'enum-' + user.id"
                    :label="`${user.name}（${user.originalIdCard ? user.originalIdCard.slice(-4) : '--'}）`"
                    :value="user.id"
                  />
                </el-select>
                <el-input v-else v-model="formData.patientName" style="width: 100%" disabled />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="手机号码：" prop="originalPhone">
                <el-input
                  v-model="formData.originalPhone"
                  placeholder="请输入手机号码"
                  maxlength="11"
                  :disabled="disabledSubmit"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="身份证号码：" prop="originalIdCard">
                <el-input
                  v-model="formData.originalIdCard"
                  placeholder="请输入身份证号码"
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="性 别：" prop="sex">
                <el-select v-model="formData.sex" filterable placeholder="请选择性别" style="width: 100%" disabled>
                  <el-option v-for="item of sexList" :key="item.value" :label="item.txt" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="年龄：" prop="age">
                <el-input
                  v-model="formData.age"
                  placeholder="请输入年龄"
                  type="number"
                  max="150"
                  min="0"
                  disabled
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="家庭住址：" prop="address">
                <el-input
                  v-model="formData.address"
                  placeholder="请输入家庭现住址"
                  :disabled="disabledSubmit"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- ====== 患者信息 end ====== -->
        <!-- ====== 转诊信息 start ====== -->
        <div class="transferDetail_moudle marginTop18">
          <label class="color1C1F2E fontSize_16"> 转诊信息</label>

          <el-row>
            <el-col :span="6">
              <el-form-item label="申请类型：" prop="referralType">
                <el-select
                  v-model="formData.referralType"
                  filterable
                  placeholder="请选择申请类型"
                  :disabled="disabledSubmit"
                  style="width: 100%"
                  @change="changeReferral"
                >
                  <el-option v-for="item in referralTypeList" :key="item.value" :label="item.txt" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item v-if="formData.referralType === 1" label="转诊建议：" prop="treatSuggest">
                <el-select
                  v-model="formData.treatSuggest"
                  filterable
                  placeholder="请选择转诊建议"
                  :disabled="disabledSubmit"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in referralAdviceList"
                    :key="item.value"
                    :label="item.txt"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="转入单位：" prop="inDepartCode">
                <el-cascader
                  ref="tree"
                  v-model="formData.inDepartCode"
                  :options="serviceStationTree"
                  :props="{
                    label: 'departName',
                    children: 'children',
                    value: 'departCode',
                    checkStrictly: true,
                    emitPath: false
                  }"
                  :show-all-levels="false"
                  filterable
                  placeholder="请选择转入单位"
                  :disabled="!formData.referralType || disabledSubmit"
                  style="width: 100%"
                  @change="getmanageDoctorFun"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="接诊医生：" prop="treatDoctorId">
                <el-select
                  v-model="formData.treatDoctorId"
                  filterable
                  placeholder="请选择接诊医生"
                  :disabled="!formData.inDepartCode || disabledSubmit"
                  :loading="treatDoctorLoading"
                  :loading-text="'加载中...'"
                  style="width: 100%"
                  @change="changeTreatDoctor"
                >
                  <el-option v-for="item in medicalDoctorList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="转入时间：" prop="planDate">
                <el-date-picker
                  v-model="formData.planDate"
                  type="date"
                  placeholder="请选择转入时间"
                  prefix-icon="el-icon-date"
                  value-format="yyyy-MM-dd"
                  format="yyyy-MM-dd"
                  :clearable="false"
                  :picker-options="pickerOptions"
                  :disabled="disabledSubmit"
                  style="width: 100%"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item v-if="formData.referralType === 2" label="转出原因：" prop="outReason">
                <el-select
                  v-model="formData.outReason"
                  filterable
                  placeholder="请选择转出原因"
                  :disabled="disabledSubmit"
                  style="width: 100%"
                >
                  <el-option v-for="item in outReasonList" :key="item.value" :label="item.txt" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item v-if="formData.referralType === 2" label="诊断结果" prop="diagnosticResult">
                <el-input
                  v-model="formData.diagnosticResult"
                  placeholder="请输入诊断结果"
                  :disabled="disabledSubmit"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="转诊医生：" prop="outDoctorName">
                <span class="color3C405C public_lineHeight36 el_form_item_span">{{ formData.outDoctorName }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="医生电话：" prop="outDoctorPhone">
                <span class="color3C405C public_lineHeight36 el_form_item_span">
                  {{ formData.outDoctorPhone }}
                </span>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="转出单位：" prop="outDepartName">
                <span class="color3C405C public_lineHeight36 el_form_item_span">{{ formData.outDepartName }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="20">
              <el-form-item
                :label="`${formData.referralType === 2 ? '主要检查结果：' : '主要现病史(转出原因)'}`"
                prop="mainCheckResult"
                class="label-top-form-item"
              >
                <el-input
                  v-model="formData.mainCheckResult"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  :placeholder="formData.referralType === 2 ? '请输入主要检查结果' : '请输入主要现病史(转出原因)'"
                  :disabled="disabledSubmit"
                />
              </el-form-item>
            </el-col>

            <el-col :span="20">
              <el-form-item
                v-if="formData.referralType === 1"
                label="初步印象："
                prop="firstImpression"
                class="label-top-form-item"
              >
                <el-input
                  v-model="formData.firstImpression"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  placeholder="请输入对患者病情作出的初步判断"
                  :disabled="disabledSubmit"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="20">
              <el-form-item
                v-if="formData.referralType === 1"
                label="主要既往史："
                prop="mainJws"
                class="label-top-form-item"
              >
                <el-input
                  v-model="formData.mainPh"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  placeholder="请输入患者患者既往存在的主要疾病史"
                  :disabled="disabledSubmit"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="20">
              <el-form-item
                label="治疗经过、下一步治疗方案及康复建议："
                prop="nextTreatPlan"
                class="label-top-form-item"
              >
                <el-input
                  v-model="formData.nextTreatPlan"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  placeholder="请输入治疗经过、下一步治疗方案及康复建议"
                  :disabled="disabledSubmit"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="20">
              <el-form-item label="附件(如检验检查报告等)：" prop="userName" class="label-top-form-item">
                <custom-upload v-model="formData.attachmentPhotoUrl" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <!-- ====== 转诊信息 end ====== -->
      </el-form>
      <div class="drug_rightForm_btns flex_center">
        <el-button class="public_dialogButton2 color333 fontSize_14 bgColor_FFF flex_center" @click="goBack">
          取消
        </el-button>
        <el-button
          v-if="!disabledSubmit"
          class="public_dialogButton colorFFF fontSize_14 bgColor_42C9A300B2DC flex_center"
          @click="submitReferralRecord"
        >
          提交
        </el-button>
      </div>
    </div>
  </div>
</template>

<script type="text/javascript" src="./js/transferApply.js"></script>

<style lang="scss" scoped>
@import './css/detail.scss';

@import '@/styles/form-overrides.scss';

/* 自定义上下结构表单项样式 */
.label-top-form-item {
  ::v-deep .el-form-item__label {
    float: none !important;
    text-align: left !important;
    line-height: 1.5 !important;
    width: 100% !important;
  }

  ::v-deep .el-form-item__content {
    margin-left: 0 !important;
    width: 100% !important;
  }
}
</style>
