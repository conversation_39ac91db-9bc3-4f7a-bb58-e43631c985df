<template>
  <div>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="模板名称：" prop="moduleName">
            <el-input v-model="form.moduleName" placeholder="请输入模板名称" :disabled="disabledSubmit" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属机构：" prop="departCode">
            <TreeSelect
              v-model="form.departCode"
              :data="departTree"
              :props="{
                children: 'children',
                label: 'departName',
                value: 'departCode'
              }"
              placeholder="请选择"
              :disabled="disabledSubmit"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="模板有效期：" prop="validDateRange">
            <el-date-picker
              v-model="form.validDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              placeholder="请选择"
              style="width: 100%"
              :disabled="disabledSubmit"
            />
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item label="模板内容：" prop="moduleContent">
            <RichText v-if="addDialogVisible" ref="richText" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { localCache } from '@/utils/cache'
import { encodeToBase64, decodeFromBase64 } from '@/utils'
import { addProtocolTemplate } from '@/api/protocolTemplate'
import TreeSelect from '@/components/TreeSelect/index.vue'
import RichText from '@/components/RichText/RichText.vue'

export default {
  name: 'AddForm',
  components: {
    TreeSelect,
    RichText
  },
  props: {
    departTree: {
      type: Array,
      default: () => []
    },
    addDialogVisible: {
      type: Boolean,
      default: false
    },
    disabledSubmit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        id: '',
        moduleName: '',
        departCode: localCache.getCache('userInfo').departCode,
        validDateRange: []
      },
      rules: {
        moduleName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
        departCode: [{ required: true, message: '请选择所属机构', trigger: 'blur' }],
        validDateRange: [{ required: true, message: '请选择模板有效期', trigger: 'blur' }]
      }
    }
  },
  methods: {
    initRichText() {
      this.form.moduleContent = ''
      this.form.validDateRange = []
      this.$refs.richText.initEditor()
    },

    // 填充表单数据通用方法
    fillFormData(row) {
      this.form = {
        id: row.id,
        moduleName: row.moduleName,
        departCode: row.departCode,
        validDateRange: [row.validStartDate, row.validEndDate],
        moduleContent: decodeFromBase64(row.moduleContent)
      }
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$refs.richText) {
            this.$refs.richText.initEditor()
            this.$refs.richText.editor.txt.html(this.form.moduleContent)
            if (this.disabledSubmit) {
              this.$refs.richText.editor.disable()
            }
          }
        }, 100)
      })
    },

    handleDetail(row) {
      this.fillFormData(row)
    },

    handleEdit(row) {
      this.fillFormData(row)
    },

    // 保存
    async handleSave() {
      try {
        // 表单验证
        await this.$refs.formRef.validate()

        // 获取富文本内容并编码
        const hasEditor = this.$refs.richText.editor
        if (hasEditor) {
          this.form.moduleContent = encodeToBase64(this.$refs.richText.editor.txt.html())
        }

        // 构建参数对象，处理日期范围和部门代码
        const [validStartDate = '', validEndDate = ''] = this.form.validDateRange || []

        const params = {
          ...this.form,
          validStartDate,
          validEndDate
        }

        await addProtocolTemplate(params)
        this.$emit('success')
      } catch (error) {
        console.error('保存模板失败:', error)
        this.$message.error('保存失败，请检查表单填写是否正确')
      }
    }
  }
}
</script>
