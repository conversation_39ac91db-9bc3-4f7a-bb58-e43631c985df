<!-- 封面内容 -->
<template>
  <div class="news-coverage">
    <div class="news-coverage-title">{{ patientInfo.departName }}</div>
    <div class="news-coverage-subtitle">慢病健康管理档案</div>
    <img src="@/assets/cspImg/reportImg.png" style="width: 100%; height: 450px" alt="封面">
    <div class="news-coverage-info">
      <div class="news-coverage-info-item">
        <div class="news-coverage-info-item-label">姓名：</div>
        <div class="news-coverage-info-item-value">{{ patientInfo.name }}</div>
      </div>
      <div class="news-coverage-info-item">
        <div class="news-coverage-info-item-label">性别：</div>
        <div class="news-coverage-info-item-value">{{ genderTransform(patientInfo.sex) }}</div>
      </div>
      <div class="news-coverage-info-item">
        <div class="news-coverage-info-item-label">年龄：</div>
        <div class="news-coverage-info-item-value">{{ patientInfo.age }}</div>
      </div>
      <div class="news-coverage-info-item">
        <div class="news-coverage-info-item-label">慢病病种：</div>
        <div class="news-coverage-info-item-value">
          {{ patientInfo.diseaseList && patientInfo.diseaseList.join('、') }}
        </div>
      </div>

      <div class="news-coverage-info-item">
        <div class="news-coverage-info-item-label">管理医生：</div>
        <div class="news-coverage-info-item-value">{{ patientInfo.manageDoctorName }}</div>
      </div>
      <div class="news-coverage-info-item">
        <div class="news-coverage-info-item-label">报告日期：</div>
        <div class="news-coverage-info-item-value">{{ getReportDate() }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import { genderTransform } from '@/utils/cspUtils'
import dayjs from 'dayjs'

export default {
  name: 'NewsCoverage',
  props: {
    patientInfo: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    genderTransform,
    getReportDate() {
      return dayjs(this.patientInfo.reportDate).format('YYYY-MM-DD')
    }
  }
}
</script>

<style scoped lang="scss">
.news-coverage {
  .news-coverage-title {
    font-size: 22px;
    font-weight: 500;
    text-align: center;
  }
  .news-coverage-subtitle {
    font-size: 16px;
    text-align: center;
    margin: 10px 0;
  }
  .news-coverage-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 16px;
    margin: 30px 0;
    font-size: 18px;
    width: 65%;
    margin: 30px auto;
    .news-coverage-info-item {
      display: flex;
      margin-bottom: 20px;
      .news-coverage-info-item-label {
        width: 100px;
        text-align: right;
        white-space: nowrap;
      }
      .news-coverage-info-item-value {
        flex: 1;
        text-align: center;
        margin-left: 10px;
        border-bottom: 1px solid #000;
        padding-left: 10px;
      }
    }
  }
}
</style>
