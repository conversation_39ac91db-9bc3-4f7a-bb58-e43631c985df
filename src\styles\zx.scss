body {
  /*修改滚动条样式 start*/

  *.pageListOh::-webkit-scrollbar {
    width: 0.35rem;
    height: 0.5rem;
  }

  *::-webkit-scrollbar {
    width: 0.35rem;
    height: 0.35rem;
  }

  *::-webkit-scrollbar-track {
    background: #f3f5f8;
    cursor: pointer;
    border-radius: 2px;
  }

  *::-webkit-scrollbar-thumb {
    background: #ccd0da;
    border-radius: 0.5rem;
    cursor: pointer;
  }

  *::-webkit-scrollbar-thumb:hover {
    background: #b2b6c0;
    cursor: pointer;
  }

  *::-webkit-scrollbar-corner {
    background: #f3f5f8;
  }
  .cp {
    cursor: pointer;
  }
  .pr {
    position: relative;
  }
  .pa {
    position: absolute;
  }
  .crumbDiv {
  }
  .mySelectPop {
    padding: 0;
    .mySelectDiv {
      max-height: 30vh;
      overflow: auto;
      .list {
        padding: 0.5rem;
        cursor: pointer;
        &:hover {
          background: #dff2ef;
          color: #000;
        }
      }
    }
  }
  .followupsDiv {
    .el-table__expand-icon {
      position: absolute;
      left: 5px;
      font-size: 0.8rem;
      margin-top: 0.5rem;
      width: 1rem;
      line-height: 1rem;
      height: 1rem;
    }
  }
  .el-date-editor .el-range-separator {
    font-size: 0.6rem;
    color: #959292;
  }
  .el-loading-mask {
    .el-loading-mask {
      display: none;
    }
    &::after {
      content: '';
      display: inline-block;
      background-image: url(../assets/img/loading.png);
      background-size: 100%;
      position: absolute;
      left: calc(50% - 2.5rem);
      top: calc(50% - 2.5rem);
      width: 5rem;
      height: 5rem;
      transform-origin: 50% 50%;
      animation: loading-ele 2s linear infinite;
    }
    background-color: rgba(0, 0, 0, 0);
    .el-loading-spinner {
      svg {
        display: none;
      }
    }
  }
  @keyframes loading-ele {
    0% {
      transform: scale(1) rotate(0);
    }
    50% {
      transform: scale(0.1) rotate(180deg);
    }
    100% {
      transform: scale(1) rotate(360deg);
    }
  }
}
.doctorPopDivs {
  height: 6.5rem !important;
  .doctorInfoPop {
    height: 100%;
    width: 100%;
    overflow: auto;
    .doctorInfoMain {
      display: flex;

      .doctorInfoImg {
        width: 3.85rem;
        height: 5.2rem;
      }
      .doctorInfoTxt {
        .doctorInfoNameTxt {
          font-size: 0.8rem;
          font-weight: 500;
          p + p {
            margin-top: 6rem;
          }
        }
        padding: 0 1rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 0.7rem;
        color: #3e425d;
        line-height: 20px;
        .val {
          color: #181a26;
        }
      }
    }
  }
}
