<template>
  <div>
    <div class="print-btn-container">
      <el-button v-print="printOptions" type="primary">打印</el-button>
    </div>
    <div id="print-guidance-sheet" ref="print" class="print-guidance-sheet">
      <h2 v-if="$route.query.menu === 'patientReception' && $route.query.active.toString() === '1'">
        高危筛查服务引导单
      </h2>
      <h2 v-if="$route.query.menu === 'patientReception' && $route.query.active.toString() === '3'">
        并发症筛查服务引导单
      </h2>
      <h2 v-if="$route.query.menu === 'patientExamination'">检验检查服务引导单</h2>

      <!-- 基本信息 -->
      <div class="section">
        <div class="section-title">基本信息</div>
        <div class="basic-info">
          <div class="info-row">
            <div class="info-item">
              <span class="label">姓名：</span>
              <span class="value">{{
                $route.query.menu === 'patientReception'
                  ? receptionWorkbenchData.patientName
                  : patientExaminationData.patientName
              }}</span>
            </div>
            <div class="info-item">
              <span class="label">性别：</span>
              <span class="value">{{
                $route.query.menu === 'patientReception'
                  ? genderTransform(receptionWorkbenchData.sex)
                  : genderTransform(patientExaminationData.sex)
              }}</span>
            </div>
            <div class="info-item">
              <span class="label">年龄：</span>
              <span class="value">{{
                $route.query.menu === 'patientReception' ? receptionWorkbenchData.age : patientExaminationData.age
              }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item address full-width">
              <span class="label">家庭住址：</span>
              <span class="value">{{
                $route.query.menu === 'patientReception'
                  ? receptionWorkbenchData.address
                  : patientExaminationData.address
              }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 检查列表 -->
      <div class="section">
        <div class="section-title">检查列表</div>
        <div class="check-list">
          <div v-for="(item, index) in itemList()" :key="index" class="check-item">
            <div class="check-left">
              <span class="check-title">检查类别: {{ item }}</span>
              <div class="check-status">
                <span>是否完成:</span>
                <input type="checkbox" class="checkbox" disabled>
              </div>
            </div>
            <div class="item-barcode">
              <Barcode :code="specialProject(item, $route.query.archiveId)" />
            </div>
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="footer">
        <div class="footer-info">
          <div>
            医生姓名：{{
              $route.query.menu === 'patientReception'
                ? receptionWorkbenchData.receptionDoctorName
                : patientExaminationData.doctorName
            }}
          </div>
          <div>
            检查机构:
            {{
              $route.query.menu === 'patientReception'
                ? receptionWorkbenchData.departName
                : patientExaminationData.departName
            }}
          </div>
          <div>开单日期：{{ currentDate() }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions, mapState } from 'vuex'
import { tabList } from '../patientReception/component/complicationsScreening'
import { getProjectData } from '@/api/examination'
import { genderTransform } from '@/utils/cspUtils'
import Barcode from '@/components/barcode/barcode.vue'
import dayjs from 'dayjs'

export default {
  name: 'PrintGuidanceSheet',
  components: {
    Barcode
  },
  data() {
    return {
      printOptions: {
        id: '#print-guidance-sheet'
      },
      highRiskScreeningProject: {
        tnb: {
          1: '指尖血糖',
          2: '糖化血红蛋白',
          3: 'OGTT'
        },
        gxy: ['24小时动态血压'],
        COPD: ['肺功能仪监测', '动脉血气', '肺部CT'],
        fangchan: ['心电图']
      },
      CheckprojectData: []
    }
  },
  computed: {
    ...mapState('receptionWorkbench', ['receptionWorkbenchData', 'complicationsScreeningData']),
    ...mapState('patientExamination', ['patientExaminationData'])
  },
  async created() {
    if (this.$route.query.menu === 'patientReception') {
      await this.getReceptionWorkbenchById({ id: this.$route.query.id })

      if (this.$route.query.active.toString() === '1') {
        this.highRiskScreeningProjectList()
      } else if (this.$route.query.active.toString() === '3') {
        this.complicationsScreeningProject()
      }
    }

    if (this.$route.query.menu === 'patientExamination') {
      await this.getPatientExaminationData({ id: this.$route.query.id })
      getProjectData({ id: this.$route.query.id }).then((res) => {
        this.CheckprojectData = res.data.itemList
      })
    }
  },
  methods: {
    genderTransform,
    ...mapActions('receptionWorkbench', ['getReceptionWorkbenchById']),
    ...mapActions('patientExamination', ['getPatientExaminationData']),
    currentDate() {
      return dayjs().format('YYYY-MM-DD HH:mm')
    },
    highRiskScreeningProjectList() {
      const bloodSugarScreening = JSON.parse(sessionStorage.getItem('bloodSugarScreening'))
      const result = ['人体成分']
      Object.keys(this.highRiskScreeningProject).forEach((key) => {
        if (this.receptionWorkbenchData.disease && this.receptionWorkbenchData.disease.includes(key)) {
          if (key === 'tnb') {
            bloodSugarScreening.forEach((item) => {
              result.push(this.highRiskScreeningProject[key][item])
            })
          } else {
            result.push(...this.highRiskScreeningProject[key])
          }
        }
      })
      return result
    },
    complicationsScreeningProject() {
      const result = []
      const tnbItemList = this.complicationsScreeningData.tnbItemList || []
      const gxyItemList = this.complicationsScreeningData.gxyItemList || []
      const fangchanItemList = this.complicationsScreeningData.fcItemList || []

      tnbItemList.concat(gxyItemList, fangchanItemList).forEach((item) => {
        const tabItem = tabList.find((tab) => tab.value === item.itemCode)
        if (tabItem) result.push(tabItem.label)
      })

      return result
    },
    checkProjectData() {
      return this.CheckprojectData.map((item) => {
        const tabItem = tabList.find((tab) => tab.value === item.itemCode)
        return tabItem ? tabItem.label : ''
      })
    },
    itemList() {
      const { menu, active } = this.$route.query
      if (menu === 'patientReception' && active === '1') return this.highRiskScreeningProjectList()
      if (menu === 'patientExamination') return this.checkProjectData()
      return this.complicationsScreeningProject()
    },
    specialProject(item, id) {
      if (item.includes('超声心动图')) return `${id}1`
      if (item.includes('颈动脉超声')) return `${id}2`
      return id
    }
  }
}
</script>

<style lang="scss" scoped>
.print-btn-container {
  margin: 20px;
  text-align: right;
}

.print-guidance-sheet {
  width: 210mm;
  min-height: 297mm;
  padding: 20mm;
  margin: 0 auto;
  background-color: white;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  box-sizing: border-box;
}

h2 {
  text-align: center;
  margin-bottom: 30px;
}

.section {
  margin-bottom: 15px;
}

.section-title {
  color: #000;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 15px;
  position: relative;
  padding-bottom: 10px;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: repeating-linear-gradient(to right, #000 0%, #000 50%, transparent 50%, transparent 100%);
    background-size: 8px 1px;
  }
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  page-break-inside: avoid;
}

.info-item {
  display: flex;
  margin-right: 20px;

  &.id-card,
  &.address {
    flex: 2;
  }

  &.phone {
    flex: 1;
  }

  &.full-width {
    flex: 1;
    width: 100%;
  }

  .label {
    color: #333;
    white-space: nowrap;
  }

  .value {
    color: #666;
  }
}

.check-list {
  border-top: 1px solid #eee;

  .check-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-bottom: 1px dashed #ddd;
    page-break-inside: avoid;
  }

  .check-left {
    display: flex;
    flex-direction: column;
  }

  .check-title {
    font-weight: 600;
    color: #333;
    font-size: 15px;
    margin-bottom: 6px;
  }

  .check-status {
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #666;
  }

  .item-barcode {
    display: flex;
    justify-content: flex-end;
    margin-right: 5px;

    img {
      height: 45px;
    }
  }

  .checkbox {
    margin-left: 5px;
    position: relative;
    top: 1px;
  }
}

.footer {
  text-align: right;

  .footer-info {
    > div {
      margin-bottom: 10px;
    }
    page-break-inside: avoid;
  }
}

@media print {
  .print-btn-container {
    display: none;
  }

  html,
  body {
    margin: 0;
    background: #fff;
  }

  .print-guidance-sheet {
    box-shadow: none;
    margin: 0;
    padding: 15mm;
    width: 100% !important;
    min-height: auto !important;
    transform: none !important;
    page-break-after: auto;
    page-break-inside: avoid;
  }

  /* 分页控制 */
  h1,
  .section,
  .footer {
    page-break-before: auto;
    page-break-after: avoid;
    page-break-inside: avoid;
  }

  .check-item,
  .info-row,
  .footer-info {
    page-break-inside: avoid;
  }

  @page {
    size: A4;
    margin: 0;
  }
}
</style>
