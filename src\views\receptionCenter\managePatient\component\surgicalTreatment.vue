<!-- 手术治疗 -->
<template>
  <div v-loading="$store.state.managePatient.loading" class="manage-by-objectives">
    <el-form ref="formRef" :model="form" label-width="100px">
      <el-form-item label="手术治疗：" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入手术治疗" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'SurgicalTreatment',
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        description: ''
      }
    }
  },
  methods: {
    initData(data) {
      this.form = data || {}
    },
    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: true,
        data: {
          data: {
            id: this.form.id,
            description: this.form.description
          },
          itemCode: this.itemTemp.value,
          taItemId: this.$store.getters.therapeuticActionDetail.itemList.find((it) => it.itemCode === this.itemTemp.value)
            .taItemId
        }
      }

      return result
    }
  }
}
</script>
