<!-- 筛查统计 -->
<template>
  <div class="screening-statistics">
    <el-card class="massScreening-search">
      <SearchForm ref="searchForm" :query-params="queryParams" @search="handleSearch" @reset="handleReset" />
    </el-card>
    <el-card class="screening-statistics-table">
      <el-table v-loading="loading" :data="tableData" style="width: 100%; table-layout: fixed">
        <template v-for="(column, index) in tableColumns">
          <el-table-column
            v-if="!column.children"
            :key="index"
            :label="column.label"
            :prop="column.prop"
            align="center"
          />

          <el-table-column v-else :key="index" :label="column.label" align="center">
            <el-table-column
              v-for="(child, cIndex) in column.children"
              :key="`${index}-${cIndex}`"
              :label="child.label"
              :prop="child.prop"
              :width="child.width"
              align="center"
            >
              <template #default="{ row }">
                <span :class="row[child.prop] > 0 && row.departName !== '总计' ? 'table-cell-value' : ''">
                  {{ row[child.prop] }}
                </span>
              </template>
            </el-table-column>
          </el-table-column>
        </template>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getScreeningStatistics } from '@/api/statisticalReport'
import { localCache } from '@/utils/cache'
import SearchForm from '../component/searchForm.vue'

export default {
  name: 'ScreeningStatistics',
  components: {
    SearchForm
  },
  data() {
    return {
      queryParams: {
        timeRange: [],
        disease: 'tnb',
        departCode: localCache.getCache('userInfo').departCode || ''
      },
      tableData: [],
      loading: false
    }
  },
  computed: {
    tableColumns() {
      const { disease } = this.queryParams
      const base = [
        {
          label: '机构名称',
          prop: 'departName'
        }
      ]

      const configs = {
        tnb: [
          {
            label: '一筛统计',
            children: [
              { label: '总人数', prop: 'firstCount' },
              { label: '无糖尿病人数', prop: 'firstNoTnbCount' },
              { label: '糖尿病前期人数', prop: 'firstProTnbCount' },
              { label: '糖尿病人数', prop: 'firstTnbCount' }
            ]
          },
          {
            label: '二筛统计',
            children: [
              { label: '糖尿病并发症筛查人数', prop: 'secondTnbCount' },
              { label: '糖尿病并发症筛查完成人数', prop: 'secondTnbCompleteCount', width: 200 }
            ]
          }
        ],
        gxy: [
          {
            label: '一筛统计',
            children: [
              { label: '总人数', prop: 'firstCount' },
              { label: '无高血压人数', prop: 'firstNoGxyCount' },
              { label: '高血压前期人数', prop: 'firstProGxyCount' },
              { label: '高血压人数', prop: 'firstGxyCount' }
            ]
          },
          {
            label: '二筛统计',
            children: [
              { label: '高血压并发症筛查人数', prop: 'secondGxyCount' },
              { label: '高血压并发症筛查完成人数', prop: 'secondGxyCompleteCount', width: 200 }
            ]
          }
        ],
        COPD: [
          {
            label: '一筛统计',
            children: [
              { label: '总人数', prop: 'firstCount' },
              { label: '无慢阻肺人数', prop: 'firstNoCopdCount' },
              { label: '慢阻肺人数', prop: 'firstCopdCount' }
            ]
          }
        ],
        fangchan: [
          {
            label: '一筛统计',
            children: [
              { label: '总人数', prop: 'firstCount' },
              { label: '无房颤人数', prop: 'firstNoFcCount' },
              { label: '房颤人数', prop: 'firstFcCount' }
            ]
          },
          {
            label: '二筛统计',
            children: [
              { label: '房颤并发症筛查人数', prop: 'secondFcCount' },
              { label: '房颤并发症筛查完成人数', prop: 'secondFcCompleteCount', width: 200 }
            ]
          }
        ]
      }

      return base.concat(configs[disease] || [])
    }
  },

  watch: {
    'queryParams.disease': {
      handler(newVal) {
        this.tableData = []
      }
    }
  },
  created() {
    this.getTableList(this.queryParams)
  },
  methods: {
    // 筛查统计
    async getTableList(params) {
      this.loading = true
      try {
        const { timeRange, ...rest } = params || {}
        const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

        const queryParams = {
          ...rest,
          startDate,
          endDate
        }
        const res = await getScreeningStatistics(queryParams)
        if (res.code === 200) {
          this.tableData = res.data || []
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },

    handleSearch() {
      this.getTableList(this.queryParams)
    },

    handleReset() {
      this.queryParams = {
        timeRange: [],
        disease: 'tnb',
        departCode: localCache.getCache('userInfo').departCode || ''
      }
      this.getTableList(this.queryParams)
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';

.screening-statistics {
  padding: 16px;
  .massScreening-search {
    margin-bottom: 20px;
  }
  ::v-deep thead {
    height: 60px;
  }
  .table-cell-value {
    // color: #0a86c8;
    cursor: pointer;
  }
}
</style>
