<!-- 餐后2h尿糖 -->
<template>
  <div class="postprandial-two-sugar">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="130px">
      <el-form-item label="尿糖（定性）：" prop="chTwoUrineSugar">
        <el-radio-group v-model="form.chTwoUrineSugar">
          <el-radio :label="1">阴性</el-radio>
          <el-radio :label="2">阳性</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'PostprandialTwoSugar',
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        chTwoUrineSugar: ''
      },
      rules: {
        chTwoUrineSugar: [{ required: true, message: '请选择尿糖（定性）' }]
      }
    }
  },
  methods: {
    initData(data) {
      this.form = {
        chTwoUrineSugar: data.chTwoUrineSugar,
        id: data.id
      }
    },
    async handleSave() {
      const result = {
        name: `${this.itemTemp.label}`,
        success: false,
        data: {
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id,
          chTwoUrineSugar: this.form.chTwoUrineSugar
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    }
  }
}
</script>
