<template>
  <div class="relevance">
    <div class="left">
      <p>村医列表</p>
      <div class="inputbox">
        <el-input
          v-model="consultant"
          placeholder="输入姓名搜索"
          prefix-icon="el-icon-search"
          size="mini"
          clearable
          @change="getConsultantList"
        />
      </div>
      <el-scrollbar style="height: calc(100% - 115px)">
        <div
          v-for="i in consultantList"
          :key="i.id"
          class="item"
          :class="{ active: i.id == consultantId }"
          @click="consultantId = i.id"
        >
          {{ i.name }}
        </div>
      </el-scrollbar>
    </div>

    <div class="right">
      <p>关联医生 <el-button type="primary" :disabled="consultantId == 0" @click="saverelation">保存 </el-button></p>
      <div class="inputbox">
        <el-cascader
          v-model="filter.department"
          :options="treeData"
          size="mini"
          style="width: 30%; min-width: 200px; margin-right: 25px"
          :props="{ label: 'name', children: 'children', value: 'id' }"
          clearable
          @change="getDoctorList"
        />
        <el-input
          v-model="filter.name"
          placeholder="输入姓名搜索"
          prefix-icon="el-icon-search"
          size="mini"
          clearable
          style="width: 30%; min-width: 200px"
          @change="getDoctorList"
        />
      </div>
      <el-scrollbar style="height: calc(100% - 130px)">
        <el-checkbox-group v-model="relationDoctor">
          <el-checkbox
            v-for="i in doctorList"
            :key="i.id"
            class="item"
            :label="i.id"
          >{{ i.name }}<span v-if="relationDoctorArr.includes(i.id)"> <i /> 已关联</span></el-checkbox>
        </el-checkbox-group>
        <!-- <div class="item" v-for="i in doctorList" :key="i.id" :class="{ 'active': i.id == doctorId }"
          @click="doctorId = i.id">
          {{ i.name }}
          <span v-if="relationDoctor.includes(i.id * 1)"> <i></i> 已关联</span>
        </div> -->
      </el-scrollbar>
    </div>
  </div>
</template>
<script>
import { listByTypeApi, hospitalAllApi, userdoctorApi, relationAddApi } from '@/api/system'

export default {
  name: 'Relevance',
  data() {
    return {
      consultant: '',
      consultantId: 0,
      consultantList: [],
      doctorList: [],
      relationDoctor: [],
      relationDoctorArr: [],
      treeData: null,
      filter: {
        department: [],
        name: ''
      }
    }
  },
  watch: {
    consultantId(a, b) {
      this.getrelationDoctor(a)
    }
  },
  created() {
    this.getConsultantList()
    this.getDoctorList()
    this.getTreeData()
  },
  methods: {
    async getConsultantList() {
      const res = await listByTypeApi({ type: 2, name: this.consultant })
      this.consultantList = res.data
    },
    async getDoctorList() {
      const res = await listByTypeApi({
        type: 1,
        name: this.filter.name,
        departCode: this.filter.department[this.filter.department.length - 1]
      })
      this.doctorList = res.data
    },
    async getTreeData() {
      const res = await hospitalAllApi(1)
      this.treeData = this.treeDataChange(res.data)
    },
    treeDataChange(data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].children.length === 0) {
          delete data[i].children
        }
        if (data[i].children) {
          this.treeDataChange(data[i].children)
        }
      }
      return data
    },
    async getrelationDoctor(id) {
      const res = await userdoctorApi(id)
      this.relationDoctor = res.data
      this.relationDoctorArr = JSON.parse(JSON.stringify(res.data))
    },
    async saverelation() {
      const res = await relationAddApi({ sourceId: this.consultantId, targetIds: this.relationDoctor, type: 1 })
      if (`${res.code}` === '200') {
        if (this.relationDoctor.length) {
          this.$message.success('关联成功')
        } else {
          this.$message.success('取消关联成功')
        }
      }
      this.consultantId = 0
    }
  }
}
</script>
<style lang="scss" scoped>
.relevance {
  width: 97%;
  margin: 30px auto;
  background-color: #fff;
  border-radius: 15px;
  height: calc(100vh - 130px);
  padding: 15px;
  display: flex;
  justify-content: space-between;

  .left,
  .right {
    width: 21%;
    height: 100%;
    border: 1px solid #d9d9d9;
    border-radius: 15px;

    p {
      color: #333;
      font-weight: 600;
      margin: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .item {
      padding: 0 20px;
      height: 40px;
      border-radius: 10px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      display: flex;
      align-items: center;
      // justify-content: space-between;
      ::v-deep .el-checkbox__label {
        width: 100%;
        display: flex;
        justify-content: space-between;
        font-size: 0.6rem;
      }

      span {
        color: #999999;
        font-size: 0.7rem;
        display: flex;
        align-items: center;

        i {
          width: 6px;
          height: 6px;
          background-color: #67c23a;
          border-radius: 50%;
          margin-right: 5px;
        }
      }
    }

    .active {
      background-color: #ecf2ff;
    }

    .inputbox {
      padding: 0 15px 30px;
    }
  }

  .right {
    width: 78%;
  }
}
::v-deep .el-checkbox:last-of-type {
  margin-right: 30px;
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}
</style>
