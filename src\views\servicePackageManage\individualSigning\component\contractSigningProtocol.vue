<template>
  <div>
    <ProDialog :visible.sync="previewDialogVisible" title="签约协议" width="960px" top="10vh" append-to-body>
      <RichText v-if="previewContent" ref="richText" />
      <div id="printContainer" style="position: absolute; left: -9999px; visibility: hidden">
        <div class="print-content" />
      </div>
      <el-button v-print="printConfig" type="primary" size="small" style="margin-top: 10px">
        <i class="iconfont icon-print" /> 打印协议(A4)
      </el-button>
    </ProDialog>
  </div>
</template>

<script>
import RichText from '@/components/RichText/RichText.vue'
import ProDialog from '@/components/ProDialog/index.vue'
import { printConfig } from './printConfig'
import { decodeFromBase64 } from '@/utils'

export default {
  name: 'ContractSigningProtocol',
  components: {
    RichText,
    ProDialog
  },
  data() {
    return {
      previewDialogVisible: false,
      previewContent: '',
      printConfig
    }
  },
  methods: {
    // 预览模板
    previewTemplate(data) {
      const content = decodeFromBase64(data)
      this.previewContent = content
      this.previewDialogVisible = true

      this.$nextTick(() => {
        setTimeout(() => {
          const { richText } = this.$refs
          if (richText && richText.initEditor) {
            richText.initEditor()
            if (richText.editor) {
              richText.editor.txt.html(content)
              richText.editor.disable()
            }
          } else {
            console.warn('richText 编辑器未初始化')
          }

          // 更新打印内容
          this.updatePrintContent('#printContainer', '.print-content', content)
        }, 100)
      })
    },

    // 更新打印内容并格式化表格边框
    updatePrintContent(containerSelector, contentSelector, htmlContent) {
      const printContent = document.querySelector(`${containerSelector} ${contentSelector}`)
      if (!printContent) return false

      // 更新内容
      printContent.innerHTML = htmlContent

      // 设置表格边框样式
      const tables = printContent.querySelectorAll('table')
      if (tables.length > 0) {
        tables.forEach((table) => {
          table.setAttribute('border', '1')
          table.style.borderCollapse = 'collapse'

          // 设置单元格边框
          const cells = table.querySelectorAll('th, td')
          cells.forEach((cell) => {
            cell.style.border = '1px solid #000'
          })
        })
      }

      return true
    }
  }
}
</script>
