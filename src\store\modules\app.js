/* eslint-disable no-shadow */
import Cookies from 'js-cookie'

const state = {
  sidebar: {
    opened: Cookies.get('sidebarStatus') ? !!+Cookies.get('sidebarStatus') : true,
    withoutAnimation: false
  },
  device: 'desktop',
  bigFont: false,
  size: Cookies.get('size') || 'medium',
  fz: +sessionStorage.getItem('fz') || 20,
  willOverdueStatus: sessionStorage.getItem('willOverdueStatus') || '',
  healthUserNumber: 0,
  deviceConnect: false,
  replaceMenus: sessionStorage.getItem('replaceMenus') ? JSON.parse(sessionStorage.getItem('replaceMenus')) : null,
  remoteConsultationVisible: false // 参会医生远程会诊弹窗是否显示
}

const mutations = {
  TOGGLE_SIDEBAR: (state, tr) => {
    state.sidebar.opened = tr || !state.sidebar.opened
    state.sidebar.withoutAnimation = tr || false
    if (state.sidebar.opened) {
      Cookies.set('sidebarStatus', 1)
    } else {
      Cookies.set('sidebarStatus', 0)
    }
  },
  CLOSE_SIDEBAR: (state, withoutAnimation) => {
    Cookies.set('sidebarStatus', 0)
    state.sidebar.opened = false
    state.sidebar.withoutAnimation = withoutAnimation
  },
  TOGGLE_DEVICE: (state, device) => {
    state.device = device
  },
  SET_SIZE: (state, size) => {
    state.size = size
    Cookies.set('size', size)
  },
  SET_BIG_FONT: (state, bigFont) => {
    state.bigFont = bigFont
  },
  SET_FONT_SIZE: (state, fz) => {
    state.fz = fz
  },

  SET_WILL_OVERDUE_STATUS: (state, willOverdueStatus) => {
    state.willOverdueStatus = willOverdueStatus
    sessionStorage.setItem('willOverdueStatus', willOverdueStatus || '')
  },

  set_health_user_number: (state, fz) => {
    state.healthUserNumber = fz
  },
  SET_REOLACE_MENUS: (state, replaceMenus) => {
    state.replaceMenus = replaceMenus
    sessionStorage.setItem('replaceMenus', replaceMenus ? JSON.stringify(replaceMenus) : null)
  },
  SET_FROM_PATH: (state, path) => {
    state.fromPath = path
  },
  SET_DEVICE_CONNECT: (state, deviceConnect) => {
    state.deviceConnect = deviceConnect
  },
  SET_REMOTE_CONSULTATION_VISIBLE: (state, visible) => {
    state.remoteConsultationVisible = visible
  }
}

const actions = {
  toggleSideBar({ commit }) {
    commit('TOGGLE_SIDEBAR', false)
  },
  toggleSideBarClose({ commit }) {
    commit('TOGGLE_SIDEBAR', true)
  },
  closeSideBar({ commit }, { withoutAnimation }) {
    commit('CLOSE_SIDEBAR', withoutAnimation)
  },
  toggleDevice({ commit }, device) {
    commit('TOGGLE_DEVICE', device)
  },
  setSize({ commit }, size) {
    commit('SET_SIZE', size)
  },
  setBigFont({ commit }, bigFont) {
    commit('SET_BIG_FONT', bigFont)
  },
  setFontSize({ commit }, fz) {
    commit('SET_FONT_SIZE', fz)
  },
  setWillOverduesTatus({ commit }, data) {
    commit('SET_WILL_OVERDUE_STATUS', data)
  },
  setHealthUserNumber({ commit }, number) {
    commit('set_health_user_number', number)
  },
  setReplaceMenus({ commit }, replaceMenus) {
    commit('SET_REOLACE_MENUS', replaceMenus)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
