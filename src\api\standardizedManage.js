import request from '@/utils/request'

// 规范管理疾病数量
export const getStandardizedManageDisease = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/disease',
    method: 'post',
    data
  })
}

// 分页查询规范管理
export const getStandardizedManageList = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/page',
    method: 'post',
    data
  })
}

// 查询规范管理历史记录
export const getStandardizedManageHistoryList = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/list/history',
    method: 'post',
    data
  })
}

// 新增规范管理患者
export const addStandardizedManage = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/create',
    method: 'post',
    data
  })
}

// 取消规范管理
export const cancelStandardizedManage = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/cancel',
    method: 'post',
    data
  })
}

// 完成管理
export const completeStandardizedManage = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/complete',
    method: 'post',
    data
  })
}

// 获取规范管理详情
export const getStandardizedManageDetail = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/detail',
    method: 'post',
    data
  })
}

// 获取规范管理详情By patientId
export const getStandardizedManageDetailByPatientId = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/detail/patientId',
    method: 'post',
    data
  })
}
// 目标管理保存
export const saveStandardizedManageTarget = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/target/save',
    method: 'post',
    data
  })
}

// 目标管理详情查询
export const getStandardizedManageTargetDetail = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/target/detail',
    method: 'post',
    data
  })
}

// 保存医学营养模板
export const saveMedicalNutritionTemplate = (data) => {
  return request({
    url: '/cspapi/backend/treatment/action/template/create',
    method: 'post',
    data
  })
}

// 模板查询
export const getMedicalNutritionTemplate = (data) => {
  return request({
    url: '/cspapi/backend/treatment/action/template/list',
    method: 'post',
    data
  })
}

// 治疗动作item列表
export const getTherapeuticActionItemList = (data) => {
  return request({
    url: '/cspapi/backend/treatment/action/item/list',
    method: 'post',
    data
  })
}

// 添加治疗动作item
export const addTherapeuticActionItem = (data) => {
  return request({
    url: '/cspapi/backend/treatment/action/item/add',
    method: 'post',
    data
  })
}

// 删除治疗动作item
export const deleteTherapeuticActionItem = (data) => {
  return request({
    url: '/cspapi/backend/treatment/action/item/remove',
    method: 'post',
    data
  })
}

// 保存治疗动作
export const saveTherapeuticAction = (data) => {
  return request({
    url: '/cspapi/backend/treatment/action/save',
    method: 'post',
    data
  })
}

// 查询治疗动作详情
export const getTherapeuticActionDetail = (data) => {
  return request({
    url: '/cspapi/backend/treatment/action/detail',
    method: 'post',
    data
  })
}

// 目标检验查询
export const getTargetInspection = (data) => {
  return request({
    url: '/cspapi/backend/target/check/detail',
    method: 'post',
    data
  })
}

// 目标检验保存
export const saveTargetInspection = (data) => {
  return request({
    url: '/cspapi/backend/target/check/save',
    method: 'post',
    data
  })
}

// 效果评估查询
export const getEffectEvaluation = (data) => {
  return request({
    url: '/cspapi/backend/target/check/list',
    method: 'post',
    data
  })
}

// 规范管理步骤查询
export const getStandardizedManageStepStatus = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/step/status',
    method: 'post',
    data
  })
}

// 保存风险评估
export const saveRiskAssessment = (data) => {
  return request({
    url: '/cspapi/backend/risk/assessment/save',
    method: 'post',
    data
  })
}

// 查询风险评估
export const getRiskAssessment = (data) => {
  return request({
    url: '/cspapi/backend/risk/assessment/detail',
    method: 'post',
    data
  })
}

// 查询即将逾期的规范管理人员
export const getStandardizedManageExpiredList = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/list/be/overdue',
    method: 'post',
    data
  })
}

// 查询血糖数据异常的患者
export const getBloodSugarAbnormalList = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/list/tnb/abnormal',
    method: 'post',
    data
  })
}

// 查询血压数据异常的患者
export const getBloodPressureAbnormalList = (data) => {
  return request({
    url: '/cspapi/backend/standard/manage/list/gxy/abnormal',
    method: 'post',
    data
  })
}
