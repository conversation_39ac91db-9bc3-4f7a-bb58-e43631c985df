import request from '@/utils/request'

export function updateAddManagerTime(idCard) {
  return request({
    url: '/cspapi/backend/user/updateAddManagerTime',
    method: 'put',
    data: { idCard }
  })
}

// 30.21.3 查看单个心电图信息
export function getEcgReviewDetail(id) {
  return request({
    url: `/cspapi/backend/ecg/review/${id}`,
    method: 'get'
  })
}

// 30.21.4 查看某人某模块某次记录的心电图List
export function getListByModuleAndPatientIdAndRecordId(params, token) {
  return request({
    url: '/cspapi/backend/ecg/review/listByModuleAndPatientIdAndRecordId',
    method: 'get',
    params,
    headers: {
      Authorization: token
    }
  })
}

// 创建转诊单（及回转单）
export function referralRecordApi(data) {
  return request({
    url: '/cspapi/backend/referral/record',
    method: 'post',
    data
  })
}

// 转诊详情
export function getReferralRecordApi(id) {
  return request({
    url: `/cspapi/backend/referral/record/${id}`,
    method: 'get'
  })
}

// 接诊
export function putReferralRecordTreatApi(id) {
  return request({
    url: `/cspapi/backend/referral/record/treat/${id}`,
    method: 'put'
  })
}

// 拒绝接诊
export function putReferralRecordRefuseApi(data) {
  return request({
    url: `/cspapi/backend/referral/record/refuse`,
    method: 'put',
    data
  })
}

// 查询转诊数量统计
export function getReferralRecordCount(data) {
  return request({
    url: `/cspapi/backend/referral/record/count`,
    method: 'post',
    data
  })
}

// 分页查询转诊记录
export function getReferralRecord(data) {
  return request({
    url: `/cspapi/backend/referral/record/page`,
    method: 'post',
    data
  })
}

// 新增转诊记录
export function addReferralRecord(data) {
  return request({
    url: '/cspapi/backend/referral/record/create',
    method: 'post',
    data
  })
}

// 撤销转诊记录
export function cancelReferralRecord(data) {
  return request({
    url: '/cspapi/backend/referral/record/cancel',
    method: 'post',
    data
  })
}

// 接诊转诊记录
export function treatReferralRecord(data) {
  return request({
    url: '/cspapi/backend/referral/record/treat',
    method: 'post',
    data
  })
}

// 拒绝接诊转诊记录
export function refuseReferralRecord(data) {
  return request({
    url: '/cspapi/backend/referral/record/refuse',
    method: 'post',
    data
  })
}

// 查询转诊记录详情
export function getReferralRecordDetail(data) {
  return request({
    url: '/cspapi/backend/referral/record/detail',
    method: 'post',
    data
  })
}
