<template>
  <el-button :class="btnClass" :type="btnType" :icon="icon" @click="$emit('click')">
    <slot />
    <template v-if="type === 'textRight'">
      <svg-icon class="v2icon_rightArrow" icon-class="v2icon_rightArrow" />
    </template>
  </el-button>
</template>
<script>
export default {
  name: 'ProButton',
  components: {},
  props: {
    /* textRight 文本加右键 | cancel 取消 | 其它正常使用如果有预制后续继续扩展 */
    type: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      btnType: '',
      btnClass: ''
    }
  },
  created() {
    this.btnType = this.type
    if (this.type === 'textRight') {
      this.btnType = 'text'
      this.btnClass = 'text_right_btn'
    } else if (this.type === 'cancel') {
      this.btnType = ''
      this.btnClass = 'cancel_btn'
    }
  },
  methods: {}
}
</script>
<style scoped lang="scss">
.cancel_btn {
  border: 1px solid #0a86c8;
  color: #0a86c8;
  background: #f3fffd;
}
.text_right_btn {
  height: auto !important;
}

#app .public_v2 .svg-icon {
  margin-right: 0rem;
}
::v-deep .v2icon_rightArrow {
  width: 0.7rem;
  height: 0.7rem;
  margin-left: 0.2rem;
}
@media (max-width: 1280px) {
  .v2icon_rightArrow {
    width: 0.6rem;
    height: 0.6rem;
  }
}
</style>
