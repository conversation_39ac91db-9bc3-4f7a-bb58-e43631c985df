<template>
  <section class="rightMemberManageBox">
    <div v-if="!inDialog" class="headerBox">
      管理成员（{{ remoteStreamList.length + 1 }}）
      <i class="el-icon-close" @click="$emit('onClose')" />
    </div>
    <div class="tabBox">
      <div class="tabItem" :class="{ activeTab: rightMemberActive === 'in' }" @click="rightMemberActive = 'in'">
        会诊中（{{ remoteStreamList.length + 1 }}）
      </div>
      <div class="tabItem" :class="{ activeTab: rightMemberActive === 'out' }" @click="rightMemberActive = 'out'">
        邀请成员
      </div>
    </div>
    <!-- 会议中 -->
    <div v-if="rightMemberActive === 'in'" class="inMemberBox" :class="{ inMemberBoxDialog: inDialog }">
      <div class="inMemberItemBox">
        <div class="infoBox">
          <div class="nameText">{{ $store.getters.name }}</div>
          <div class="roleText">(我)</div>
        </div>
        <div class="microBox">
          <!-- <svg-icon icon-class="zzzicon-microphone-mute" /> -->
          <svg-icon v-if="muteFlag" icon-class="zzzicon-microphone-mute" />
          <svg-icon v-if="!muteFlag" icon-class="zzzicon-microphone" />
        </div>
      </div>
      <div v-for="remoteItem in remoteStreamList" :key="remoteItem.userId" class="inMemberItemBox">
        <div class="infoBox">
          <div class="nameText">{{ remoteItem.userName }}</div>
        </div>
        <div class="microBox">
          <svg-icon
            v-if="
              muteFlagList.find(it => it.userId === remoteItem.userId) &&
                muteFlagList.find(it => it.userId === remoteItem.userId).muteFlag
            "
            icon-class="zzzicon-microphone-mute"
          />
          <svg-icon v-else icon-class="zzzicon-microphone" />
        </div>
      </div>
    </div>
    <!-- 邀请成员 -->
    <div v-if="rightMemberActive === 'out'" class="outMemberBox" :class="{ outMemberBoxDialog: inDialog }">
      <div class="outMemberFilterBox">
        <el-input v-model="memberFilterTextInner" placeholder="姓名或首字母" prefix-icon="el-icon-search" />
      </div>
      <section class="doctorBox" :class="{ doctorBoxDialog: inDialog }">
        <div
          v-for="docItem in doctors"
          :key="docItem.id + Math.random()"
          class="outMemberItemBox"
          @click="$emit('onAdd', docItem)"
        >
          <div class="nameBox">
            {{ docItem.name }}
            <el-badge v-if="docItem.status === 3" is-dot type="success">&ensp;&ensp;</el-badge>
            <el-badge v-if="docItem.status === 2" is-dot type="danger">&ensp;&ensp;</el-badge>
            <el-badge v-if="docItem.status === 4" is-dot type="info">&ensp;&ensp;</el-badge>
          </div>
          <div class="callBox">
            <span
              v-if="
                !remoteStreamList.find(i => i.userId === docItem.id) && awaitList.find(i => i.userId === docItem.id)
              "
            >
              <img class="callingImg" :src="require('@/assets/dashboard_images/calling.gif')" alt="" srcset="">
            </span>
            <span v-else :class="{ notAvaliable: disId.includes(docItem.id) || docItem.status !== 3 }">
              <svg-icon icon-class="zzzicon-call" />
            </span>
          </div>
        </div>
      </section>
    </div>
  </section>
</template>

<script>
export default {
  name: 'MemberManage',
  props: ['remoteStreamList', 'awaitList', 'doctors', 'disId', 'muteFlagList', 'inDialog'],
  data() {
    return {
      // in / out
      rightMemberActive: 'in',
      memberFilterTextInner: ''
    }
  },
  watch: {
    memberFilterTextInner(newVal) {
      this.$emit('onFilterChange', newVal)
    }
  }
}
</script>
<style lang="scss" scoped>
.rightMemberManageBox {
  width: 13vw;
  height: 34vw;
  flex: none;

  .headerBox {
    border-radius: 0 0.625vw 0 0;
    position: relative;
    background: #f3fffd;
    height: calc(1vw + 36px);
    padding: 0.78vw;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.73vw;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #222222;
    user-select: none;
    i {
      font-size: 0.73vw;
      cursor: pointer;
    }
  }

  .tabBox {
    display: flex;
    justify-content: flex-start;
    .tabItem {
      width: 50%;
      height: 2.3vw;
      background: #ebf7f5;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 0.625vw;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #666666;
      user-select: none;
      cursor: pointer;

      &.activeTab {
        background: #f3fffd;
        color: #0a86c8;
      }
    }
  }

  .inMemberBox {
    height: calc(100% - (1vw + 36px + 0.78vw * 2 + 2.3vw));
    padding: 0.78vw 1.56vw;

    &.inMemberBoxDialog {
      height: calc(100% - 2.3vw) !important;
    }

    .inMemberItemBox {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 25px;

      .infoBox {
        .nameText {
          height: 1vw;
          font-size: 0.73vw;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 1vw;
        }
        .roleText {
          height: 0.885vw;
          font-size: 0.625vw;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          line-height: 0.885vw;
        }
      }
      .microBox {
        .svg-icon {
          width: 0.73vw;
          height: 0.73vw;
        }
      }
    }
  }
  .outMemberBox {
    height: calc(100% - 3.3vw - 36px);
    &.outMemberBoxDialog {
      height: calc(100% - 2.3vw);
    }
    .outMemberFilterBox {
      margin-bottom: 1vw;
      padding: 0.5vw 0.75vw 0;
    }

    .doctorBox {
      overflow-y: auto;
      height: calc(100% - 40px - 1.5vw);
      &.doctorBoxDialog {
        height: calc(100% - 40px - 1.5vw);
      }
    }

    .outMemberItemBox {
      padding: 0.5vw 0.75vw;

      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 1vw;
      margin-bottom: 0.75vw;

      .nameBox {
        font-size: 0.73vw;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
        line-height: 1vw;
      }
      .callBox {
        display: flex;
        align-items: center;
        .callingImg {
          width: 1vw;
          height: 1vw;
        }
        .svg-icon {
          width: 1vw;
          height: 1vw;
          color: #3eca70;
          cursor: pointer;
        }
        .notAvaliable {
          .svg-icon {
            color: #c7c7c7;
            cursor: not-allowed;
          }
        }
      }
    }
  }
}
</style>
