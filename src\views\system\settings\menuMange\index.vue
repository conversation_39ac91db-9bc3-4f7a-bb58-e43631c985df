<template>
  <div v-loading="loading" class="menuMange_container">
    <div class="menu-table">
      <el-button type="primary" style="margin-bottom: 10px" @click="handleMenuAdd">新增</el-button>

      <el-table
        border
        :data="menuList"
        row-key="id"
        :tree-props="{ children: 'children' }"
        height="78vh"
        highlight-current-row
        default-expand-all
      >
        <el-table-column prop="name" label="菜单名称" align="left" show-overflow-tooltip />
        <el-table-column prop="sort" label="排序号" align="center" />
        <el-table-column prop="path" label="请求地址" align="center" show-overflow-tooltip />
        <el-table-column prop="componentName" label="路由名称" align="center" show-overflow-tooltip />
        <el-table-column prop="component" label="组件路由" align="center" show-overflow-tooltip />
        <el-table-column prop="type" label="菜单类型" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.type === 'F'" type="success">目录</el-tag>
            <el-tag v-else-if="scope.row.type === 'M'" type="warning">菜单</el-tag>
            <el-tag v-else type="danger">操作</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="hidden" label="菜单状态" align="center">
          <template #default="scope">
            <el-tag v-if="!scope.row.hidden" type="success">显示</el-tag>
            <el-tag v-else type="danger">隐藏</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="perms" label="权限标识" align="center" show-overflow-tooltip />
        <el-table-column prop="operate" label="操作" align="center">
          <template #default="scope">
            <el-button
              v-if="scope.row.type !== 'B'"
              type="text"
              size="small"
              @click="handleMenuItemAdd(scope.row)"
            >新增</el-button>
            <el-button type="text" size="small" @click="handleMenuItemEdit(scope.row)">编辑</el-button>
            <!-- 是否确认删除 -->
            <el-popconfirm title="确定删除吗？" popper-class="my-popconfirm" @confirm="handleMenuItemDelete(scope.row)">
              <el-button
                slot="reference"
                type="text"
                size="small"
                style="color: red; margin-left: 10px"
              >删除</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <ProDialog :visible.sync="menuVisible" destroy-on-close title="新增菜单" width="800px" top="10vh">
        <el-form ref="menuFormRef" :model="menuForm" :rules="menuRules" label-width="100px">
          <el-form-item label="上级菜单：" prop="parentId">
            <Treeselect
              v-model="menuForm.parentId"
              class="custom_tree_select"
              :options="superiorMenuList"
              clearable
              placeholder="请选择上级菜单"
            />
          </el-form-item>
          <el-form-item label="菜单类型：" prop="type">
            <el-radio-group v-model="menuForm.type" @change="handleMenuTypeChange">
              <el-radio label="F" :disabled="!!radioDisabled && radioDisabled !== 'F'">目录</el-radio>
              <el-radio label="M" :disabled="!!radioDisabled && radioDisabled !== 'M'">菜单</el-radio>
              <el-radio label="B" :disabled="!!radioDisabled && radioDisabled !== 'B'">操作</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="`${label}名称：`" prop="name">
            <el-input v-model="menuForm.name" />
          </el-form-item>
          <template v-if="menuForm.type !== 'B'">
            <el-form-item label="请求地址：" prop="path">
              <el-input v-model="menuForm.path" />
            </el-form-item>
            <template v-if="menuForm.type === 'M'">
              <el-form-item label="路由名称：" prop="componentName">
                <el-input v-model="menuForm.componentName" />
              </el-form-item>
            </template>
            <el-form-item label="组件路由：" prop="component">
              <el-input v-model="menuForm.component" />
            </el-form-item>
          </template>
          <template v-if="menuForm.type !== 'F'">
            <el-form-item label="权限标识：" prop="perms">
              <el-input v-model="menuForm.perms" />
            </el-form-item>
          </template>
          <el-form-item label="显示排序：" prop="sort">
            <el-input-number v-model="menuForm.sort" style="width: 100%" />
          </el-form-item>
          <el-form-item label="图标：" prop="icon">
            <el-input v-model="menuForm.icon" />
          </el-form-item>
          <el-form-item label="菜单状态：" prop="hidden">
            <el-radio-group v-model="menuForm.hidden">
              <el-radio :label="0">显示</el-radio>
              <el-radio :label="1">隐藏</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click="handleMenuClose">关闭</el-button>
          <el-button type="primary" :loading="loading" @click="handleMenuAddSubmit">确定</el-button>
        </template>
      </ProDialog>
    </div>
  </div>
</template>

<script>
import ProDialog from '@/components/ProDialog'
import Treeselect from '@riophae/vue-treeselect'
import { getMenuList, addMenu, getMenuById, deleteMenu, updateMenu } from '@/api/menu'
import { cloneDeep } from 'lodash'

export default {
  name: 'MenuMange',
  components: {
    ProDialog,
    Treeselect
  },
  data() {
    return {
      loading: false,
      menuVisible: false,
      menuList: [],
      superiorMenuList: [],
      radioDisabled: '',
      label: '菜单',
      menuForm: {
        parentId: 0,
        type: '',
        name: '',
        sort: undefined,
        path: '',
        component: '',
        componentName: '',
        perms: '',
        hidden: 0,
        icon: ''
      },
      menuRules: {
        type: [{ required: true, message: '请选择菜单类型', trigger: 'change' }],
        name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
        sort: [{ required: true, message: '请输入显示排序', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getMenuListFn()
  },
  methods: {
    // 菜单新增
    handleMenuAdd() {
      this.radioDisabled = ''
      this.label = '菜单'
      this.resetForm()
      this.menuVisible = true
    },

    // 重置表单
    resetForm() {
      this.menuForm = {
        parentId: 0,
        type: '',
        name: '',
        sort: undefined,
        path: '',
        component: '',
        componentName: '',
        perms: '',
        hidden: 0,
        icon: ''
      }
    },

    // 获取菜单列表
    getMenuListFn() {
      this.loading = true
      getMenuList()
        .then((res) => {
          this.menuList = res.data || []
          const list = this.transformNameToLabel(this.menuList)
          list.unshift({
            label: '无',
            id: 0,
            parentId: 0
          })
          this.superiorMenuList = list
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 树结构转换
    transformNameToLabel(obj) {
      if (Array.isArray(obj)) {
        return obj.map(this.transformNameToLabel)
      } else if (typeof obj === 'object' && obj !== null) {
        const { name, children, ...rest } = obj
        const transformedObj = {
          label: name,
          ...rest
        }

        if (Array.isArray(children) && children.length > 0) {
          transformedObj.children = children.map(this.transformNameToLabel)
        }

        return transformedObj
      }
      return obj
    },

    // 获取菜单详情
    async getMenuByIdFn(params) {
      const res = await getMenuById(params)
      return res
    },

    // 菜单类型改变
    handleMenuTypeChange(val) {
      this.resetForm()
      this.menuForm.type = val
      this.label = val === 'F' ? '目录' : val === 'M' ? '菜单' : '操作'
    },

    // 新增接口
    async addMenuFn() {
      try {
        const params = cloneDeep(this.menuForm)
        const res = await addMenu({
          ...params,
          parentId: params.parentId || 0
        })
        return res
      } catch (error) {
        console.log(error)
      } finally {
        this.radioDisabled = ''
        this.label = '菜单'
        this.loading = false
        this.menuVisible = false
      }
    },

    // 更新接口
    async updateMenuFn() {
      try {
        const params = cloneDeep(this.menuForm)
        const res = await updateMenu(params)
        return res
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
        this.menuVisible = false
      }
    },

    // 列表中新增
    async handleMenuItemAdd(row) {
      this.radioDisabled = ''
      this.label = '菜单'
      const res = await this.getMenuByIdFn({ id: row.id })
      if (res.code === 200) {
        this.menuForm.parentId = res.data.id
        this.menuVisible = true
      }
    },

    // 列表中编辑
    async handleMenuItemEdit(row) {
      this.label = row.type === 'F' ? '目录' : row.type === 'M' ? '菜单' : '操作'
      const res = await this.getMenuByIdFn({ id: row.id })
      if (res.code === 200) {
        const form = cloneDeep(this.menuForm)
        Object.keys(form).forEach((key) => {
          form[key] = res.data[key]
        })
        this.menuForm = {
          ...form,
          id: row.id
        }
        this.radioDisabled = row.type
        this.menuVisible = true
      }
    },

    // 列表中删除
    handleMenuItemDelete(row) {
      deleteMenu({ id: row.id }).then((item) => {
        if (item.code === 200) {
          this.$message.success('删除成功')
          this.getMenuListFn()
        }
      })
    },

    // 关闭
    handleMenuClose() {
      this.menuVisible = false
      this.$nextTick(() => {
        this.resetForm()
      })
    },

    // 提交
    handleMenuAddSubmit() {
      this.$refs.menuFormRef.validate(async(valid) => {
        if (valid) {
          this.loading = true
          if (this.menuForm.id) {
            await this.updateMenuFn()
          } else {
            await this.addMenuFn()
          }
          this.getMenuListFn()
          this.resetForm()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.menuMange_container {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #f9fffb;
  .custom_tree_select {
    ::v-deep .vue-treeselect__menu {
      font-size: 0.7rem;
      color: #606266;
      .vue-treeselect__label {
        font-weight: normal;
      }
      .vue-treeselect__option--selected {
        background: #daf3f2 !important;
      }
    }
  }
  ::v-deep .el-table .cell {
    line-height: 45px;
    display: block !important;
  }
  ::v-deep .el-dialog__body {
    padding: 30px 20px;
  }
  ::v-deep .el-form-item__label {
    text-align-last: auto;
    text-align: end;
    line-height: 36px !important;
  }
  ::v-deep .el-form-item__content {
    padding-right: 50px !important;
    line-height: 36px !important;
  }
  ::v-deep .el-input-number__decrease,
  ::v-deep .el-input-number__increase {
    display: none;
  }
  ::v-deep .el-input-number .el-input__inner {
    padding: 0 15px;
    text-align: left;
  }
}
</style>
