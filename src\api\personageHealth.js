import store from '@/store'
import request from '@/utils/request'

// 查询已经绑定服务的用户信息列表(首页)
export function getVisitInfoFromUserDb(data) {
  return request({
    url: '/cspapi/backend/visitCheck/groupUser/getVisitInfoFromUserdb',
    method: 'get',
    params: data
  })
}
// 查询已经绑定服务的用户信息列表(首页)
export function getHealth(data) {
  return request({
    url: '/cspapi/backend/base/user/page',
    method: 'post',
    data
  })
}
// 查询即将逾期的随访数据
export function getWillHealth(data) {
  return request({
    url: '/cspapi/backend/visitphm/visit/listAddRecord',
    method: 'get',
    params: data
  })
}
// 每种疾病对应的患者数
export function getgroupByDiseaseCodeApi(data) {
  return request({
    url: '/cspapi/backend/user/statistic/patient/groupByDiseaseCode',
    method: 'get',
    params: data
  })
}
// 每种合并症对应的人数
export function getComorbiditiesCountApi(data) {
  return request({
    url: '/cspapi/backend/user/statistic/patient/comorbidities',
    method: 'get',
    params: data
  })
}

// 查询所有病种(用于select)
export function getDiseaseList() {
  return request({
    url: '/cspapi/backend/disease/list',
    method: 'get'
  })
}
// 查询未绑定服务的新用户列表（添加已有用户）
export function getHaveUser(data) {
  return request({
    url: '/cspapi/backend/user/queryNewUsers',
    method: 'post',
    data
  })
}

// 根据状态查询服务包
export function getSimple() {
  return request({
    url: '/cspapi/backend/service-package/status/1',
    method: 'get'
  })
}
// 查询所有健康顾问
export function getListByType() {
  return request({
    url: '/cspapi/backend/user/listByType',
    method: 'get',
    params: { type: 2 }
  })
}
// 新增用户档案
export function getBackendUser(data) {
  return request({
    url: '/cspapi/backend/user',
    method: 'post',
    data
  })
}
// 患者关联医生顾问 + 绑定服务包
export function getBindAdd(data) {
  return request({
    url: '/cspapi/backend/service-package/bind/add2',
    method: 'post',
    data
  })
}

// 个人概况   ！！！！

// 根据用户id查询用户信息
export function getBackendUserID(id) {
  return request({
    url: `/cspapi/backend/user/${id}`,
    method: 'get'
  })
}
// 根据用户id查询用户信息
export function getStatusUserID(id) {
  return request({
    url: `/cspapi/backend/bodyCheck/status/${id}`,
    method: 'get'
  })
}
// (风险预警)查询最近4条告警信息
export function getQueryPhrLastAlarmsID(id) {
  return request({
    url: `/cspapi/backend/alarm/hr/queryPhrLastAlarms/${id}`,
    method: 'get'
  })
}
// 查询用户绑定的服务包
export function getBindFindID(id) {
  return request({
    url: `/cspapi/backend/service-package/bind/find/${id}`,
    method: 'get'
  })
}
// 修改预览信息
export function postUpdateByUserId(data) {
  return request({
    url: '/cspapi/backend/user/patient',
    method: 'put',
    data
  })
}

// 个人资料 ！！！！

// 查看个人资料
export function getByUserId(data) {
  return request({
    url: '/cspapi/backend/personalInformation/getByUserId',
    method: 'get',
    params: data
  })
}
// 新增个人资料
export function getPersonalInformationAdd(data) {
  return request({
    url: '/cspapi/backend/personalInformation/add',
    method: 'post',
    data
  })
}
//  修改个人资料
export function getPersonalInformationUpdate(data) {
  return request({
    url: '/cspapi/backend/personalInformation/update',
    method: 'post',
    data
  })
}

// 体征数据 ！！！！
// 查询个人健康数据/体征数据-最近一次数据(可切换日期)
// export function getDataByDate(id) {
//   return request({
//     url: '/cspapi/backend/healthSportInfo/getLastTimeData/' +id,
//     method: 'get',
//   })
// }
export function getDataByDate({ id, date }) {
  return request({
    url: `/cspapi/backend/healthSportInfo/getLastData/${id}/${date}`,
    method: 'get'
  })
}

// (体征数据-历史预警数据(根据时间查询个人健康数据))
export function getQueryAlarmInfoByUser(data) {
  return request({
    url: '/cspapi/backend/phr/queryAlarmInfoByUser',
    method: 'post',
    data
  })
}

// 佩戴状态(某人最近24h)
export function getWearStatusUserId(id) {
  return request({
    url: `/cspapi/backend/wearStatus/listByUserId/${id}`,
    method: 'get'
  })
}

// 条件查询心电检测记录列表
export function getEcgResultsByCondition(data) {
  return request({
    url: '/cspapi/backend/healthanalytics/heartrate/initiative/ecgResultsByCondition',
    method: 'get',
    params: data
  })
}
// 根据alarmId查询单个心电图波形数据
export function getQueryAlarmByID(data) {
  return request({
    url: '/cspapi/backend/alarm/queryAlarmByID',
    method: 'get',
    params: data
  })
}
// 解读心电图
export function getEcgResultDeal(data) {
  return request({
    url: '/cspapi/backend/healthanalytics/heartrate/initiative/ecgResultDeal',
    method: 'post',
    data
  })
}
// 查看单个ECG测量数据
export function getEcgInitiative(id) {
  return request({
    url: `/cspapi/backend/healthanalytics/heartrate/initiative/ecg/${id}`,
    method: 'get'
  })
}
// 通用解读(用这个)
export function activedealApi(data) {
  return request({
    url: '/cspapi/backend/active/ppg/deal',
    method: 'put',
    data
  })
}

// 检验影像报告！！！！！！！！！！！

// 查看某人的检验,影像报告(list)
export function getListReportByUserId(data) {
  return request({
    url: '/cspapi/backend/userMedicalReport/listReportByUserId',
    method: 'get',
    params: data
  })
}

// 多文件上传
export function postUploadFile(data) {
  const { patientInfo } = store.getters
  data.append('platform', 'minio')
  if (patientInfo) {
    data.append('patientId', patientInfo.patientId || patientInfo.id)
    data.append('patientName', patientInfo.name)
  } else {
    data.append('patientId', localStorage.getItem('userPatientId'))
    data.append('patientName', localStorage.getItem('userPatientName'))
  }
  return request({
    url: '/cspapi/backend/cos/uploadFile',
    method: 'post',
    data
  })
}

//  新增检验,影像报告
export function postUserMedicalReport(data) {
  return request({
    url: '/cspapi/backend/userMedicalReport/add',
    method: 'post',
    data
  })
}

// 修改检验,影像报告
export function postUserMedicalReportUpdate(data) {
  return request({
    url: '/cspapi/backend/userMedicalReport/update',
    method: 'post',
    data
  })
}

// 大小图-查看某人的检验,影像报告(list)
export function getReport(data) {
  return request({
    url: '/cspapi/backend/userMedicalReport/getReport',
    method: 'get',
    params: data
  })
}

// 删除单个检验,影像报告
export function getUserMedicalReportDelete(data) {
  return request({
    url: '/cspapi/backend/userMedicalReport/delete',
    method: 'get',
    params: data
  })
}

// 体检报告 ！！！！！！！！！！！

// 查看某人的体检报告(list)
export function getPhysicalExamination(data) {
  return request({
    url: '/cspapi/backend/ecgReport/listReportByUserId',
    method: 'get',
    params: data
  })
}
// 查看某人的影像记录
export function getvideoReportListApi(data) {
  return request({
    url: '/cspapi/backend/videoReport/listReportByUserId',
    method: 'get',
    params: data
  })
}
// 新增体检报告
export function getUserReportAdd(data) {
  return request({
    url: '/cspapi/backend/ecgReport/add',
    method: 'post',
    data
  })
}
// 新增影像记录
export function getvideoReportAdd(data) {
  return request({
    url: '/cspapi/backend/videoReport/add',
    method: 'post',
    data
  })
}

// 修改体检报告
export function getUserReportUpdate(data) {
  return request({
    url: '/cspapi/backend/ecgReport/update',
    method: 'post',
    data
  })
}
// 修改影像记录
export function getUservideoReportUpdate(data) {
  return request({
    url: '/cspapi/backend/videoReport/update',
    method: 'post',
    data
  })
}
// 大小图-查看某人的检验,影像报告(list)
export function getListReportByUserIdTinyAndBig(data) {
  return request({
    url: '/cspapi/backend/ecgReport/getReport',
    method: 'get',
    params: data
  })
}
// 大小图-查看就诊影像报告(list)
export function getListvideoReportByUserIdTinyAndBig(data) {
  return request({
    url: '/cspapi/backend/videoReport/getReport',
    method: 'get',
    params: data
  })
}
// 删除单个体检报告
export function getUserReportDelete(data) {
  return request({
    url: '/cspapi/backend/ecgReport/delete',
    method: 'get',
    params: data
  })
}
// 删除单个体检报告
export function getvideoReportDelete(data) {
  return request({
    url: '/cspapi/backend/videoReport/delete',
    method: 'get',
    params: data
  })
}

// 下载！！！
export function downloadApi(data) {
  return request({
    url: '/cspapi/backend/cos/download',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 健康日记 ！！！！！！！！！！

// 7.15 日历控件
export function calendarApi(id) {
  return request({
    url: `/cspapi/backend/healthSportInfo/calendar/${id}`,
    method: 'get'
  })
}
// 查看某人某天的健康日记list
export function cardIdApi(id, date) {
  return request({
    url: `/cspapi/backend/healthSportInfo/card/${id}?day=${date}`,
    method: 'get'
  })
}
// 心率,
export function heartApi(id, day, group) {
  return request({
    url: `/cspapi/backend/healthSportInfo/chart/heart/${id}`,
    method: 'get',
    params: { day, group }
  })
}
// 血氧图
export function oxygenApi(id, day, group) {
  return request({
    url: `/cspapi/backend/healthSportInfo/chart/oxygen/${id}`,
    method: 'get',
    params: { day, group }
  })
}
// 体温
export function temperatureApi(id, day) {
  return request({
    url: `/cspapi/backend/healthSportInfo/chart/temperature/${id}`,
    method: 'get',
    params: { day }
  })
}
// 睡眠
export function sleepApi(id, day) {
  return request({
    url: `/cspapi/backend/sleep/list/${id}`,
    method: 'get',
    params: { day }
  })
}
// 睡眠
export function removeSleepApi(id) {
  return request({
    url: `/cspapi/backend/sleep/${id}`,
    method: 'delete'
  })
}
// 睡眠时间线
export function sleepStatisticApi(id, day, sleepId) {
  return request({
    url: `/cspapi/backend/sleep/timeline/${id}`,
    method: 'get',
    params: { sleepId }
  })
}
// 睡眠时间线
// export function sleepStatisticApi(id, day) {
//   return request({
//     url: `/cspapi/backend/sleep/statistic/${id}`,
//     method: 'get',
//     params: { day }
//   })
// }
// 血压血糖
export function bloodSugarAndPressureApi(id, day) {
  return request({
    url: `/cspapi/backend/healthSportInfo/bloodSugarAndPressure/${id}`,
    method: 'get',
    params: { day }
  })
}
// 查看体温列表
export function getTemperatureActiveApi(data) {
  return request({
    url: '/cspapi/backend/temperatureActive',
    method: 'get',
    params: data
  })
}
// 查看血糖列表
export function getBloodSugarApi(data) {
  return request({
    url: '/cspapi/backend/bloodSugar',
    method: 'get',
    params: data
  })
}
// 查看血压列表
export function getBloodPressureActiveApi(data) {
  return request({
    url: '/cspapi/backend/bloodPressureActive',
    method: 'get',
    params: data
  })
}

//   查看某人的每分钟的睡眠详情数据
// export function sleepEveryMinuteApi(id, day) {
//   return request({
//     url: `/cspapi/backend//fullCycle/chart/sleep/everyMinute/${id}`,
//     method: 'get',
//     params: { day }
//   })
// }
//   查看某人的每分钟的睡眠详情数据
export function sleepEveryMinuteApi(id, day, sleepId) {
  return request({
    url: `/cspapi/backend/fullCycle/chart/sleep/everyMinute/${id}`,
    method: 'get',
    params: { sleepId }
  })
}
//   HRV
export function hrvApi(id, day) {
  return request({
    url: `/cspapi/backend/healthSportInfo/hrv/${id}`,
    method: 'get',
    params: { day }
  })
}

// 全周期数据 ！！！！！！！！！

// 风险预警曲线
export function riskCountApi(id, data) {
  return request({
    url: `/cspapi/backend/fullCycle/chart/alarm/risk/count/${id}`,
    method: 'get',
    params: data
  })
}
// 心率
export function chartHeartApi(id, data) {
  return request({
    url: `/cspapi/backend/fullCycle/chart/heart/${id}`,
    method: 'get',
    params: data
  })
}
// 血氧
export function chartOxygenApi(id, data) {
  return request({
    url: `/cspapi/backend/fullCycle/chart/oxygen/${id}`,
    method: 'get',
    params: data
  })
}
// 体温
export function chartTemperatureApi(id, data) {
  return request({
    url: `/cspapi/backend/fullCycle/chart/temperature/${id}`,
    method: 'get',
    params: data
  })
}
// 体温
export function bloodPressureApi(id, data) {
  return request({
    url: `/cspapi/backend/fullCycle/chart/bloodPressure/${id}`,
    method: 'get',
    params: data
  })
}
// 血糖
export function bloodSugarApi(id, data) {
  return request({
    url: `/cspapi/backend/fullCycle/chart/bloodSugar/${id}`,
    method: 'get',
    params: data
  })
}
// 运动(步数+距离+卡路里)
export function chartSportApi(id, data) {
  return request({
    url: `/cspapi/backend/fullCycle/chart/sport/${id}`,
    method: 'get',
    params: data
  })
}

// 查看某人已经随访的数据
export function recordDidApi(id, data) {
  return request({
    url: `/cspapi/backend/visit/record/did/${id}`,
    method: 'get',
    params: data
  })
}
// 查看某人已经随访的数据
export function recordApi(id) {
  return request({
    url: `/cspapi/backend/visit/record/${id}`,
    method: 'get'
  })
}
// 日历打点  预警
export function alarmAlenderApi(data) {
  return request({
    url: '/cspapi/backend/alarm/hr/calender',
    method: 'get',
    params: data
  })
}

// 最近诊疗记录
export function getregisteredrecordnewApi(data) {
  return request({
    url: '/cspapi/backend/registeredrecord/pageRegedPatient',
    method: 'get',
    params: data
  })
}

// 远程就诊记录(某患者)
export function getRemoteConsultationApi(data) {
  return request({
    url: '/cspapi/backend/remoteConsultation/page',
    method: 'get',
    params: data
  })
}

// 远程就诊记录 视频回放
export function getTencentVodApi(data) {
  return request({
    url: '/cspapi/backend/tencent/vod',
    method: 'get',
    params: data
  })
}

// 绑定机构名称
export function serviceStationEdit(data) {
  return request({
    url: '/cspapi/backend/serviceStation/patient',
    method: 'post',
    data
  })
}

// 上下转诊 ！！！！！
// 上下转诊
export function transferTreatmentApi(data) {
  return request({
    url: '/cspapi/backend/serviceStation/change',
    method: 'post',
    data
  })
}

// 上下转诊记录-分页
export function transferTreatmentPageApi(data) {
  return request({
    url: '/cspapi/backend/serviceStation/change/page',
    method: 'get',
    params: data
  })
}
//  转诊记录列表  针对个人的
export function transferTreatmentlistByPatientIdApi(data) {
  return request({
    url: '/cspapi/backend/serviceStation/change/listByPatientId',
    method: 'get',
    params: data
  })
}
// 上转的select就诊点
export function getListParentByServiceStationIdApi(serviceStationId) {
  return request({
    url: '/cspapi/backend/serviceStation/listParentByServiceStationId',
    method: 'get',
    params: { serviceStationId }
  })
}
// 上转的select就诊点  id
export function getListParentByIdApi(serviceStationId) {
  return request({
    url: '/cspapi/backend/serviceStation/listParentByServiceStationId/select',
    method: 'get',
    params: { serviceStationId }
  })
}
//  卡片统计(转入+转出)
export function getstransferTreatmentTatisticApi(data) {
  return request({
    url: '/cspapi/backend/serviceStation/change/statistic',
    method: 'get',
    params: data
  })
}
// 某人最后一次转诊记录
export function getLastChangeRecordByPatientIdApi(patientId) {
  return request({
    url: '/cspapi/backend/serviceStation/change/getLastChangeRecordByPatientId',
    method: 'get',
    params: { patientId }
  })
}
// 根据角色code查询相关用户 list
export function getlistUserByRoleCodeApi(params) {
  return request({
    url: '/cspapi/backend/role/user/listUserByRoleCode',
    method: 'get',
    params
  })
}

// 查看体温列表
export function getTemperatureLimitApi(data) {
  return request({
    url: '/cspapi/backend/temperature/limit',
    method: 'get',
    params: data
  })
}

// 获取文章推送、视频推送列表
export function getScienceArticlePageApi(data) {
  return request({
    url: '/cspapi/backend/science/article/page',
    method: 'get',
    params: data
  })
}

// 获取视频推送列表
export function getScienceVideoPageApi(data) {
  return request({
    url: '/cspapi/backend/science/video/page',
    method: 'get',
    params: data
  })
}

// 获取文章详情
export function getScienceArticleDetailApi(id) {
  return request({
    url: `/cspapi/backend/science/article/${id}`,
    method: 'get'
  })
}

// 获取视频详情
export function getScienceVideoDetailApi(id) {
  return request({
    url: `/cspapi/backend/science/video/${id}`,
    method: 'get'
  })
}

// 居民健康数据库count
export function getPhrUserDBCountApi(data) {
  return request({
    url: '/cspapi/backend/base/user/disease',
    method: 'post',
    data
  })
}

// 居民档案删除
export function updateSigleUserInfo(data) {
  return request({
    url: '/cspapi/backend/user/updateSigleUserInfo',
    method: 'post',
    data
  })
}

// 居民档案根据身份证查id
export function getUserIdByCardId(data) {
  return request({
    url: '/cspapi/backend/user/getUserIdByIdCard',
    method: 'post',
    data
  })
}

// 判断patient_id是否有家庭，若有家庭是否是户主
export function getFamilyIdentity(residentId) {
  return request({
    url: `/cspapi/backend/familyDoctor/family/identity/${residentId}`,
    method: 'get'
  })
}

// 查看家庭信息
export function getFamilyInfo(familyId) {
  return request({
    url: `/cspapi/backend/familyDoctor/family/${familyId}`,
    method: 'get'
  })
}

// 根据用户id查询 最后一次 体征信息
export function getLastByUserIdApi(data) {
  return request({
    url: '/cspapi/backend/patient/report/health/getLastByUserId',
    method: 'get',
    params: data
  })
}

// 血压趋势(收缩压+舒张压)
export function getbloodPressureApi(data) {
  return request({
    url: '/cspapi/backend/patient/report/health/char/bloodPressure',
    method: 'get',
    params: data
  })
}

// 血糖趋势(空腹+饭后)
export function getbloodSugarApi(data) {
  return request({
    url: '/cspapi/backend/patient/report/health/char/bloodSugar',
    method: 'get',
    params: data
  })
}

// 血脂趋势-低密度脂蛋白 LDL-C
export function getCharLdlcApi(data) {
  return request({
    url: '/cspapi/backend/patient/report/health/char/ldlc',
    method: 'get',
    params: data
  })
}

// 体重趋势
export function getWeightApi(data) {
  return request({
    url: '/cspapi/backend/patient/report/health/char/weight',
    method: 'get',
    params: data
  })
}
// 随访时间线
export function getTimeline(data) {
  return request({
    url: '/cspapi/backend/patient/report/health/timeline',
    method: 'get',
    params: data
  })
}

// 接诊
export function jzBypatientIdApi(data) {
  return request({
    url: '/cspapi/backend/reception/workbenches/detail/patientId',
    method: 'post',
    data
  })
}
