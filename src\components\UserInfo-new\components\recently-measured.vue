<template>
  <div class="data-box public_v2">
    <div class="bloodPressItem">
      <img src="@/assets/personage_images/home-pressure-new.png" alt="">
      <div class="value-box">
        <span class="value-box-title fontSize_16">血压</span>
        <div class="value">
          <span v-if="lastMeasureData.sp" class="span fontSize_14">
            <strong class="fontSize_16 fontWeight600">{{ lastMeasureData.dp }}/{{ lastMeasureData.sp }}</strong>
            mmHg
          </span>
          <span v-else class="noval"><strong style="color: #999" class="fontSize_14">暂无数据</strong></span>
          <div v-if="lastMeasureData.pressureTime" class="time fontSize_14">
            {{ lastMeasureData.pressureTime | date2YMDsHI }}
          </div>
        </div>
      </div>
    </div>
    <div class="bloodSugarItem">
      <img src="@/assets/personage_images/home-sugger-new.png" alt="">
      <div class="value-box">
        <span class="value-box-title fontSize_16">
          {{ lastMeasureData.sugarType === 1 ? '空腹' : lastMeasureData.sugarType === 2 ? '饭后' : '饭后两小时' }}血糖
        </span>
        <div class="value">
          <span v-if="lastMeasureData.sugarValue" class="span fontSize_14">
            <strong class="fontSize_16 fontWeight600" :style="globalBloodSugarStatus(lastMeasureData.sugarValue)">{{
              lastMeasureData.sugarValue
            }}</strong>
            mmol/L
          </span>
          <span v-else class="noval"><strong style="color: #999" class="fontSize_14">暂无数据</strong></span>
          <div v-if="lastMeasureData.sugarDate" class="time fontSize_14">
            {{ lastMeasureData.sugarDate | date2YMDsHI }}
          </div>
        </div>
      </div>
    </div>
    <div class="ECGItem" style="width: 100%">
      <img src="@/assets/personage_images/home-ECG.png" alt="心电图">
      <div class="value-box">
        <span class="value-box-title fontSize_16">心电图</span>
        <div class="value">
          <span v-if="lastMeasureData.ecgResult" class="span fontSize_14">
            {{
              lastMeasureData.ecgResult &&
                lastMeasureData.ecgResult
                  .split(',')
                  .map(item => ecgOptions.find(option => option.value === item).label)
                  .join(',')
            }}
          </span>
          <span v-else class="noval"><strong class="fontSize_14">暂无数据</strong></span>
          <div v-if="lastMeasureData.ecgTime" class="time fontSize_14">
            {{ lastMeasureData.ecgTime | date2YMDsHI }}
          </div>
        </div>
      </div>
    </div>
    <div class="bloodCholesterolItem">
      <img src="@/assets/personage_images/home-fat-new.png" alt="总胆固醇">
      <div class="value-box">
        <div class="value-List">
          <div class="value-item">
            <div class="value-title fontSize_16">总胆固醇</div>
            <div v-if="lastMeasureData.tc" class="val fontSize_14">
              <strong class="fontSize_16 fontWeight600">{{ lastMeasureData.tc }}</strong>
              mmol/L
            </div>
            <div v-else class="noval"><strong style="color: #999" class="fontSize_14">暂无数据</strong></div>
          </div>
          <div class="value-item">
            <div class="value-title fontSize_16">甘油三酯</div>
            <div v-if="lastMeasureData.tg" class="val fontSize_14">
              <strong class="fontSize_16 fontWeight600">{{ lastMeasureData.tg }}</strong>
              mmol/L
            </div>
            <div v-else class="noval"><strong style="color: #999" class="fontSize_14">暂无数据</strong></div>
          </div>
          <div class="value-item fontSize_16">
            <div class="value-title fontSize_16">低密度脂蛋白</div>
            <div v-if="lastMeasureData.ldlc" class="val fontSize_14">
              <strong class="fontSize_16 fontWeight600">{{ lastMeasureData.ldlc }}</strong>
              mmol/L
            </div>
            <div v-else class="noval"><strong style="color: #999" class="fontSize_14">暂无数据</strong></div>
          </div>
          <div class="value-item">
            <div class="value-title fontSize_16">高密度脂蛋白</div>
            <div v-if="lastMeasureData.hdlc" class="val fontSize_14">
              <strong class="fontSize_16 fontWeight600">{{ lastMeasureData.hdlc }}</strong>
              mmol/L
            </div>
            <div v-else class="noval"><strong style="color: #999" class="fontSize_14">暂无数据</strong></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<!-- eslint-disable vue/prop-name-casing -->
<script>
import { ecgOptions } from '@/views/receptionCenter/patientReception/component/complicationsScreening.js'

export default {
  name: 'DynamicMonitorData',

  props: {
    lastMeasureData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      ecgDialogId: '',
      ecgOptions
    }
  }
}
</script>
<style lang="scss" scoped>
.data-box {
  padding: 0 0.5vw;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  & > div.bloodCholesterolItem,
  & > div {
    padding: 0.3vw 0.78125vw;
    height: 4.6vw;
  }

  & > div {
    width: 48%;

    border-radius: 10px;
    display: flex;
    align-items: center;

    margin-bottom: 1.2rem;

    img {
      width: 2.4rem;
      height: 2.4rem;
    }

    .value-box {
      // width: calc(100% - 0.78125vw - 2.4rem);
      width: 70%;
      margin: 0 0 0 0.5vw;

      .value-box-title {
        display: flex;
        justify-content: space-between;
        color: #222222;
        white-space: nowrap;

        .type {
          font-size: 0.625vw;
          font-weight: 400;
          color: #666666;
          white-space: nowrap;
        }
      }

      .value {
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        margin-top: 0.2rem;
        padding-right: 0.5rem;

        span {
          font-size: 0.625vw;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          white-space: nowrap;

          strong {
            color: #000000;
            font-weight: 500;
          }
        }

        .time {
          color: #999999;
          font-size: 0.625vw;
          font-weight: 400;
          margin-top: 0.2rem;
        }

        .noval {
          strong {
            font-size: 0.7291vw;
          }
        }
      }
    }
  }

  .bloodPressItem {
    background-color: #ffedeb;
  }

  .bloodSugarItem {
    background-color: #ffecda;
  }

  .temperatureItem {
    background-color: #cff7d5;
  }

  .ECGItem {
    background: linear-gradient(315deg, #ff8a56 0%, #ffb493 100%);
    position: relative;

    .value-box {
      .value-box-title {
        color: #fff;
        white-space: nowrap;

        .type {
          color: #fff;
        }
      }

      .value {
        span {
          width: 50%;
          color: #fff;
          text-overflow: ellipsis;
          overflow: hidden;
          word-break: break-all;
          white-space: nowrap;

          strong {
            color: #fff;
          }
        }

        .time {
          color: #fff;
        }

        .noval {
          strong {
            color: #fff;
          }
        }
      }
    }

    ::v-deep .ECGItem_lookDetailBtn.el-button {
      width: 4rem;
      text-align: center;

      > span {
        justify-content: center !important;
      }
    }

    @media (max-width: 1280px) {
      .ECGItem_lookDetailBtn.el-button {
        width: 3rem;
        position: absolute;
        right: 0.5rem;
        top: 0.5rem;
        font-size: 0.6rem !important;
        padding: 00 0.5rem !important;
        height: 1rem !important;
        line-height: 1rem !important;
      }
    }
  }

  .bloodSugarItem_empty {
    width: 4rem;
    text-align: center;
    @media (max-width: 1280px) {
      width: 3rem;
    }
  }

  & > div.bloodCholesterolItem {
    width: 100%;

    border-radius: 10px;
    display: flex;
    align-items: center;

    background-color: #ffedeb;
    margin-bottom: 0;

    img {
      width: 3.125vw;
      height: 3.125vw;
    }

    .value-box {
      width: calc(100% - 4.4vw);
      margin: 0 0 0 0.5vw;
      display: flex;
      // align-items: center;
      justify-content: space-between;
      flex-direction: column;

      .value-List {
        display: flex;
        align-items: center;

        .value-item {
          margin-right: 1.04vw;

          .value-title {
            font-size: 0.729vw;
            font-weight: 400;
            color: #222222;
            white-space: nowrap;
          }

          .val {
            margin-top: 0.2rem;
            font-size: 0.625vw;
            font-weight: 400;
            color: #666666;
            white-space: nowrap;

            strong {
              color: #000000;
              font-weight: 500;
            }
          }

          .noval {
            margin-top: 0.2rem;

            strong {
              font-weight: 500;
            }
          }
        }
      }

      .time {
        display: flex;
        align-items: end;
        font-size: 0.625vw;
        color: #999999;
      }
    }

    .time {
      display: flex;
      align-items: end;
      font-size: 0.625vw;
      margin-top: 0.2rem;
      color: #999999;
    }
  }
}

::v-deep .ecgPageContainer .topInfoBox .infoCollectionBox .infoItemBox {
  margin-right: 0.8vw;
}

@media (max-width: 1600px) {
  .data-box {
    & > div.bloodCholesterolItem,
    & > div {
      height: 7.2vw;
    }
  }
}
</style>
