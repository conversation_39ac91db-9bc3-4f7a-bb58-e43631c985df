<template>
  <div class="result-title">
    <div class="line" />
    <span class="text">
      <slot>{{ title }}</slot>
    </span>
    <span v-if="desc" class="desc">{{ desc }}</span>
  </div>
</template>

<script>
export default {
  name: 'FlagComponent',
  props: {
    title: {
      type: String,
      required: true
    },
    desc: {
      type: String,
      required: false
    }
  }
}
</script>

<style lang="scss" scoped>
.result-title {
  display: flex;
  align-items: center;
}
.line {
  width: 4px;
  height: 20px;
  background: #1890ff;
  border-radius: 2px;
  margin-right: 8px;
}
.text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}
.desc {
  font-size: 14px;
  color: red;
  margin-left: 16px;
}
</style>
