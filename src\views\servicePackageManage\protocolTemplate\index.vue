<template>
  <div class="protocol-template">
    <el-card class="protocol-template-search">
      <el-form :model="queryParams" label-width="120px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="医疗机构：" prop="depart">
              <TreeSelect
                v-model="queryParams.departCode"
                :data="departTree"
                :props="{
                  children: 'children',
                  label: 'departName',
                  value: 'departCode'
                }"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="模板名称：" prop="teamName">
              <el-input v-model="queryParams.moduleName" placeholder="请输入团队名称" />
            </el-form-item>
          </el-col>

          <el-col :span="6" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="protocol-template-table">
      <el-button type="primary" style="margin-bottom: 10px" @click="handleAdd">新增</el-button>
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 100px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #enableFlag="{ row }">
          <el-switch
            v-model="row.enableFlag"
            :active-value="1"
            :inactive-value="0"
            @change="handleEnableFlagChange(row)"
          />
        </template>

        <template #operation="{ row }">
          <el-button type="text" @click="handleDetail(row)">查看</el-button>
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" style="color: red" @click="handleDelete(row)">删除</el-button>
        </template>
      </base-table>
    </el-card>

    <ProDialog :visible.sync="addDialogVisible" title="新增" width="1200px">
      <AddForm
        ref="addForm"
        :depart-tree="departTree"
        :add-dialog-visible="addDialogVisible"
        :disabled-submit="disabledSubmit"
        @success="handleSuccess"
      />
      <template #footer>
        <el-button @click="addDialogVisible = false">关闭</el-button>
        <el-button v-if="!disabledSubmit" type="primary" @click="handleConfirm">确定</el-button>
      </template>
    </ProDialog>
  </div>
</template>

<script>
import { getOrgTreeByIdApi } from '@/api/system'
import { getProtocolTemplatePageList, deleteProtocolTemplate, enableProtocolTemplate } from '@/api/protocolTemplate'
import { getUserId } from '@/utils/auth'
import { localCache } from '@/utils/cache'
import BaseTable from '@/components/BaseTable/index.vue'
import TreeSelect from '@/components/TreeSelect/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ProDialog from '@/components/ProDialog/index.vue'
import AddForm from './component/addForm.vue'

export default {
  name: 'ProtocolTemplate',
  components: {
    BaseTable,
    TreeSelect,
    ProDialog,
    AddForm
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        departCode: localCache.getCache('userInfo').departCode,
        moduleName: ''
      },
      disabledSubmit: false,
      departTree: [],
      columns: [
        { label: '机构名称', prop: 'departName', align: 'center' },
        { label: '模板名称', prop: 'moduleName', align: 'center' },
        { label: '启用状态', prop: 'enableFlag', align: 'center', slot: 'enableFlag' },
        { label: '创建人', prop: 'createUsername', align: 'center' },
        { label: '创建时间', prop: 'createTime', align: 'center' },
        { label: '修改人', prop: 'updateUsername', align: 'center' },
        { label: '修改时间', prop: 'updateTime', align: 'center' },
        { label: '操作', prop: 'operation', align: 'center', width: 150, slot: 'operation' }
      ]
    }
  },
  created() {
    this.getDepartTree()
  },
  methods: {
    async getTableList(params) {
      return await getProtocolTemplatePageList(params)
    },
    // 机构树
    async getDepartTree() {
      const res = await getOrgTreeByIdApi({
        patientId: getUserId()
      })
      this.departTree = res.data
    },

    handleEnableFlagChange(row) {
      console.log('启用状态变更:', row.enableFlag)
      // 这里可以添加调用API更新状态的代码
      enableProtocolTemplate({
        id: row.id
      }).then((res) => {
        this.$message.success('启用状态变更成功')
      })
    },

    handleAdd() {
      this.addDialogVisible = true
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$refs.addForm) {
            this.$refs.addForm.initRichText()
          }
        }, 100)
      })
    },

    handleDetail(row) {
      this.disabledSubmit = true
      this.addDialogVisible = true
      this.$nextTick(() => {
        this.$refs.addForm.handleDetail(row)
      })
    },

    handleEdit(row) {
      this.disabledSubmit = false
      this.addDialogVisible = true
      this.$nextTick(() => {
        this.$refs.addForm.handleEdit(row)
      })
    },

    handleDelete(row) {
      this.handleConfirmDelete({
        params: {
          id: row.id
        },
        deleteApi: deleteProtocolTemplate,
        message: '确定删除该模板吗？'
      })
    },

    handleConfirm() {
      this.$refs.addForm.handleSave()
    },

    handleSuccess() {
      this.addDialogVisible = false
      this.handleSearch()
    },

    handleReset() {
      this.queryParams = {
        departCode: localCache.getCache('userInfo').departCode || '',
        moduleName: '',
        pageNo: 1,
        pageSize: 20
      }
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.protocol-template {
  display: flex;
  flex-direction: column;
  height: 100%;
  .protocol-template-search {
    margin: 16px;
    height: 77px;
  }
  .protocol-template-table {
    margin: 0 16px;
    flex: 1;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
}
</style>
