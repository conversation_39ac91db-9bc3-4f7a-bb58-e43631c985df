<!-- 震动阈值检查 -->
<template>
  <div class="vibration-threshold-check">
    <div v-if="showDeviceCode($route.path)" class="device-barcode">
      <Barcode :code="`${getDeviceCode($route.path, getSourceData())}`" />
    </div>
    <el-table :data="tableData" border style="width: 100%" :span-method="objectSpanMethod">
      <el-table-column label="位置" prop="position" align="center" />
      <el-table-column label="检查项目" prop="item" align="center" />
      <el-table-column label="右足" prop="right" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.type === 'radio'">
            <el-radio-group v-model="scope.row.right">
              <el-radio :label="1">正常</el-radio>
              <el-radio :label="2">减弱</el-radio>
              <el-radio :label="3">消失</el-radio>
            </el-radio-group>
          </template>
          <template v-else>
            <custom-input-number v-model="scope.row.right" size="mini" />
          </template>
        </template>
      </el-table-column>
      <el-table-column label="左足" prop="left" align="center">
        <template slot-scope="scope">
          <template v-if="scope.row.type === 'radio'">
            <el-radio-group v-model="scope.row.left">
              <el-radio :label="1">正常</el-radio>
              <el-radio :label="2">减弱</el-radio>
              <el-radio :label="3">消失</el-radio>
            </el-radio-group>
          </template>
          <template v-else>
            <custom-input-number v-model="scope.row.left" size="mini" />
          </template>
        </template>
      </el-table-column>
    </el-table>

    <el-form ref="formRef" :model="form" :rules="rules" label-width="160px" style="margin-top: 30px">
      <el-form-item label="检查结果描述：">
        <el-input v-model="form.vtDescription" type="textarea" :rows="2" />
      </el-form-item>

      <el-form-item label="震动阈值检查结果：" prop="vtResult">
        <el-radio-group v-model="form.vtResult">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="2">异常</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="上传报告图片">
        <custom-upload v-model="form.attachmentPhotoUrl" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapDataToTable, getDeviceCode, showDeviceCode } from '@/utils/cspUtils'
import { cloneDeep } from 'lodash'
import CustomUpload from '@/components/customUpload/index.vue'
import Barcode from '@/components/barcode/barcode.vue'

export default {
  name: 'VibrationThresholdCheck',
  components: {
    CustomUpload,
    Barcode
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        vtDescription: '',
        vtResult: '',
        attachmentPhotoUrl: ''
      },
      rules: {
        vtResult: [{ required: true, message: '请选择震动阈值检查结果' }]
      },
      tableData: [
        {
          position: '深感觉震动觉',
          item: '',
          right: '',
          left: '',
          type: 'input',
          rightDeepValue: 'right',
          leftDeepValue: 'left'
        },

        {
          position: '其他检查项目',
          item: '触压觉',
          right: '',
          left: '',
          type: 'radio',
          rightTouchResult: 'right',
          leftTouchResult: 'left'
        },
        {
          position: '其他检查项目',
          item: '疼痛觉',
          right: '',
          left: '',
          type: 'radio',
          rightPainResult: 'right',
          leftPainResult: 'left'
        },
        {
          position: '其他检查项目',
          item: '凉温觉',
          right: '',
          left: '',
          type: 'radio',
          rightCoolResult: 'right',
          leftCoolResult: 'left'
        },
        {
          position: '其他检查项目',
          item: '跟腱反射',
          right: '',
          left: '',
          type: 'radio',
          rightReflectResult: 'right',
          leftReflectResult: 'left'
        },

        {
          position: '足部皮肤温度检查',
          item: '足背（℃）',
          right: '',
          left: '',
          type: 'input',
          rightInstepTempValue: 'right',
          leftInstepTempValue: 'left'
        },
        {
          position: '足部皮肤温度检查',
          item: '内侧足弓（℃）',
          right: '',
          left: '',
          type: 'input',
          rightArchTempValue: 'right',
          leftArchTempValue: 'left'
        },
        {
          position: '足部皮肤温度检查',
          item: '足后跟（℃）',
          right: '',
          left: '',
          type: 'input',
          rightHeelTempValue: 'right',
          leftHeelTempValue: 'left'
        },
        {
          position: '足部皮肤温度检查',
          item: '前脚掌（℃）',
          right: '',
          left: '',
          type: 'input',
          rightForeTempValue: 'right',
          leftForeTempValue: 'left'
        },

        {
          position: '临床表现',
          item: '',
          right: '',
          left: '',
          type: 'input',
          rightClinicalFeature: 'right',
          leftClinicalFeature: 'left'
        }
      ]
    }
  },
  methods: {
    showDeviceCode,
    getDeviceCode,
    getSourceData() {
      return this.$route.path === '/receptionCenter/patientReception'
        ? this.$store.getters.complicationsScreeningData
        : this.$store.state.patientExamination.patientExaminationData
    },
    initData(data) {
      this.tableData = mapDataToTable(data, cloneDeep(this.tableData))
      this.form = {
        vtDescription: data.vtDescription,
        vtResult: data.vtResult,
        attachmentPhotoUrl: data.attachmentPhotoUrl,
        id: data.id
      }
    },
    objectSpanMethod({ rowIndex, columnIndex }) {
      // 第一行：位置 + 检查项目合并为一个单元格（跨2列）
      if (rowIndex === 0) {
        if (columnIndex === 0) {
          return { rowspan: 1, colspan: 2 } // 合并前两列
        } else if (columnIndex === 1) {
          return { rowspan: 0, colspan: 0 } // 被合并隐藏
        }
        return { rowspan: 1, colspan: 1 } // 右足和左足照常显示
      }

      // 后续"位置"列合并逻辑
      if (columnIndex === 0) {
        if (rowIndex === 1) return { rowspan: 4, colspan: 1 }
        if (rowIndex >= 2 && rowIndex <= 4) return { rowspan: 0, colspan: 0 }
        if (rowIndex === 5) return { rowspan: 4, colspan: 1 }
        if (rowIndex >= 6 && rowIndex <= 8) return { rowspan: 0, colspan: 0 }
        if (rowIndex === 9) return { rowspan: 1, colspan: 1 }
      }
    },
    async handleSave() {
      const tableDataTemp = this.extractTableData(this.tableData, ['right', 'left']).reduce((acc, item) => {
        acc = Object.assign(acc, item)
        return acc
      }, {})
      const result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          ...tableDataTemp,
          ...this.form,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    },

    extractTableData(data, mappingFields = ['right', 'left']) {
      return data.map((item) => {
        const result = {}
        for (const key in item) {
          if (key.startsWith('right') && mappingFields.includes(item[key])) {
            result[key] = item[item[key]]
          }
          if (key.startsWith('left') && mappingFields.includes(item[key])) {
            result[key] = item[item[key]]
          }
        }
        return result
      })
    }
  }
}
</script>

<style scoped lang="scss">
.device-barcode {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
