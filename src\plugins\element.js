import Vue from 'vue'
import Cookies from 'js-cookie'

// Element UI 组件
import {
  Pagination,
  Dialog,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Menu,
  Submenu,
  MenuItem,
  Input,
  InputNumber,
  Radio,
  RadioGroup,
  RadioButton,
  Checkbox,
  CheckboxGroup,
  Switch,
  Select,
  Option,
  Button,
  ButtonGroup,
  Rate,
  Table,
  TableColumn,
  Empty,
  Descriptions,
  DescriptionsItem,
  DatePicker,
  TimePicker,
  Popover,
  Tooltip,
  Breadcrumb,
  BreadcrumbItem,
  Form,
  FormItem,
  Tabs,
  TabPane,
  Tag,
  Tree,
  Alert,
  Icon,
  Row,
  Col,
  Upload,
  Spinner,
  Badge,
  Card,
  Autocomplete,
  Carousel,
  CarouselItem,
  Collapse,
  CollapseItem,
  Avatar,
  Cascader,
  CascaderPanel,
  Container,
  Header,
  Aside,
  Main,
  Footer,
  Timeline,
  TimelineItem,
  Link,
  Scrollbar,
  Progress,
  Divider,
  Slider,
  Image,
  Loading,
  MessageBox,
  Message,
  Drawer,
  Popconfirm,
  Skeleton,
  SkeletonItem,
  Transfer,
  Steps,
  Step,
  Notification
} from 'element-ui'

// 设置默认尺寸
const elementSize = Cookies.get('size') || 'medium'

// 批量注册Element UI组件
const elementComponents = [
  Pagination,
  Dialog,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Menu,
  Submenu,
  MenuItem,
  Input,
  InputNumber,
  Radio,
  RadioGroup,
  RadioButton,
  Checkbox,
  CheckboxGroup,
  Switch,
  Select,
  Option,
  Button,
  ButtonGroup,
  Rate,
  Table,
  TableColumn,
  Empty,
  Descriptions,
  DescriptionsItem,
  DatePicker,
  TimePicker,
  Popover,
  Tooltip,
  Breadcrumb,
  BreadcrumbItem,
  Form,
  FormItem,
  Tabs,
  TabPane,
  Tag,
  Tree,
  Alert,
  Icon,
  Row,
  Col,
  Upload,
  Spinner,
  Badge,
  Card,
  Autocomplete,
  Carousel,
  CarouselItem,
  Collapse,
  CollapseItem,
  Avatar,
  Cascader,
  CascaderPanel,
  Container,
  Header,
  Aside,
  Main,
  Footer,
  Timeline,
  TimelineItem,
  Link,
  Scrollbar,
  Progress,
  Divider,
  Slider,
  Image,
  Drawer,
  Popconfirm,
  Skeleton,
  SkeletonItem,
  Transfer,
  Steps,
  Step
]

elementComponents.forEach((component) => {
  Vue.use(component, { size: elementSize })
})

// 注册指令
Vue.use(Loading.directive)

// 注册消息提示
Vue.prototype.$alert = MessageBox.alert
Vue.prototype.$confirm = MessageBox.confirm
Vue.prototype.$message = Message
Vue.prototype.$notify = Notification
Vue.prototype.$prompt = MessageBox.prompt

export default {
  // 导出以便在main.js中可以引用
}
