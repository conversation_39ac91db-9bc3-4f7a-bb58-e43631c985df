<!-- 家医团队 -->
<template>
  <div class="family-medical-team">
    <el-card class="family-medical-team-search">
      <el-form :model="queryParams" label-width="120px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="医疗机构：" prop="depart">
              <TreeSelect
                v-model="queryParams.departCode"
                :data="departTree"
                :props="{
                  children: 'children',
                  label: 'departName',
                  value: 'departCode'
                }"
                placeholder="请选择"
                @change="handleDepartChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="团队名称：" prop="teamName">
              <el-input v-model="queryParams.teamName" placeholder="请输入团队名称" />
            </el-form-item>
          </el-col>

          <el-col :span="6" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="family-medical-team-table">
      <el-button type="primary" style="margin-bottom: 10px" @click="handleAdd">新增</el-button>
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 90px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #operation="{ row }">
          <el-button type="text" @click="handleDetail(row)">查看</el-button>
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" style="color: red" @click="handleDelete(row)">删除</el-button>
        </template>
      </base-table>
    </el-card>

    <ProDialog :visible.sync="addDialogVisible" :title="title" width="960px">
      <AddForm ref="addForm" :depart-tree="departTree" @refresh="refreshTable" />
      <template #footer>
        <el-button @click="handleCancel">关闭</el-button>
      </template>
    </ProDialog>
  </div>
</template>

<script>
import { getFamilyMedicalTeamList, deleteFamilyMedicalTeam } from '@/api/familyMedicalTeam'
import { getOrgTreeByIdApi } from '@/api/system'
import { getUserId } from '@/utils/auth'
import { localCache } from '@/utils/cache'
import BaseTable from '@/components/BaseTable/index.vue'
import TreeSelect from '@/components/TreeSelect/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ProDialog from '@/components/ProDialog/index.vue'
import AddForm from './component/addForm.vue'

export default {
  name: 'FamilyMedicalTeam',
  components: {
    BaseTable,
    TreeSelect,
    ProDialog,
    AddForm
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        departCode: localCache.getCache('userInfo').departCode || ''
      },
      departTree: [],
      title: '新增',
      columns: [
        { label: '机构名称', prop: 'departName', align: 'center' },
        { label: '团队名称', prop: 'teamName', align: 'center' },
        { label: '创建人', prop: 'createUsername', align: 'center' },
        { label: '创建时间', prop: 'createTime', align: 'center' },
        { label: '操作', prop: 'operation', align: 'center', slot: 'operation', width: 160 }
      ]
    }
  },
  created() {
    this.getDepartTree()
  },
  methods: {
    async getTableList(params) {
      return await getFamilyMedicalTeamList(params)
    },

    // 机构树
    async getDepartTree() {
      const res = await getOrgTreeByIdApi({
        patientId: getUserId()
      })
      this.departTree = res.data
    },

    handleDepartChange(value) {
      this.queryParams.departCode = value
    },
    handleCancel() {
      this.addDialogVisible = false
    },

    handleAdd() {
      this.addDialogVisible = true
      this.title = '新增'
      this.$nextTick(() => {
        this.$refs.addForm.handleDetail(null, 'add')
      })
    },

    handleDetail(row) {
      this.addDialogVisible = true
      this.title = '查看'
      this.$nextTick(() => {
        this.$refs.addForm.handleDetail(row, 'view')
      })
    },

    handleEdit(row) {
      this.addDialogVisible = true
      this.title = '编辑'
      this.$nextTick(() => {
        this.$refs.addForm.handleDetail(row, 'edit')
      })
    },

    handleDelete(row) {
      this.handleConfirmDelete({
        params: { id: row.id },
        deleteApi: deleteFamilyMedicalTeam,
        message: '确定删除该团队吗？'
      })
    },

    refreshTable() {
      this.handleSearch()
    },

    handleReset() {
      this.queryParams = {
        departCode: localCache.getCache('userInfo').departCode || '',
        teamName: '',
        pageNo: 1,
        pageSize: 20
      }
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.family-medical-team {
  display: flex;
  flex-direction: column;
  height: 100%;
  .family-medical-team-search {
    margin: 16px;
    height: 77px;
  }
  .family-medical-team-table {
    margin: 0 16px;
    flex: 1;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
}
</style>
