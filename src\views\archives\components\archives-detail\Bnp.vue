<!-- 检验检查记录 -->
<template>
  <div class="inspection-record">
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      row-key="id"
      :columns="columns"
      :show-pagination="showPagination"
    />
  </div>
</template>

<script>
import { getUserDbBnpRecord } from '@/api/archives'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'

export default {
  name: 'Bnp',
  components: {
    BaseTable
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        patientId: this.$route.query.id
      },
      columns: [
        { label: '时间', prop: 'createTime' },
        { label: 'BNP', prop: 'bnp' },
        { label: 'NT-proBNP', prop: 'ntProBnp' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      return await getUserDbBnpRecord(params)
    }
  }
}
</script>
