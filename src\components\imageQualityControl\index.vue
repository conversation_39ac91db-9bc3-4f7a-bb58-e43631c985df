<template>
  <ProDialog
    :visible.sync="visible"
    fullscreen
    :close-on-press-escape="false"
    class="image-quality-control-dialog"
    :before-close="handleClose"
  >
    <template #title>
      <span class="title">影像质控</span>
    </template>

    <div class="image-quality-control">
      <div class="tool">
        <div class="tool-buttons">
          <div
            v-for="tool in toolList"
            :key="tool.name"
            class="tool-item"
            :class="{
              active: activeTool === tool.name,
              disabled: tool.disabled
            }"
            :title="tool.label"
            @click="!tool.disabled && tool.action()"
          >
            <svg-icon :icon-class="tool.icon" class="tool-icon" />
          </div>
        </div>
      </div>

      <div class="annotation">
        <!-- DICOM图像查看器容器 -->
        <div ref="dicomElement" class="dicom-viewer" />

        <!-- 多帧滚动条 - 仅在多帧时显示 -->
        <div v-if="totalFrames > 1" class="frame-scroll-bar">
          <div ref="scrollTrack" class="scroll-track" @click="handleScrollTrackClick">
            <div
              ref="scrollThumb"
              class="scroll-thumb"
              :style="scrollThumbStyle"
              @mousedown="handleScrollThumbMouseDown"
            />
          </div>
          <div class="frame-indicator">{{ currentImageIdIndex + 1 }} / {{ totalFrames }}</div>
        </div>
      </div>

      <!-- 右侧信息面板 - 显示文件列表和患者信息 -->
      <div class="image-information">
        <!-- 信息面板头部 - 时间和视图切换 -->
        <div class="image-information-header">
          <svg-icon icon-class="clock" />
          <div class="create-time">{{ createTime }}</div>
          <div class="switch">
            <!-- 视图切换按钮 -->
            <svg-icon icon-class="list" style="cursor: pointer" />
            <svg-icon icon-class="card" style="margin-left: 16px; cursor: pointer" />
          </div>
        </div>

        <!-- 患者基本信息 -->
        <div class="patient-info">
          <div class="patient-name">{{ detail.patientName }}</div>
          <div class="patient-age">{{ detail.age }}岁</div>
        </div>

        <div class="content">
          <!-- 图片文件列表 -->
          <div class="photo">
            <p>图片</p>
            <div class="photo-list">
              <div
                v-for="item in photoList"
                :key="item.id"
                class="photo-item"
                :class="{ selected: selectedItemId === item.id }"
                @click="handleClick(item)"
              >
                <img :src="getFullUrl(item.url)" alt="">
                <!-- 显示图像帧数 -->
                <div class="frame-count">{{ item.frameCount }} 幅</div>
              </div>
            </div>
          </div>

          <!-- 视频文件列表 -->
          <div class="video">
            <p>视频</p>
            <div class="video-list">
              <div
                v-for="item in videoList"
                :key="item.id"
                class="video-item"
                :class="{ selected: selectedItemId === item.id }"
                @click="handleClick(item)"
              >
                <video class="video-player">
                  <source :src="getFullUrl(item.url)">
                </video>
                <!-- 视频播放图标 -->
                <img src="@/assets/cspImg/video-play.png" alt="播放视频" class="video-play-icon">
                <!-- 显示视频帧数 -->
                <div class="frame-count">{{ item.frameCount }} 幅</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 操作记录 -->
        <div class="operation-record">
          <p>操作记录</p>
          <div class="operation-record-list">
            <div v-for="item in measurements" :key="item.id" class="operation-record-item">
              <div class="operation-record-item-content">
                <div class="operation-record-item-header">
                  <div class="operation-record-item-content-tool">{{ getToolNameInChinese(item.toolName) }}</div>
                  <div v-if="!disabled" class="operation-record-item-actions">
                    <!-- <i class="el-icon-edit-outline edit-btn" title="编辑" /> -->
                    <i class="el-icon-delete delete-btn" title="删除" @click="deleteMeasurement(item)" />
                  </div>
                </div>
                <div v-if="item.toolName === 'Length'" class="operation-record-item-content-data">
                  <div>{{ item.length.toFixed(2) }} mm</div>
                </div>
                <div
                  v-if="item.toolName === 'EllipticalRoi' || item.toolName === 'RectangleRoi'"
                  class="operation-record-item-content-data"
                >
                  <div>Area: {{ item.cachedStats.area.toFixed(2) }} mm²</div>
                </div>
                <div v-if="item.toolName === 'ArrowAnnotate'" class="operation-record-item-content-data">
                  <div>{{ item.text }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框底部操作按钮 -->
    <div slot="footer">
      <div class="footer-content">
        <!-- 多帧DICOM控制栏 - 仅在多帧图像时显示 -->
        <!-- <div v-if="totalFrames > 1" class="frame-controls">
          <div class="frame-controls-left">

            <el-button size="mini" type="primary" icon="el-icon-arrow-left" @click="showPreviousFrame">
              上一帧
            </el-button>

            <el-button
              size="mini"
              :type="isPlaying ? 'warning' : 'success'"
              :icon="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'"
              @click="togglePlayPause"
            >
              {{ isPlaying ? '暂停' : '播放' }}
            </el-button>

            <el-button size="mini" type="primary" icon="el-icon-arrow-right" @click="showNextFrame"> 下一帧 </el-button>

            <span class="frame-info">{{ currentImageIdIndex + 1 }} / {{ totalFrames }}</span>
          </div>
        </div> -->
        <div class="footer-content-right">
          <el-button @click="handleClose">关闭</el-button>
          <el-button v-if="!disabled" type="primary" @click="saveMeasurements">保存</el-button>
        </div>
      </div>
    </div>
  </ProDialog>
</template>

<script>
import { getImageFileList, saveImageFileList } from '@/api/qualityControl'
import { getFullUrl } from '@/utils/cspUtils'
import { cloneDeep } from 'lodash'
import ProDialog from '@/components/ProDialog/index.vue'
import cornerstone from 'cornerstone-core'
import cornerstoneTools from 'cornerstone-tools'
import cornerstoneWADOImageLoader from 'cornerstone-wado-image-loader'
import cornerstoneWebImageLoader from 'cornerstone-web-image-loader'
import dicomParser from 'dicom-parser'
import cornerstoneMath from 'cornerstone-math'
import Hammer from 'hammerjs'

// Cornerstone 库配置 - 设置外部依赖
cornerstoneWADOImageLoader.external.cornerstone = cornerstone // WADO图像加载器使用cornerstone核心
cornerstoneWADOImageLoader.external.dicomParser = dicomParser // WADO图像加载器使用DICOM解析器
cornerstoneWebImageLoader.external.cornerstone = cornerstone // Web图像加载器使用cornerstone核心
cornerstoneTools.external.cornerstone = cornerstone // 工具库使用cornerstone核心
cornerstoneTools.external.cornerstoneMath = cornerstoneMath // 工具库使用数学库
cornerstoneTools.external.Hammer = Hammer // 工具库使用触摸手势库

// 初始化cornerstone工具库
cornerstoneTools.init()

// 获取cornerstone工具事件常量
const { EVENTS } = cornerstoneTools

// 设置工具颜色
cornerstoneTools.toolColors.setToolColor('red')

// 重写箭头标注工具的文本输入方法，改为中文提示
if (!window.originalPrompt) {
  window.originalPrompt = window.prompt
  window.prompt = function(message, defaultText) {
    // 箭头标注工具的各种英文提示，统一替换为中文
    if (
      message === 'Enter your annotation:' ||
      message.includes('Enter your annotation') ||
      message === 'Enter annotation text:' ||
      message.includes('annotation')
    ) {
      return window.originalPrompt('请输入标注内容:', defaultText || '')
    }
    // 其他常见的英文提示也可以替换为中文
    if (message === 'Enter text:' || message === 'Please enter text:') {
      return window.originalPrompt('请输入文本:', defaultText || '')
    }
    // 其他情况保持原样
    return window.originalPrompt(message, defaultText)
  }
}

// 注册医学影像查看工具（cornerstone-tools v6需要指定name）
cornerstoneTools.addTool(cornerstoneTools.PanTool, { name: 'Pan' }) // 平移工具
cornerstoneTools.addTool(cornerstoneTools.ZoomTool, { name: 'Zoom' }) // 缩放工具
cornerstoneTools.addTool(cornerstoneTools.LengthTool, { name: 'Length' }) // 长度测量工具
cornerstoneTools.addTool(cornerstoneTools.RectangleRoiTool, { name: 'RectangleRoi' }) // 矩形测量工具
cornerstoneTools.addTool(cornerstoneTools.EllipticalRoiTool, { name: 'EllipticalRoi' }) // 椭圆测量工具
cornerstoneTools.addTool(cornerstoneTools.ArrowAnnotateTool, { name: 'ArrowAnnotate' }) // 箭头标注工具
cornerstoneTools.addTool(cornerstoneTools.WwwcTool, { name: 'Wwwc' }) // 光标工具

export default {
  name: 'ImageQualityControl',
  components: { ProDialog },
  props: {
    detail: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false }
  },
  data() {
    return {
      visible: false, // 对话框显示状态

      // 文件列表数据
      photoList: [], // 图片文件列表
      videoList: [], // 视频文件列表
      dicomList: [], // DICOM文件列表
      createTime: '', // 文件创建时间
      selectedItemId: null, // 当前选中的文件ID

      // DICOM多帧控制
      imageIds: [], // 存储所有帧的imageId数组
      currentImageIdIndex: 0, // 当前显示的帧索引
      totalFrames: 1, // 总帧数
      isPlaying: false, // 是否正在播放动画
      playInterval: null, // 播放定时器
      frameRate: 1, // 播放帧率（FPS）

      // cornerstone工具状态
      loaded: false, // 是否已为元素加载工具
      activeTool: 'Pan', // 当前激活的工具名称
      measurements: [], // 测量数据存储
      measurementAddedHandler: null, // 测量添加事件处理器引用
      wheelEventHandler: null, // 滚轮事件处理器引用
      wheelTimeout: null, // 滚轮防抖定时器
      isNeedSave: false, // 是否需要保存
      pendingReviewOperation: null, // 待回显的标注数据
      allFrameMeasurements: [], // 存储所有帧的标注数据

      // 滚动条相关状态
      isDragging: false, // 是否正在拖拽滚动条
      dragStartY: 0, // 拖拽开始时的Y坐标
      dragStartFrame: 0, // 拖拽开始时的帧索引
      scrollTrackHeight: 0, // 滚动条轨道高度

      // 工具配置列表 - 声明式对象动态渲染
      toolList: [
        {
          name: 'Pan',
          label: '平移',
          icon: 'Pan',
          action: () => this.activateTool('Pan')
        },
        {
          name: 'Zoom',
          label: '缩放',
          icon: 'Zoom',
          action: () => this.activateTool('Zoom')
        },
        {
          name: 'Length',
          label: '长度',
          icon: 'Length',
          disabled: this.disabled,
          action: () => this.activateTool('Length')
        },
        {
          name: 'RectangleRoi',
          label: '矩形',
          icon: 'RectangleRoi',
          disabled: this.disabled,
          action: () => this.activateTool('RectangleRoi')
        },
        {
          name: 'EllipticalRoi',
          label: '椭圆',
          icon: 'EllipticalRoi',
          disabled: this.disabled,
          action: () => this.activateTool('EllipticalRoi')
        },
        {
          name: 'ArrowAnnotate',
          label: '箭头标注',
          icon: 'ArrowAnnotate',
          disabled: this.disabled,
          action: () => this.activateTool('ArrowAnnotate')
        },
        {
          name: 'Wwwc',
          label: '亮度',
          icon: 'Wwwc',
          disabled: this.disabled,
          action: () => this.activateTool('Wwwc')
        },
        {
          name: 'video-play',
          label: '播放',
          icon: 'video-play',
          disabled: this.disabled,
          action: () => this.playVideo('video-play')
        },
        {
          name: 'clear',
          label: '清除',
          icon: 'clear',
          disabled: this.disabled,
          action: () => {
            if (this.activeTool === 'clear') {
              this.activeTool = ''
            } else {
              this.activeTool = 'clear'
              this.clearMeasurements('clear')
            }
          }
        },
        {
          name: 'reset',
          label: '重置',
          icon: 'reset',
          disabled: this.disabled,
          action: () => {
            if (this.activeTool === 'reset') {
              this.activeTool = ''
            } else {
              this.activeTool = 'reset'
              this.resetViewer('reset')
            }
          }
        }
      ]
    }
  },
  computed: {
    // 计算滚动条滑块的样式
    scrollThumbStyle() {
      if (this.totalFrames <= 1) return {}

      // 获取滚动条轨道高度
      const trackHeight = this.scrollTrackHeight || 400 // 默认高度

      // 计算滑块高度 - 最小20px，最大轨道高度的1/3
      const minThumbHeight = 20
      const maxThumbHeight = trackHeight / 3
      const thumbHeight = Math.max(minThumbHeight, Math.min(maxThumbHeight, (trackHeight / this.totalFrames) * 5))

      // 计算滑块位置 - 基于当前帧在总帧数中的比例
      const progress = this.currentImageIdIndex / (this.totalFrames - 1)
      const maxTop = trackHeight - thumbHeight
      const top = progress * maxTop

      return {
        height: `${thumbHeight}px`,
        top: `${top}px`,
        transform: 'translateY(0)' // 确保不受其他样式影响
      }
    }
  },
  beforeDestroy() {
    // 组件销毁前重置状态
    this.resetDialogState()

    // 恢复原始的prompt方法
    if (window.originalPrompt) {
      window.prompt = window.originalPrompt
    }
  },
  methods: {
    getFullUrl,

    async getImageFileListFn(params) {
      const res = await getImageFileList(params)
      if (res.code === 200 && res.data && res.data.length > 0) {
        this.createTime = res.data[0].createTime
        this.isNeedSave = false
        this.photoList = res.data
          .map((item) =>
            (item.photoUrl
              ? { url: item.photoUrl, id: item.id, frameCount: item.frameCount, reviewOperation: item.reviewOperation }
              : null))
          .filter(Boolean)
        this.videoList = res.data
          .map((item) =>
            (item.videoUrl
              ? { url: item.videoUrl, id: item.id, frameCount: item.frameCount, reviewOperation: item.reviewOperation }
              : null))
          .filter(Boolean)
        this.dicomList = res.data
          .map((item) =>
            (item.dicomUrl
              ? { url: item.dicomUrl, id: item.id, frameCount: item.frameCount, reviewOperation: item.reviewOperation }
              : null))
          .filter(Boolean)

        // 只有在没有选中文件时才自动选择第一个
        if (!this.selectedItemId && this.photoList.length > 0) {
          setTimeout(() => {
            this.handleClick(this.photoList[0])
          }, 500)
        } else if (!this.selectedItemId && this.videoList.length > 0) {
          setTimeout(() => {
            this.handleClick(this.videoList[0])
          }, 500)
        }

        return res
      }
    },

    async handleClick(item) {
      if (this.measurements.length > 0 && this.isNeedSave) {
        this.$message.error('请先保存标注')
        return
      }
      this.selectedItemId = item.id
      const dicom = this.dicomList.find((it) => it.id === item.id)
      if (dicom && dicom.url) {
        await this.loadDicom(this.getFullUrl(dicom.url))
        // 回显保存的测量数据
        if (dicom.reviewOperation) {
          this.pendingReviewOperation = dicom.reviewOperation
          const reviewOperation = JSON.parse(dicom.reviewOperation)
          // 保存所有帧的标注数据
          this.allFrameMeasurements = reviewOperation
          // 延迟回显，确保DICOM加载完成
          setTimeout(() => {
            this.getReviewOperation(this.$refs.dicomElement, reviewOperation)
          }, 200)
        }
      }
    },

    // 删除测量标注
    deleteMeasurement(item) {
      // 从cornerstone图像上删除对应的标注
      const element = this.$refs.dicomElement
      if (element) {
        // 获取当前工具状态
        const toolState = cornerstoneTools.getToolState(element, item.toolName)

        if (toolState && toolState.data) {
          // 找到对应的标注数据并删除
          const index = toolState.data.findIndex((data) => data.uuid === item.uuid)

          if (index !== -1) {
            toolState.data.splice(index, 1)
            // 刷新图像显示以移除标注
            cornerstone.updateImage(element)
            // 从本地测量数据中删除
            this.measurements = this.measurements.filter((it) => it.uuid !== item.uuid)
            // 从所有帧标注数据中删除
            this.allFrameMeasurements = this.allFrameMeasurements.filter((it) => it.uuid !== item.uuid)
          } else {
            this.$message.error('没有找到对应的标注')
          }
        }
      }
    },

    // 回显保存的测量数据
    async getReviewOperation(element, reviewOperation) {
      if (!reviewOperation || !Array.isArray(reviewOperation)) return

      // 清除现有的工具状态
      cornerstoneTools.clearToolState(element, 'Length')
      cornerstoneTools.clearToolState(element, 'RectangleRoi')
      cornerstoneTools.clearToolState(element, 'EllipticalRoi')
      cornerstoneTools.clearToolState(element, 'ArrowAnnotate')
      // 过滤当前帧的标注数据
      const currentFrameMeasurements = reviewOperation.filter(
        (item) => item.currentImageIdIndex === this.currentImageIdIndex
      )

      currentFrameMeasurements.forEach((item) => {
        const toolData = {
          visible: true, // 保证可见
          active: false, // 非激活，不能拖动
          invalidated: false, // 重要：设为false，让标注立即显示
          handles: item.handles,
          uuid: item.uuid,
          length: item.length,
          text: item.text,
          cachedStats: item.cachedStats
        }

        cornerstoneTools.addToolState(element, item.toolName, toolData)
      })

      // 强制刷新图像，确保标注显示
      cornerstone.updateImage(element)

      // 激活所有工具，让标注能够显示
      cornerstoneTools.setToolActiveForElement(element, 'Length', { mouseButtonMask: 1 })
      cornerstoneTools.setToolActiveForElement(element, 'RectangleRoi', { mouseButtonMask: 1 })
      cornerstoneTools.setToolActiveForElement(element, 'EllipticalRoi', { mouseButtonMask: 1 })
      cornerstoneTools.setToolActiveForElement(element, 'ArrowAnnotate', { mouseButtonMask: 1 })
      cornerstoneTools.setToolActiveForElement(element, 'Pan', { mouseButtonMask: 1 })

      if (this.isPlaying) {
        this.activeTool = 'video-play'
      } else {
        this.activeTool = 'Pan'
      }

      // 更新本地测量数据用于UI显示 - 只显示当前帧的标注
      this.measurements = cloneDeep(currentFrameMeasurements)
    },

    // 更新所有帧的标注数据
    updateAllFrameMeasurements(newMeasurement) {
      // 移除同帧同UUID的旧标注（如果存在）
      this.allFrameMeasurements = this.allFrameMeasurements.filter(
        (item) => !(item.currentImageIdIndex === newMeasurement.currentImageIdIndex && item.uuid === newMeasurement.uuid)
      )

      // 添加新标注
      this.allFrameMeasurements.push(newMeasurement)
    },

    // 重新加载当前帧的标注
    reloadAnnotationsForCurrentFrame() {
      const element = this.$refs.dicomElement
      if (!element || !this.allFrameMeasurements.length) return

      try {
        // 使用保存的所有帧标注数据
        this.getReviewOperation(element, this.allFrameMeasurements)
      } catch (error) {
        console.error('重新加载标注失败:', error)
      }
    },

    // 保存测量数据
    async saveMeasurements() {
      if (this.allFrameMeasurements.length > 0) {
        const saveParams = {
          id: this.selectedItemId,
          reviewOperation: JSON.stringify(this.allFrameMeasurements)
        }
        const res = await saveImageFileList(saveParams)
        if (res.code === 200) {
          this.$message.success('保存成功')
          this.isNeedSave = false

          // 保存当前状态，用于恢复
          const currentState = {
            selectedItemId: this.selectedItemId,
            currentImageIdIndex: this.currentImageIdIndex,
            totalFrames: this.totalFrames,
            imageIds: [...this.imageIds],
            allFrameMeasurements: [...this.allFrameMeasurements],
            isPlaying: this.isPlaying
          }

          const params = {
            recordId: this.detail.data.recordId,
            module: 'cs',
            item: this.detail.itemCode,
            patientId: this.detail.data.patientId
          }

          // 重新加载文件列表后恢复状态
          await this.getImageFileListFn(params)

          // 恢复当前帧状态
          this.restoreCurrentFrameState(currentState)
        }
      } else {
        this.$message.error('请先进行标注')
      }
    },

    // 加载DICOM文件到cornerstone查看器
    async loadDicom(imageId) {
      const element = this.$refs.dicomElement
      if (!element) {
        console.error('DICOM元素未找到')
        return
      }

      cornerstone.enable(element)

      // 添加鼠标滚轮事件监听器，实现帧切换
      this.addWheelEventListener(element)

      try {
        const baseUrl = imageId

        const dataSet = await cornerstoneWADOImageLoader.wadouri.dataSetCacheManager.load(
          baseUrl,
          cornerstoneWADOImageLoader.internal.xhrRequest
        )

        // await this.getPixelSpacing(dataSet)

        // 获取总帧数
        this.totalFrames = dataSet.intString('x00280008')
        if (!this.totalFrames) {
          this.totalFrames = 1
        }

        // 生成所有帧的imageId
        this.imageIds = []
        for (let i = 0; i < this.totalFrames; i++) {
          this.imageIds.push(`wadouri:${baseUrl}?frame=${i}`)
        }

        // 重置状态
        this.currentImageIdIndex = 0
        this.isPlaying = false
        if (this.playInterval) {
          clearInterval(this.playInterval)
          this.playInterval = null
        }

        // 添加自定义元数据提供者（只添加一次）
        // cornerstone.metaData.addProvider(this.customPixelSpacingProvider)

        // 加载并显示第一帧
        await this.loadAndDisplayImage(this.imageIds[0])

        this.resetViewer()

        // 更新滚动条高度
        this.updateScrollTrackHeight()

        // 卸载数据集缓存
        cornerstoneWADOImageLoader.wadouri.dataSetCacheManager.unload(baseUrl)

        // 为元素添加工具 - 这是关键步骤
        if (!this.loaded) {
          try {
            // 为特定元素添加工具
            cornerstoneTools.addToolForElement(element, cornerstoneTools.PanTool, { name: 'Pan' })
            cornerstoneTools.addToolForElement(element, cornerstoneTools.ZoomTool, { name: 'Zoom' })
            cornerstoneTools.addToolForElement(element, cornerstoneTools.LengthTool, { name: 'Length' })
            cornerstoneTools.addToolForElement(element, cornerstoneTools.RectangleRoiTool, { name: 'RectangleRoi' })
            cornerstoneTools.addToolForElement(element, cornerstoneTools.EllipticalRoiTool, { name: 'EllipticalRoi' })
            cornerstoneTools.addToolForElement(element, cornerstoneTools.ArrowAnnotateTool, {
              name: 'ArrowAnnotate',
              configuration: {
                changeTextCallback: (data, eventData, callback) => {
                  const value = window.prompt('请输入编辑后的标注文字', data && data.text ? data.text : '')

                  this.measurementAddedHandler({
                    detail: {
                      measurementData: {
                        ...data,
                        text: value
                      }
                    }
                  })
                  callback(value)
                }
              }
            })

            cornerstoneTools.addToolForElement(element, cornerstoneTools.WwwcTool, { name: 'Wwwc' })
            // 为元素激活默认工具
            cornerstoneTools.setToolActiveForElement(element, 'Pan', { mouseButtonMask: 1 })

            // 添加测量添加事件监听器
            this.measurementAddedHandler = (evt) => {
              const eventData = evt.detail
              this.eventDataHandler(eventData, imageId)
              this.isNeedSave = true
            }
            element.addEventListener(EVENTS.MEASUREMENT_COMPLETED, this.measurementAddedHandler)
          } catch (error) {
            console.error('为元素添加工具失败:', error)
          }

          this.loaded = true
        }

        // 设置当前激活工具为平移
        this.activeTool = 'Pan'
      } catch (err) {
        console.log('err', err)
        this.$message.error('DICOM文件加载失败')
      }
    },

    // 监听事件数据处理
    eventDataHandler(eventData, imageId) {
      const measurementsTemp = cloneDeep(this.measurements)
      let currentMeasurementData = cloneDeep(eventData.measurementData)

      const idx = measurementsTemp.findIndex((item) => item.uuid === currentMeasurementData.uuid)
      if (idx !== -1) {
        currentMeasurementData = {
          ...measurementsTemp[idx],
          ...currentMeasurementData
        }

        measurementsTemp[idx] = currentMeasurementData
      } else {
        currentMeasurementData = cloneDeep({
          ...eventData.measurementData,
          toolName: eventData.toolName,
          imageId,
          currentImageIdIndex: this.currentImageIdIndex
        })
        measurementsTemp.push(currentMeasurementData)
      }

      this.measurements = measurementsTemp

      // 同时更新所有帧的标注数据
      this.updateAllFrameMeasurements(currentMeasurementData)
    },

    // 显示上一帧（多帧DICOM文件导航）
    showPreviousFrame() {
      if (this.imageIds.length <= 1) return
      // 循环索引：当前帧-1，如果小于0则跳到最后一帧
      this.currentImageIdIndex = (this.currentImageIdIndex - 1 + this.imageIds.length) % this.imageIds.length
      this.loadAndDisplayImage(this.imageIds[this.currentImageIdIndex])

      // 帧切换后重新回显标注
      this.reloadAnnotationsForCurrentFrame()
    },

    // 显示下一帧（多帧DICOM文件导航）
    showNextFrame() {
      if (this.imageIds.length <= 1) return
      // 循环索引：当前帧+1，如果超出总帧数则跳到第一帧
      this.currentImageIdIndex = (this.currentImageIdIndex + 1) % this.imageIds.length
      this.loadAndDisplayImage(this.imageIds[this.currentImageIdIndex])

      // 帧切换后重新回显标注
      this.reloadAnnotationsForCurrentFrame()
    },

    // 切换播放/暂停状态（多帧DICOM动画播放）
    togglePlayPause() {
      if (this.imageIds.length <= 1) return

      if (this.isPlaying) {
        // 暂停播放：清除定时器
        clearInterval(this.playInterval)
        this.playInterval = null
        this.isPlaying = false
      } else {
        // 开始播放：创建定时器按帧率切换图像
        const interval = 1000 / this.frameRate // 计算间隔时间（毫秒）
        this.playInterval = setInterval(() => {
          this.currentImageIdIndex = (this.currentImageIdIndex + 1) % this.imageIds.length
          this.loadAndDisplayImage(this.imageIds[this.currentImageIdIndex])
          // 播放时也重新回显标注
          this.reloadAnnotationsForCurrentFrame()
        }, interval)
        this.isPlaying = true
      }
    },

    // 获取 Pixel Spacing，并返回 { row, col }
    async getPixelSpacing(image) {
      const dataSet = image.data
      const ultrasoundRegionSequenceTag = 'x00186011' // Ultrasound Region Sequence
      const sequenceElement = dataSet.elements[ultrasoundRegionSequenceTag]

      if (!sequenceElement || !sequenceElement.items) return null

      let spacingRow = null
      let spacingCol = null
      let hasMm = false

      for (let i = 0; i < sequenceElement.items.length; i++) {
        const itemDataSet = sequenceElement.items[i].dataSet
        const unit = itemDataSet.uint16('x00186024') // 单位
        let row = Math.abs(itemDataSet.double('x0018602c'))
        let col = Math.abs(itemDataSet.double('x0018602e'))

        if (!row || !col) continue

        // 单位换算
        switch (unit) {
          case 1:
            row *= 1000
            col *= 1000
            break // 米 → 毫米
          case 2:
            row *= 10
            col *= 10
            break // 厘米 → 毫米
          case 3:
            break // 毫米，不变
          case 4:
            row /= 1000
            col /= 1000
            break // 微米 → 毫米
        }

        // 找到 unit=3 优先使用
        if (unit === 3) {
          spacingRow = row
          spacingCol = col
          hasMm = true
          break
        }

        // 否则暂存第一个有效值
        if (!hasMm && spacingRow == null && spacingCol == null) {
          spacingRow = row
          spacingCol = col
        }
      }

      return spacingRow && spacingCol ? { row: spacingRow, col: spacingCol } : null
    },

    // 加载并显示指定的图像帧
    async loadAndDisplayImage(imageId) {
      const element = this.$refs.dicomElement
      if (!element) return

      try {
        // 获取当前帧索引
        const frameIndex = this.imageIds.indexOf(imageId)
        if (frameIndex === -1) return

        // 从缓存或网络加载图像
        const image = await cornerstone.loadAndCacheImage(imageId)

        // x00280030 是cornerstonejs工具默认取像素间距的tag
        const existPixelSpacing = image.data.elements.x00280030
        if (!existPixelSpacing) {
          // 获取 spacing
          const spacing = await this.getPixelSpacing(image)

          if (spacing) {
            // 写入 image 对象
            image.rowPixelSpacing = spacing.row
            image.columnPixelSpacing = spacing.col

            // 注册到 metadata provider
            cornerstone.metaData.addProvider((type, id) => {
              if (id === imageId && type === 'imagePlaneModule') {
                return {
                  rowPixelSpacing: spacing.row,
                  columnPixelSpacing: spacing.col
                }
              }
            }, 10000) // 高优先级
          }
        }

        console.log('image', image)

        cornerstone.displayImage(element, image)

        // 更新当前帧索引
        this.currentImageIdIndex = frameIndex

        // 切帧后刷新标注
        this.reloadAnnotationsForCurrentFrame()
      } catch (err) {
        console.error('加载图像失败:', err)
      }
    },

    // 激活指定的cornerstone工具
    activateTool(toolName) {
      const element = this.$refs.dicomElement
      if (!element) return

      if (this.activeTool === toolName) {
        // 如果点击的是当前激活的工具，则切换回平移工具
        cornerstoneTools.setToolActiveForElement(element, 'Pan', { mouseButtonMask: 1 })
        this.activeTool = 'Pan'
      } else {
        // 激活新选择的工具
        cornerstoneTools.setToolActiveForElement(element, toolName, { mouseButtonMask: 1 })
        this.activeTool = toolName
      }
    },

    // 清除当前帧的测量标注
    clearMeasurements() {
      const element = this.$refs.dicomElement
      if (element) {
        // 清除当前帧的标注数据
        cornerstoneTools.clearToolState(element, 'Length')
        cornerstoneTools.clearToolState(element, 'RectangleRoi')
        cornerstoneTools.clearToolState(element, 'EllipticalRoi')
        cornerstoneTools.clearToolState(element, 'ArrowAnnotate')
        // 刷新图像显示以移除标注
        cornerstone.updateImage(element)
      }
      // 清空当前帧的本地测量数据
      this.measurements = []
      // 从所有帧标注数据中移除当前帧的标注
      this.allFrameMeasurements = this.allFrameMeasurements.filter(
        (item) => item.currentImageIdIndex !== this.currentImageIdIndex
      )
      this.isNeedSave = false
      this.deactivateAllTools(element)
    },

    // 禁用或解禁cornerstone工具
    deactivateAllTools(element, disable = false) {
      const tools = ['Zoom', 'Length', 'RectangleRoi', 'EllipticalRoi', 'ArrowAnnotate', 'Wwwc']
      // 注意：不包含Pan工具，因为Pan是基础导航工具

      tools.forEach((toolName) => {
        if (disable) {
          cornerstoneTools.setToolDisabledForElement(element, toolName)
        } else {
          cornerstoneTools.setToolPassiveForElement(element, toolName)
        }
      })

      // 确保Pan工具始终可用
      if (!disable) {
        cornerstoneTools.setToolActiveForElement(element, 'Pan', { mouseButtonMask: 1 })
      }
    },

    // 播放&暂停视频
    playVideo(toolName) {
      const element = this.$refs.dicomElement
      if (!element) return
      if (this.activeTool === toolName) {
        this.activeTool = ''
      } else {
        this.activeTool = toolName
      }
      this.togglePlayPause()
      this.deactivateAllTools(element)
    },

    // 处理关闭对话框
    handleClose() {
      // 如果有未保存的标注，提示用户
      if (this.isNeedSave) {
        this.$confirm('有未保存的标注，确定要关闭吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.closeDialog()
          })
          .catch(() => {
            // 用户取消关闭
          })
      } else {
        this.closeDialog()
      }
    },

    // 关闭对话框并重置状态
    closeDialog() {
      this.visible = false
      // 重置状态
      this.resetDialogState()
    },

    // 重置对话框状态
    resetDialogState() {
      // 停止播放
      if (this.playInterval) {
        clearInterval(this.playInterval)
        this.playInterval = null
      }
      this.isPlaying = false

      // 清理滚轮防抖定时器
      if (this.wheelTimeout) {
        clearTimeout(this.wheelTimeout)
        this.wheelTimeout = null
      }

      // 重置数据
      this.measurements = []
      this.allFrameMeasurements = []
      this.pendingReviewOperation = null
      this.isNeedSave = false
      this.selectedItemId = null
      this.currentImageIdIndex = 0
      this.totalFrames = 1
      this.imageIds = []
      this.loaded = false
      this.activeTool = 'Pan'

      // 重置滚动条状态
      this.isDragging = false
      this.dragStartY = 0
      this.dragStartFrame = 0
      this.scrollTrackHeight = 0

      // 移除事件监听器
      const element = this.$refs.dicomElement
      if (element) {
        // 移除测量事件监听器
        if (this.measurementAddedHandler) {
          element.removeEventListener(EVENTS.MEASUREMENT_COMPLETED, this.measurementAddedHandler)
          this.measurementAddedHandler = null
        }

        // 移除滚轮事件监听器
        if (this.wheelEventHandler) {
          element.removeEventListener('wheel', this.wheelEventHandler)
          this.wheelEventHandler = null
        }
      }

      // 清理滚动条拖拽事件监听器
      if (this.isDragging) {
        document.removeEventListener('mousemove', this.handleScrollThumbMouseMove)
        document.removeEventListener('mouseup', this.handleScrollThumbMouseUp)
        document.body.style.userSelect = ''
      }

      // 移除元数据提供者
      try {
        cornerstone.metaData.removeProvider(this.customPixelSpacingProvider)
      } catch (error) {
        // 忽略移除失败的错误
      }
    },

    // 工具名称中文映射
    getToolNameInChinese(toolName) {
      const toolNameMap = {
        Length: '长度测量',
        RectangleRoi: '矩形测量',
        EllipticalRoi: '椭圆测量',
        ArrowAnnotate: '箭头标注'
      }
      return toolNameMap[toolName] || toolName
    },

    // 添加鼠标滚轮事件监听器，实现帧切换
    addWheelEventListener(element) {
      // 移除之前的事件监听器（如果存在）
      element.removeEventListener('wheel', this.handleWheelEvent)

      // 添加新的事件监听器
      this.wheelEventHandler = this.handleWheelEvent.bind(this)
      element.addEventListener('wheel', this.wheelEventHandler, { passive: false })
    },

    // 处理鼠标滚轮事件
    handleWheelEvent(event) {
      // 只有在多帧图像时才处理滚轮事件
      if (this.totalFrames <= 1) return

      // 阻止默认的滚轮行为（如页面滚动）
      event.preventDefault()

      // 添加防抖，避免滚动过快
      if (this.wheelTimeout) {
        clearTimeout(this.wheelTimeout)
      }

      this.wheelTimeout = setTimeout(() => {
        // 获取滚轮方向
        const { deltaY } = event

        if (deltaY > 0) {
          // 向下滚动，显示下一帧
          this.showNextFrame()
        } else if (deltaY < 0) {
          // 向上滚动，显示上一帧
          this.showPreviousFrame()
        }
      }, 200) // 200ms防抖延迟
    },

    // 处理滚动条轨道点击事件
    handleScrollTrackClick(event) {
      if (this.totalFrames <= 1 || this.isDragging) return

      const track = this.$refs.scrollTrack
      if (!track) return

      const rect = track.getBoundingClientRect()
      const clickY = event.clientY - rect.top
      const trackHeight = rect.height

      // 计算点击位置对应的帧索引
      const progress = clickY / trackHeight
      const targetFrame = Math.round(progress * (this.totalFrames - 1))
      const clampedFrame = Math.max(0, Math.min(this.totalFrames - 1, targetFrame))

      // 切换到目标帧
      this.jumpToFrame(clampedFrame)
    },

    // 处理滚动条滑块鼠标按下事件
    handleScrollThumbMouseDown(event) {
      if (this.totalFrames <= 1) return

      event.preventDefault()
      event.stopPropagation()

      this.isDragging = true
      this.dragStartY = event.clientY
      this.dragStartFrame = this.currentImageIdIndex

      // 添加全局鼠标事件监听器
      document.addEventListener('mousemove', this.handleScrollThumbMouseMove)
      document.addEventListener('mouseup', this.handleScrollThumbMouseUp)

      // 添加用户选择禁用，防止拖拽时选中文本
      document.body.style.userSelect = 'none'
    },

    // 处理滚动条滑块鼠标移动事件
    handleScrollThumbMouseMove(event) {
      if (!this.isDragging || this.totalFrames <= 1) return

      const track = this.$refs.scrollTrack
      if (!track) return

      const rect = track.getBoundingClientRect()
      const trackHeight = rect.height

      // 计算鼠标移动的距离对应的帧数变化
      const deltaY = event.clientY - this.dragStartY
      const deltaProgress = deltaY / trackHeight
      const deltaFrames = deltaProgress * (this.totalFrames - 1)

      // 计算目标帧索引
      const targetFrame = Math.round(this.dragStartFrame + deltaFrames)
      const clampedFrame = Math.max(0, Math.min(this.totalFrames - 1, targetFrame))

      // 切换到目标帧
      this.jumpToFrame(clampedFrame)
    },

    // 处理滚动条滑块鼠标释放事件
    handleScrollThumbMouseUp() {
      if (!this.isDragging) return

      this.isDragging = false

      // 移除全局鼠标事件监听器
      document.removeEventListener('mousemove', this.handleScrollThumbMouseMove)
      document.removeEventListener('mouseup', this.handleScrollThumbMouseUp)

      // 恢复用户选择
      document.body.style.userSelect = ''
    },

    // 跳转到指定帧
    jumpToFrame(frameIndex) {
      if (frameIndex === this.currentImageIdIndex || this.totalFrames <= 1) return

      this.currentImageIdIndex = frameIndex
      this.loadAndDisplayImage(this.imageIds[frameIndex])

      // 帧切换后重新回显标注
      this.reloadAnnotationsForCurrentFrame()
    },

    // 更新滚动条轨道高度
    updateScrollTrackHeight() {
      this.$nextTick(() => {
        const track = this.$refs.scrollTrack
        if (track) {
          this.scrollTrackHeight = track.offsetHeight
        }
      })
    },

    // 恢复当前帧状态
    async restoreCurrentFrameState(savedState) {
      if (!savedState || !savedState.selectedItemId) return

      try {
        // 等待DOM更新完成
        await this.$nextTick()

        // 恢复基本状态
        this.selectedItemId = savedState.selectedItemId
        this.currentImageIdIndex = savedState.currentImageIdIndex
        this.totalFrames = savedState.totalFrames
        this.imageIds = savedState.imageIds
        this.allFrameMeasurements = savedState.allFrameMeasurements
        this.isPlaying = savedState.isPlaying

        // 重新加载当前选中的DICOM文件
        const dicom = this.dicomList.find((it) => it.id === savedState.selectedItemId)
        if (dicom && dicom.url) {
          // 直接加载到指定帧，不重新解析DICOM
          await this.loadAndDisplayImage(savedState.imageIds[savedState.currentImageIdIndex])

          // 恢复标注数据
          if (savedState.allFrameMeasurements.length > 0) {
            this.getReviewOperation(this.$refs.dicomElement, savedState.allFrameMeasurements)
          }

          // 更新滚动条高度
          this.updateScrollTrackHeight()

          // 如果之前在播放状态，恢复播放
          if (savedState.isPlaying && this.totalFrames > 1) {
            this.togglePlayPause()
          }
        }
      } catch (error) {
        console.error('恢复当前帧状态失败:', error)
      }
    },

    // 重置查看器到默认状态
    // 恢复默认的缩放、平移和窗宽窗位设置
    resetViewer() {
      const element = this.$refs.dicomElement
      if (!element) return

      // 获取当前显示的图像
      const image = cornerstone.getImage(element)
      if (!image) return

      // 获取图像的默认视窗参数并应用
      const defaultViewport = cornerstone.getDefaultViewportForImage(element, image)
      cornerstone.setViewport(element, defaultViewport)

      // 清除所有测量标注
      this.clearMeasurements()
    }
  }
}
</script>

<style lang="scss" scoped>
.image-quality-control-dialog {
  ::v-deep .pro_dialog {
    background-color: #252525;
    .el-dialog__header {
      background-color: #252525;
    }
    .el-dialog__body {
      padding: 12px 20px 12px 6px;
    }
  }
  .title {
    color: #fff;
  }
  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .frame-controls {
      display: flex;
      align-items: center;
      gap: 10px;

      .frame-controls-left {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .frame-info {
        margin-left: 10px;
        font-weight: bold;
        color: #409eff;
      }

      .wheel-hint {
        margin-left: 10px;
        font-size: 12px;
        color: #909399;
        opacity: 0.8;
      }
    }

    .footer-content-right {
      display: flex;
      gap: 10px;
      margin-left: auto;
    }
  }
}
.image-quality-control {
  display: flex;
  height: 85vh;
  gap: 10px;
  .tool {
    width: 50px;
    height: 100%;
    background-color: #555;

    .tool-buttons {
      display: flex;
      flex-direction: column;
      gap: 10px;
      padding: 10px;
      .tool-item {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        .tool-icon {
          width: 24px !important;
          height: 24px !important;
          margin: 4px;
        }
        &.active {
          background: #38aafd;
        }
        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }

  .annotation {
    flex: 1;
    border: 1px solid #4bc0f1;
    position: relative;

    .dicom-viewer {
      width: 100%;
      height: 100%;
    }

    // 多帧滚动条样式
    .frame-scroll-bar {
      position: absolute;
      right: 10px;
      top: 10px;
      bottom: 10px;
      width: 30px;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 8px;
      z-index: 15;

      .scroll-track {
        flex: 1;
        width: 6px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 3px;
        position: relative;
        cursor: pointer;
        transition: all 0.2s ease;
        min-height: 100px;

        &:hover {
          width: 8px;
          background-color: rgba(255, 255, 255, 0.3);

          .scroll-thumb {
            width: 8px;
            background-color: rgba(75, 192, 241, 0.9);
          }
        }

        .scroll-thumb {
          position: absolute;
          left: 0;
          width: 6px;
          background-color: rgba(75, 192, 241, 0.7);
          border-radius: 3px;
          cursor: grab;
          transition: all 0.2s ease;

          &:hover {
            background-color: rgba(75, 192, 241, 0.9);
          }

          &:active {
            cursor: grabbing;
            background-color: #4bc0f1;
          }
        }
      }

      .frame-indicator {
        background-color: rgba(0, 0, 0, 0.8);
        color: #4bc0f1;
        padding: 4px 6px;
        border-radius: 4px;
        font-size: 10px;
        font-weight: bold;
        white-space: nowrap;
        border: 1px solid rgba(75, 192, 241, 0.3);
        backdrop-filter: blur(4px);
        min-width: 40px;
        text-align: center;
      }
    }

    .debug-panel {
      position: absolute;
      top: 20px;
      right: 20px;
      background-color: rgba(0, 0, 0, 0.8);
      padding: 16px;
      border-radius: 8px;
      border: 1px solid #4bc0f1;
      z-index: 10;
      min-width: 200px;

      .debug-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #4bc0f1;
          font-size: 12px;
          font-weight: bold;
        }

        .value {
          color: #fff;
          font-size: 12px;
          font-family: monospace;
        }
      }
    }

    .frame-controls {
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      flex-direction: column;
      gap: 12px;
      background-color: rgba(0, 0, 0, 0.8);
      padding: 16px 24px;
      border-radius: 25px;
      border: 1px solid #4bc0f1;
      z-index: 10;
      min-width: 400px;

      .frame-controls-left {
        display: flex;
        align-items: center;
        gap: 16px;
        justify-content: center;

        .frame-info {
          color: #fff;
          font-size: 14px;
          font-weight: bold;
          min-width: 60px;
          text-align: center;
        }

        .el-button {
          border-radius: 20px;

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }

      .frame-controls-right {
        display: flex;
        align-items: center;
        gap: 12px;
        justify-content: center;

        .frame-rate-label {
          color: #fff;
          font-size: 12px;
          white-space: nowrap;
        }

        .frame-rate-slider {
          width: 120px;
          height: 6px;
          border-radius: 3px;
          background: #4bc0f1;
          outline: none;

          &::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #fff;
            cursor: pointer;
            border: 2px solid #4bc0f1;
          }

          &::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #fff;
            cursor: pointer;
            border: 2px solid #4bc0f1;
          }
        }

        .frame-rate-value {
          color: #fff;
          font-size: 12px;
          font-weight: bold;
          min-width: 50px;
          text-align: center;
        }
      }
    }
  }

  .image-information {
    width: 300px;
    height: 100%;
    border: 1px solid #fff;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .image-information-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      height: 50px;
      .create-time {
        color: #fff;
      }
    }
    .patient-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 20px;
      background-color: #6aa9ba;
      color: #fff;
      font-size: 16px;
      height: 50px;
    }
    .content {
      color: #fff;
      padding: 16px;
      flex: 1;
      min-height: 300px;
      overflow-y: auto;
      .photo {
        .photo-list {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 8px;
          margin: 8px 0;
          cursor: pointer;

          .photo-item {
            position: relative;
            border: 2px solid transparent;
            border-radius: 6px;
            transition: all 0.3s ease;
            cursor: pointer;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            &.selected {
              border-color: #ffd700;
              box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
            }

            img {
              width: 100%;
              height: auto;
              border-radius: 4px;
              object-fit: cover;
            }

            .frame-count {
              position: absolute;
              bottom: 8px;
              left: 8px;
              background-color: rgba(0, 0, 0, 0.7);
              color: #fff;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              z-index: 5;
            }
          }
        }
      }

      .video {
        .video-list {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 8px;
          margin: 8px 0;
          cursor: pointer;

          .video-item {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid transparent;
            border-radius: 6px;
            transition: all 0.3s ease;
            cursor: pointer;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            &.selected {
              border-color: #ffd700;
              box-shadow: 0 0 15px rgba(255, 215, 0, 0.6);
            }

            .video-player {
              width: 100%;
              height: auto;
              border-radius: 4px;
              object-fit: cover;
            }

            .video-play-icon {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 40px;
              height: 40px;
              z-index: 10;
            }

            .frame-count {
              position: absolute;
              bottom: 8px;
              left: 8px;
              background-color: rgba(0, 0, 0, 0.7);
              color: #fff;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              z-index: 5;
            }
          }
        }
      }
    }
    .operation-record {
      color: #fff;
      height: 290px;
      padding: 0 16px 16px 16px;
      overflow-y: auto;

      .operation-record-list {
        margin-top: 8px;

        .operation-record-item {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 4px;
          margin-bottom: 8px;
          padding: 8px 10px;

          &:hover {
            background: rgba(255, 255, 255, 0.08);
          }

          &:last-child {
            margin-bottom: 0;
          }

          .operation-record-item-content {
            .operation-record-item-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 6px;

              .operation-record-item-content-tool {
                font-size: 14px;
                font-weight: 500;
                color: #4bc0f1;
                flex: 1;
              }

              .operation-record-item-actions {
                display: flex;
                gap: 8px;

                .edit-btn,
                .delete-btn {
                  font-size: 14px;
                  cursor: pointer;
                  padding: 2px;
                  border-radius: 3px;
                  transition: all 0.2s ease;
                  opacity: 0.6;

                  &:hover {
                    opacity: 1;
                  }
                }

                .edit-btn {
                  color: #409eff;

                  &:hover {
                    background: rgba(64, 158, 255, 0.1);
                  }
                }

                .delete-btn {
                  color: #f56c6c;

                  &:hover {
                    background: rgba(245, 108, 108, 0.1);
                  }
                }
              }
            }

            .operation-record-item-content-data {
              background: rgba(0, 0, 0, 0.1);
              border-radius: 3px;
              padding: 6px 8px;
              margin-top: 6px;

              div {
                font-size: 12px;
                line-height: 1.4;
                color: #ccc;

                &:not(:last-child) {
                  margin-bottom: 2px;
                }
              }
            }
          }
        }

        /* 空状态样式 */
        &:empty::after {
          content: '暂无测量记录';
          display: block;
          text-align: center;
          color: #909399;
          font-size: 12px;
          padding: 20px;
          opacity: 0.6;
        }
      }
    }

    /* 统一滚动条样式 */
    .content,
    .operation-record {
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(75, 192, 241, 0.3);
        border-radius: 3px;
        transition: background 0.3s ease;

        &:hover {
          background: rgba(75, 192, 241, 0.5);
        }
      }
    }
  }
}
</style>
