<!-- 心电图检查 -->
<template>
  <div class="ecg">
    <div class="ecg-data">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <CheckboxGroupField v-model="form.ecgResult" :item="checkboxItem" />
          </el-col>
          <el-col :span="24">
            <el-form-item label="检查所见：">
              <el-input v-model="form.ecgFinding" type="textarea" :rows="3" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="上传报告图片">
              <custom-upload v-model="form.ecgAttachmentUrl" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="ecg-img">
      <img src="@/assets/cspImg/ecg.png" alt="ecg">
    </div>
  </div>
</template>

<script>
import { ecgOptions } from '@/views/receptionCenter/patientReception/component/complicationsScreening.js'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'
import CustomUpload from '@/components/customUpload/index.vue'

export default {
  name: 'ECG',
  components: {
    CheckboxGroupField,
    CustomUpload
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      checkboxItem: {
        label: '检查结果：',
        type: 'checkbox',
        required: true,
        mutuallyExclusive: true, // 互斥
        prop: 'ecgResult',
        options: [...ecgOptions]
      },
      form: {
        ecgResult: [],
        ecgFinding: '',
        ecgAttachmentUrl: ''
      },
      rules: {
        ecgResult: [{ required: true, message: '请选择检查结果' }]
      }
    }
  },
  methods: {
    initData(data) {
      this.form = {
        ecgResult: data.ecgResult ? data.ecgResult.split(',') : [],
        ecgFinding: data.ecgFinding,
        ecgAttachmentUrl: data.ecgAttachmentUrl,
        id: data.id
      }
    },
    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          ecgResult: this.form.ecgResult.join(','),
          ecgFinding: this.form.ecgFinding,
          ecgAttachmentUrl: this.form.ecgAttachmentUrl,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    }
  }
}
</script>

<style lang="scss" scoped>
.ecg {
  display: flex;
  justify-content: space-between;
  gap: 50px;
}
.ecg-data {
  width: 70%;
}
.ecg-img {
  margin-top: 50px;
  flex: 1;
}
</style>
