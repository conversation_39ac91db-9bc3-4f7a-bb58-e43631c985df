<template>
  <div class="csp_home">
    <el-row>
      <el-col :span="9">
        <el-card>
          <div class="diease-count">
            <div class="flag">
              <img src="@/assets/cspImg/brokenLine.png" alt="" class="broken-line">
              <strong>慢病筛查人数</strong>
            </div>
            <div class="diease-contetnt">
              <div v-for="item in dieaseList" :key="item.code" class="diease-item">
                <div class="diease-item-content">
                  <div class="diease-item-content-count">{{ item.count }}</div>
                  <img :src="requireShowDisease(item.diseaseCode)" alt="" class="diease-icon">
                  <div class="diease-item-content-name">{{ item.diseaseName }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="10">
        <el-card>
          <div class="standard">
            <div class="flag">
              <img src="@/assets/cspImg/brokenLine.png" alt="" class="broken-line">
              <strong>规范管理</strong>
            </div>
            <div class="standard-content">
              <div v-for="item in standardList" :key="item.code" class="standard-item">
                <div class="standard-item-content">
                  <img :src="requireShowDisease(item.code)" alt="" class="standard-icon">
                  <div class="standard-item-content-count">{{ item.count }}</div>
                  <div class="standard-item-content-name">{{ item.diseaseName }}</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="5">
        <el-card>
          <div class="getway-container">
            <div class="flag">
              <img src="@/assets/cspImg/brokenLine.png" alt="" class="broken-line">
              <strong>边缘计算控制终端</strong>
            </div>
            <div class="image-container">
              <img v-if="getwayStatus === 1" src="@/assets/cspImg/getwayOnline.png" alt="" class="getway-image">
              <img v-else src="@/assets/cspImg/getwayOffline.png" alt="" class="getway-image">
              <div v-if="getwayStatus === 1" class="getway-status online">在线</div>
              <div v-else class="getway-status offline">离线</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="11">
        <el-card>
          <div style="height: 280px">
            <div class="screening-statistics-container">
              <div class="flag">
                <img src="@/assets/cspImg/brokenLine.png" alt="" class="broken-line">
                <strong>筛查统计</strong>
              </div>
              <div class="screening-statistics">
                <span :class="{ active: screeningType === 'one' }" @click="getOneScreeningCountFn">一筛人数</span>
                <span :class="{ active: screeningType === 'two' }" @click="getTwoScreeningCountFn">二筛人数</span>
              </div>
            </div>
            <ScreeningStatisticsEchart :chart-data="screeningList" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="13">
        <el-card>
          <div style="height: 280px">
            <div class="flag">
              <img src="@/assets/cspImg/brokenLine.png" alt="" class="broken-line">
              <strong>筛查数据</strong>
            </div>
            <ScreeningData :screening-data="screeningData" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="11">
        <el-card>
          <div style="height: 300px">
            <div class="flag">
              <img src="@/assets/cspImg/brokenLine.png" alt="慢病筛查人数" class="broken-line">
              <strong>接诊人数</strong>
            </div>
            <PatientsReceivedEchart :patients-received-data="patientsReceivedData" />
          </div>
        </el-card>
      </el-col>

      <el-col :span="13">
        <el-card>
          <div style="height: 300px">
            <div class="flag">
              <img src="@/assets/cspImg/brokenLine.png" alt="" class="broken-line">
              <strong>人群构成</strong>
            </div>
            <PopulationCompositionEchart :population-composition-data="populationCompositionData" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  getDiseaseScreeningCount,
  getStandardManageCount,
  getScreeningCount,
  getScreeningCountSecond,
  getScreeningData,
  getReceptionCount,
  getPopulationComposition
} from '@/api/cspHome'
import { getGatewayListApi } from '@/api/system'
import { localCache } from '@/utils/cache'
import { screeningEnum, populationCompositionEnum } from '@/utils/enum'
import ScreeningStatisticsEchart from './screeningStatisticsEchart.vue'
import ScreeningData from './screeningData.vue'
import PatientsReceivedEchart from './patientsReceivedEchart.vue'
import PopulationCompositionEchart from './populationCompositionEchart.vue'

export default {
  name: 'CspHome',
  components: {
    ScreeningStatisticsEchart,
    ScreeningData,
    PatientsReceivedEchart,
    PopulationCompositionEchart
  },
  data() {
    return {
      screeningEnum,
      populationCompositionEnum,
      dieaseList: [],
      standardList: [],
      screeningList: [], // 筛查人数
      screeningType: 'one', // 筛查类型
      screeningData: [], // 筛查数据
      patientsReceivedData: [], // 接诊人数
      populationCompositionData: [], // 人群构成
      getwayStatus: 0 // 边缘计算控制终端状态
    }
  },
  created() {
    this.getCspHomeDataFn()
    this.getStandardManageCountFn()
    this.getOneScreeningCountFn()
    this.getScreeningDataFn()
    this.getReceptionCountFn()
    this.getPopulationCompositionFn()
    this.getEdgeComputingControlTerminalFn()
  },
  methods: {
    // 慢病图标 是否展示
    requireShowDisease(code) {
      try {
        if (code === 'fangchan') {
          code = 'fc'
        }
        if (code === 'COPD') {
          code = 'copd'
        }
        const data = require(`@/assets/disease/${code}.png`)
        if (data) {
          return data
        } else {
          return false
        }
      } catch (error) {
        return false
      }
    },

    // 获取慢病筛查人数
    async getCspHomeDataFn() {
      const res = await getDiseaseScreeningCount({})
      if (res.code === 200) {
        this.dieaseList = res.data.map((item) => {
          return {
            count: item.count,
            diseaseName: item.diseaseName,
            diseaseCode:
              item.diseaseCode === 'fangchan' ? 'fc' : item.diseaseCode === 'COPD' ? 'copd' : item.diseaseCode
          }
        })
      }
    },

    // 获取规范管理人数
    async getStandardManageCountFn() {
      const res = await getStandardManageCount({})
      if (res.code === 200) {
        this.standardList = res.data.map((item) => {
          return {
            count: item.count,
            diseaseCode: item.diseaseCode,
            diseaseName: item.diseaseName,
            code:
              item.diseaseCode === 'fangchan'
                ? 'fcStandard'
                : item.diseaseCode === 'tnb'
                  ? 'tnbStandard'
                  : item.diseaseCode === 'gxy'
                    ? 'gxyStandard'
                    : item.diseaseCode === 'threeHigh'
                      ? 'allStandard'
                      : ''
          }
        })
      }
    },

    // 获取边缘计算控制终端
    async getEdgeComputingControlTerminalFn() {
      const res = await getGatewayListApi({
        departmentId: localCache.getCache('userInfo').departId || ''
      })
      if (res.code === 200) {
        this.getwayStatus = res.data && res.data.length > 0 ? res.data[0].status : 0
      }
    },

    // 筛查统计-一筛
    async getOneScreeningCountFn() {
      this.screeningType = 'one'
      const res = await getScreeningCount({})
      if (res.code === 200) {
        this.screeningList = res.data
      }
    },

    // 筛查统计-二筛
    async getTwoScreeningCountFn() {
      this.screeningType = 'two'
      const res = await getScreeningCountSecond({})
      if (res.code === 200) {
        this.screeningList = res.data
      }
    },

    // 筛查数据
    async getScreeningDataFn() {
      const res = await getScreeningData({})
      if (res.code === 200) {
        this.screeningData = Object.entries(res.data).map(([key, value]) => {
          const item = screeningEnum.find((it) => it.code === key)
          return {
            ...item,
            value
          }
        })
      }
    },

    // 接诊人数
    async getReceptionCountFn() {
      const res = await getReceptionCount({})
      if (res.code === 200) {
        this.patientsReceivedData = res.data
      }
    },

    // 人群构成
    async getPopulationCompositionFn() {
      const res = await getPopulationComposition({})
      if (res.code === 200) {
        this.populationCompositionData = Object.entries(res.data).map(([key, value]) => {
          const item = populationCompositionEnum.find((it) => it.code === key)
          return {
            ...item,
            value
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.csp_home {
  height: 100%;
  font-size: 16px;
  overflow: auto;
  padding: 4px;
  .el-card {
    margin: 4px;
  }
  .flag {
    width: 200px;
    height: 30px;
    display: flex;
    align-items: center;
    gap: 8px;
    .broken-line {
      height: 20px;
      width: 20px;
    }
  }

  .diease-count {
    height: 130px;
    .diease-contetnt {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .diease-item {
        .diease-item-content {
          width: 90px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
          gap: 8px;
          .diease-item-content-count {
            font-weight: 600;
          }
          .diease-icon {
            width: 30px;
            height: 30px;
          }
          .diease-item-content-name {
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
    }
  }
  .standard {
    height: 130px;
    .standard-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .standard-item {
        .standard-item-content {
          width: 120px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
          gap: 5px;
          .standard-item-content-count {
            font-weight: 600;
            font-size: 24px;
            color: #409eff;
          }
          .standard-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #ecf5ff;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
              width: 30px;
              height: 30px;
            }
          }
          .standard-item-content-name {
            font-size: 14px;
            font-weight: 500;
            color: #606266;
          }
        }
      }
    }
  }
  .getway-container {
    height: 130px;
    text-align: center;
    .image-container {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;
      margin-top: 10px;
      .getway-status {
        width: 80px;
        color: #fff;
        padding: 4px 8px;
        border-radius: 20px;
      }
      .online {
        background-color: #00ba0f;
      }
      .offline {
        background-color: #a1a1a1;
      }
    }
  }
  .screening-statistics-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .screening-statistics {
      span {
        cursor: pointer;
        font-size: 14px;
        margin-right: 10px;
        &.active {
          color: #409eff;
        }
      }
    }
  }
}
</style>
