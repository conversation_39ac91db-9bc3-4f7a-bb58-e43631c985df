/* 公用样式
 * 要求
 *  1、一个模块的样式需要注释
 *  2、禁止使用id选择器
 *  3、使用rem，勿用px
 *  4、使用有意义的命名（类似 .a  .ccc .ssss ...  禁止使用）
 *  5、驼峰命名 （personName、personTel、perTel、renYanXinXi、）
 */

/* 公用样式 Start */

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

html,
body {
    height: 100%;
    width: 100% !important;
    position: relative;
    overflow: hidden;
    font-size: 20px;
    color: var(--color);
    padding: 0 !important;
    overflow: hidden;
}
.my-custom-confirm-reportFill {
    width: 500px
}

ul,
li {
    list-style: none;
}

/*修改滚动条样式 start*/
/*修改滚动条样式 start*/
*::-webkit-scrollbar {
    width: 0.35rem;
    height: 0.35rem;
}

*::-webkit-scrollbar-track {
    background: #f3f5f8;
    cursor: pointer;
    border-radius: 2px;
}

*::-webkit-scrollbar-thumb {
    background: #ccd0da;
    border-radius: 0.5rem;
    cursor: pointer;
}

*::-webkit-scrollbar-thumb:hover {
    background: #b2b6c0;
    cursor: pointer;
}

*::-webkit-scrollbar-corner {
    background: #f3f5f8;
}

.el-date-editor .el-range-separator {
    font-size: 0.6rem;
    color: #959292;
}

/*修改滚动条样式 end*/
.cp {
    cursor: pointer;
}

.cp * {
    cursor: pointer;
}

.cp .cpn {
    cursor: no-drop;
}

.cpn {
    cursor: no-drop;
}

.pr {
    position: relative;
}

.pa {
    position: absolute;
}

.centerS {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%)
}

.txtOh {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
}

.wsn {
    white-space: nowrap;
}
.txtOhTwo {
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.txtOhThree {
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
.tc {
    text-align: center;
}

.tl {
    text-align: left;
}

.tr {
    text-align: right;
}

.dib {
    display: inline-block;
}

.iconfont {
    font-size: 0.7rem;
}

.tac {
    text-align: center;
}
.tal {
    text-align: left;
}
.tar {
    text-align: right;
}

.fz12 {
    font-size: 0.6rem;
}

.fz13 {
    font-size: 0.65rem;
}

.fz14 {
    font-size: 0.7rem;
}

.fz15 {
    font-size: 0.75rem;
}

.fz16 {
    font-size: 0.8rem;
}

.fz17 {
    font-size: 0.85rem;
}

.fz18 {
    font-size: 0.9rem;
}

.fz19 {
    font-size: 0.95rem;
}

.fz20 {
    font-size: 1rem;
}

.tr90 {
    transform: rotate(90deg);
}

.tr-90 {
    transform: rotate(-90deg);
}

.oh {
    overflow: hidden;
    position: relative;
}

.hide {
    display: none;
}

.hide2 {
    visibility: hidden;
}

.h10 {
    height: 100%;
}

.w10 {
    width: 100%;
}

.w5 {
    width: 50%;
}

.w3 {
    width: 33.33%;
}

.w2 {
    width: 20%;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.fwb {
    font-weight: bold;
}

.fn {
    float: none;
}

.cb {
    clear: both;
}

.vam {
    vertical-align: middle;
}

.yellow {
    color: #FB961A;
}

.greey {
    color: #999999;
}

.red {
    color: #EE5757;
}

.green {
    color: #0A86C8;
}

.ml20 {
    margin-left: 1rem;
}

.mr20 {
    margin-right: 1rem;
}

.ml10 {
    margin-left: .5rem;
}

.mr10 {
    margin-right: .5rem;
}

.echarsDiv {
    width: 100%;
    height: 100%;
    background-color: red;
    float: left;
}

.btn {
    border: 0 none;
    line-height: 1.5rem;
    height: 1.5rem;
    border-radius: 0.3rem;
    cursor: pointer;
    font-size: 0.8rem;
    font-weight: 500;
    padding: 0 1.2rem;
    letter-spacing: 0.1vw;
}
.cpn.btn {
    cursor: no-drop;
}
.addBtn {
    padding: 0 .6rem;
    font-size: 0.8rem;
}

.addBtn .iconfont {
    font-size: 1rem;
    vertical-align: middle;
    margin-right: 0.1rem;
}

.addBtn label {
    font-size: 0.8rem;
    vertical-align: top;
    cursor: pointer;
}
.btn.noBor{
    border: 0 none;
}
.btn.noBj{
    background-color: transparent;
}
.btn.noBor:hover,
.btn.noBor:focus{
    border: 0 none;
}
.btn.noBj:hover,
.btn.noBj:focus{
    background-color: transparent;
}
.btnGreen,
.btnGreen:hover,
.btnGreen:focus {
    background: var(--btnBJColor);
    color: var(--btnColor);
    opacity: 1;
    border: 1px solid var(--btnBorderColor);
}

.btnCancel,
.btnCancel:hover,
.btnCancel:focus {
    background: var(--btnCancelBJColor);
    color: var(--btnCancelColor);
    opacity: 1;
    border: 1px solid var(--btnCancelBorderColor);
}

.btnGreenLine,
.btnGreenLine:hover,
.btnGreenLine:focus {
    background: var(--btnLineBJColor);
    color: var(--colorSecond);
    opacity: 1;
    border: 1px solid var(--btnBorderColor);
}
.btnRedLine,
.btnRedLine:hover,
.btnRedLine:focus {
    background: var(--btnRedBJColor);
    color: var(--btnRedColor);
    opacity: 1;
    border: 1px solid var(--btnRedBorderColor);
}
.btnWhite,
.btnWhite:hover,
.btnWhite:focus {
    background: var(--btnWhiteBJColor);
    color: var(--btnWhiteColor);
    opacity: 1;
    border: 1px solid var(--btnWhiteBorderColor);
}

.btnWhite-green,
.btnWhite-green:hover,
.btnWhite-green:focus {
    background: var(--btnWhiteBJColor);
    color: var(--colorActive);
    opacity: 1;
    border: 1px solid var(--btnWhiteBorderColor);
}
.btnLinear,
.btnLinear:hover,
.btnLinear:focus {
    background: var(--btnBJLinear);
    color: var(--btnLinear);
    opacity: 1;
    border: 0px none;
    line-height: 1.6rem;
    height: 1.6rem;
    padding: 0 0.8rem;
    font-size: 0.7rem;
}

.btnIco.btn i{
  margin-right: 0.3rem;
  font-size: 0.6rem
}

.btnGreey,
.btnGreey:hover,
.btnGreey:focus {
    background: var(--btnGreeyBJColor);
    color: var(--btnGreeyColor);
    opacity: 1;
    border: 1px solid var(--btnGreeyBorderColor);
}

.btnGreey-green,
.btnGreey-green:hover,
.btnGreey-green:focus {
    background: var(--btnGreeyBJColor);
    color: var(--colorActive);
    opacity: 1;
    border: 1px solid var(--btnGreeyBorderColor);
}

.btnReset,
.btnReset:hover,
.btnReset:focus {
    background: #fff;
    color:  rgba(0, 0, 0, 0.85);
    opacity: 1;
    border: 1px solid #CED3DB;
}
.btnReset .iconfont{
    margin-right: .3rem;
}
.searchBtn .iconfont {
    margin-right: .3rem;
}

.btnBack,
.btnBack:hover,
.btnBack:focus {
    background: var(--btnBackBJColor);
    color: var(--btnBackColor);
    opacity: 1;
    border: 1px solid var(--btnBackBorderColor);
    padding: 0 .8rem;
    font-size: 0.7rem;
}
