<template>
  <ProDialog ref="proDialog" title="需要验证您的身份" :visible.sync="dialogVisible" width="600px">
    <div class="sms-verification-content">
      <p>您已开启双重验证，为了账号安全，请输入验证码。</p>
      <p>{{ hidePhone(account) }}</p>
      <el-input v-model="smsCode" placeholder="请输入验证码" style="width: 90%">
        <span slot="suffix" class="send-sms-code" :class="{ disabled: isSendDisabled }" @click="sendSmsCode">
          {{ sendCodeText }}
        </span>
      </el-input>
    </div>

    <span slot="footer">
      <el-button @click="dialogVisible = false">关闭</el-button>
      <el-button type="primary" :loading="smsLoading" @click="handleConfirm">确定</el-button>
    </span>
  </ProDialog>
</template>

<script>
import ProDialog from '@/components/ProDialog/index.vue'
import { getSmsCode } from '@/api/user'

export default {
  name: 'SmsVerification',
  components: {
    ProDialog
  },
  props: {
    account: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      smsCode: '',
      countdown: 0,
      smsLoading: false,
      timer: null
    }
  },
  computed: {
    sendCodeText() {
      return this.countdown > 0 ? `${this.countdown}秒后重新发送` : '发送验证码'
    },
    isSendDisabled() {
      return this.countdown > 0
    }
  },

  // 组件销毁时清理定时器
  beforeDestroy() {
    this.clearTimer()
  },
  methods: {
    async sendSmsCode() {
      // 如果正在倒计时中，则禁止发送
      if (this.countdown > 0) {
        return
      }

      const res = await getSmsCode({
        phone: this.account,
        platform: 'pc'
      })

      if (res.code !== 200) {
        this.$message.error(res.msg)
        return
      }

      this.$message.success('验证码已发送')
      this.startCountdown()
    },

    // 开始60秒倒计时
    startCountdown() {
      this.countdown = 60
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          this.clearTimer()
        }
      }, 1000)
    },

    // 清除定时器
    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
      this.countdown = 0
    },

    hidePhone(phone) {
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    },

    handleConfirm() {
      this.$emit('confirm', this.smsCode)
    }
  }
}
</script>

<style lang="scss" scoped>
.sms-verification-content {
  p {
    margin: 8px 0;
  }

  .send-sms-code {
    color: #0a86c8;
    cursor: pointer;
    font-size: 12px;
    padding-right: 8px;
    transition: all 0.3s ease;

    &:hover:not(.disabled) {
      color: #007bb5;
    }

    &.disabled {
      color: #c0c4cc;
      cursor: not-allowed;
    }
  }
}
</style>
