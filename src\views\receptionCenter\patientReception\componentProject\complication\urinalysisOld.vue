<!-- 尿常规检查 -->
<template>
  <div class="urinalysis">
    <section class="tabContainer">
      <device-icon
        :socket-connect="deviceConnect"
        style="margin-bottom: 8px; display: flex; justify-content: flex-end"
      />
      <div class="content">
        <div class="form-container">
          <el-form ref="formRef" :model="form" label-width="100px">
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="白细胞：">
                  <el-select v-model="form.leukocyteResult" placeholder="请选择" size="mini" style="width: 40%">
                    <el-option label="-" value="-" />
                    <el-option label="+-" value="+-" />
                    <el-option label="+" value="+" />
                    <el-option label="++" value="++" />
                    <el-option label="+++" value="+++" />
                  </el-select>
                  <custom-input-number v-model="form.leukocyteValue" style="width: 45%">
                    <template #append> cells/uL </template>
                  </custom-input-number>
                  <range-tooltip
                    style="margin-left: 8px"
                    :range="urinalysis.leukocyte.range"
                    :description="urinalysis.leukocyte.description"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="亚硝酸盐：">
                  <el-select v-model="form.nitriteResult" placeholder="请选择" size="mini" style="width: 40%">
                    <el-option label="-" value="-" />
                    <el-option label="+-" value="+-" />
                    <el-option label="+" value="+" />
                    <el-option label="++" value="++" />
                    <el-option label="+++" value="+++" />
                  </el-select>
                  <custom-input-number v-model="form.nitriteValue" style="width: 45%">
                    <template #append> mmol/L </template>
                  </custom-input-number>
                  <range-tooltip
                    style="margin-left: 8px"
                    :range="urinalysis.nitrite.range"
                    :description="urinalysis.nitrite.description"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="尿胆原：">
                  <el-select v-model="form.uroResult" placeholder="请选择" size="mini" style="width: 40%">
                    <el-option label="-" value="-" />
                    <el-option label="+-" value="+-" />
                    <el-option label="+" value="+" />
                    <el-option label="++" value="++" />
                    <el-option label="+++" value="+++" />
                  </el-select>
                  <custom-input-number v-model="form.uroValue" style="width: 45%">
                    <template #append> umol/L </template>
                  </custom-input-number>
                  <range-tooltip
                    style="margin-left: 8px"
                    :range="urinalysis.uro.range"
                    :description="urinalysis.uro.description"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="蛋白质：">
                  <el-select v-model="form.proteinResult" placeholder="请选择" size="mini" style="width: 40%">
                    <el-option label="-" value="-" />
                    <el-option label="+-" value="+-" />
                    <el-option label="+" value="+" />
                    <el-option label="++" value="++" />
                    <el-option label="+++" value="+++" />
                  </el-select>
                  <custom-input-number v-model="form.proteinValue" style="width: 45%">
                    <template #append> g/L </template>
                  </custom-input-number>
                  <range-tooltip
                    style="margin-left: 8px"
                    :range="urinalysis.protein.range"
                    :description="urinalysis.protein.description"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="pH值：">
                  <custom-input-number v-model="form.phValue" style="width: 85%">
                    <template #append> pH </template>
                  </custom-input-number>
                  <range-tooltip
                    style="margin-left: 8px"
                    :range="urinalysis.ph.range"
                    :description="urinalysis.ph.description"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="潜血：">
                  <el-select v-model="form.occultBloodResult" placeholder="请选择" size="mini" style="width: 40%">
                    <el-option label="-" value="-" />
                    <el-option label="+-" value="+-" />
                    <el-option label="+" value="+" />
                    <el-option label="++" value="++" />
                    <el-option label="+++" value="+++" />
                  </el-select>
                  <custom-input-number v-model="form.occultBloodValue" style="width: 45%">
                    <template #append> cells/uL </template>
                  </custom-input-number>
                  <range-tooltip
                    style="margin-left: 8px"
                    :range="urinalysis.occultBlood.range"
                    :description="urinalysis.occultBlood.description"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="比重：">
                  <custom-input-number v-model="form.proportion" style="width: 85%">
                    <template #append> g/L </template>
                  </custom-input-number>
                  <range-tooltip
                    style="margin-left: 8px"
                    :range="urinalysis.proportion.range"
                    :description="urinalysis.proportion.description"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="抗坏血酸：">
                  <el-select v-model="form.ascorbicAcidResult" placeholder="请选择" size="mini" style="width: 40%">
                    <el-option label="-" value="-" />
                    <el-option label="+-" value="+-" />
                    <el-option label="+" value="+" />
                    <el-option label="++" value="++" />
                    <el-option label="+++" value="+++" />
                  </el-select>
                  <custom-input-number v-model="form.ascorbicAcidValue" style="width: 45%">
                    <template #append> mmol/L </template>
                  </custom-input-number>
                  <range-tooltip
                    style="margin-left: 8px"
                    :range="urinalysis.ascorbicAcid.range"
                    :description="urinalysis.ascorbicAcid.description"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="酮体：">
                  <el-select v-model="form.ketoneBodyResult" placeholder="请选择" size="mini" style="width: 40%">
                    <el-option label="-" value="-" />
                    <el-option label="+-" value="+-" />
                    <el-option label="+" value="+" />
                    <el-option label="++" value="++" />
                    <el-option label="+++" value="+++" />
                  </el-select>
                  <custom-input-number v-model="form.ketoneBodyValue" style="width: 45%">
                    <template #append> mmol/L </template>
                  </custom-input-number>
                  <range-tooltip
                    style="margin-left: 8px"
                    :range="urinalysis.ketoneBody.range"
                    :description="urinalysis.ketoneBody.description"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="胆红素：">
                  <el-select v-model="form.bilirubinResult" placeholder="请选择" size="mini" style="width: 40%">
                    <el-option label="-" value="-" />
                    <el-option label="+-" value="+-" />
                    <el-option label="+" value="+" />
                    <el-option label="++" value="++" />
                    <el-option label="+++" value="+++" />
                  </el-select>
                  <custom-input-number v-model="form.bilirubinValue" style="width: 45%">
                    <template #append> umol/L </template>
                  </custom-input-number>
                  <range-tooltip
                    style="margin-left: 8px"
                    :range="urinalysis.bilirubin.range"
                    :description="urinalysis.bilirubin.description"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="葡萄糖：">
                  <el-select v-model="form.glucoseResult" placeholder="请选择" size="mini" style="width: 40%">
                    <el-option label="-" value="-" />
                    <el-option label="+-" value="+-" />
                    <el-option label="+" value="+" />
                    <el-option label="++" value="++" />
                    <el-option label="+++" value="+++" />
                  </el-select>
                  <custom-input-number v-model="form.glucoseValue" style="width: 45%">
                    <template #append> mmol/L </template>
                  </custom-input-number>
                  <range-tooltip
                    style="margin-left: 8px"
                    :range="urinalysis.glucose.range"
                    :description="urinalysis.glucose.description"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="ocr-recognition">
          <ocr-recognition v-if="activeTab === 'URINE_ROUTINE'" type="ut" />
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { mapGetters } from 'vuex'
import { urinalysis } from './staticTableData.js'
import DeviceIcon from '@/components/deviceIcon/index.vue'
import OcrRecognition from '@/components/ocrRecognition/index.vue'
import RangeTooltip from '@/components/RangeTooltip/index.vue'

export default {
  name: 'Urinalysis',
  components: {
    DeviceIcon,
    OcrRecognition,
    RangeTooltip
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    },
    activeTab: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      urinalysis,
      form: {
        leukocyteResult: '',
        leukocyteValue: '',
        nitriteResult: '',
        nitriteValue: '',
        uroResult: '',
        uroValue: '',
        proteinResult: '',
        proteinValue: '',
        phValue: '',
        occultBloodResult: '',
        occultBloodValue: '',
        proportion: '',
        ascorbicAcidResult: '',
        ascorbicAcidValue: '',
        ketoneBodyResult: '',
        ketoneBodyValue: '',
        bilirubinResult: '',
        bilirubinValue: '',
        glucoseResult: '',
        glucoseValue: ''
      }
    }
  },
  computed: {
    ...mapGetters(['deviceConnect'])
  },
  methods: {
    initData(data) {
      const formTemp = cloneDeep(this.form)
      if (data) {
        Object.keys(data).forEach((key) => {
          formTemp[key] = data[key]
        })
        this.form.id = data.id
      }
      this.form = formTemp
    },
    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: true,
        data: {
          ...this.form,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      return result
    }
  }
}
</script>
<style lang="scss" scoped>
.urinalysis {
  width: 100%;
  height: 100%;
  .tabContainer {
    width: 100%;
    .content {
      width: 100%;
      display: flex;
      justify-content: space-between;
      gap: 16px;
      .form-container {
        width: 40%;
      }
      .ocr-recognition {
        flex: 1;
      }
    }
  }
  ::v-deep .el-input-group__append {
    padding: 0 !important;
    width: 60px;
    text-align: center;
  }
}
</style>
