<template>
  <div class="all-message">
    <el-card class="search-card">
      <el-form :model="queryParams" label-width="120px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="普查时间：">
              <el-date-picker
                v-model="queryParams.timeRange"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="消息类型：">
              <el-select v-model="queryParams.type" placeholder="请选择" style="width: 100%">
                <el-option label="登录信息" value="登录信息" />
                <el-option label="超声质控" value="超声质控" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="table-card">
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 60px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #operation="{ row }">
          <el-button v-if="row.title !== '登录'" type="text" size="small" @click="handleRead(row)">查看</el-button>
        </template>
      </base-table>
    </el-card>
  </div>
</template>

<script>
import { getMessageNotificationPage, markRead } from '@/api/messageNotification'
import { getUserId } from '@/utils/auth'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import dayjs from 'dayjs'

export default {
  name: 'AllMessage',
  components: {
    BaseTable
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        timeRange: [dayjs().subtract(1, 'week').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        type: null
      },
      columns: [
        { label: '时间', prop: 'sendTime' },
        { label: '消息标题', prop: 'title' },
        { label: '消息类型', prop: 'type' },
        { label: '消息内容', prop: 'content', width: 400, showOverflowTooltip: true },
        { label: '操作', prop: 'operation', width: 100, slot: 'operation' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }

      return await getMessageNotificationPage({
        ...queryParams,
        userId: getUserId()
      })
    },

    async handleRead(row) {
      const res = await markRead({
        userId: getUserId(),
        messageId: row.id
      })

      if (res.code !== 200) return

      let routeConfig = null

      switch (row.title) {
        case '发起超声质控申请':
          routeConfig = {
            path: '/qualityControl/ultrasonicQualityControl/detail',
            query: {
              id: row.bizRecord,
              itemCode: row.bizModule
            }
          }
          break

        case '超声质控审批回复':
          routeConfig = {
            path:
              row.bizModule === 'check' ? '/receptionCenter/patientExamination' : '/receptionCenter/patientReception',
            query: { id: row.bizRecord }
          }
          break

        default:
          // 其他消息类型不跳转
          break
      }

      if (routeConfig) {
        this.$router.push(routeConfig)
      }
    },

    handleReset() {
      this.queryParams = {
        timeRange: [dayjs().subtract(1, 'week').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
        type: null,
        pageNo: 1,
        pageSize: 20
      }
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.all-message {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-card {
    margin: 16px;
    height: 77px;
  }

  .table-card {
    flex: 1;
    margin: 0 16px;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
}
</style>
