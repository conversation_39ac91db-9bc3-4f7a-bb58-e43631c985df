<template>
  <div class="service-items">
    <div class="service-items-btn">
      <flag-component title="服务项" style="margin-bottom: 10px" />
      <div class="service-items-btn-right">
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button v-if="selectionList.length > 0" type="primary" @click="handleAssociate">关联服务包</el-button>
      </div>
    </div>
    <div class="service-items-table">
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 70px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
        @selection-change="handleSelectionChange"
      >
        <template #operation="{ row }">
          <el-button type="text" @click="handleDetail(row)">查看</el-button>
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" style="color: red" @click="handleDelete(row)">删除</el-button>
        </template>
      </base-table>
    </div>

    <ProDialog :visible.sync="addDialogVisible" :title="`${title}服务项`" width="800px">
      <el-form ref="formRef" :model="form" label-width="150px" :rules="rules">
        <el-form-item label="服务项名称：" prop="siName">
          <el-input v-model="form.siName" placeholder="请输入服务项名称" :disabled="disabledSubmit" />
        </el-form-item>
        <el-form-item label="服务频次：" prop="siCnt">
          <el-input-number
            v-model="form.siCnt"
            placeholder="请输入服务频次"
            style="width: 100%"
            :disabled="disabledSubmit"
          />
        </el-form-item>
        <el-form-item label="频率数（月）：" prop="siFrequency">
          <el-input-number
            v-model="form.siFrequency"
            placeholder="请输入频率数"
            style="width: 100%"
            :disabled="disabledSubmit"
          />
        </el-form-item>
        <el-form-item label="服务项描述：">
          <el-input v-model="form.siDesc" type="textarea" placeholder="请输入服务项描述" :disabled="disabledSubmit" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="addDialogVisible = false">关闭</el-button>
        <el-button v-if="!disabledSubmit" type="primary" @click="handleConfirm">确定</el-button>
      </template>
    </ProDialog>

    <ProDialog :visible.sync="servicePackDialogVisible" title="关联服务包" width="800px">
      <el-table :data="servicePackList" border style="width: 100%">
        <el-table-column type="radio" width="55" align="center">
          <template #default="{ row }">
            <el-radio v-model="selectedRow" :label="row.id">{{ '' }}</el-radio>
          </template>
        </el-table-column>
        <el-table-column label="服务包名称" prop="spName" align="center" />
        <el-table-column label="服务包描述" prop="spDesc" align="center" />
      </el-table>
      <template #footer>
        <el-button @click="servicePackDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleAssociateConfirm">确定</el-button>
      </template>
    </ProDialog>
  </div>
</template>

<script>
import { localCache } from '@/utils/cache'
import { getServiceItemList, saveServiceItem, deleteServiceItem, bindServicePackage } from '@/api/servicePackageManage'
import FlagComponent from '@/components/flagComponent/index.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ProDialog from '@/components/ProDialog/index.vue'

export default {
  name: 'ServiceItems',
  components: {
    FlagComponent,
    BaseTable,
    ProDialog
  },
  mixins: [tableMixin],
  data() {
    return {
      form: {
        id: '',
        spId: '',
        departCode: localCache.getCache('userInfo').departCode,
        siName: '',
        siCnt: '',
        siDesc: '',
        siFrequency: undefined
      },
      rules: {
        siName: [{ required: true, message: '请输入服务项名称', trigger: 'blur' }],
        siCnt: [{ required: true, message: '请输入服务频次', trigger: 'blur' }],
        siFrequency: [{ required: true, message: '请输入频率数', trigger: 'blur' }]
      },
      queryParams: {
        departCode: localCache.getCache('userInfo').departCode,
        spId: ''
      },
      addDialogVisible: false,
      disabledSubmit: false,
      selectionList: [],
      servicePackDialogVisible: false,
      servicePackList: [],
      selectedRow: '',
      title: '',
      columns: [
        { type: 'selection', width: 70, align: 'center' },
        { label: '服务项名称', prop: 'siName', align: 'center' },
        { label: '服务频次', prop: 'siCnt', align: 'center' },
        { label: '频率数（月）', prop: 'siFrequency', align: 'center' },
        { label: '服务项描述', prop: 'siDesc', align: 'center' },
        { label: '操作', prop: 'operation', align: 'center', width: 150, slot: 'operation' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      return await getServiceItemList(params)
    },

    // 服务项新增、查看、编辑公共处理方法
    openDialog(type, row = {}) {
      // type: add-新增 detail-查看详情 edit-编辑
      this.disabledSubmit = type === 'detail'
      this.addDialogVisible = true

      // 重置表单数据
      const defaultForm = {
        id: type === 'add' ? '' : row.id,
        spId: type === 'add' ? this.queryParams.spId : row.spId,
        departCode: localCache.getCache('userInfo').departCode,
        siName: type === 'add' ? '' : row.siName,
        siCnt: type === 'add' ? '' : row.siCnt,
        siDesc: type === 'add' ? '' : row.siDesc,
        siFrequency: type === 'add' ? undefined : row.siFrequency
      }

      this.form = defaultForm
    },

    // 服务项新增
    handleAdd() {
      this.title = '新增'
      this.openDialog('add')
    },

    // 服务项详情
    handleDetail(row) {
      this.title = '查看'
      this.openDialog('detail', row)
    },

    // 服务项编辑
    handleEdit(row) {
      this.title = '编辑'
      this.openDialog('edit', row)
    },

    // 服务项删除
    handleDelete(row) {
      this.handleConfirmDelete({
        params: { id: row.id },
        deleteApi: deleteServiceItem,
        message: '确定删除该服务项吗？'
      })
    },

    // 服务项保存
    async handleConfirm() {
      await this.$refs.formRef.validate()
      const params = {
        ...this.form
      }
      await saveServiceItem(params)
      this.addDialogVisible = false
      this.handleSearch()
    },

    // 服务项关联服务包
    handleAssociate() {
      this.selectedRow = ''
      this.servicePackDialogVisible = true
    },

    // 服务项选择(关联服务包)
    handleSelectionChange(selection) {
      this.selectionList = selection.map((item) => item.id)
    },

    async handleAssociateConfirm() {
      if (!this.selectedRow) {
        this.$message.warning('请选择服务包')
        return
      }
      const params = {
        spId: this.selectedRow,
        siIdList: this.selectionList
      }
      await bindServicePackage(params)
      this.servicePackDialogVisible = false
      this.$message.success('关联服务包成功')
      this.selectionList = []
      this.$refs.baseTable.clearSelection()
      this.$emit('handleAssociateSuccess', this.selectedRow)
    }
  }
}
</script>

<style lang="scss" scoped>
.service-items {
  display: flex;
  flex-direction: column;
  height: 100%;
  .service-items-table {
    flex: 1;
  }
}

.service-items-btn {
  height: 38px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
</style>
