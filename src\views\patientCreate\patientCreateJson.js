const putOnRecordJson = [
  {
    label: '机构名称',
    prop: 'departCode',
    type: 'cascader',
    class: 'general',
    span: 10,
    required: true,
    options: []
  },
  {
    label: '慢病病种',
    prop: 'disease',
    type: 'checkbox',
    class: 'general',
    span: 14,
    options: [
      { label: '高血压', value: 'gxy' },
      { label: '糖尿病', value: 'tnb' },
      { label: '慢阻肺', value: 'COPD' },
      { label: '房颤', value: 'fangchan' }
    ],
    required: true
  },
  {
    label: '管理状态',
    prop: 'isDie',
    type: 'radio',
    class: 'general',
    span: (formData) => (formData.isDie === 1 ? 6 : 24),
    options: [
      { label: '在管', value: 0 },
      { label: '死亡', value: 1 }
    ],
    required: true
  },

  {
    label: '死亡原因',
    prop: 'deathReason',
    type: 'input',
    class: 'general',
    span: 8,
    required: true,
    visibleOn: (formData) => formData.isDie === 1
  },
  {
    label: '死亡时间',
    prop: 'deathDate',
    type: 'date',
    class: 'general',
    span: 10,
    required: true,
    visibleOn: (formData) => formData.isDie === 1
  },
  {
    label: '建档医生',
    prop: 'doctorName',
    type: 'input',
    class: 'general',
    span: 6,
    disabled: true,
    required: false
  },
  {
    label: '建档时间',
    prop: 'filingTime',
    type: 'date',
    class: 'general',
    span: 8,
    required: false
  },
  {
    label: '责任医生',
    prop: 'manageDoctorId',
    type: 'select',
    class: 'general',
    span: 10,
    required: false,
    options: []
  },
  {
    label: '姓名',
    prop: 'name',
    type: 'input',
    class: 'general',
    span: 6,
    required: true
  },
  {
    label: '证件类型',
    prop: 'idType',
    type: 'select',
    class: 'general',
    span: 8,
    required: true,
    options: []
  },
  {
    label: '证件号码',
    prop: 'idCard',
    type: 'input',
    class: 'general',
    span: 10,
    required: true
  },

  {
    label: '国籍',
    prop: 'countryCode',
    type: 'select',
    class: 'general',
    span: 6,
    required: true,
    options: []
  },

  {
    label: '出生日期',
    prop: 'birthday',
    type: 'date',
    class: 'general',
    span: 8,
    required: true
  },

  {
    label: '性别',
    prop: 'sex',
    type: 'radio',
    class: 'general',
    span: 10,
    required: true,
    options: [
      { label: '女', value: 0 },
      { label: '男', value: 1 },
      { label: '未说明', value: 2 },
      { label: '未知', value: 3 }
    ]
  },

  {
    label: '年龄',
    prop: 'age',
    type: 'input',
    class: 'general',
    span: 6,
    required: false
  },

  {
    label: '民族',
    prop: 'nation',
    type: 'select',
    class: 'general',
    span: 8,
    required: false,
    options: []
  },

  {
    label: '籍贯',
    prop: 'nativeAddress',
    type: 'select',
    class: 'general',
    span: 10,
    required: false,
    options: []
  },

  {
    label: '本人电话',
    prop: 'phone',
    type: 'input',
    class: 'general',
    span: 14,
    required: true
  },
  {
    label: '出生地',
    prop: 'birthAddress',
    type: 'input',
    class: 'general',
    span: 10,
    required: false
  },

  {
    label: '紧急联系人',
    prop: '',
    type: 'custom',
    span: 24,
    class: 'jjlxr',
    required: false,
    customs: {
      1: [
        {
          label: '1、姓名',
          prop: 'contactName1',
          type: 'input',
          class: 'general',
          span: 8,
          required: false
        },
        {
          label: '关系',
          prop: 'contactRelation1',
          type: 'select',
          class: 'general',
          span: 8,
          required: false,
          options: []
        },
        {
          label: '电话',
          prop: 'contactPhone1',
          type: 'input',
          class: 'general',
          span: 8,
          required: false
        }
      ],
      2: [
        {
          label: '2、姓名',
          prop: 'contactName2',
          type: 'input',
          class: 'general',
          span: 8,
          required: false
        },
        {
          label: '关系',
          prop: 'contactRelation2',
          type: 'select',
          class: 'general',
          span: 8,
          required: false,
          options: []
        },
        {
          label: '电话',
          prop: 'contactPhone2',
          type: 'input',
          class: 'general',
          span: 8,
          required: false
        }
      ]
    }
  },

  {
    label: '户口性质',
    prop: 'hkNature',
    type: 'radio',
    class: 'general',
    span: 7,
    options: [
      { label: '城镇', value: 1 },
      { label: '农村', value: 2 }
    ]
  },
  {
    label: '人员属性',
    prop: 'staffAttribute',
    type: 'checkbox',
    class: 'general',
    span: 17,
    options: [
      { label: '一般', value: '1' },
      { label: '五保', value: '2' },
      { label: '困难', value: '3' },
      { label: '低收入人口(特困)', value: '4' },
      { label: '军烈', value: '5' },
      { label: '残疾', value: '6' },
      { label: '其他', value: '7' }
    ]
  },
  {
    label: '常住类型',
    prop: 'registerType',
    type: 'radio',
    class: 'general',
    span: 7,
    required: false,
    options: [
      { label: '非户籍', value: 1 },
      { label: '户籍', value: 2 }
    ]
  },
  {
    label: '现居地址',
    prop: 'currentAddress',
    type: 'input',
    class: 'general',
    span: 17,
    required: false
  },
  {
    label: '文化程度',
    prop: 'educationLevel',
    type: 'radio',
    class: 'general',
    span: 24,
    required: false,
    options: [
      { label: '文盲或半文盲', value: 1 },
      { label: '小学', value: 2 },
      { label: '初中', value: 3 },
      { label: '高中', value: 4 },
      { label: '技校', value: 5 },
      { label: '中专', value: 6 },
      { label: '大专', value: 7 },
      { label: '大学本科', value: 8 },
      { label: '研究生', value: 9 },
      { label: '不详', value: 10 }
    ]
  },
  {
    label: '职业',
    prop: 'career',
    type: 'radio',
    class: 'general',
    span: 24,
    required: false,
    options: [
      { label: '国家机关、党群组织、企业、事业单位负责人', value: 1 },
      { label: '专业技术人员', value: 2 },
      { label: '办事人员和有关人员', value: 3 },
      { label: '社会生产服务和生活服务人员', value: 4 },
      { label: '农、林、牧、渔、水利业生产人员', value: 5 },
      { label: '生产、运输设备操作人员及有关人员', value: 6 },
      { label: '军人', value: 7 },
      { label: '不便分类的其他从业人员', value: 8 },
      { label: '无职业', value: 9 }
    ]
  },
  {
    label: '婚姻状况',
    prop: 'marital',
    type: 'radio',
    class: 'general',
    span: 24,
    required: false,
    options: [
      { label: '未婚', value: 1 },
      { label: '已婚', value: 2 },
      { label: '离婚', value: 3 },
      { label: '丧偶', value: 4 },
      { label: '未说明的婚姻状况', value: 5 }
    ]
  },
  {
    label: '医疗费用支付方式',
    prop: 'payType',
    type: 'checkbox',
    class: 'general',
    span: 24,
    required: false,
    options: [
      { label: '城镇职工基本医疗保险', value: '1' },
      { label: '城镇居民基本医疗保险', value: '2' },
      { label: '新型农村合作医疗', value: '3' },
      { label: '贫困救助', value: '4' },
      { label: '商业医疗保险', value: '5' },
      { label: '全公费', value: '6' },
      { label: '全自费', value: '7' },
      { label: '其他', value: '8' }
    ]
  },
  {
    label: '建档机构',
    prop: 'departName',
    type: 'input',
    class: 'general',
    disabled: true,
    span: 12,
    required: false
  },
  {
    label: '建档机构电话',
    prop: 'departPhone',
    type: 'input',
    class: 'general',
    disabled: true,
    span: 12,
    required: false
  }
]

export default putOnRecordJson
