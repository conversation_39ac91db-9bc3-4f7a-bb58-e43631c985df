import Vue from 'vue'
import request from '@/utils/request'
import * as allJs from '@/assets/js/allJs.js'
import { validateOnlyNumber } from '@/utils/cspUtils'

// 注册全局属性和方法
const registerGlobalProperties = () => {
  // 工具函数
  Vue.prototype.$validateOnlyNumber = validateOnlyNumber
  Vue.prototype.$http = request
  Vue.prototype.$allJs = allJs

  // 创建事件总线
  const evBus = new Vue()
  Vue.prototype.$evBus = evBus
}

export default registerGlobalProperties
