<!-- 糖化血红蛋白 -->
<template>
  <div class="glycosylated-hemoglobin">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="HbA1C：" prop="hba1cValue">
        <custom-input-number v-model="form.hba1cValue" style="width: 30%">
          <template #append>%</template>
        </custom-input-number>
      </el-form-item>
    </el-form>
    <static-table :columns="glycosylatedHemoglobin.columns" :table-data="glycosylatedHemoglobin.staticTableData" />
  </div>
</template>

<script>
import { glycosylatedHemoglobin } from './staticTableData'
import StaticTable from '@/components/staticTable/index.vue'

export default {
  name: 'GlycosylatedHemoglobin',
  components: {
    StaticTable
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      glycosylatedHemoglobin,
      form: {
        hba1cValue: ''
      },
      rules: {
        hba1cValue: [{ required: true, message: '请输入HbA1C' }]
      }
    }
  },
  methods: {
    initData(data) {
      this.form = {
        hba1cValue: data.hba1cValue,
        id: data.id
      }
    },
    async handleSave() {
      const result = {
        name: `${this.itemTemp.label}`,
        success: false,
        data: {
          hba1cValue: this.form.hba1cValue,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    }
  }
}
</script>
