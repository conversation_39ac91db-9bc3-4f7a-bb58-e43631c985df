/* 统计分析样式 */
.public_v2.bodyContainer.statistic {
  height: calc(100vh - 50px - 2.1354vw);

  .mt1 {
    margin-top: 1rem;
  }
  .search-title {
    font-weight: bold;
    font-size: 0.6rem;
    // font-size: 0.7rem;
    color: #666666;
    line-height: 0.8rem;
    margin: 0 0 0.7rem;

    &.flex-box {
      display: flex;
      align-items: center;
      white-space: nowrap;
    }
  }
  // 时间
  .choose_btn {
    height: 1.6rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.2rem;
    .btn_item {
      width: 4rem;
      height: 100%;
      background: #d8d8d8;
      display: inline-block;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 0.8rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 0.7rem;
      color: #5f5f5f;
      cursor: pointer;
      &.is_active {
        font-family: PingFangSC, PingFang SC;
        font-weight: 600;
        color: #ffffff;
        background: #0a86c8;
      }
    }
  }

  // 头部筛选
  .choose_top {
    display: flex;
    align-items: center;

    .choose_from {
      width: 100%;
      height: 1.6rem;
      // padding-left: 1.5rem;
      display: flex;

      .choose_label {
        width: 5.3rem;
        display: flex;
        align-items: center;
        height: 100%;
      }

      .choose_label_item {
        width: 11.5rem;
        height: 100%;
        margin-right: 5.4rem;
      }
      .right_btn {
        display: flex;
        align-items: center;

        > .el-button {
          margin-right: 0.8rem;
        }
      }
    }
  }

  // 左边树形
  .tree-box {
    width: 100%;
    height: calc(100% - 36px - 0.7rem - 2.8rem - 0.8rem - 0.7rem);
    border: 1px solid #dfdfdf;
    // margin: 0.2rem 0 1rem;
    overflow: auto;
    padding: 0.5rem;
    &.tree_box_content {
      height: calc(100% - 36px - 0.7rem - 2.8rem - 0.8rem - 0.7rem - 2.8rem);
    }

    &.tree_box_follow {
      height: calc(100% - 36px - 0.7rem - 2.8rem - 0.8rem - 0.7rem - 0.8rem - 0.7rem - 12rem);
    }
  }

  .public_contentLeft {
    width: 15rem;
    padding: 0.8rem;
    position: relative;
    overflow-y: auto;

    &.hidden {
      width: 0.8rem;
      padding: 0;
      overflow: hidden;

      & > .public_label_list {
        display: none;
      }
    }
  }

  .ecgDetail_module {
    div {
      cursor: pointer;
      width: 7.5rem;
      color: #666666;
      background: #ededed;
    }

    .ecgDetail_moduleBtn_left {
      border-radius: 0.8rem 0px 0px 0.8rem;
    }

    .ecgDetail_moduleBtn_right {
      border-radius: 0px 5rem 5rem 0px;
    }

    .ecgDetail_moduleBtnActive {
      color: #ffffff;
      background-color: #0a86c8;
    }
  }

  .public_contentRight {
    display: flex;
    flex-grow: 1;
    margin-left: 0.4rem;
    width: calc(100% - 15rem);
    height: 100%;
    .choose_top {
      height: 4rem;
    }

    .table_box {
      height: calc(100% - 4rem);
      overflow-y: auto;
      &.table_box_content {
        height: 100%;
      }
    }

    .ecgDetail_isDanger {
      width: 1rem;
      height: 1rem;
      border-radius: 50%;
      background: #ff0000;
      margin-right: 0.2rem;
    }

    .statusCircle {
      width: 0.3rem;
      height: 0.3rem;
      border-radius: 50%;
      margin-right: 0.3rem;
      display: inline-block;
    }

    .statusCircle8177D9 {
      background-color: #8177d9;
    }

    .statusCircle5CCF78 {
      background-color: #5ccf78;
    }

    .statusCircleFF0000 {
      background-color: #ff0000;
    }

    .statusCircleFF2C76 {
      background-color: #ff2c76;
    }

    .ecgDetail_data {
      color: #666666;

      li + li {
        margin-left: 1.5rem;
      }

      .ecgDetail_dataToday {
        color: #000;
      }

      .ecgDetail_dataWaitApply {
        color: #8177d9;
      }

      .ecgDetail_dataApply {
        color: #5ccf78;
      }

      .ecgDetail_dataInvalid {
        color: #ff2c76;
      }

      .ecgDetail_dataCritical {
        color: #ff0000;
      }

      .ecgDetail_assist {
        color: #45d6f8;
      }
    }
  }

  .aside {
    position: absolute;
    top: calc(50% - 2.5rem);
    right: 0;
    width: 0.7rem;
    height: 5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #eaeaea;
    cursor: pointer;
    border-top-left-radius: 1rem;
    border-bottom-left-radius: 1rem;
    clip-path: polygon(0% 20%, 100% 0%, 100% 100%, 0% 80%);
  }

  .scrollbar_area {
    border: 1px solid #dfdfdf;
  }
  .colorRed {
    color: red;
  }
  .color73124255 {
    color: rgba(73, 124, 255, 1);
  }
}

/* 弹出框样式添加 dialog_common */
#app .el-dialog__wrapper.dialog_common .el-dialog {
  box-shadow: 0px 0.1rem 0.5rem 0px rgba(0, 0, 0, 0.2);
  border-radius: 0.8rem;
}

#app .el-dialog__wrapper.dialog_common .el-dialog .el-dialog__header {
  height: 3.15rem;
  background: #f6fdf8;
  border-radius: 16px 16px 0px 0px;
  position: relative;
}

#app .el-dialog__wrapper.dialog_common .el-dialog .el-dialog__title {
  font-family: PingFangSC, PingFang SC;
  font-weight: 600;
  font-size: 0.9rem;
  color: #0a86c8;
}

#app .el-dialog__wrapper.dialog_common .el-dialog .el-dialog__header .el-dialog__headerbtn {
  top: 50%;
  transform: translateY(-50%);
}

#app .el-dialog__wrapper.dialog_common .el-dialog .el-dialog__body {
  height: 50vh;
  padding: 0.9rem 0.6rem;
}

/* 取消按钮样式 */
//  添加 close_btn
#app .bottom_btn_box .el-button.close_btn.el-button--default:hover,
#app .bottom_btn_box .el-button.close_btn.el-button--default:focus {
  border: 1px solid #dcdfe6;
  color: #606266;
  background: #ffffff;
  // opacity: 0.7;
}

/*   卫健委看板 下拉选择和时间样式 */
// 下拉选择添加 choose_select_common
#app .el-select.choose_select_common {
  width: 10vw;
  height: 1.875vw;
}

#app .el-select.choose_select_common .el-input {
  height: 1.875vw;
}

#app .el-select.choose_select_common .el-input .el-input__inner {
  height: 100%;
}

#app .el-select.choose_select_common .el-input .el-input__suffix-inner {
  display: inline-block;
  height: 100%;
}
#app .el-select.choose_select_common .el-input .el-input__icon {
  position: relative;
}
#app .el-select.choose_select_common .el-input .el-icon-arrow-up:before {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
}

// 时间选择添加 choose_time_common
#app .el-date-editor.el-range-editor.choose_time_common {
  width: 20vw;
  height: 1.875vw;
}

#app .el-date-editor.el-range-editor.choose_time_common .el-input__icon {
  position: relative;
}

#app .el-date-editor.el-range-editor.choose_time_common .el-input__icon:before {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
}

#app .el-date-editor.el-range-editor.choose_time_common .el-range-separator {
  display: flex;
  align-items: center;
}

/* 取消table单元格padding */
#app .el-table .el-table__cell {
  padding: 0;
}

/* 数据看板列表样式 */
#app .el-table.show_table .el-table__cell {
  padding: 0;
  font-size: 0.7rem;
  height: 2.4rem;
  line-height: 0.8rem;
}

// #app .el-table.show_table .el-table__header th {
//   background: linear-gradient(180deg, #dcf4e8 0%, #ebf9f2 100%);
// }
#app .el-table.show_table th.el-table__cell {
  background: linear-gradient(180deg, #eee 0%, #f0f0f0 100%);
  color: #666666;
  font-size: 0.7rem;
  height: 2.4rem;
  line-height: 0.8rem;
  font-weight: 600;
}

// hover效果
#app .el-table.show_table .el-table__body tr:hover > td {
  background-color: inherit;
}

// 隔行变色
#app .el-table.show_table .el-table__body tr:nth-child(2n) > td {
  background: #fafafa;
}
