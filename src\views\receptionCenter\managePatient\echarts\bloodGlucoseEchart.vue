<template>
  <div ref="bloodGlucoseChart" style="width: 100%; height: 400px" />
</template>

<script>
import * as echarts from 'echarts'
import dayjs from 'dayjs'

export default {
  name: 'BloodGlucoseEchart',
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    tableData: {
      handler() {
        this.initChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.bloodGlucoseChart)
      }

      const validData = this.tableData.filter((item) => item.measureTime && item.sugarValue && item.sugarType)
      if (validData.length === 0) {
        return
      }

      const sugarTypes = [
        ...new Set(
          validData.map((item) => {
            switch (item.sugarType) {
              case 1:
                return '空腹血糖'
              case 2:
                return '随机血糖'
              case 3:
                return '餐后2h血糖'
              case 4:
                return '糖化血红蛋白'
              default:
                return ''
            }
          })
        )
      ]

      const seriesData = sugarTypes.map((type) => {
        const data = validData
          .filter((item) => {
            const typeName =
              item.sugarType === 1
                ? '空腹血糖'
                : item.sugarType === 2
                  ? '随机血糖'
                  : item.sugarType === 3
                    ? '餐后2h血糖'
                    : item.sugarType === 4
                      ? '糖化血红蛋白'
                      : ''
            return typeName === type
          })
          .map((item) => {
            const isoTime = dayjs(item.measureTime).toISOString() // 标准格式
            return {
              name: isoTime,
              value: [isoTime, item.sugarValue]
            }
          })
          .sort((a, b) => new Date(a.name) - new Date(b.name))

        return {
          name: type,
          type: 'line',
          data: data.map((item) => item.value),
          symbol: 'circle',
          symbolSize: 8
        }
      })

      const option = {
        tooltip: {
          trigger: 'axis',
          formatter(params) {
            let result = `${dayjs(params[0].value[0]).format('YYYY-MM-DD HH:mm')}<br/>`
            params.forEach((item) => {
              result += `${item.marker} ${item.seriesName}: ${item.value[1]}<br/>`
            })
            return result
          }
        },
        legend: {
          data: sugarTypes,
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'time',
          boundaryGap: false,
          axisLabel: {
            formatter: (value) => dayjs(value).format('YYYY-MM-DD HH:mm')
          }
        },
        yAxis: {
          type: 'value',
          name: '血糖值'
        },
        series: seriesData
      }

      this.chart.setOption(option, true)
      this.chart.resize()
    }
  }
}
</script>
