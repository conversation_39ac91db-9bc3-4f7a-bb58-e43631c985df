<template>
  <!-- eslint-disable vue/no-use-v-if-with-v-for -->
  <div :class="['wiTrtcWindowContainer', hideMinType ? 'hideBox' : '', viewType]">
    <!-- 左侧主模块 -->
    <section class="leftAssideBox">
      <div v-if="speakers" class="speakMessageBox">正在讲话：{{ speakers }}</div>
      <div v-if="viewType !== 'newView'" class="topToolbarBox">
        <!-- 宫格视图： 平均分布/右侧列表 | 音频音量 | 静音/解除静音 | 管理成员 | 结束会议 -->
        <div class="leftToolBox">
          <div class="visialTypeBox">
            <div class="labelBox">宫格视图：</div>
            <div class="radioGroupBox">
              <el-radio-group
                v-model="visialType"
                :disabled="typeChanging"
                class="topModuleTypeRadio"
                @change="replayAll"
              >
                <el-radio-button label="平均分布"> 平均分布 </el-radio-button>
                <el-radio-button label="右侧列表"> 右侧列表 </el-radio-button>
              </el-radio-group>
              <el-select v-model="videoDef" class="videoDefSelect" size="mini" @change="videoDefChange">
                <el-option v-for="item in videoDefs" :key="'videoDef-' + item" :label="item" :value="item" />
              </el-select>
            </div>
          </div>
        </div>
        <div class="rightToolbarBox">
          <div class="volumeBoxOutter">
            <el-popover popper-class="customPopper" width="20px" trigger="click">
              <el-slider v-model="volumn" vertical height="100px" :show-tooltip="false" @input="changeVolumn" />
              <div slot="reference" class="volumeBox">
                <svg-icon icon-class="zzzicon-volumn" />
                音频音量
              </div>
            </el-popover>
          </div>
          <div class="muteBox" :class="{ microOpen: !muteFlag }" @click="doToggleMute">
            <svg-icon v-if="muteFlag" icon-class="zzzicon-microphone-mute" />
            <svg-icon v-if="!muteFlag" icon-class="zzzicon-microphone" />
            <span>{{ muteFlag ? '已静音' : '麦克风开启' }}</span>
          </div>
          <div v-if="!isMember" class="memberBox" @click="doOpenManageMember">
            <svg-icon icon-class="user" />
            管理成员（{{ remoteStreamList.length + 1 }}）
          </div>
          <div v-if="isMember" class="memberBox" @click="doOpenManageMemberDialog">
            <svg-icon icon-class="user" />
            管理成员（{{ remoteStreamList.length + 1 }}）
          </div>
          <div v-if="!isMember" class="endBox" @click="doOverTele">
            <svg-icon icon-class="zzzicon-close" />
            结束会诊
          </div>
        </div>
      </div>
      <div class="streamVideoBox" :class="{ rbBorderRadiusNone: rightMemberManageVisible, viewType }">
        <!-- 平均分布 -->
        <div
          :class="[
            visialType === '平均分布' || viewType !== 'newView' ? 'layoutType1' : 'layoutType2',
            'flexStreamContainer',
            viewType
          ]"
        >
          <!-- 本地 -->
          <div v-if="localStream" :class="['localStreamItemBox flexStreamItem', bigShowIndex === 0 ? 'inLeft' : '']">
            <div
              id="local_stream"
              @dblclick="doVideoDbClick"
              @click="visialType === '平均分布' ? doVideoClick('local_stream') : bigShowClick(null, -1)"
            />
            <span class="righBottomTag">{{ $store.getters.name }}(本人)</span>
            <div class="networkQuality">
              <img :src="require(`@/assets/dashboard_images/networkQuality${networkQuality}.png`)" alt="">
            </div>
          </div>
          <template v-if="remoteStreamList && remoteStreamList.length !== 0">
            <div
              v-for="(item, remoteIndex) in remoteStreamList"
              :key="'bigShowItem' + remoteIndex"
              :class="['remoteStreamItemBox flexStreamItem', bigShowIndex === remoteIndex + 1 ? 'inLeft' : '']"
            >
              <div class="streamOutterBox">
                <div
                  :id="'video_bo_box_' + item.id"
                  :class="{ 'distantStream': item.view }"
                  @dblclick="doVideoDbClick"
                  @click="
                    visialType === '平均分布'
                      ? doVideoClick('video_bo_box_' + item.id)
                      : bigShowClick(item, remoteIndex)
                  "
                  v-html="item.view"
                />
                <span class="righBottomTag">{{ item.userName }}</span>
                <div class="networkQuality">
                  <img
                    :src="
                      require(`@/assets/dashboard_images/networkQuality${
                        networkQualityList.find(it => it.userId === item.userId)
                          ? networkQualityList.find(it => it.userId === item.userId).networkQuality
                          : 0
                      }.png`)
                    "
                    alt=""
                  >
                </div>
              </div>
            </div>
          </template>
          <!--          <template v-if="awaitList && awaitList.length !== 0">-->
          <!--            <div-->
          <!--              v-for="i in awaitList"-->
          <!--              v-if="!remoteStreamList.find(e => e.userId === i.userId)"-->
          <!--              :key="i.userId + Math.random()"-->
          <!--              class="awaitStreamItemBox flexStreamItem"-->
          <!--            >-->
          <!--              <div class="distantStream awaitStream">-->
          <!--                <el-empty-->
          <!--                  :description="i.username + ' 呼叫中...'"-->
          <!--                  :image="require('@/assets/dashboard_images/awaitDoctor.png')"-->
          <!--                />-->
          <!--              </div>-->
          <!--            </div>-->
          <!--          </template>-->
          <template v-if="leaveList && leaveList.length !== 0">
            <div
              v-for="i in leaveList"
              v-if="!remoteStreamList.find(e => e.userId === i.userId)"
              :key="i.userId + Math.random()"
              class="awaitStreamItemBox flexStreamItem"
            >
              <div class="distantStream leaveStream">
                <el-empty
                  :description="i.userName + ' 已离开远程会诊'"
                  :image="require('@/assets/dashboard_images/awaitDoctor.png')"
                />
              </div>
            </div>
          </template>
        </div>
        <!-- <div v-if="visialType === '111右侧列表'" class="rightListStreamContainer">
          <div class="streamListContainer">
            <div
              v-if="localStream"
              class="localStreamItemBox listStreamItem"
              :class="{ bigShowItem: bigShowIndex === 0 }"
              @click="bigShowClick(null, -1)"
              @dblclick="doVideoDbClick"
            >
              <div id="local_stream" />
              <span class="righBottomTag">{{ $store.getters.name }}(本人)</span>
              <div class="networkQuality">
                <img :src="require(`@/assets/dashboard_images/networkQuality${networkQuality}.png`)" alt="">
              </div>
            </div>
            <template v-if="remoteStreamList && remoteStreamList.length !== 0">
              <div
                v-for="(item, remoteIndex) in remoteStreamList"
                :key="item.id"
                class="remoteStreamItemBox listStreamItem"
                :class="{ bigShowItem: bigShowIndex === remoteIndex + 1 }"
                @click="bigShowClick(item, remoteIndex)"
                @dblclick="doVideoDbClick"
              >
                <div class="streamOutterBox">
                  <div :id="'video_bo_box_' + item.id" :class="{ 'distantStream': item.view }" v-html="item.view" />
                  <span class="righBottomTag">{{ item.userName }}</span>
                  <div class="networkQuality">
                    <img
                      :src="
                        require(`@/assets/dashboard_images/networkQuality${
                          networkQualityList.find(it => it.userId === item.userId)
                            ? networkQualityList.find(it => it.userId === item.userId).networkQuality
                            : 0
                        }.png`)
                      "
                      alt=""
                    >
                  </div>
                </div>
              </div>
            </template>
            <template v-if="awaitList && awaitList.length !== 0">
              <div
                v-for="i in awaitList"
                v-if="!remoteStreamList.find(e => e.userId === i.userId)"
                :key="i.userId + Math.random()"
                class="awaitStreamItemBox listStreamItem"
                :class="{ bigShowItem: bigShowIndex === remoteIndex + 1 }"
                @click="bigShowIndex = remoteIndex + 1"
              >
                <div class="distantStream awaitStream">
                  <el-empty
                    :description="i.username + ' 呼叫中...'"
                    :image="require('@/assets/dashboard_images/awaitDoctor.png')"
                  />
                </div>
              </div>
            </template>
            <template v-if="leaveList && leaveList.length !== 0">
              <div
                v-for="i in leaveList"
                v-if="!remoteStreamList.find(e => e.userId === i.userId)"
                :key="i.userId + Math.random()"
                class="awaitStreamItemBox listStreamItem"
              >
                <div class="distantStream leaveStream">
                  <el-empty
                    :description="i.userName + ' 已离开远程会诊'"
                    :image="require('@/assets/dashboard_images/awaitDoctor.png')"
                  />
                </div>
              </div>
            </template>
          </div>
        </div> -->

        <div v-if="viewType === 'newView'" class="topToolbarBox newView">
          <!-- 宫格视图： 平均分布/右侧列表 | 音频音量 | 静音/解除静音 | 管理成员 | 结束会议 -->
          <div class="rightToolbarBox">
            <div class="volumeBoxOutter">
              <el-popover popper-class="customPopper" width="20px" trigger="click">
                <el-slider v-model="volumn" vertical height="100px" :show-tooltip="false" @input="changeVolumn" />
                <div slot="reference" class="volumeBox">
                  <svg-icon icon-class="zzzicon-volumn" />
                  音频音量
                </div>
              </el-popover>
            </div>
            <div class="muteBox" :class="{ microOpen: !muteFlag }" @click="doToggleMute">
              <svg-icon v-if="muteFlag" icon-class="zzzicon-microphone-mute" />
              <svg-icon v-if="!muteFlag" icon-class="zzzicon-microphone" />
              <span>{{ muteFlag ? '已静音' : '麦克风开启' }}</span>
            </div>
            <!-- <el-select v-model="videoDef" class="videoDefSelect" size="mini" @change="videoDefChange">
              <el-option v-for="item in videoDefs" :key="'videoDef3-' + item" :label="item" :value="item" />
            </el-select> -->
            <div class="fblChangeDiv">
              <label
                v-for="item in videoDefs"
                :key="'videoDef2-' + item"
                :class="[videoDef === item ? 'active' : ' ', 'list cp']"
                @click=";(videoDef = item), videoDefChange(item)"
              >{{ item }}</label>
            </div>
          </div>
        </div>
      </div>
    </section>
    <!-- 管理成员右侧弹窗 -->
    <transition name="drawer-rtl" mode="out-in">
      <memberManage
        v-if="rightMemberManageVisible"
        :remote-stream-list="remoteStreamList"
        :await-list="awaitList"
        :doctors="filterDoctors"
        :dis-id="disId"
        :mute-flag-list="muteFlagList"
        @onClose="rightMemberManageVisible = false"
        @onFilterChange="
          v => {
            memberFilterText = v
          }
        "
        @onAdd="doAdd"
      />
    </transition>
    <!-- 管理成员dialog -->
    <transition name="drawer-rtl" mode="out-in">
      <div v-if="manageMemberDialogVisible" class="memberDialog">
        <memberManage
          :remote-stream-list="remoteStreamList"
          :await-list="awaitList"
          :doctors="filterDoctors"
          :dis-id="disId"
          :mute-flag-list="muteFlagList"
          @onClose="manageMemberDialogVisible = false"
          @onFilterChange="
            v => {
              memberFilterText = v
            }
          "
          @onAdd="doAdd"
        />
      </div>
    </transition>
  </div>
</template>
<script>
import Pinyin from 'js-pinyin'
import TRTC from 'trtc-js-sdk'
import memberManage from './components/memberManage.vue'

export default {
  name: 'WiTrtcWindow',
  components: {
    memberManage
  },
  props: [
    'localStream',
    'remoteStreamList',
    'userId',
    'awaitList',
    'leaveList',
    'speakers',
    'doctors',
    'disId',
    'isMember',
    'networkQuality',
    'networkQualityList',
    'muteFlagList',
    'hideMinType',
    'viewType'
  ],
  data() {
    return {
      videoDef: '480p',
      bigShowIndex: 0,
      muteFlag: false,
      typeChanging: false,
      // 平均分布 | 右侧列表
      visialType: '平均分布',
      rightMemberManageVisible: false,
      memberFilterText: '',
      volumn: 100,
      manageMemberDialogVisible: false,
      videoDefs: ['1080p', '720p', '480p']
    }
  },
  computed: {
    filterDoctors() {
      return this.doctors.filter((i) => this.filterNode(this.memberFilterText, i))
    }
  },
  methods: {
    setBigIndex(index) {
      this.bigShowIndex = index
    },
    handleLeave(leaveIndex) {
      if (leaveIndex + 1 === this.bigShowIndex) {
        this.bigShowIndex = 0
      }
    },
    videoDefChange(v) {
      this.localStream.setVideoProfile(v)
      // this.remoteStreamList.map((remoteItem) => {
      //   console.log(`remoteItem`, remoteItem)
      //   // remoteItem.remoteStream.setVideoProfile(v)
      //   return remoteItem
      // })
    },
    filterNode(value, data) {
      if (!value) return true
      const upV = value.toUpperCase()
      const { _pinyin, _pinyinCamel } = this.convertToPinyin(data.name)
      return data.name.indexOf(value) !== -1 || _pinyin.indexOf(upV) !== -1 || _pinyinCamel.indexOf(upV) !== -1
    },
    // 转拼音
    convertToPinyin(cnStr) {
      const _pinyin = Pinyin.getFullChars(cnStr).toUpperCase()
      const _pinyinCamel = Pinyin.getCamelChars(cnStr).toUpperCase()
      return { _pinyin, _pinyinCamel }
    },
    // 静音/取消静音
    doToggleMute() {
      this.muteFlag = !this.muteFlag
      this.$emit('onMuteFlagChange', this.muteFlag)
      if (this.muteFlag) {
        this.closeMicrophone()
      } else {
        this.openMicrophone()
      }
    }, // 关闭麦克风
    closeMicrophone() {
      this.localStream.muteAudio()
      const audioTrack = this.localStream.getAudioTrack()
      if (audioTrack) {
        audioTrack.stop()
      }
    }, // 打开麦克风，重新采集麦克风并替换 audio track
    async openMicrophone() {
      const stream = TRTC.createStream({ audio: true, video: false })
      await stream.initialize()
      this.localStream.unmuteAudio()
      await this.localStream.replaceTrack(stream.getAudioTrack())
    },
    // 结束会诊
    doOverTele() {
      this.$emit('onOverTele')
    },
    // 重新play视频
    replayAll() {},
    replayOk() {
      setTimeout(() => {
        this.typeChanging = false
        if (this.muteFlag) {
          this.closeMicrophone()
        }
      }, 666)
    },
    // 管理成员
    doOpenManageMember() {
      this.$emit('refreshDoctor')
      this.rightMemberManageVisible = true
    },
    doOpenManageMemberDialog() {
      this.$emit('refreshDoctor')
      this.manageMemberDialogVisible = true
    },
    // 添加
    doAdd(docItem) {
      if (!(this.disId.includes(docItem.id) || docItem.status !== 3)) {
        this.$emit('onAdd', docItem)
      }
    },
    // 左侧大的放大
    bigShowClick(item, remoteIndex) {
      if (item === null && remoteIndex === -1 && this.bigShowIndex === 0) {
        this.doVideoClick(`local_stream`)
      } else if (this.bigShowIndex === remoteIndex + 1) {
        this.doVideoClick(`video_bo_box_${item.id}`)
      } else {
        this.bigShowIndex = remoteIndex + 1
      }
    },
    changeVolumn(val) {
      if (this.localStream) {
        this.localStream.setAudioVolume(val / 100)
      }
      if (this.remoteStreamList && this.remoteStreamList.length > 0) {
        this.remoteStreamList.map((remoteItem) => {
          remoteItem.remoteStream.setAudioVolume(val / 100)
          return remoteItem
        })
      }
      // this.localStream.setMICVolume(val / 100)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialogMemberManage {
  width: 100% !important;
}
.wiTrtcWindowContainer {
  &.newView {
    border: 0 none;
  }
  width: 100%;
  border-radius: 0.625vw;
  border: 0.052083vw solid #dedede;
  display: flex;
  flex-wrap: nowrap;
  position: relative;

  .memberDialog {
    height: 100%;
    position: absolute;
    background-color: #fff;
    border-radius: 0.625vw;
    box-shadow: 0px 0px 0.416664vw 0px rgba(0, 0, 0, 0.1);
    right: 0;
    padding-bottom: 0.7vw;
  }
  .leftAssideBox {
    width: 100%;
    position: relative;
    .speakMessageBox {
      position: absolute;
      top: 2vw;
      left: 50%;
      transform: translateX(-50%);
      background: #dff0f5;
      border-radius: 0.3125vw;
      font-size: 0.73vw;
      padding: 0.5vw 1vw;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #222222;
      z-index: 1;
    }
    .topToolbarBox {
      &.newView {
        background-color: #f3f3f3;
        width: 100%;
        .rightToolbarBox {
          width: 100%;
        }
      }
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.5vw 1vw 0.5vw 1.56vw;
      .leftToolBox {
        height: 36px;
        display: flex;
        justify-content: flex-start;
        .visialTypeBox {
          display: flex;
          .labelBox {
            height: 36px;
            font-size: 0.73vw;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 36px;
          }
          .radioGroupBox {
            height: 36px;
            line-height: 36px;
          }
        }
        .videoDefSelect {
          width: 86px;
          ::v-deep .el-input__inner {
            color: #0a86c8;
            font-size: 0.7rem !important;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            border: none !important;
          }
        }
      }
      .rightToolbarBox {
        height: 36px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        .svg-icon {
          width: 0.73vw;
          height: 0.73vw;
          margin-right: 0.26vw;
        }
        .volumeBox {
          display: flex;
          align-items: center;
          font-size: 0.73vw;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          margin-right: 0.6vw;
          cursor: pointer;
        }
        .muteBox {
          display: flex;
          align-items: center;
          font-size: 0.73vw;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          margin-right: 0.6vw;
          cursor: pointer;
          cursor: pointer;
          &.microOpen {
            .svg-icon {
              color: #67c23a !important;
            }
          }
        }
        .memberBox {
          display: flex;
          align-items: center;
          font-size: 0.73vw;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 600;
          color: #222222;
          margin-right: 2.6vw;
          cursor: pointer;
          &.noMRBox {
            margin-right: 0;
          }
          .svg-icon {
            color: #0a86c8;
          }
        }
        .endBox {
          display: flex;
          align-items: center;
          font-size: 0.833328vw;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 600;
          color: #f67f7d;
          cursor: pointer;
          .svg-icon {
            width: 1.1vw;
            color: #f67f7d;
            height: 1.1vw;
            margin-right: 0.26vw;
          }
        }
      }
    }
    #local_stream {
      width: 100%;
      height: 100%;
    }
    .streamVideoBox {
      &.viewType {
        background: rgba(0, 0, 0, 0);
        height: auto;
      }
      background: #f0f7f9;
      border-radius: 0 0 0.625vw 0.625vw;
      height: 32vw;
      position: relative;
      &.rbBorderRadiusNone {
        border-radius: 0 0 0 0.625vw;
      }
      .flexStreamContainer {
        &.newView {
          .flexStreamItem {
            width: 100%;
            height: 14vw;
          }
        }
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        padding: 1vw 2vw 0vw;
        height: 32vw;
        overflow-y: auto;
        // .remoteStreamItemBox {
        // }
        // .awaitStreamItemBox {
        // }

        .flexStreamItem {
          width: calc((100% - 0.885411vw) / 2);
          height: 16.5vw;
          border-radius: 0.625vw;
          overflow: hidden;
          position: relative;
          margin: 0 0.885411vw 0.885411vw 0;
          cursor: pointer;
          user-select: none;
          &:nth-child(2n) {
            margin-right: 0;
          }
          .streamOutterBox {
            position: relative;
          }
        }
        &.layoutType2 {
          display: block;
          width: 14.5vw;
          float: right;
          padding: 1vw 0 0 0;
          overflow-x: hidden;
          overflow-y: auto;
          .flexStreamItem {
            width: 13.5vw;
            height: 10.2vw;
            &.inLeft {
              position: absolute;
              left: 1vw;
              top: 1vw;
              width: calc(100% - 16.5vw);
              height: calc(100% - 2vw);
            }
          }
        }
      }
      .rightListStreamContainer {
        position: relative;
        padding: 1vw;
        height: 32vw;
        display: flex;
        justify-content: flex-end;

        .streamListContainer {
          height: 100%;
          overflow-x: hidden;
          overflow-y: auto;
          .listStreamItem {
            width: 13.5vw;
            height: 10.2vw;
            border-radius: 0.625vw;
            overflow: hidden;
            position: relative;
            margin-bottom: 1vw;
            cursor: pointer;
            user-select: none;
            .streamOutterBox {
              position: relative;
            }
            &.bigShowItem {
              position: absolute;
              left: 1vw;
              top: 1vw;
              width: calc(100% - 16.5vw);
              height: calc(100% - 2vw);
            }
          }
        }
      }
    }

    .righBottomTag {
      position: absolute;
      padding: 3px 8px;
      right: 0px;
      bottom: 0px;
      background: #000000;
      border-radius: 10px 0px 12px 0px;
      font-size: 0.6rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
    }
    .networkQuality {
      position: absolute;
      left: 0px;
      top: 0px;
      background-color: rgba(0, 0, 0, 0.5);
      width: 52px;
      height: 28px;
      border-radius: 0px 0px 15px 0px;
      display: flex;
      align-items: center;
      justify-content: center;
      img {
        width: 23px;
        height: 16px;
      }
    }

    .distantStream {
      width: 100%;
      height: 100%;
      position: relative;
      overflow: hidden;
      border-radius: 12px;
      overflow: hidden;

      ::v-deep .remoteStreamBox {
        position: relative;
        top: 50%;
        transform: translateY(-50%);
        width: 100%;
        height: 100%;
      }
      .el-empty {
        position: relative;
        top: 50%;
        transform: translateY(-50%) scale(0.888);
        ::v-deep .el-empty__image {
          width: 4.5vw;
        }
      }
    }

    .streamOutterBox {
      width: 100%;
      height: 100%;
      position: relative;
    }
    .awaitStream {
      aspect-ratio: 1.5;
      background-color: #f3f3f3;
      width: 100%;
      cursor: pointer;
      overflow: hidden;
    }
    .leaveStream {
      aspect-ratio: 1.5;
      background-color: #f9f5f1;
      width: 100%;
      cursor: pointer;
      overflow: hidden;
    }
  }

  .rightMemberManageBox {
    width: 13vw;
    height: 34vw;
    flex: none;

    .headerBox {
      border-radius: 0 0.625vw 0 0;
      position: relative;
      background: #f3fffd;
      height: calc(1vw + 36px);
      padding: 0.78vw;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 0.73vw;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 600;
      color: #222222;
      user-select: none;
      i {
        font-size: 0.73vw;
        cursor: pointer;
      }
    }

    .tabBox {
      display: flex;
      justify-content: flex-start;
      .tabItem {
        width: 50%;
        height: 2.3vw;
        background: #ebf7f5;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 0.625vw;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 600;
        color: #666666;
        user-select: none;
        cursor: pointer;

        &.activeTab {
          background: #f3fffd;
          color: #0a86c8;
        }
      }
    }

    .inMemberBox {
      height: calc(100% - (1vw + 36px + 0.78vw * 2 + 2.3vw));
      padding: 0.78vw 1.56vw;

      .inMemberItemBox {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 25px;

        .infoBox {
          .nameText {
            height: 1vw;
            font-size: 0.73vw;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #222222;
            line-height: 1vw;
          }
          .roleText {
            height: 0.885vw;
            font-size: 0.625vw;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
            line-height: 0.885vw;
          }
        }
        .microBox {
          .svg-icon {
            width: 0.73vw;
            height: 0.73vw;
          }
        }
      }
    }
    .outMemberBox {
      .outMemberFilterBox {
        margin-bottom: 1vw;
        padding: 0.5vw 0.75vw 0;
      }

      .outMemberItemBox {
        padding: 0.5vw 0.75vw;

        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 1vw;
        margin-bottom: 0.75vw;

        .nameBox {
          font-size: 0.73vw;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #222222;
          line-height: 1vw;
        }
        .callBox {
          .svg-icon {
            width: 1vw;
            height: 1vw;
            color: #3eca70;
            cursor: pointer;
          }
          .notAvaliable {
            .svg-icon {
              color: #c7c7c7;
              cursor: not-allowed;
            }
          }
        }
      }
    }
  }
}
.drawer-rtl-enter-active,
.drawer-rtl-leave-active {
  transition: all 0.15s;
}
.drawer-rtl-enter,
.drawer-rtl-leave-to {
  transform: translateX(13vw);
}
.fblChangeDiv {
  margin-left: auto;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 0.8rem;
  color: #222222;
  .list {
    padding: 0 1rem;
  }
  .active {
    color: #16c2d9;
  }
}
</style>
<style lang="scss">
.customPopper {
  min-width: 64px !important;
}
</style>
