/* eslint-disable no-shadow */
/* eslint-disable no-multi-assign */
/**
 * Created by PanJiaChen on 16/11/18.
 */

import router from '@/router'

import store from '@/store'
import { MessageBox, Message } from 'element-ui'
import dayjs from 'dayjs'
import request from '@/utils/request'
/**
 * @description:校验路由是否存在
 * @param {*} path
 * @return {*}
 * @author: LiSuwan
 * @Date: 2024-11-15 11:47:45
 */
export function routeExists(path) {
  let hasRoute = false

  if (path.indexOf('?') !== -1) {
    path = path.slice(0, path.indexOf('?'))
  }

  const pathArr = path.split('/')

  // eslint-disable-next-line array-callback-return
  hasRoute = router.options.routes.some((route) => {
    if (route.children && route.children.length > 0) {
      return route.children.some((child) => {
        return child.path === pathArr[2]
      })
    } else if (pathArr.length === 2) {
      return route.path.indexOf(pathArr[1]) !== -1
    }
  })

  if (hasRoute === false) {
    MessageBox.confirm('', '提示', {
      showConfirmButton: false,
      showCancelButton: false,
      customClass: 'noViewingPermission',
      type: 'warn'
    })
    return hasRoute
  }
  return hasRoute
}

/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (typeof time === 'string') {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if (typeof time === 'number' && time.toString().length === 10) {
      time *= 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') {
      return ['日', '一', '二', '三', '四', '五', '六'][value]
    }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (`${time}`.length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return `${Math.ceil(diff / 60)}分钟前`
  } else if (diff < 3600 * 24) {
    return `${Math.ceil(diff / 3600)}小时前`
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return `${d.getMonth() + 1}月${d.getDate()}日${d.getHours()}时${d.getMinutes()}分`
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (let i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) s++
    else if (code > 0x7ff && code <= 0xffff) s += 2
    if (code >= 0xdc00 && code <= 0xdfff) i--
  }
  return s
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map((key) => {
      if (json[key] === undefined) return ''
      return `${encodeURIComponent(key)}=${encodeURIComponent(json[key])}`
    })
  ).join('&')
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach((v) => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += `${className}`
  } else {
    classString = classString.substr(0, nameIndex) + classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout,
    args,
    context,
    timestamp,
    result

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * 节流函数
 * @param {Function} func 节流方法体
 * @param {Number} delay 节流间隔
 * @returns void
 */
export function throttle(func, delay) {
  let timerId = null
  let lastExecTime = 0

  return function(...args) {
    const currentTime = Date.now()
    const elapsed = currentTime - lastExecTime

    if (!lastExecTime || elapsed >= delay) {
      lastExecTime = currentTime
      func.apply(this, args)
    } else if (!timerId) {
      timerId = setTimeout(() => {
        lastExecTime = Date.now()
        func.apply(this, args)
        timerId = null
      }, delay - elapsed)
    }
  }
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = `${+new Date()}`
  const randomNum = `${parseInt((1 + Math.random()) * 65536)}`
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp(`(\\s|^)${cls}(\\s|$)`))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ` ${cls}`
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp(`(\\s|^)${cls}(\\s|$)`)
    ele.className = ele.className.replace(reg, ' ')
  }
}

/**
 * 从身份证获取出生年月日 如： 1995/10/24
 * @param {string} idStr 身份证号
 */
export function getYMDFromIdCard(idStr) {
  return `${idStr.substr(6, 4)}-${Number(idStr.substr(10, 2))}-${Number(idStr.substr(12, 2))}`.replace(/-/g, '/')
}

/**
 * 根据BMI转换身体状态 如： 18 -> 偏轻
 * @param {string} idStr 身份证号
 */
export function bmiStatusTrans(bmi) {
  let result = '过重'
  if (bmi < 18.5) {
    result = '偏轻'
  } else if (bmi < 24) {
    result = '标准'
  } else if (bmi < 28) {
    result = '偏重'
  }
  return result
}
// 检查摄像头
export const checkCamera = function(_cb) {
  let videoNum = 0
  let microphoneNum = 0
  if (navigator.mediaDevices) {
    navigator.mediaDevices
      .enumerateDevices()
      .then((devices) => {
        devices.forEach((device) => {
          if (Object.prototype.toString.call(device) === '[object InputDeviceInfo]') {
            console.log(`device`, device)
            if (device.kind === 'videoinput') videoNum += 1
            if (device.kind === 'audioinput') microphoneNum += 1
          }
        })
        _cb({
          videoNum,
          microphoneNum
        })
      })
      .catch((err) => {
        console.log('checkCamera err', err)
      })
  } else {
    _cb({
      videoNum: 1,
      microphoneNum: 1
    })
  }
}
// timestamp 转 xxx天
export const convertTimestamp2Day = (timestamp) => {
  return Math.ceil(timestamp / (1000 * 60 * 60 * 24))
}
// isValue
export const isValue = (val) => {
  return val !== '' && val !== null && val !== undefined
}
// isObjValue
export const isObjValue = (val) => {
  return val !== '' && val !== null && val !== undefined && JSON.stringify(val) !== '{}'
}
// 为空返回 /
export const tEmpty = (val) => {
  return isValue(val) ? val : '/'
}
// 校验角色权限
export function checkRolePermission(val) {
  let hasPermission = false
  const roleCodeList = store.getters.roles.map((item) => item.roleCode)
  // Array
  if (val instanceof Array === true) {
    val.map((codeItem) => {
      if (roleCodeList.includes(codeItem)) {
        hasPermission = true
      }
      return codeItem
    })
  }
  // String
  if (typeof val === 'string') {
    if (roleCodeList.includes(val)) {
      hasPermission = true
    }
  }
  return hasPermission
}
// 提取字符串指定关键词（数组），并给定指定颜色
export const colorStringBySomeStr = (string, colorStrList) => {
  let stringTmp = string
  colorStrList.map((strItem) => {
    console.log(`stringTmp.includes(strItem.str)`, stringTmp.includes(strItem.str))
    if (stringTmp.includes(strItem.str)) {
      stringTmp = stringTmp.replace(strItem.str, `<span style="color: ${strItem.color};">${strItem.str}</span>`)
    }
    return strItem
  })
  return stringTmp
}

/**
 * @description: 获取今天的日期
 * @author: LiSuwan
 * @Date: 2024-08-30 14:42:12
 */
export const getToday = function() {
  const date = new Date()
  const year = date.getFullYear()
  let month = date.getMonth() + 1
  if (month < 10) {
    month = `0${month}`
  }
  const day = date.getDate()
  return `${String(year)}-${month}-${day}`
}

/**
 * @description: 格式化日期
 * @param {*} date：日期
 * @param {*} fmt：格式
 * @return {*}:返回处理好的日期
 * @author: LiSuwan
 * @Date: 2024-08-30 14:44:00
 */
export const formatDate = function(date, fmt) {
  const time = new Date(date)
  const o = {
    'M+': time.getMonth() + 1, // 月份
    'D+': time.getDate(), // 日
    'H+': time.getHours(), // 小时
    'm+': time.getMinutes(), // 分
    's+': time.getSeconds(), // 秒
    'q+': Math.floor((time.getMonth() + 3) / 3), // 季度
    S: time.getMilliseconds() // 毫秒
  }

  if (/(Y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, String(time.getFullYear()).substr(4 - RegExp.$1.length))
  }
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : `00${o[k]}`.substr(String(o[k]).length))
    }
  }

  return fmt
}

/**
 * @description: 最近一周
 * @author: LiSuwan
 * @Date: 2024-08-30 14:59:31
 */
export const reactWeek = (type = 1) => {
  const start = new Date()
  return type === 1 ? start.getTime() - 3600 * 1000 * 24 * 7 : start.getTime() + 3600 * 1000 * 24 * 7
}

/**
 * @description: 最近30天
 * @author: LiSuwan
 * @Date: 2024-08-30 14:59:31
 */
export const react30Day = (days = 30, type = 1) => {
  const currentDate = new Date()
  currentDate.setDate(type === 1 ? currentDate.getDate() - days : currentDate.getDate() + days)

  // 如果需要获取年份、月份和日期的字符串形式：
  const year = currentDate.getFullYear()
  const month = currentDate.getMonth() + 1 // 月份从0开始，需要加1
  const day = currentDate.getDate()
  const formattedDate = `${year}-${month}-${day}`
  return formattedDate
}

/**
 * @description: 获取前三个月
 * @author: LiSuwan
 * @Date: 2024-08-30 14:59:39
 */
export const beforeThree = (months = 3, type = 1) => {
  const dates = new Date()
  dates.setMonth(type === 1 ? dates.getMonth() - months : dates.getMonth() + months)
  const pastMonth = dates.getMonth() + 1
  const pastDay = dates.getDate()
  let pastMonthStr = ''
  let pastDayStr = ''
  if (pastMonth >= 1 && pastMonth <= 9) {
    pastMonthStr = `0${pastMonth}`
  } else {
    pastMonthStr = `${pastMonth}`
  }
  if (pastDay >= 0 && pastDay <= 9) {
    pastDayStr = `0${pastDay}`
  } else {
    pastDayStr = `${pastDay}`
  }
  const endDate = `${dates.getFullYear()}-${pastMonthStr}-${pastDayStr}`
  return endDate
}
/**
 * @description: 获取后三个月
 * @author: LiSuwan
 * @Date: 2024-08-30 14:59:39
 */
export const afterThree = () => {
  const dates = new Date()
  dates.setMonth(dates.getMonth() + 3)
  const pastMonth = dates.getMonth() + 1
  const pastDay = dates.getDate()
  let pastMonthStr = ''
  let pastDayStr = ''
  if (pastMonth >= 1 && pastMonth <= 9) {
    pastMonthStr = `0${pastMonth}`
  } else {
    pastMonthStr = `${pastMonth}`
  }
  if (pastDay >= 0 && pastDay <= 9) {
    pastDayStr = `0${pastDay}`
  } else {
    pastDayStr = `${pastDay}`
  }
  const endDate = `${dates.getFullYear()}-${pastMonthStr}-${pastDayStr}`
  return endDate
}

/**
 * @description: 最近半年
 * @author: LiSuwan
 * @Date: 2024-08-30 14:59:51
 */
export const haflYear = (type = 1) => {
  // 先获取当前时间
  const curDate = new Date().getTime()
  // 将半年的时间单位换算成毫秒
  const halfYear = (365 / 2) * 24 * 3600 * 1000
  // 半年前的时间（毫秒单位）
  const pastResult = type === 1 ? curDate - halfYear : curDate + halfYear
  // 日期函数，定义起点为半年前
  const pastDate = new Date(pastResult)
  const pastYear = pastDate.getFullYear()
  const pastMonth = pastDate.getMonth() + 1
  const pastDay = pastDate.getDate()
  let pastMonthStr = ''
  let pastDayStr = ''
  if (pastMonth >= 1 && pastMonth <= 9) {
    pastMonthStr = `0${pastMonth}`
  } else {
    pastMonthStr = `${pastMonth}`
  }
  if (pastDay >= 0 && pastDay <= 9) {
    pastDayStr = `0${pastDay}`
  } else {
    pastDayStr = `${pastDay}`
  }
  const endDate = `${pastYear}-${pastMonthStr}-${pastDayStr}`
  return endDate
}

/**
 * @description: 最近一年
 * @author: LiSuwan
 * @Date: 2024-08-30 15:00:03
 */
export const reactYear = (type = 1) => {
  const nowDate = new Date()
  const dates = new Date(nowDate)
  dates.setDate(type === 1 ? dates.getDate() - 365 : dates.getDate() + 365)
  const seperator1 = '-'
  const year = dates.getFullYear()
  const month = dates.getMonth() + 1
  const strDate = dates.getDate()
  let pastMonthStr = ''
  let pastDayStr = ''
  if (month >= 1 && month <= 9) {
    pastMonthStr = `0${month}`
  } else {
    pastMonthStr = `${month}`
  }
  if (strDate >= 0 && strDate <= 9) {
    pastDayStr = `0${strDate}`
  } else {
    pastDayStr = `${strDate}`
  }
  const currentdate = year + seperator1 + pastMonthStr + seperator1 + pastDayStr
  return currentdate
}

// 秒数转天、小时、分钟、秒的对象
export function convertSecondsToDaysHours(time) {
  // 将秒数转换成分钟
  let minutes = time / 60
  // 将分钟转换成小时
  let hours = minutes / 60
  // 将小时转换成天
  let days = hours / 24
  let timeStr = ''

  // 向下取整
  days = Math.floor(days)
  hours = Math.floor(hours % 24)
  minutes = Math.floor(minutes % 60)
  const seconds = Math.floor(time % 60)

  if (days > 0) {
    timeStr = `${days}天`
  }

  if (hours > 0) {
    timeStr += `${hours}小时`
  }

  if (days === 0) {
    timeStr += `${minutes}分钟`
  }

  if (time < 60) {
    timeStr = '不足一分钟'
  }

  // 返回天、小时、分钟、秒的对象
  return {
    timeStr,
    days,
    hours,
    minutes,
    seconds
  }
}

/**
 * @description: 转小时，分钟，秒数
 * @author: LiSuwan
 * @Date: 2024-11-01 14:22:33
 */
export function secondsToDHMS(seconds) {
  // const days = Math.floor(seconds / (24 * 3600)) // 计算天数
  seconds %= 24 * 3600 // 剩余秒数
  const hours = Math.floor(seconds / 3600) // 计算小时
  seconds %= 3600 // 剩余秒数
  const minutes = Math.floor(seconds / 60) // 计算分钟
  seconds %= 60 // 剩余秒数

  return `${hours}小时${minutes}分钟${seconds}秒`
}

// ====== echarts图表 start ======
export function chartsfontSize12() {
  return document.documentElement.clientWidth <= 1280 ? '0.5rem' : '0.6rem'
}

export function chartsfontSize14() {
  return document.documentElement.clientWidth <= 1280 ? '0.6rem' : '0.7rem'
}

export function chartsGrid(type = 1) {
  if (document.documentElement.clientWidth <= 1280) {
    return {
      left: '10%',
      top: '25%',
      bottom: '20%',
      right: type === 2 ? '2%' : '8%'
    }
  } else {
    return {
      left: '6%',
      top: '25%',
      bottom: '15%',
      right: '5%'
    }
  }
}

export function chartsGrid2(type = 1) {
  if (document.documentElement.clientWidth <= 1280) {
    return {
      left: type === 2 ? '10%' : '8%',
      top: '12%',
      bottom: '12%',
      right: type === 2 ? '10%' : '5%'
    }
  } else {
    return {
      left: type === 2 ? '8%' : '8%',
      top: '12%',
      bottom: '10%',
      right: type === 2 ? '8%' : '0%'
    }
  }
}

export function chartsGrid3() {
  if (document.documentElement.clientWidth <= 1280) {
    return {
      left: '8%',
      top: '25%',
      bottom: '20%',
      right: '5%'
    }
  } else {
    return {
      left: '5%',
      top: '20%',
      bottom: '15%',
      right: '0%'
    }
  }
}
export function chartsGrid4(type = 1) {
  if (document.documentElement.clientWidth <= 1280) {
    return {
      left: type === 2 ? '10%' : '8%',
      top: '20%',
      bottom: '15%',
      right: type === 2 ? '8%' : '5%'
    }
  } else {
    return {
      left: type === 2 ? '8%' : '5%',
      top: '12%',
      bottom: '10%',
      right: type === 2 ? '8%' : '0%'
    }
  }
}

export function chartsGrid5() {
  if (document.documentElement.clientWidth <= 1280) {
    return {
      left: '8%',
      top: '20%',
      bottom: '20%',
      right: '2%'
    }
  } else {
    return {
      left: '5%',
      top: '20%',
      bottom: '20%',
      right: '2%'
    }
  }
}

/**
 * @description: 设置Y轴名称
 * @author: LiSuwan
 * @Date: 2024-11-20 10:23:40
 */
export function setYAxisName(max, unit = '个') {
  let YAxisName = ''
  if (max > 10000) {
    YAxisName = '单位: 万'
  } else if (max > 1000) {
    YAxisName = '单位: 千'
  } else {
    YAxisName = `单位: ${unit}`
  }

  return YAxisName
}

export function formatterYAxisLabel(maxNum, value) {
  if (maxNum > 10000) {
    return `${value / 10000}`
  }

  if (maxNum > 1000) {
    return `${value / 1000}`
  }

  return value
}

// ====== echarts图表 end ======

/**
 * @description:根据不同环境显示不同的信息
 * @author: LiSuwan
 * @Date: 2024-11-21 11:56:48
 */

/**
 * @description: 截取小数点后数字
 * @param {*} num：数字
 * @param {*} digit：位数
 * @author: LiSuwan
 * @Date: 2024-11-27 18:34:55
 */
export function truncateToOneDecimalPlace(num, digit = 10) {
  // 1. 获取原始数值
  const originalNum = num

  // 2. 将原始数值乘以10
  const multipliedNum = originalNum * digit

  // 3. 使用Math.floor()函数对乘以10后的结果向下取整
  const truncatedNum = Math.floor(multipliedNum)

  // 4. 将取整后的结果除以10
  const result = truncatedNum / digit

  // 5. 返回处理后的数值
  return result
}

/**
 * @description: 转万，千
 * @author: LiSuwan
 * @Date: 2024-11-20 10:23:40
 */
export function setNumbers(max) {
  let numbers = ''
  if (max > 10000) {
    numbers = `${truncateToOneDecimalPlace(max / 10000)}万`
  } else if (max > 1000) {
    numbers = `${truncateToOneDecimalPlace(max / 1000)}千`
  } else {
    numbers = max
  }

  return numbers
}

// 获取指定date年份至当前年份的数据
export function getYearByCurrent(year = 2024) {
  const currentYear = new Date().getFullYear()

  // 初始化年份列表
  const years = []

  // 从2020年开始，直到当前年份，将每个年份添加到列表中
  for (year; year <= currentYear; year++) {
    years.push({
      text: year
    })
  }

  return years
}

/**
 * @description: 获取指定年份的开始日期结束日期
 * @param {*} year
 * @author: LiSuwan
 * @Date: 2024-12-31 11:39:35
 */
export function getYearDateRange(year) {
  // 创建开始日期对象，设置为指定年份的第一天（1月1日）
  const startDate = new Date(year, 0, 1) // 月份从0开始，0表示1月
  console.log(startDate, '开始日期')

  // 创建结束日期对象，设置为指定年份的最后一天（12月31日）
  // 可以通过设置下一年的第一天，然后减去1毫秒来得到当前年份的最后一天的前一秒（实际上是12月31日23:59:59.999）
  const endDate = new Date(year + 1, 0, 0) // 这实际上是下一年的第一天
  endDate.setMilliseconds(endDate.getMilliseconds()) // 调整为当前年份的最后时刻

  console.log(endDate, '开始日期')
  // 返回格式化后的开始日期和结束日期
  return {
    startDate: formatDate(startDate, 'YYYY-MM-DD'),
    endDate: formatDate(endDate, 'YYYY-MM-DD')
  }
}

/**
 * @description: 获取指定年份指定季度的开始日期结束日期
 * @param {*} year
 * @param {*} quarter
 * @return {*}
 * @author: LiSuwan
 * @Date: 2024-12-31 14:06:54
 */
export function getQuarterDateRange(year, quarter) {
  // 定义季度的开始月份（基于0索引，所以1月对应0，4月对应3，依此类推）
  const startMonths = [0, 3, 6, 9]

  // 获取季度的开始月份（基于JavaScript的月份索引）
  const startMonth = startMonths[quarter]

  // 创建开始日期对象（设置为该月的第一天）

  const startDate = new Date(year, startMonth, 1)

  // 计算季度的结束月份（下一季度的开始月份减1）
  const endMonth = startMonths[quarter] + 3
  // 注意：这里要处理跨年的情况，但因为我们只关心当前年份，所以结束日期是当前年份该季度的最后一天

  // 创建结束日期对象（设置为该月的最后一天）
  // 由于JavaScript的Date对象月份是从0开始的，且设置月份为超出范围的值会自动调整到正确的月份和年份，
  // 所以我们可以通过设置月份为下一季度的第一个月份，然后日期为0，来得到当前季度的最后一天。

  const endDate = new Date(year, endMonth, 0)

  // 返回格式化后的开始日期和结束日期（如果需要格式化的话）

  return {
    startDate: formatDate(startDate, 'YYYY-MM-DD'),
    endDate: formatDate(endDate, 'YYYY-MM-DD')
  }
}

// 计算时间选择区间
export const calcTimeInterval = (startDate, endDate) => {
  // 计算起止日期
  const start = dayjs(startDate)
  const end = dayjs(endDate)

  // 计算完整的月份差
  const fullMonths = end.diff(start, 'month')

  // 计算剩余的天数
  const daysDiff = end.diff(start.add(fullMonths, 'month'), 'day')

  // 获取当前月的总天数
  const daysInCurrentMonth = start.add(fullMonths, 'month').daysInMonth()

  // 计算小数部分
  const fraction = daysDiff / daysInCurrentMonth

  // 最终精确月份差
  const preciseMonths = fullMonths + fraction

  return preciseMonths.toFixed(6)
}

/**
 * @description: 用于给树形结构添加customId, 用于tree组件的key,目前原结构存在的nodeId存在重复
 * @param {*} data
 * @return {*}
 * @author: lizhaolu
 * @Date: 2025-03-04 15:56:54
 */
export const addFlattenedIds = (data) => {
  // 扁平化所有节点
  const nodes = []
  function flatten(node) {
    nodes.push(node)
    if (node.children && node.children.length > 0) {
      node.children.forEach((child) => flatten(child))
    }
  }
  flatten(data)

  // 添加id属性
  nodes.forEach((node, index) => {
    node.customId = index + 1 // index从0开始，id从1开始
  })

  return data
}

// 体检类型根据年龄判断
export const getMedicalTypeByAge = (age, arr) => {
  if (age >= 7 && age <= 64) {
    return arr.filter((item) => (item.type || item.batchType) !== 'OLD_CHECK')
  }
}

// 通用导出
export function handleExport({ url, params = {}, method = 'GET', filename = '文件导出.xlsx' }) {
  const config = {
    url,
    method: method.toUpperCase(),
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json'
    }
  }
  if (config.method === 'GET') {
    config.params = params
  } else {
    config.data = params
  }
  return request(config)
    .then((res) => {
      // 创建 blob 下载
      const blob = new Blob([res])
      const link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(link.href)
      Message.success('文件导出成功')
    })
    .catch((error) => {
      Message.error('文件导出失败')
      throw error
    })
}

// base64编码
export function encodeToBase64(str) {
  const encoder = new TextEncoder()
  const bytes = encoder.encode(str)
  const base64 = btoa(String.fromCharCode(...bytes))
  return base64
}

// base64解码
export function decodeFromBase64(base64Str) {
  const binary = atob(base64Str)
  const bytes = Uint8Array.from(binary, (char) => char.charCodeAt(0))
  const decoder = new TextDecoder()
  return decoder.decode(bytes)
}
