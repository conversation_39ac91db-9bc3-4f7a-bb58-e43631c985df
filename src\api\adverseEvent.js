import request from '@/utils/request'

// 分页查询不良事件
export const getAdverseEventList = (data) => {
  return request({
    url: '/cspapi/backend/bad/event/page',
    method: 'post',
    data
  })
}

// 查询不良事件历史记录
export const getAdverseEventHistoryList = (data) => {
  return request({
    url: '/cspapi/backend/bad/event/list/history',
    method: 'post',
    data
  })
}

// 保存不良事件
export const addAdverseEvent = (data) => {
  return request({
    url: '/cspapi/backend/bad/event/save',
    method: 'post',
    data
  })
}

// 删除不良事件
export const deleteAdverseEvent = (data) => {
  return request({
    url: '/cspapi/backend/bad/event/remove',
    method: 'post',
    data
  })
}

// 不良事件统计
export const getAdverseEventStatistics = (data) => {
  return request({
    url: '/cspapi/backend/bad/event/count',
    method: 'post',
    data
  })
}
