<template>
  <div class="inspection-testing">
    <el-card class="inspection-testing-search">
      <el-form :model="queryParams" label-width="120px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="姓名：">
              <el-input v-model="queryParams.keyword" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="检验日期：">
              <el-date-picker
                v-model="queryParams.timeRange"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="检查项目：">
              <el-select v-model="queryParams.itemCodeList" multiple placeholder="请选择检查项目" style="width: 100%">
                <el-option v-for="item in checkProjectList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="inspection-testing-table">
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 50px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <!-- 慢病病种 -->
        <template #disease="{ row }">
          <ChronicDiseaseType :record="row" />
        </template>

        <template #sex="{ row }">
          <span>{{ genderTransform(row.sex) }}</span>
        </template>

        <template #idCard="{ row }">
          <EncryptionStr :cipher-text="row.idCard" :replace="row.idCardReplace" />
        </template>

        <template #phone="{ row }">
          <EncryptionStr :cipher-text="row.phone" :replace="row.phoneReplace" />
        </template>

        <template #status="{ row }">
          <el-tag v-if="row.status === 1" type="primary">检查中</el-tag>
          <el-tag v-else-if="row.status === 5" type="success">已完成</el-tag>
          <el-tag v-else-if="row.status === 9" type="danger">已取消</el-tag>
        </template>

        <template #operation="{ row }">
          <OperateMenu :row="row">
            <template #view>
              <el-button v-if="row.status !== 1" type="text" size="small" @click="handleView(row)"> 详情</el-button>
            </template>
            <template v-if="row.status === 1" #continue>
              <el-button type="text" size="small" @click="handleContinue(row)">继续检查</el-button>
            </template>
            <template v-if="row.status === 1" #cancel>
              <el-button type="text" size="small" @click="handleCancel(row)">取消检查</el-button>
            </template>
            <template v-if="row.status === 1" #finish>
              <el-button type="text" size="small" @click="handleFinish(row)">完成检查</el-button>
            </template>
          </OperateMenu>
        </template>
      </base-table>
    </el-card>

    <CancelModal ref="cancelDialog" @confirm="handleCancelConfirm" />
  </div>
</template>

<script>
import { genderTransform } from '@/utils/cspUtils'
import { getInspectionTestingList, completeCheck, cancelCheck } from '@/api/examination'
import { tabList } from '@/views/receptionCenter/patientReception/component/complicationsScreening.js'
import BaseTable from '@/components/BaseTable/index.vue'
import ChronicDiseaseType from '@/components/chronicDiseaseType/index.vue'
import tableMixin from '@/mixins/tableMixin'
import CancelModal from '@/views/receptionCenter/receptionWorkbench/component/cancelModal.vue'
import OperateMenu from '@/components/operateMenu/index.vue'
import EncryptionStr from '@/components/encryptionStr/index.vue'

export default {
  name: 'Examination',
  components: { BaseTable, ChronicDiseaseType, CancelModal, OperateMenu, EncryptionStr },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        keyword: '',
        timeRange: [],
        itemCodeList: []
      },
      checkProjectList: tabList,
      loading: false,
      tableData: [],
      record: '',
      columns: [
        // { type: 'index', label: '序号' },
        { prop: 'patientName', label: '姓名', width: 120 },
        { prop: 'disease', label: '慢病病种', width: 150, slot: 'disease' },
        { prop: 'sex', label: '性别', slot: 'sex' },
        { prop: 'age', label: '年龄' },
        { prop: 'idCardReplace', label: '身份证号', width: 190, slot: 'idCard' },
        { prop: 'phoneReplace', label: '手机号码', width: 140, slot: 'phone' },
        { prop: 'address', label: '地址', width: 180, showOverflowTooltip: true },
        { prop: 'itemNameStr', label: '检查项目', width: 180, showOverflowTooltip: true },
        { prop: 'doctorName', label: '检验医生' },
        { prop: 'inspectDate', label: '检验日期', width: 120 },
        { prop: 'status', label: '状态', slot: 'status' },
        { prop: 'cancelReason', label: '取消检验原因', width: 160, showOverflowTooltip: true },
        {
          prop: 'operation',
          label: '操作',
          slot: 'operation',
          width: 160,
          fixed: window.innerWidth < 1600 ? 'right' : false
        }
      ]
    }
  },

  methods: {
    genderTransform,
    async getTableList(params) {
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }

      return await getInspectionTestingList(queryParams)
    },

    handleCancel(row) {
      this.record = row
      this.$refs.cancelDialog.form.cancelReason = ''
      this.$refs.cancelDialog.visible = true
    },

    async handleCancelConfirm(reason) {
      const res = await cancelCheck({
        id: this.record.id,
        cancelReason: reason
      })
      if (res.code === 200) {
        this.$message.success('取消成功')
        this.handleReset()
        this.$refs.cancelDialog.visible = false
      }
    },

    handleContinue(row) {
      this.$store.commit('patientExamination/SET_PATIENT_EXAMINATION_DATA', {})
      this.$router.push({
        path: '/receptionCenter/patientExamination',
        query: {
          id: row.id
        }
      })
    },

    async handleFinish(row) {
      const res = await completeCheck({
        id: row.id
      })
      if (res.code === 200) {
        this.$message.success('检查完成')
        this.handleReset()
      }
    },

    handleView(row) {
      this.$store.commit('patientExamination/SET_PATIENT_EXAMINATION_DATA', {})
      this.$router.push({
        path: '/receptionCenter/patientExamination',
        query: { id: row.id }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/form-overrides.scss';
.inspection-testing {
  display: flex;
  flex-direction: column;
  height: 100%;
  .inspection-testing-search {
    margin: 16px;
    height: 77px;
  }
  .inspection-testing-table {
    flex: 1;
    margin: 0 16px;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
}
</style>
