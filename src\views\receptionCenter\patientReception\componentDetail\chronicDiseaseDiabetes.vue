<!-- 慢病病种-糖尿病 -->
<template>
  <div class="chronic-disease-diabetes">
    <el-form ref="formRef" :model="form" label-width="130px" :rules="rules">
      <el-form-item label="血糖筛查方式：" prop="bloodSugarScreening">
        <el-checkbox-group v-model="form.bloodSugarScreening">
          <el-checkbox label="1">指尖血糖</el-checkbox>
          <el-checkbox label="2">糖化血红蛋白</el-checkbox>
          <el-checkbox label="3">OGTT</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <flag-component v-if="form.bloodSugarScreening.includes('1')" title="指尖血糖" style="margin-bottom: 10px" />
      <el-form-item v-if="form.bloodSugarScreening.includes('1')" label="血糖类型：" prop="fingerSugarType">
        <el-radio-group v-model="form.fingerSugarType">
          <el-radio :label="1">空腹血糖</el-radio>
          <el-radio :label="2">随机血糖</el-radio>
          <el-radio :label="3">餐后2h血糖</el-radio>
          <el-radio :label="4">既往空腹血糖</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item
        v-if="form.bloodSugarScreening.includes('1')"
        :label="
          form.fingerSugarType === 4
            ? '既往空腹血糖：'
            : form.fingerSugarType === 3
              ? '餐后2h血糖：'
              : form.fingerSugarType === 2
                ? '随机血糖：'
                : form.fingerSugarType === 1
                  ? '空腹血糖：'
                  : '血糖'
        "
        prop="fingerSugarValue"
      >
        <custom-input-number v-model="form.fingerSugarValue" style="width: 30%">
          <template #append>mmol/L</template>
        </custom-input-number>
      </el-form-item>

      <flag-component v-if="form.bloodSugarScreening.includes('2')" title="糖化血红蛋白" style="margin-bottom: 10px" />
      <el-form-item v-if="form.bloodSugarScreening.includes('2')" label="糖化血红蛋白：" prop="hba1cValue">
        <custom-input-number v-model="form.hba1cValue" style="width: 30%">
          <template #append>%</template>
        </custom-input-number>
      </el-form-item>

      <flag-component v-if="form.bloodSugarScreening.includes('3')" title="OGTT" style="margin-bottom: 10px" />
      <!-- 空腹血糖 -->
      <el-form-item v-if="form.bloodSugarScreening.includes('3')" label="空腹血糖：" prop="kfSugarValue">
        <custom-input-number v-model="form.kfSugarValue" style="width: 30%">
          <template #append>mmol/L</template>
        </custom-input-number>
      </el-form-item>

      <el-form-item v-if="form.bloodSugarScreening.includes('3')" label="餐后0.5h血糖：" prop="chHalfSugarValue">
        <custom-input-number v-model="form.chHalfSugarValue" style="width: 30%">
          <template #append>mmol/L</template>
        </custom-input-number>
      </el-form-item>

      <el-form-item v-if="form.bloodSugarScreening.includes('3')" label="餐后1h血糖：" prop="chOneSugarValue">
        <custom-input-number v-model="form.chOneSugarValue" style="width: 30%">
          <template #append>mmol/L</template>
        </custom-input-number>
      </el-form-item>

      <el-form-item v-if="form.bloodSugarScreening.includes('3')" label="餐后2h血糖：" prop="chTwoSugarValue">
        <custom-input-number v-model="form.chTwoSugarValue" style="width: 30%">
          <template #append>mmol/L</template>
        </custom-input-number>
      </el-form-item>

      <el-form-item v-if="form.bloodSugarScreening.includes('3')" label="餐后3h血糖：" prop="chThreeSugarValue">
        <custom-input-number v-model="form.chThreeSugarValue" style="width: 30%">
          <template #append>mmol/L</template>
        </custom-input-number>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import FlagComponent from '@/components/flagComponent/index.vue'

export default {
  name: 'ChronicDiseaseDiabetes',
  components: {
    FlagComponent
  },
  data() {
    return {
      form: {
        bloodSugarScreening: ['1', '2', '3'],
        fingerSugarType: 1,
        fingerSugarValue: '',
        kfSugarValue: '',
        hba1cValue: '',
        chHalfSugarValue: '',
        chOneSugarValue: '',
        chTwoSugarValue: '',
        chThreeSugarValue: ''
      },
      rules: {
        bloodSugarScreening: [{ type: 'array', required: true, message: '血糖筛查方式', trigger: 'change' }],
        fingerSugarType: [{ required: true, message: '血糖类型', trigger: 'change' }],
        kfSugarValue: [{ required: true, message: '空腹血糖', trigger: 'blur' }],
        fingerSugarValue: [{ required: true, message: '指尖血糖', trigger: 'blur' }],
        hba1cValue: [{ required: true, message: '糖化血红蛋白', trigger: 'blur' }],
        chTwoSugarValue: [{ required: true, message: '餐后2h血糖', trigger: 'blur' }]
      },
      fingertipSugarItemId: '',
      hba1cItemId: '',
      ogttItemId: '',
      fingertipSugarItemDetailId: '',
      hba1cItemDetailId: '',
      ogttItemDetailId: ''
    }
  },
  watch: {
    'form.bloodSugarScreening': {
      handler(newVal) {
        // 存下来，在打印引导单那边使用
        sessionStorage.setItem('bloodSugarScreening', JSON.stringify(newVal))
      },
      immediate: true
    }
  },
  methods: {
    initData(data) {
      const form = cloneDeep(this.form)

      const fingertipSugarData = data.find((item) => item.itemCode === 'FINGER_SUGAR') || {}
      const hba1cData = data.find((item) => item.itemCode === 'SUGAR_HEMOGLOBIN') || {}
      const ogttData = data.find((item) => item.itemCode === 'OGTT') || {}

      this.fingertipSugarItemId = fingertipSugarData.id
      this.hba1cItemId = hba1cData.id
      this.ogttItemId = ogttData.id
      this.fingertipSugarItemDetailId = fingertipSugarData.data && fingertipSugarData.data.id
      this.hba1cItemDetailId = hba1cData.data && hba1cData.data.id
      this.ogttItemDetailId = ogttData.data && ogttData.data.id
      form.bloodSugarScreening = this.$store.getters.highRiskScreeningData.highRiskScreening.bloodSugarScreening
        ? this.$store.getters.highRiskScreeningData.highRiskScreening.bloodSugarScreening.split(',')
        : ['1', '2', '3']
      form.fingerSugarType = (fingertipSugarData.data && fingertipSugarData.data.fingerSugarType) || 1
      form.fingerSugarValue = fingertipSugarData.data && fingertipSugarData.data.fingerSugarValue
      form.hba1cValue = hba1cData.data && hba1cData.data.hba1cValue
      form.kfSugarValue = ogttData.data && ogttData.data.kfSugarValue
      form.chHalfSugarValue = ogttData.data && ogttData.data.chHalfSugarValue
      form.chOneSugarValue = ogttData.data && ogttData.data.chOneSugarValue
      form.chTwoSugarValue = ogttData.data && ogttData.data.chTwoSugarValue
      form.chThreeSugarValue = ogttData.data && ogttData.data.chThreeSugarValue

      this.form = form
    },
    async handleSave() {
      const result = {
        name: '糖尿病-血糖监测',
        success: false,
        data: {
          bloodSugarScreening: this.form.bloodSugarScreening.join(','),
          fingertip: {
            fingerSugarType: this.form.fingerSugarType,
            fingerSugarValue: this.form.fingerSugarValue,
            itemId: this.fingertipSugarItemId,
            itemDetailId: this.fingertipSugarItemDetailId,
            itemCode: 'FINGER_SUGAR',
            name: '指尖血糖'
          },
          hba1c: {
            hba1cValue: this.form.hba1cValue,
            itemId: this.hba1cItemId,
            itemDetailId: this.hba1cItemDetailId,
            itemCode: 'SUGAR_HEMOGLOBIN',
            name: '糖化血红蛋白'
          },
          ogtt: {
            kfSugarValue: this.form.kfSugarValue,
            chHalfSugarValue: this.form.chHalfSugarValue,
            chOneSugarValue: this.form.chOneSugarValue,
            chTwoSugarValue: this.form.chTwoSugarValue,
            chThreeSugarValue: this.form.chThreeSugarValue,
            itemId: this.ogttItemId,
            itemDetailId: this.ogttItemDetailId,
            itemCode: 'OGTT',
            name: 'OGTT'
          }
        }
      }

      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn('糖尿病-血糖监测校验异常', err)
        result.success = false
      }

      return result
    }
  }
}
</script>

<style lang="scss" scoped>
.chronic-disease-diabetes {
  padding: 16px;
}
</style>
