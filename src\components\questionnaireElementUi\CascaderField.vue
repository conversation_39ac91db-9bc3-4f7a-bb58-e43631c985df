<template>
  <el-form-item :label="item.label" :prop="item.prop">
    <div :class="item.class">
      <el-cascader
        v-model="localValue"
        :options="item.options"
        :props="cascaderProps"
        :placeholder="item.placeholder || '请选择'"
        :clearable="item.clearable !== false"
        :filterable="item.filterable === true"
        :show-all-levels="false"
        style="width: 100%"
        @change="handleChange"
      >
        <template slot-scope="{ node, data }">
          <span>{{ data.label }}</span>
          <span v-if="!node.isLeaf"> ({{ node.children.length }})</span>
        </template>
      </el-cascader>
    </div>
  </el-form-item>
</template>

<script>
export default {
  name: 'CascaderField',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    value: {
      type: [Array, String, Number],
      default: () => []
    }
  },
  data() {
    return {
      cascaderProps: {
        expandTrigger: 'hover',
        checkStrictly: true, // 允许选择任意一级选项
        ...this.item.props
      }
    }
  },
  computed: {
    localValue: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  },
  methods: {
    handleChange(value) {
      // 如果需要额外的处理逻辑可以在这里添加
      if (this.item.onChange) {
        this.item.onChange(value)
      }
    }
  }
}
</script>
