/* eslint-disable no-shadow */
import { getDrugManagementList } from '@/api/drugManagement'
import { getConsultationRecordDetailApi } from '@/api/remoteConsultation'

const state = {
  drugList: [],
  consultationRecordDetail: {}
}

const mutations = {
  SET_DRUG_LIST(state, list) {
    state.drugList = list
  },

  SET_CONSULTATION_RECORD_DETAIL(state, detail) {
    state.consultationRecordDetail = detail
  }
}

const actions = {
  async getDrugList({ commit, state }) {
    try {
      const res = await getDrugManagementList({ pageNo: 1, pageSize: 1000 })
      if (res.code === 200) {
        commit('SET_DRUG_LIST', res.data.list)
      }
    } catch (error) {
      console.error('获取药品列表失败：', error)
    }

    return state.drugList
  },

  // 会诊记录详情
  async getConsultationRecordDetail({ commit }, payload) {
    try {
      const res = await getConsultationRecordDetailApi({
        id: payload.roomId
      })
      if (res.code === 200) {
        commit('SET_CONSULTATION_RECORD_DETAIL', res.data)
      }
    } catch (error) {
      console.error('获取会诊记录详情失败：', error)
    }
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
