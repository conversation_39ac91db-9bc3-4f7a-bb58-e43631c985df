<template>
  <el-drawer :visible.sync="drawer" direction="rtl" size="25%" append-to-body :show-close="false">
    <div class="title">
      <span>消息通知</span>
    </div>
    <div class="content">
      <div class="message-notice-content">
        <el-collapse v-model="activeTypes">
          <el-collapse-item v-for="group in messageList" :key="group.type" :name="group.type">
            <template slot="title">
              <div class="group-title">
                <span class="type-text">{{ group.type }}</span>
                <span v-if="group.freshCount" class="count">{{ group.freshCount }}</span>
              </div>
            </template>
            <div
              v-for="item in group.messageVOList"
              :key="item.id"
              class="message-notice-item"
              :class="item.status === 0 ? 'unread' : 'read'"
            >
              <div class="item-header">
                <i class="el-icon-bell item-icon" />
                <span v-if="item.status === 0" class="unread-dot" />
                <span class="time">{{ item.sendTime || item.createTime }}</span>
                <i class="el-icon-close close-icon" @click="handleClose(item)" />
              </div>
              <div class="item-body">
                <div v-if="item.title" class="title-line">{{ item.title }}</div>
                <div class="desc-line">
                  <span v-if="item.content" class="text">{{ item.content }}</span>
                  <a
                    v-if="item.bizModule && item.bizModule !== '登录信息'"
                    class="link"
                    @click="handleRead(item)"
                  >请及时查看！</a>
                  <a v-else class="link" @click="handleLoginRead(item)">标记已读！</a>
                </div>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
        <el-empty v-if="!messageList || messageList.length === 0" description="暂无消息" />
      </div>
    </div>
    <div class="footer">
      <el-button @click="handleClear">清空</el-button>
      <el-button type="primary" @click="handleShowAll">显示全部</el-button>
    </div>
  </el-drawer>
</template>

<script>
import { clearMessage, markRead } from '@/api/messageNotification'
import { getUserId } from '@/utils/auth'
import { Notification } from 'element-ui'

export default {
  name: 'MessageNotice',
  data() {
    return {
      drawer: false,
      // 父组件一定传入数组：this.$refs.messageNotice.messageList = res.data.typeList
      messageList: [],
      activeTypes: ['登录信息', '超声质控']
    }
  },

  methods: {
    // 清除某条消息
    async handleClose(item) {
      const res = await clearMessage({
        userId: getUserId(),
        messageId: item.id
      })
      if (res.code === 200) {
        this.$emit('refreshMessage')
      }
    },
    // 标记某条消息已读
    async handleRead(item) {
      const res = await markRead({
        userId: getUserId(),
        messageId: item.id
      })

      if (res.code !== 200) return

      this.$emit('refreshMessage')
      this.drawer = false

      let routeConfig = null

      switch (item.title) {
        case '发起超声质控申请':
          routeConfig = {
            path: '/qualityControl/ultrasonicQualityControl/detail',
            query: {
              id: item.bizRecord,
              itemCode: item.bizModule
            }
          }
          break

        case '超声质控审批回复':
          routeConfig = {
            path:
              item.bizModule === 'check' ? '/receptionCenter/patientExamination' : '/receptionCenter/patientReception',
            query: { id: item.bizRecord }
          }
          break
      }

      if (routeConfig) {
        this.$router.push(routeConfig)
      }
    },
    // 清空所有消息
    async handleClear() {
      const res = await clearMessage({
        userId: getUserId()
      })
      if (res.code === 200) {
        this.$emit('refreshMessage')
        Notification.closeAll()
      }
    },
    // 跳转全部消息
    handleShowAll() {
      this.drawer = false
      setTimeout(() => {
        this.$router.push('/messageNotification/allMessage')
      }, 300)
    },
    // 标记登录信息已读
    async handleLoginRead(item) {
      const res = await markRead({
        userId: getUserId(),
        messageId: item.id
      })

      if (res.code !== 200) return

      this.$emit('refreshMessage')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer__header {
  margin-bottom: 0;
}
::v-deep .el-drawer__body {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0;
}

.title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  padding-bottom: 10px;
  border-bottom: 1px solid #e6e6e6;
}
.content {
  flex: 1; /* 占满剩余空间 */
  overflow: auto; /* 内容区可滚动 */
  padding: 10px;
}

.message-notice-content {
  ::v-deep .el-collapse-item__header {
    height: 42px;
    line-height: 42px;
    padding: 0 8px;
    font-weight: 600;
    background: #f0f4fa;
  }

  .group-title {
    display: flex;
    align-items: center;
    width: 100%;
    .type-text {
      color: #303133;
      font-size: 16px;
    }
    .count {
      margin-left: 8px;
      color: #fff;
      background: #ff4d4f;
      border-radius: 10px;
      padding: 0 6px;
      font-size: 12px;
      line-height: 18px;
      height: 18px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }

  .message-notice-item {
    padding: 12px 8px 14px 8px;
    border-bottom: 1px solid #f0f0f0;
    background: #f0f4fa;
    &:last-child {
      border-bottom: none;
    }

    &.unread {
      .item-icon {
        background: #0891cd;
      }
      .title-line {
        font-weight: 700;
        color: #303133;
      }
    }
    &.read {
      .item-icon {
        background: #dcdfe6;
      }
      .title-line {
        font-weight: 600;
        color: #909399;
      }
      .desc-line {
        color: #a8abb2;
      }
    }

    .item-header {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 6px;

      .item-icon {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background: #0891cd;
        color: #fff;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }

      .unread-dot {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #ff4d4f;
        display: inline-block;
      }

      .time {
        color: #999;
        font-size: 12px;
        flex: 1;
      }

      .close-icon {
        color: #c0c4cc;
        cursor: pointer;
        font-size: 16px;
        &:hover {
          color: #909399;
        }
      }
    }

    .item-body {
      .title-line {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 2px;
      }

      .desc-line {
        font-size: 13px;
        color: #606266;
        line-height: 20px;

        .link {
          color: #409eff;
          margin-left: 6px;
          cursor: pointer;
          text-decoration: none;
        }
      }
    }
  }
}

.footer {
  padding: 10px 30px;
  border-top: 1px solid #e6e6e6;
  display: flex;
  justify-content: space-between;
}
</style>
