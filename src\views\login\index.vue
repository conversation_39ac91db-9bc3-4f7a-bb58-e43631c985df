<template>
  <div
    class="login-container public_v2"
    :style="`background-image: url(${require('@/assets/login_images/lgoin_v3_bg.png')});`"
  >
    <div class="login-box">
      <div class="right">
        <div class="logoBox">
          <!-- LOGO -->
          <img :src="targetEnv.LOGO" alt="">
        </div>
        <div class="titleContainer">
          <!-- TITLE -->
          <div class="titleBox">{{ targetEnv.TEXT }}</div>
          <div v-if="targetEnv.TITLE" class="subTitle">{{ targetEnv.TITLE }}</div>
        </div>
        <div class="errorMessageBox" :style="`background:${msg === '' ? '#fff' : ''}`">{{ msg }}</div>
        <el-tabs v-model="activeName" :stretch="true" @tab-click="handleClick">
          <el-tab-pane label="密码登录" name="password">
            <span slot="label" class="tabs-label"> 密码登录 </span>
          </el-tab-pane>

          <div v-if="activeName === 'password'">
            <div style="font-size: 0.8rem; color: #666666; padding-bottom: 0.5rem">账号</div>
            <div class="login-mobile">
              <el-input v-model="loginForm.phone" placeholder="请输入账号">
                <template slot="prepend">
                  <svg-icon icon-class="user" style="color: #dedede" />
                </template>
              </el-input>
            </div>
            <div style="font-size: 0.8rem; color: #666666; padding-bottom: 0.5rem">密码</div>
            <div class="login-code">
              <el-tooltip v-model="capsTooltip" content="大写键已打开" placement="right" manual>
                <el-input
                  ref="password"
                  v-model="loginForm.password"
                  name="password"
                  autocomplete="on"
                  :validate-event="false"
                  :type="passwordType"
                  placeholder="请输入密码"
                  @keyup.native="checkCapslock"
                  @blur="capsTooltip = false"
                  @keyup.enter.native="handleLogin"
                >
                  <template slot="prepend">
                    <svg-icon icon-class="password" style="color: #dedede" />
                  </template>
                  <template slot="append">
                    <span class="show-pwd" @click="showPwd">
                      <svg-icon :icon-class="passwordType === 'password' ? 'eye-close' : 'eye-open'" />
                    </span>
                  </template>
                </el-input>
              </el-tooltip>
            </div>

            <el-button
              :loading="loading"
              type="primary"
              class="handleLoginButton flex_center"
              tabindex="4"
              @click.native.prevent="handleLogin"
            >{{ loading ? '正在登录' : '立即登录' }}
            </el-button>
          </div>
        </el-tabs>
      </div>

      <pro-dialog :visible.sync="visible" title="选择机构" width="650px" top="22vh">
        <div class="org-list">
          <div v-for="item in orgList" :key="item.id" class="org-item" @click="handleSelectOrg(item)">
            <div class="org-item-row is-top">
              <svg-icon icon-class="depart" class="departIcon" />
              <span class="label-text">组织机构：</span>
              <span class="value">{{ item.departName }}</span>
            </div>
            <div class="org-item-row two-cols">
              <div class="pair">
                <svg-icon icon-class="username" />
                <span class="label-text">姓名：</span>
                <span class="name">{{ item.username }}</span>
              </div>
              <div class="pair">
                <svg-icon icon-class="userid" />
                <span class="label-text">用户ID：</span>
                <span class="userid">{{ item.userid }}</span>
              </div>
            </div>
          </div>
        </div>
      </pro-dialog>

      <!-- 短信验证 -->
      <sms-verification ref="smsVerification" :account="loginForm.phone" @confirm="handleSmsConfirm" />
    </div>
    <p class="errorMessageBox_label fontSize_12">技术实现：卫软(江苏)科技有限公司</p>
  </div>
</template>

<script>
import { loginSetting as targetEnv } from './loginEnum'
import { validUsername } from '@/utils/validate'
import { getVerCode, getWxQrcodeApi, getWxQrcodeStatusApi } from '@/api/user'

import { localCache } from '@/utils/cache'
import { setToken, setUserId } from '@/utils/auth'
import QRCode from 'qrcodejs2'
import CryptoJS from '@/utils/CryptoJS'
import ProDialog from '@/components/ProDialog/index.vue'
import SmsVerification from './component/smsVerification.vue'

export default {
  name: 'Login',
  components: { ProDialog, SmsVerification },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (value === '') {
        this.msg = '账号不能为空。'
        setTimeout(() => {
          this.msg = ''
        }, 3000)
        callback(new Error(' '))
      } else if (!validUsername(value)) {
        this.msg = '账号密码错误。'
        setTimeout(() => {
          this.msg = ''
        }, 3000)
        callback(new Error(' '))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length === 0) {
        this.msg = '密码不能为空'
        setTimeout(() => {
          this.msg = ''
        }, 3000)
        callback(new Error(' '))
      } else if (value.length < 6) {
        this.msg = '密码不能小于6位。'
        setTimeout(() => {
          this.msg = ''
        }, 3000)
        callback(new Error(' '))
      } else {
        callback()
      }
    }
    return {
      loginMobile: '',
      loginCode: '',
      // 页面静态变量
      targetEnv,

      loginForm: {
        phone: '',
        password: ''
      },
      loginRules: {
        phone: [{ required: true, validator: validateUsername }],
        password: [{ required: true, validator: validatePassword }]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      // showDialog: false,
      redirect: undefined,
      otherQuery: {},
      remember: false,
      msg: '',
      activeName: 'password',
      wxQrcode: '',
      wxQrcodeLoading: false,
      loseEfficacyDate: '',
      typeNumber: 0,
      wxAuthorization: false,
      timer: null, // 定时器
      countdown: 60, // 倒计时时间
      isCounting: false, // 是否正在倒计时
      codeLoading: false, // 验证码loading
      visible: false,
      orgList: []
    }
  },
  computed: {
    buttonText() {
      return this.isCounting ? `${this.countdown}秒后重新获取` : '获取验证码'
    }
  },
  watch: {
    $route: {
      handler(route) {
        const { query } = route
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  destroyed() {
    window.removeEventListener('storage', this.afterQRScan)
  },
  methods: {
    checkCapslock(e) {
      const { key } = e
      this.capsTooltip = key && key.length === 1 && key >= 'A' && key <= 'Z'
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    startCountdown() {
      this.isCounting = true
      this.countdown = 60
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--
        } else {
          this.resetCountdown()
        }
      }, 1000)
    },
    resetCountdown() {
      clearInterval(this.timer)
      this.isCounting = false
    },
    phoneValid() {
      // 简单验证手机号格式（仅为示例）
      const phoneRegex = /^[1]([3-9])[0-9]{9}$/
      return phoneRegex.test(this.loginMobile)
    },
    handleCode() {
      console.log(this.loginMobile)
      if (!this.loginMobile) {
        return this.$message.warning('请输入手机号码')
      }
      const valid = this.phoneValid()
      if (!valid) {
        return this.$message.warning('请输入正确的手机号码')
      }
      this.getCodeApi()
    },
    async getCodeApi() {
      this.codeLoading = true
      try {
        const data = await getVerCode({
          phone: this.loginMobile,
          platform: 'pc'
        })
        this.startCountdown() // 开始倒计时
        this.$message.success(data.msg)
      } catch (e) {
        this.resetCountdown()
      } finally {
        this.codeLoading = false
      }
    },
    handleClick(tab, event) {
      this.loseEfficacyDate = new Date().getTime()
      if (this.$refs.scanCode) this.$refs.scanCode.innerHTML = ''
      if (this.activeName === 'scanCode') {
        this.getWxQrcode().then(() => {
          this.getWxQrcodeStatus()
        })
      }
    },
    async getWxQrcode() {
      this.wxQrcodeLoading = true
      this.wxAuthorization = false
      this.typeNumber = 0
      this.loseEfficacyDate = new Date().getTime()
      setTimeout(() => {
        if (this.activeName === 'scanCode' && new Date().getTime() - this.loseEfficacyDate > 5 * 60 * 1000) {
          this.loseEfficacyDate = ''
        }
      }, 5 * 60 * 1000)
      try {
        const res = await getWxQrcodeApi()
        if (res.code === 200 && res.data) {
          this.wxQrcode = res.data.qrcode
          // this.creatQrCode(`${URL_ENUM.HTTP_ENV}/?pc_code=${res.data.qrcode}`)
          this.creatQrCode(`https://miniprogram.starcds.cn/?pc_code=${res.data.qrcode}`)
          this.wxQrcodeLoading = false
        }
      } catch (error) {
        this.wxQrcodeLoading = false
      }
    },
    // https://test.cunyinet.cn/?pc_code=
    creatQrCode(str) {
      if (this.$refs.scanCode) this.$refs.scanCode.innerHTML = ''
      // eslint-disable-next-line no-unused-vars
      const qrcode = new QRCode(this.$refs.scanCode, {
        text: str || '', // 需要转换为二维码的内容
        width: 250,
        height: 250,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H
        // correctLevel 容错级别，可设置为：
        // QRCode.CorrectLevel.L
        // QRCode.CorrectLevel.M
        // QRCode.CorrectLevel.Q
        // QRCode.CorrectLevel.H
        // L - M - Q - H 码密度越来越高，默认不设置是用 H
      })
    },
    async getWxQrcodeStatus() {
      if (this.activeName === 'scanCode') {
        const res = await getWxQrcodeStatusApi(this.wxQrcode)
        if (res.code === 200 && res.data) {
          this.typeNumber = res.data.status
          // 1 成功 其他  失败
          if (res.data.status === 1) {
            // 成功 却换页面
            // this.wxQrcode
            // getLoginPcByWxApi({ qrcode: this.wxQrcode, platform: 'pc' }).then((res) => {
            //   console.log('this.wxQrcode   res', res)
            // })
            setTimeout(() => {
              this.$store.dispatch('user/scanCodeLogin', { qrcode: this.wxQrcode, platform: 'pc' }).catch((err) => {
                console.log('err', err)
                this.loseEfficacyDate = ''
              })
            }, 1000)
          } else {
            if (res.data.status === 2) {
              this.wxAuthorization = true
            }
            setTimeout(() => {
              this.getWxQrcodeStatus()
            }, 2000)
          }
        }
      }
    },
    async handleLogin() {
      if (!this.loginForm.phone) {
        return this.$message.warning('请输入账号')
      }
      if (!this.loginForm.password) {
        return this.$message.warning('请输入密码')
      }
      const password = CryptoJS.encrypt(this.loginForm.password)
      this.loading = true
      const res = await this.$store
        .dispatch('user/LOGIN', {
          phone: this.loginForm.phone,
          password,
          hospital_code: 'weiruan'
        })
        .finally(() => {
          this.loading = false
        })

      // 开启短信验证
      if (res.msg === 'user.sms.need.login') {
        this.$refs.smsVerification.dialogVisible = true
        this.$refs.smsVerification.smsCode = ''
        return
      }

      if (res.data && res.data.length > 1) {
        this.orgList = res.data
        this.visible = true
      } else {
        const org = res.data ? res.data[0] : null
        this.handleSelectOrg(org)
      }
    },

    // 处理选择组织机构
    handleSelectOrg(org = {}) {
      this.visible = false
      this.$store.commit('user/SET_TOKEN', org.access_token)
      this.$store.commit('user/SET_USERID', org.userid)
      localCache.setCache('tenantId', org.tenantId)
      setToken(org.access_token)
      setUserId(org.userid)
      this.$router.push({ name: 'Home' })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },

    // 短信验证
    async handleSmsConfirm(smsCode) {
      if (!smsCode) {
        return this.$message.warning('请输入验证码')
      }
      this.$refs.smsVerification.smsLoading = true
      const res = await this.$store.dispatch('user/SMS_LOGIN', { phone: this.loginForm.phone, smsCode }).finally(() => {
        this.$refs.smsVerification.smsLoading = false
      })
      if (res.data && res.data.length > 1) {
        this.orgList = res.data
        this.visible = true
      } else {
        const org = res.data ? res.data[0] : null
        this.handleSelectOrg(org)
      }
    }
  }
}
</script>

<style lang="scss">
$bg: #283443;
$light_gray: #333;
$cursor: #333;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */

.el-tooltip__popper.is-light {
  padding: 2px !important;
  border: 1px solid #ddd;
}

.el-tooltip__popper.is-light[x-placement^='bottom'] .popper__arrow {
  left: 13px !important;
}

.el-tooltip__popper.is-light[x-placement^='bottom'] .popper__arrow {
  border-bottom-color: #ddd !important;
}
</style>

<style lang="scss" scoped>
.public_copyright {
  position: absolute;
  bottom: 1.5rem;
}
$bg: #98dbff;
$dark_gray: #889aa4;
$light_gray: #333;

.logoBox {
  text-align: center;
  width: 100%;
  display: flex;

  img {
    width: 5.416632vw;
    height: 4.6335vw;
    height: auto;
    margin: 0 auto 0.52083vw;
  }
}
.login-mobile,
.login-code {
  height: 2.8rem;
  margin-bottom: 1rem;
}

.input-code-text {
  color: #0a86c8;
  cursor: pointer;
}

::v-deep .el-tabs__active-bar {
  background-color: transparent !important;
  background-image: linear-gradient(
    90deg,
    transparent 0,
    transparent 27%,
    #0a86c8 0,
    #0a86c8 73%,
    transparent 0,
    transparent
  );
}

/* 兼容主题：保留选择器，最小声明避免空规则 */
::v-deep .el-tabs__header {
  border-bottom: none;
}

::v-deep .tabs-label {
  font-size: 0.8rem !important;
}

::v-deep .el-tabs__active-bar {
  margin: 0 auto;
  width: 100px;
}

::v-deep .el-input__inner:focus {
  outline: none;
  border-color: #dcdfe6;
}

::v-deep .login-mobile .el-input-group__prepend,
::v-deep .login-code .el-input-group__prepend {
  background: unset;
}

::v-deep .login-mobile .el-input-group__append,
::v-deep .login-code .el-input-group__append {
  background: unset;
}

::v-deep .login-mobile .el-input,
::v-deep .login-code .el-input {
  height: 2.8rem;
}

::v-deep .login-mobile .el-input__inner,
::v-deep .login-code .el-input__inner {
  border-left: unset;
  height: 2.8rem !important;
}

::v-deep .login-code .el-input__inner {
  border-right: unset;
}

.login-container {
  min-height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  background-position: left 50%;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-color: #fff;

  @media screen and (max-width: 959px) {
    .left {
      display: none;
    }
  }

  .errorMessageBox_label {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 1rem;
    color: #0b86c7;
  }

  /* 组织机构列表样式 */
  .org-list {
    max-height: 400px;
    overflow-y: auto;
    padding: 12px;
    background: transparent;
  }

  .org-item {
    background: #ffffff;
    border-radius: 12px;
    margin-bottom: 30px;
    padding: 16px;
    box-shadow: 0px 0px 4px 4px rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.2s ease;
  }

  //选中最后一个
  .org-item:last-child {
    margin-bottom: 0;
  }

  .org-item:hover {
    background: #5cb3f5;
    color: #fff;
    .departIcon {
      filter: contrast(0) brightness(2);
    }
  }

  .org-item-row {
    display: flex;
    align-items: center;
    font-size: 16px;
    margin-bottom: 8px;
    .value {
      color: #333;
      font-weight: 600;
    }
    .name {
      color: #333;
    }
  }

  .org-item-row:last-child {
    margin-bottom: 0;
  }

  .org-item-row.is-top {
    padding-bottom: 12px;
    border-bottom: 1px dashed #e0e0e0;
    margin-bottom: 12px;
  }

  .org-item:hover .org-item-row.is-top {
    border-bottom-color: rgba(255, 255, 255, 0.3);
  }

  .org-item-row.two-cols {
    justify-content: space-between;
  }

  .org-item-row.two-cols .pair {
    display: flex;
    align-items: center;
    flex: 1;
  }

  .org-item-row.two-cols .pair:last-child {
    justify-content: flex-end;
  }

  .label-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  /* 图标占位类，后续添加背景图片 */
  .label-icon-org {
    background-image: none;
  }

  .label-icon-user {
    background-image: none;
  }

  .label-icon-id {
    background-image: none;
  }

  .label-text {
    color: #666666;
    margin-right: 4px;
  }

  .org-item:hover .label-text {
    color: #fff;
  }

  .org-item:hover .value,
  .org-item:hover .name,
  .org-item:hover .userid {
    color: #fff;
  }

  .logo-title {
    position: absolute;
    top: 20px;
    left: 10%;
    display: flex;
    align-items: center;
    font-family: 'Alibaba PuHuiTi 2.0';
    font-style: normal;
    font-weight: 500;
    font-size: 0.7rem;

    color: #3a3e40;

    img {
      width: 32px;
      height: auto;
      margin-right: 10px;
    }
  }

  .top {
    position: absolute;
    top: 1.1rem;
    right: 0;
    padding: 0 3.906225vw;
    color: #222222;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    .login_qrcodeIcon {
      width: 0.7rem;
      height: 0.7rem;
      margin-right: 0.3rem;
    }

    .login_telIcon {
      width: 0.65rem;
      height: 0.6rem;
      margin-right: 0.3rem;
    }

    span {
      margin-right: 33px;
      font-size: #222222 !important;
    }

    .qrcode {
      display: flex;
      align-items: center;
    }
  }

  .login-box {
    @media screen and (min-width: 1501px) {
      width: 50%;
      height: 100%;
      position: absolute;
      top: 0;
      right: 0;

      .right {
        width: 26.0415vw;
        background-color: rgba(255, 255, 255, 0.9);
        padding: 1.5rem;
        border-radius: 0.5rem;
      }
    }

    @media screen and (max-width: 1500px) and (min-width: 960px) {
      width: 46.093455vw;
      height: 100%;
      position: absolute;
      top: 0;
      right: 0;

      .right {
        width: 30vw;
        background-color: rgba(255, 255, 255, 0.9);
        padding: 1.5rem;
        border-radius: 0.5rem;
      }
    }

    @media screen and (max-width: 959px) {
      width: 100%;
      height: 100vh;
      border-radius: 0;

      .right {
        width: 100%;
        max-width: 400px;
      }
    }

    background-color: transparent;
    display: flex;
    justify-content: center;
    align-items: center;

    .formItemLabel {
      font-size: 0.833328vw;
      line-height: 1.249992vw;
      color: #666666;
      margin-bottom: 0.416664vw;
    }
  }

  .titleContainer {
    margin-bottom: 0.52083vw;

    .titleBox {
      margin: 0;
      margin-bottom: 2rem;
      font-weight: 500;
      height: 1.666656vw;
      font-size: 1.2rem;
      text-align: center;
      font-family: PingFangSC-Medium, PingFang SC;
      color: #222222;
    }

    .subTitle {
      margin: 0;
      height: 1.145826vw;
      font-size: 0.833328vw;
      text-align: center;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.145826vw;
    }
  }

  .errorMessageBox {
    height: 2.60415vw;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;
    line-height: 2.60415vw;
    margin-bottom: 0.52083vw;
    background: #fff4f4;
    border-radius: 0.52083vw;
    font-size: 0.624996vw;
    color: #c55;
    display: none;
  }

  .eFormItemCustom {
    margin-bottom: 1.5vw;

    &.eFormItemCustomLast {
      margin-bottom: 1.25vw;
    }

    .loginInputSelf {
      height: 2.5vw;
      line-height: 2.5vw;
      width: 100%;

      ::v-deep .el-input__inner {
        height: 2.5vw;
        border-radius: 0.52083vw;
        padding: 0 2.4vw;
        border: 0.052083vw solid #dedede;
        font-size: 0.833328vw !important;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;

        &::-webkit-input-placeholder {
          color: #999999;
        }
      }
    }
  }

  .rememberRadioBox {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 3vw;

    .rememberText {
      margin-right: 0.5208333vw;
      height: 1.1458vw;
      font-size: 0.72916vw;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
      line-height: 1.1458vw;
    }

    ::v-deep .el-switch {
      width: 2.08333vw;
      font-size: 0.72916662vw !important;
      line-height: 1.0416666vw !important;
      height: 1.0416666vw !important;
    }

    ::v-deep .el-switch__core {
      width: 2.0833vw !important;
      height: 1.041666vw !important;
      outline: none !important;
      border-radius: 0.52083vw !important;
      border-width: 0.15624999vw !important;

      &::after {
        width: 0.67708vw !important;
        height: 0.67708vw !important;
        top: 50%;
        transform: translateY(-50%);
      }
    }

    ::v-deep .el-switch.is-checked .el-switch__core::after {
      left: 100% !important;
      margin-left: -0.72916vw !important;
    }
  }

  ::v-deep .handleLoginButton {
    height: 3vw !important;
    text-align: center;
    background: #0a86c8;
    border-radius: 10px;
    border-radius: 0.5208333vw;
    width: 100%;
    border: 0;
    font-size: 0.8333vw !important;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 1rem;

    span {
      justify-content: center !important;
      display: unset !important;
    }
  }

  .show-pwd {
    position: absolute;
    z-index: 10;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    svg {
      cursor: pointer;
      width: 0.833328vw;
      height: 0.9166608vw;
      color: #999999;
    }
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }
}

.tabs-label {
  font-weight: 500;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;

  ::v-deep svg {
    width: 24px;
    height: 24px;
    margin-right: 10px;
  }
}

.wx-authorization {
  width: 100%;
  text-align: center;

  img {
    display: block;
    margin: 2.604166vw auto 0;
    width: 9.114583vw;
    height: 11.979166vw;
  }

  span {
    display: block;
    margin: 3.125vw auto 0.78125vw;
    font-weight: 400;
    font-size: 1.041666vw;
    color: #222222;
    line-height: 1.4583333vw;
  }

  ::v-deep .el-button {
    font-size: 0.9375vw;
  }
}

.scanCode {
  position: relative;
  width: 9.7rem;
  height: 9.7rem;
  margin: 0px auto;
  overflow: hidden;

  .code.lose-efficacy {
    opacity: 0.1;
  }

  ::v-deep .code {
    img {
      width: 9.7rem;
      height: 9.7rem;
      border-radius: 0.3rem;
    }
  }

  .lose-efficacy {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    bottom: 0;
    text-align: center;

    i {
      margin-top: 2.7604166vw;
      font-size: #666;
    }

    span {
      display: block;
      font-weight: 400;

      color: #222222;
      line-height: 1.45833vw;
      margin: 0.78125vw 0 1.041666vw;
    }
  }
}

.scanCode-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 12.55rem;
  //height: 1rem;
  // background: linear-gradient(120deg, #62debb 0%, #2cc0e3 100%);
  border-radius: 1.15rem;
  font-weight: 500;
  color: #222;
  margin: 1rem auto;

  img {
    width: 1.25vw;
    height: 1.25vw;
    margin-right: 0.520833vw;
  }
}

::v-deep .el-tabs__nav-wrap::after {
  background-color: transparent;
}

::v-deep .el-tabs__content {
  @media screen and (min-width: 1501px) {
    height: 17vw;
  }
  @media screen and (max-width: 959px) {
    height: 24vw;
  }
}

.lgoin_reloadBtn {
  cursor: pointer;

  .lgoin_load {
    width: 0.9rem;
    height: 0.9rem;
    margin-right: 0.5rem;
  }
}
</style>
<style lang="scss">
/* chrome */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

/* 火狐浏览器 */
input[type='number'] {
  appearance: textfield;
}

.codePop {
  position: absolute;
  left: 0;
  top: 0;
  width: 200%;
  height: 100%;

  img {
    width: 100%;
    height: 100%;
    transition: transform 0.2s linear;
    transform: translateX(0);
  }

  &.codePop_1 {
    img {
      transform: translateX(-50%);
    }
  }
}

.right {
  .svg-icon {
    margin-right: 0 !important;
  }
}
</style>
