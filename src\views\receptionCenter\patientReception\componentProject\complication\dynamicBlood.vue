<!-- 动态血压心电图 -->
<template>
  <div class="dynamic-blood">
    <el-form ref="formRef" :model="dynamicBloodForm" :rules="rules" label-width="180px">
      <el-row>
        <flag-component title="心率" />
        <el-col v-for="item in dynamicBlood.heartRate" :key="item.label" :span="8">
          <el-form-item :label="item.label">
            <custom-input-number v-model="dynamicBloodForm[item.prop]">
              <template v-if="item.append" #append>
                {{ item.append }}
              </template>
            </custom-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <flag-component title="室性" />
        <el-col v-for="item in dynamicBlood.ventricular" :key="item.label" :span="8">
          <el-form-item :label="item.label">
            <custom-input-number v-model="dynamicBloodForm[item.prop]">
              <template v-if="item.append" #append>
                {{ item.append }}
              </template>
            </custom-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <flag-component title="房性" />
        <el-col v-for="item in dynamicBlood.atrial" :key="item.label" :span="8">
          <el-form-item :label="item.label">
            <custom-input-number v-model="dynamicBloodForm[item.prop]">
              <template v-if="item.append" #append>
                {{ item.append }}
              </template>
            </custom-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <flag-component title="交界性" />
        <el-col v-for="item in dynamicBlood.beat" :key="item.label" :span="8">
          <el-form-item :label="item.label">
            <custom-input-number v-model="dynamicBloodForm[item.prop]">
              <template v-if="item.append" #append>
                {{ item.append }}
              </template>
            </custom-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <flag-component title="HRV" />
        <el-col v-for="item in dynamicBlood.hrv" :key="item.label" :span="8">
          <el-form-item :label="item.label">
            <custom-input-number v-model="dynamicBloodForm[item.prop]">
              <template v-if="item.append" #append>
                {{ item.append }}
              </template>
            </custom-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <flag-component title="ST段分析" />
        <el-col v-for="item in dynamicBlood.stSegment" :key="item.label" :span="8">
          <el-form-item :label="item.label">
            <custom-input-number v-model="dynamicBloodForm[item.prop]">
              <template v-if="item.append" #append>
                {{ item.append }}
              </template>
            </custom-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <flag-component title="房颤/房扑" />
        <el-col v-for="item in dynamicBlood.pause" :key="item.label" :span="8">
          <el-form-item :label="item.label">
            <custom-input-number v-model="dynamicBloodForm[item.prop]">
              <template v-if="item.append" #append>
                {{ item.append }}
              </template>
            </custom-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <flag-component title="起搏" />
        <el-col v-for="item in dynamicBlood.sympathetic" :key="item.label" :span="8">
          <el-form-item :label="item.label">
            <custom-input-number v-model="dynamicBloodForm[item.prop]">
              <template v-if="item.append" #append>
                {{ item.append }}
              </template>
            </custom-input-number>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="检查结论">
        <el-input v-model="dynamicBloodForm.dynamicsEcgDescription" type="textarea" :rows="3" />
      </el-form-item>

      <el-form-item label="医生建议">
        <el-input v-model="dynamicBloodForm.dynamicsEcgSuggest" type="textarea" :rows="3" />
      </el-form-item>

      <CheckboxGroupField v-model="dynamicBloodForm.dynamicsEcgResult" :item="checkboxItem" />

      <el-form-item label="上传报告图片">
        <custom-upload v-model="dynamicBloodForm.attachmentPhotoUrl" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { dynamicBlood } from '../../component/complicationsScreening'
import { ecgOptions } from '@/views/receptionCenter/patientReception/component/complicationsScreening.js'
import FlagComponent from '@/components/flagComponent/index.vue'
// import ChronicDiseasesHype from '../../componentDetail/chronicDiseasesHype.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'
import CustomUpload from '@/components/customUpload/index.vue'

export default {
  name: 'DynamicBlood',
  components: {
    FlagComponent,
    CheckboxGroupField,
    CustomUpload
    // ChronicDiseasesHype
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const dynamicBloodForm = {
      dynamicsEcgDescription: '',
      dynamicsEcgSuggest: '',
      dynamicsEcgResult: [],
      attachmentPhotoUrl: ''
    }
    Object.entries(dynamicBlood).map(([key, value]) => {
      return Object.values(value).forEach((item) => {
        dynamicBloodForm[item.prop] = ''
      })
    })
    return {
      dynamicBlood,
      dynamicBloodForm,
      checkboxItem: {
        label: '检查结果：',
        type: 'checkbox',
        required: true,
        mutuallyExclusive: true, // 互斥
        prop: 'dynamicsEcgResult',
        options: [...ecgOptions]
      },
      rules: {
        dynamicsEcgResult: [{ required: true, message: '请选择检查结果', trigger: 'change' }]
      }
    }
  },
  methods: {
    initData(data) {
      Object.keys(this.dynamicBloodForm).forEach((key) => {
        if (key === 'dynamicsEcgResult') {
          this.dynamicBloodForm[key] = data[key] ? data[key].split(',') : []
        } else {
          this.dynamicBloodForm[key] = data[key]
        }
      })
      this.dynamicBloodForm.id = data.id
    },
    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          ...this.dynamicBloodForm,
          dynamicsEcgResult: this.dynamicBloodForm.dynamicsEcgResult.join(','),
          attachmentPhotoUrl: this.dynamicBloodForm.attachmentPhotoUrl,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.dynamicBloodForm.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    }
  }
}
</script>
