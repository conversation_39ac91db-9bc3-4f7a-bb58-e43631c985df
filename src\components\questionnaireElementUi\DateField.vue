<template>
  <el-form-item :label="item.label" :prop="item.prop">
    <div :class="item.class">
      <el-date-picker
        v-model="localValue"
        type="date"
        format="yyyy-MM-dd"
        value-format="yyyy-MM-dd"
        placeholder="请选择日期"
        :disabled="item.disabled"
      />
    </div>
  </el-form-item>
</template>

<script>
export default {
  name: 'DateField',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: ['item', 'value'],
  computed: {
    localValue: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  }
}
</script>
