<!-- 出血 -->
<template>
  <div class="bleeding">
    <el-table :data="tableData" border stripe>
      <el-table-column prop="clinicalFeature" label="临床特点" width="200" align="center" />
      <el-table-column prop="description" label="说明" min-width="400" align="center" />
      <el-table-column label="分值" width="200" align="left">
        <template slot-scope="scope">
          <el-radio-group v-model="scope.row.score">
            <el-radio v-for="option in scope.row.scoreOptions" :key="option.value" :label="option.value">
              {{ option.label }}
            </el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
    </el-table>
    <div class="total-score">
      <span>总分：{{ totalScore }}</span>
    </div>
    <div class="note">
      <p>
        注：INR为国际标准化比值，AST为谷草转氨酶，ALT为谷丙转氨酶，ALP为碱性磷酸酶，
        大出血为任何需要医院治疗的或需输血的出血(除外出血性卒中)。
        各项诊断标准卡在HAS-BLED评分量表中提及及多以男性和酒精蛋白 < 130g/L,女性 <
        120g/L作为判断标准。严重出血小板减少在HAS-BLED评分量中研究提及及。血小板计数 < 50x10¹⁰/L是抗凝禁忌 <
        100X10¹⁰/L需要多学科评估；1mmHg=0.133 kPa
      </p>
    </div>
  </div>
</template>

<script>
import { mapDataToTable } from '@/utils/cspUtils'
import { cloneDeep } from 'lodash'

export default {
  name: 'Bleeding',
  data() {
    return {
      tableData: [
        {
          clinicalFeature: '未控制的高血压(H)',
          description: '定义为收缩压>160mmHg',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 }
          ],
          cxGxyRisk: 'score'
        },
        {
          clinicalFeature: '肝肾功能异常(各1分)(A)',
          description:
            '肝功能异常定义为肝硬化或者肝酶1-2倍正常上限,AST/ALT/ALP>3倍正常上限。肾功能异常定义为透析或肾移植或血清肌酐>200wmol/L',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 },
            { label: '2', value: 2 }
          ],
          cxLiverRisk: 'score'
        },
        {
          clinicalFeature: '卒中(S)',
          description: '包括缺血性卒中和出血性卒中',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 }
          ],
          cxNzzRisk: 'score'
        },
        {
          clinicalFeature: '出血(B)',
          description: '出血史或出血倾向(既往大出血"窦血"或严重小板减少)',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 }
          ],
          cxRisk: 'score'
        },
        {
          clinicalFeature: 'INR值易波动(L)',
          description: 'INR不稳定/过高,或在治疗窗内的时间<60%',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 }
          ],
          cxInrRisk: 'score'
        },
        {
          clinicalFeature: '老年(E)',
          description: '年龄>65岁',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 }
          ],
          cxOldRisk: 'score'
        },
        {
          clinicalFeature: '药物或过量饮酒(各1分)(D)',
          description: '药物指合并应用抗血小板药物或非甾体类抗炎药,过量饮酒是指乙醇摄入量>112g/周',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 },
            { label: '2', value: 2 }
          ],
          cxMedicalRisk: 'score'
        }
      ]
    }
  },
  computed: {
    totalScore() {
      return this.tableData.reduce((sum, item) => sum + (typeof item.score === 'number' ? item.score : 0), 0)
    }
  },
  methods: {
    initData(data) {
      this.tableData = mapDataToTable(data, cloneDeep(this.tableData))
      this.tableData.forEach((row) => {
        row.score = row.score || 0
      })
    },
    async handleSave() {
      const resultData = this.extractTableData(this.tableData)
      const result = {
        name: '出血风险评估',
        success: true,
        data: {
          ...resultData,
          cxScore: this.totalScore
        }
      }
      return result
    },
    extractTableData(tableData) {
      const result = {}

      tableData.forEach((row) => {
        for (const key in row) {
          if (row[key] === 'score') {
            result[key] = row[row[key]]
          }
        }
      })

      return result
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.bleeding {
  padding: 20px;

  .total-score {
    margin-top: 20px;
    font-size: 16px;
    font-weight: bold;
    text-align: right;
  }

  .note {
    margin-top: 15px;
    font-size: 16px;
    line-height: 1.5;
    color: red;
  }
}
</style>
