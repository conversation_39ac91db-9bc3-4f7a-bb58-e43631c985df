// 3.0 相关css
// 导入Element UI样式
import '@/assets/font/iconfont.css'
import '@/assets/css/color.css'
import '@/assets/css/style.css'
import '@/assets/css/pageUserZX.css'
import '@/assets/css/pageUserFBY.css'
import '@/assets/css/styleFBY.css'
import '@/assets/css/styleXZS.css'
import '@/assets/css/colorFBY.css'
import '@/assets/css/familyDoctor.scss'
import '@/assets/text/text.css'

// 全局样式
import '@/styles/public.scss'
import '@/styles/index.scss'
import '@/styles/zx.scss'
import '@/styles/publicX.scss'
import '@/styles/1440max.scss'
// import 'element-ui/lib/theme-chalk/index.css'

// 样式导入函数（为了保持与其他插件文件格式一致）
const importStyles = () => {
  // 样式已通过导入语句自动加载，此处无需额外操作
}

export default importStyles
