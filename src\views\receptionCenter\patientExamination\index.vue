<template>
  <div class="patient-examination">
    <el-card>
      <patient-info
        :patient-info="$store.state.patientExamination.patientExaminationData"
        :reg-id="$route.query.id"
        :from-type="'hide'"
      />
    </el-card>

    <el-card v-loading="$store.state.patientExamination.loading">
      <div class="examination-list">
        <el-tabs
          v-model="activeTab"
          type="card"
          style="padding: 16px"
          addable
          :closable="showTabList.length > 1"
          @edit="handleTabsEdit"
        >
          <el-tab-pane v-for="item in showTabList" :key="item.value" :label="item.label" :name="item.value">
            <div class="examination-content">
              <component
                :is="item.component"
                v-if="item.component"
                :ref="`${item.component}Ref`"
                :item-temp="item"
                :active-tab="activeTab"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <ProDialog ref="proDialog" title="添加项目" :visible.sync="dialogVisible" width="500px" top="30vh">
        <div class="pro-dialog-content">
          <el-checkbox-group v-model="checkList">
            <el-col v-for="item in tabList" :key="item.value" :span="12" style="margin-bottom: 8px">
              <el-checkbox :label="item.value" :disabled="item.status === 2">
                {{ item.label }}
              </el-checkbox>
            </el-col>
          </el-checkbox-group>
        </div>
        <template #footer>
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
        </template>
      </ProDialog>
    </el-card>

    <div
      v-if="$store.state.patientExamination.patientExaminationData.status === 1 && !historyId"
      style="
        position: fixed;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        z-index: 888;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        background-color: #fff;
        padding: 10px;
        border-radius: 4px;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
      "
    >
      <el-button
        type="success"
        style="background-color: #0bae01; border-color: #0bae01"
        :loading="$store.state.patientExamination.saveBtnLoading"
        @click="handleSave"
      >
        保存
      </el-button>
      <el-button type="primary" @click="handlePrint">打印引导单</el-button>
    </div>
  </div>
</template>

<script>
import PatientInfo from '@/components/patientInfo/index.vue'
import { getProjectData, getAddProjectList, projectAdd, projectRemove, saveInspectionProject } from '@/api/examination'
import { tabList } from '@/views/receptionCenter/patientReception/component/complicationsScreening.js'
import ProDialog from '@/components/ProDialog/index.vue'
import FingertipBloodGlucose from '@/views/receptionCenter/patientReception/componentProject/complication/fingertipBloodGlucose.vue'
import PostprandialTwoSugar from '@/views/receptionCenter/patientReception/componentProject/complication/postprandialTwoSugar.vue'
import FastingVenousBlood from '@/views/receptionCenter/patientReception/componentProject/complication/fastingVenousBlood.vue'
import GlycosylatedHemoglobin from '@/views/receptionCenter/patientReception/componentProject/complication/glycosylatedHemoglobin.vue'
import OGTT from '@/views/receptionCenter/patientReception/componentProject/complication/OGTT.vue'
import Biochemistry from '@/views/receptionCenter/patientReception/componentProject/complication/biochemistry.vue'
import CarotidUltrasound from '@/views/receptionCenter/patientReception/componentProject/complication/carotidUltrasound.vue'
import Urinalysis from '@/views/receptionCenter/patientReception/componentProject/complication/urinalysis.vue'
import Emg from '@/views/receptionCenter/patientReception/componentProject/complication/emg.vue'
import FundusExamination from '@/views/receptionCenter/patientReception/componentProject/complication/fundusExamination.vue'
import ACR from '@/views/receptionCenter/patientReception/componentProject/complication/ACR.vue'
import VibrationThresholdCheck from '@/views/receptionCenter/patientReception/componentProject/complication/vibrationThresholdCheck.vue'
import BloodPressure from '@/views/receptionCenter/patientReception/componentProject/complication/bloodPressure.vue'
import ECG from '@/views/receptionCenter/patientReception/componentProject/complication/ECG.vue'
import Arteriosclerosis from '@/views/receptionCenter/patientReception/componentProject/complication/arteriosclerosis.vue'
import DynamicBlood from '@/views/receptionCenter/patientReception/componentProject/complication/dynamicBlood.vue'
import Echocardiography from '@/views/receptionCenter/patientReception/componentProject/complication/echocardiography.vue'
import BloodRoutine from '@/views/receptionCenter/patientReception/componentProject/complication/bloodRoutine.vue'
import Echocardiogram from '@/views/receptionCenter/patientReception/componentProject/complication/echocardiogram.vue'
import BNP from '@/views/receptionCenter/patientReception/componentProject/complication/BNP.vue'
import PeripheralArtery from '@/views/receptionCenter/patientReception/componentProject/complication/peripheralArtery.vue'

export default {
  name: 'PatientExamination',
  components: {
    PatientInfo,
    ProDialog,
    FingertipBloodGlucose, // 指尖血糖
    PostprandialTwoSugar, // 餐后2h尿糖
    FastingVenousBlood, // 空腹静脉血糖
    GlycosylatedHemoglobin, // 糖化血红蛋白
    OGTT, // OGTT
    Biochemistry, // 生化
    CarotidUltrasound, // 颈动脉超声检查
    Emg, // 肌电图检查
    FundusExamination, // 眼底检查
    ACR, // ACR检查
    Urinalysis, // 尿常规
    VibrationThresholdCheck, // 震动阈值检查
    BloodPressure, // 24小时动态血压
    ECG, // 心电图检查
    Arteriosclerosis, // 动脉硬化
    DynamicBlood, // 动态血压心电图
    Echocardiography, // 心脏彩超
    BloodRoutine, // 血常规
    Echocardiogram, // 超声心动图
    BNP, // BNP
    PeripheralArtery // 外周动脉
  },
  props: {
    historyId: {
      // 远程会诊需要在Modal中打开
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tabList,
      checkList: [],
      showTabList: [],
      activeTab: '',
      dialogVisible: false
    }
  },
  created() {
    this.$store.dispatch('patientExamination/getPatientExaminationData', {
      id: this.historyId || this.$route.query.id
    })
    this.getAddProjectListFn()
  },
  methods: {
    // 获取检查所有检查项目
    async getAddProjectListFn() {
      this.$store.commit('patientExamination/SET_LOADING', true)
      const res = await getAddProjectList({
        id: this.historyId || this.$route.query.id
      })
      if (res.code !== 200) return

      const data = res.data || []
      const updatedTabList = this.tabList.map((tab) => {
        const matchedItem = data.find((d) => d.code === tab.value)
        return {
          ...tab,
          status: matchedItem ? matchedItem.status : tab.status
        }
      })

      this.tabList = updatedTabList
      this.checkList = updatedTabList.filter((item) => item.status === 2).map((item) => item.value)
      this.getProjectDataFn()
    },

    // 获取检查项目数据
    async getProjectDataFn() {
      try {
        const res = await getProjectData({
          id: this.historyId || this.$route.query.id
        })
        if (res.code !== 200) return

        setTimeout(() => {
          this.projectItemsInit(res.data)
        }, 1000)

        this.showTabList = (res.data.itemList || []).map((item) => {
          const tab = this.tabList.find((it) => it.value === item.itemCode)
          return {
            ...tab,
            id: item.id
          }
        })
        this.activeTab = this.showTabList.length > 0 ? this.showTabList[0].value : ''
        this.dialogVisible = !(this.showTabList.length > 0)
      } catch (error) {
        this.$message.error('获取检查项目数据失败')
      }
    },

    // 项目初始化赋值
    projectItemsInit(response) {
      try {
        const tabLists = [...this.showTabList]
        const returnData = [...(response.itemList || [])]
        tabLists.forEach((item) => {
          const { data } = returnData.find((it) => it.itemCode === item.value)
          if (data) {
            this.$refs[`${item.component}Ref`][0].initData(data)
          } else {
            this.$refs[`${item.component}Ref`][0].clearValidate && this.$refs[`${item.component}Ref`][0].clearValidate()
          }
        })
      } catch (error) {
        console.log('error', error)
      } finally {
        this.$store.commit('patientExamination/SET_LOADING', false)
      }
    },

    async handleTabsEdit(value, action) {
      const projectItem = this.tabList.find((it) => it.value === value)
      if (action === 'remove') {
        try {
          await this.$confirm(`确定移除 ${projectItem.label} 项目吗？`, '操作确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'error',
            iconClass: 'el-icon-delete-solid',
            customClass: 'remove-confirm',
            closeOnClickModal: false,
            confirmButtonClass: 'bgred'
          })
          const res = await projectRemove({
            eiId: this.$route.query.id,
            itemCodeList: [value]
          })
          if (res.code === 200) {
            this.$message.success('移除成功')
            this.getAddProjectListFn()
          }
        } catch (error) {
          this.$message.info('已取消移除')
        }
      }

      if (action === 'add') {
        this.dialogVisible = true
      }
    },

    // 确定添加项目
    handleConfirm() {
      projectAdd({
        eiId: this.$route.query.id,
        itemCodeList: this.checkList
      }).then((res) => {
        if (res.code === 200) {
          this.$message.success('添加成功')
          this.getAddProjectListFn()
          this.dialogVisible = false
        }
      })
    },

    async handleSave() {
      try {
        this.$store.commit('patientExamination/SET_SAVE_BTN_LOADING', true)
        const tabLists = [...this.showTabList]

        const results = await Promise.all(tabLists.map((item) => this.$refs[`${item.component}Ref`][0].handleSave()))

        // 收集未通过项的提示
        const warnings = results.filter((item) => !item.success).map((item) => `${item.name}存在必填未填项！`)

        // 弹出警告信息
        for (const warning of warnings) {
          this.$message.warning(warning)

          // eslint-disable-next-line no-await-in-loop
          await new Promise((resolve) => setTimeout(resolve, 500))
        }

        const status = warnings.length > 0 ? 1 : 5

        const res = await saveInspectionProject({
          id: this.$route.query.id,
          status,
          itemDetailList: results.map((item) => item.data)
        })

        if (res.code === 200) {
          this.$message.success('保存成功')
          this.getAddProjectListFn()
        }
      } catch (error) {
        console.log('error', error)
      } finally {
        this.$store.commit('patientExamination/SET_SAVE_BTN_LOADING', false)
      }
    },

    handlePrint() {
      this.$router.push({
        path: '/receptionCenter/printGuidanceSheet',
        query: {
          id: this.$route.query.id,
          menu: 'patientExamination',
          archiveId: this.$store.state.patientExamination.patientExaminationData.archiveId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.patient-examination {
  ::v-deep .el-tabs__new-tab {
    width: 80px;
    height: 30px;
    line-height: 30px;
  }
  ::v-deep .el-tabs__new-tab:after {
    content: '添加项目';
  }
  ::v-deep .el-tabs__new-tab:hover {
    color: #fff;
  }
  .el-card {
    margin: 16px;
  }
  .examination-content {
    padding: 16px;
    border: 1px solid #e6ebf5;
  }
  ::v-deep .el-tabs__new-tab {
    background-color: #41a1d4;
  }
}
</style>
