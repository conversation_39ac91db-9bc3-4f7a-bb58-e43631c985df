<template>
  <div class="service-pack-container">
    <flag-component title="服务包" style="margin-bottom: 10px" />
    <div>
      <el-button type="text" @click="handleAdd"> <i class="vam iconfont icon-add">&nbsp;新增</i></el-button>
      <el-button type="text" @click="handleEdit">
        <i class="vam iconfont icon-edit">&nbsp;修改</i>
      </el-button>
      <el-button type="text" @click="handleDelete">
        <i class="vam iconfont icon-delete" style="color: red">&nbsp;删除</i>
      </el-button>
    </div>
    <div
      v-for="(item, index) in servicePackList"
      :key="index"
      class="service-pack-item"
      :class="{ 'gradient-bg': checkId === item.id }"
      @click="handleCheckId(item)"
    >
      <div class="service-pack-content">
        <div class="service-pack-info">
          <div class="service-pack-name">
            <span>名称：</span>
            <span>{{ item.spName }}</span>
          </div>
          <div class="service-pack-validity">
            <span>有效期：</span>
            <span>{{ item.validStartDate }} - {{ item.validEndDate }}</span>
          </div>
          <div class="service-pack-price">
            <span>价格：</span>
            <span>{{ item.spAmt }} 元</span>
          </div>
        </div>
        <div class="service-pack-actions">
          <el-button type="text" class="check-btn" @click="handleDetail(item)"> 查看 </el-button>
        </div>
      </div>
      <div class="service-pack-switch">
        <el-switch
          v-model="item.enableFlag"
          active-color="#13ce66"
          inactive-color="#ff4949"
          @change="handleSwitchChange(item)"
        />
      </div>
    </div>

    <ProDialog :visible.sync="dialogVisible" :title="title" width="800px">
      <el-form :model="form" label-width="120px" :rules="rules">
        <el-row>
          <el-col :span="24">
            <el-form-item label="服务包名称：" prop="spName">
              <el-input v-model="form.spName" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="服务包有效期：" prop="validStartDate">
              <el-date-picker
                v-model="form.validDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="服务包价格：" prop="spAmt">
              <el-input-number v-model="form.spAmt" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item label="服务包描述：" prop="spDesc">
              <el-input v-model="form.spDesc" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template v-if="title !== '查看服务包'" #footer>
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </ProDialog>
  </div>
</template>

<script>
import {
  getServicePackageList,
  saveServicePackage,
  deleteServicePackage,
  enableServicePackage
} from '@/api/servicePackageManage'
import { localCache } from '@/utils/cache'
import FlagComponent from '@/components/flagComponent/index.vue'
import ProDialog from '@/components/ProDialog/index.vue'

export default {
  name: 'ServicePack',
  components: {
    FlagComponent,
    ProDialog
  },
  data() {
    return {
      servicePackList: [],
      dialogVisible: false,
      title: '',
      form: {
        spName: '',
        validDateRange: [],
        spAmt: undefined,
        spDesc: ''
      },
      checkId: '',
      rules: {
        spName: [{ required: true, message: '请输入服务包名称', trigger: 'blur' }],
        validDateRange: [{ required: true, message: '请选择服务包有效期', trigger: 'change' }],
        spAmt: [{ required: true, message: '请输入服务包价格', trigger: 'blur' }]
      }
    }
  },
  methods: {
    // 点击服务包
    handleCheckId(item) {
      if (this.checkId === item.id) {
        this.checkId = ''
        this.$emit('checkIdChange', '')
        return
      }
      this.checkId = item.id
      this.$emit('checkIdChange', item.id)
    },

    // 获取服务包列表
    getServicePackListFn() {
      const departCode = localCache.getCache('userInfo').departCode || ''
      const params = {
        departCode,
        pageNo: 1,
        pageSize: 1000
      }
      getServicePackageList(params).then((res) => {
        if (res.code === 200) {
          this.servicePackList = res.data.list.map((item) => ({
            ...item,
            enableFlag: item.enableFlag !== 0
          }))
          this.$emit('getServicePackList', this.servicePackList)
        }
      })
    },

    handleAdd() {
      this.dialogVisible = true
      this.title = '新增服务包'
      this.form = {
        id: '',
        spName: '',
        validDateRange: [],
        spAmt: undefined,
        spDesc: ''
      }
    },

    handleEdit() {
      if (!this.checkId) {
        this.$message.warning('请选择服务包')
        return
      }
      this.dialogVisible = true
      this.title = '修改服务包'
      const item = this.servicePackList.find((it) => it.id === this.checkId)
      this.form = {
        id: item.id,
        spName: item.spName,
        validDateRange: [item.validStartDate, item.validEndDate],
        spAmt: item.spAmt,
        spDesc: item.spDesc
      }
    },

    handleDelete() {
      if (!this.checkId) {
        this.$message.warning('请选择服务包')
        return
      }
      const id = this.checkId
      const item = this.servicePackList.find((it) => it.id === id)
      this.$confirm(`确定删除${item.spName}服务包吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteServicePackage({ id }).then((res) => {
          if (res.code === 200) {
            this.$message.success('删除成功')
            this.getServicePackListFn()
          }
        })
      })
    },

    // 查看详情
    handleDetail(item) {
      this.dialogVisible = true
      this.title = '查看服务包'
      this.form = item
    },

    // 切换选择状态
    handleSwitchChange(item) {
      enableServicePackage({ id: item.id }).then((res) => {
        if (res.code === 200) {
          this.$message.success('切换成功')
          this.getServicePackListFn()
        }
      })
    },

    handleSubmit() {
      const params = {
        ...this.form,
        departCode: localCache.getCache('userInfo').departCode,
        validStartDate: this.form.validDateRange[0],
        validEndDate: this.form.validDateRange[1]
      }
      saveServicePackage(params).then((res) => {
        if (res.code === 200) {
          this.$message.success('保存成功')
          this.dialogVisible = false
          this.getServicePackListFn()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.service-pack-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  .service-pack-item {
    border-radius: 8px;
    padding: 16px 20px;
    margin-bottom: 16px;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    position: relative;

    &.gradient-bg {
      background: linear-gradient(to right, #26c6da, #00bcd4);
      color: #fff;

      .check-btn {
        color: #fff;
      }
    }

    .service-pack-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
    }

    .service-pack-info {
      flex: 1;

      > div {
        margin-bottom: 8px;
        font-size: 14px;

        &:last-child {
          margin-bottom: 0;
        }
      }

      .service-pack-name {
        font-size: 16px;
        font-weight: 500;
      }
    }

    .service-pack-actions {
      margin-left: 16px;

      .check-btn {
        font-size: 14px;
        padding: 0;
      }
    }

    .service-pack-switch {
      position: absolute;
      right: 20px;
      bottom: 16px;
    }
  }
}
</style>
