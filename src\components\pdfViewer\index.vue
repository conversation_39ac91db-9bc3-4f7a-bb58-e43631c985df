<template>
  <div class="pdf-viewer">
    <!-- PDF内容 -->
    <div class="pdf-content">
      <pdf :src="pdfSource" :scale="scale" style="width: 100%" />
    </div>
  </div>
</template>

<script>
import pdf from 'vue-pdf'

export default {
  name: 'PDFViewer',
  components: {
    pdf
  },
  props: {
    url: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      pdfSource: null,
      scale: 3
    }
  },
  watch: {
    url: {
      immediate: true,
      handler(newUrl) {
        if (newUrl) {
          this.loadPdf(newUrl)
        }
      }
    }
  },
  methods: {
    loadPdf(url) {
      console.log('url', url)
      // 使用vue-pdf的加载任务
      const loadingTask = pdf.createLoadingTask(url)
      this.pdfSource = loadingTask
    }
  }
}
</script>

<style scoped>
.pdf-viewer {
  width: 100%;
  height: 100%;
  min-height: 500px;
  display: flex;
  flex-direction: column;
}

.pdf-content {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  padding: 20px;
  width: 100%;
}

/* 改进canvas渲染质量 */
.pdf-content >>> canvas {
  max-width: 100% !important;
  height: auto !important;
}
</style>
