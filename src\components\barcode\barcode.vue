<template>
  <div style="text-align: center">
    <img ref="barcodeEl" class="barcode-img" alt="barcode">
  </div>
</template>

<script>
import JsBarcode from 'jsbarcode'

export default {
  name: 'Barcode',
  props: {
    // 条形码内容
    code: {
      type: String,
      default: '0'
    },
    // 条形码格式
    format: {
      type: String,
      default: 'CODE128'
    },
    // 条之间的宽度
    width: {
      type: Number,
      default: 2
    },
    // 条的高度
    height: {
      type: Number,
      default: 50
    },
    // 是否显示文本
    displayValue: {
      type: Boolean,
      default: true
    },
    // 文本大小
    fontSize: {
      type: Number,
      default: 12
    },
    // 边距
    margin: {
      type: Number,
      default: 5
    },
    // 文本位置
    textPosition: {
      type: String,
      default: 'bottom'
    },
    // 背景色
    background: {
      type: String,
      default: '#ffffff'
    },
    // 条形码颜色
    lineColor: {
      type: String,
      default: '#000000'
    }
  },
  watch: {
    // 监听code变化，重新生成条形码
    code: {
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            this.generateBarcode()
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.generateBarcode()
    })
  },
  methods: {
    // 生成条形码的统一方法
    generateBarcode() {
      try {
        const value = this.code || '0'
        const options = {
          format: this.format,
          width: this.width,
          height: this.height,
          displayValue: this.displayValue,
          fontSize: this.fontSize,
          margin: this.margin,
          textPosition: this.textPosition,
          background: this.background,
          lineColor: this.lineColor
        }

        JsBarcode(this.$refs.barcodeEl, value, options)

        this.$emit('success', value)
      } catch (error) {
        console.error('条形码生成错误:', error)
        this.$emit('error', error)
      }
    }
  }
}
</script>

<style scoped>
.barcode-img {
  max-width: 100%;
  height: auto;
}
</style>
