export default {
  bind(el, binding, vnode) {
    // 防抖函数
    function debounce(func, wait, immediate) {
      // func函数，wait为时间周期
      let timeout
      return function () {
        const context = this
        const args = arguments
        const later = function () {
          timeout = null
          if (!immediate) {
            func.apply(context, args)
          }
        }
        const callNow = immediate && !timeout
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
        if (callNow) {
          func.apply(context, args)
        }
      }
    }
    const wrapDom = el.querySelector('.el-autocomplete-suggestion__wrap')
    const listDom = el.querySelector('.el-autocomplete-suggestion__wrap  .el-autocomplete-suggestion__list')
    // 将滚动事件通过参数传入防抖函数中
    const myEfficientFn = debounce(() => {
      const condition = wrapDom.offsetHeight + wrapDom.scrollTop + 10 - listDom.offsetHeight
      if (condition > 0 && !vnode.context.loading) {
        // 滚动到底部则执行滚动方法load，binding.value就是v-scrollLoad绑定的值，加()表示执行绑定的方法
        binding.value()
      }
    }, 250)
    wrapDom.addEventListener('scroll', myEfficientFn, false) // 绑定滚动事件
  }
}
