import request from '@/utils/request'

// 分页查询质控管理
export const getQualityControlList = (data) => {
  return request({
    url: '/cspapi/backend/audit/page',
    method: 'post',
    data
  })
}

// 质控管理详情
export const getQualityControlDetail = (data) => {
  return request({
    url: '/cspapi/backend/audit/detail',
    method: 'post',
    data
  })
}

// 审核质控管理
export const auditQualityControl = (data) => {
  return request({
    url: '/cspapi/backend/audit/save',
    method: 'post',
    data
  })
}

// 质控统计-按机构
export const getQualityControlStatisticsByDepart = (data) => {
  return request({
    url: '/cspapi/backend/audit/depart/count',
    method: 'post',
    data
  })
}

// 质控统计-按审核员
export const getQualityControlStatisticsByPerson = (data) => {
  return request({
    url: '/cspapi/backend/audit/auditor/count',
    method: 'post',
    data
  })
}

// 质控统计-按日期查询
export const getQualityControlStatisticsByDate = (data) => {
  return request({
    url: '/cspapi/backend/audit/date/count',
    method: 'post',
    data
  })
}

// 获取影像文件列表
export const getImageFileList = (data) => {
  return request({
    url: '/cspapi/backend/imagereview/file/detail',
    method: 'post',
    data
  })
}

// 保存影像文件
export const saveImageFileList = (data) => {
  return request({
    url: '/cspapi/backend/imagereview/file/save',
    method: 'post',
    data
  })
}
