import { GENDER_ENUM } from './enum'

/**
 * 将对象中的字段值映射到表格数组中对应的字段
 * @param {Object} sourceData - 源对象
 * @param {Array} table - 表格数据
 * @returns {Array} 映射后结果
 */
export function mapDataToTable(sourceData, table) {
  return table.map((row) => {
    const newRow = { ...row }
    for (const key in row) {
      if (sourceData.hasOwnProperty(key)) {
        const targetField = row[key] // 例如 'count'、'value'
        newRow[targetField] = sourceData[key]
      }
    }
    return newRow
  })
}

// 手机号和固定电话校验
/**
 * 手机号和固定电话校验规则
 * @param {string} phone - 手机号或固定电话
 * @returns {boolean} 校验结果
 */
export function validatePhone(phone) {
  const phoneReg = /^[0-9\-]{1,16}$/
  return phoneReg.test(phone)
}

/**
 * 手机号校验函数 - 用于Element UI表单验证
 * @param {Object} rule - 验证规则对象
 * @param {string} value - 要验证的值
 * @param {Function} callback - 回调函数
 */
export function phoneValidator(rule, value, callback) {
  if (!value) {
    callback(new Error('请输入电话号码'))
  } else if (!validatePhone(value)) {
    callback(new Error('请输入正确电话号码'))
  } else {
    callback()
  }
}

// 身份证校验
/**
 * 身份证号校验规则
 * @param {string} idCard - 身份证号
 * @returns {boolean} 校验结果
 */
export function validateIdCard(idCard) {
  // 身份证号码为15位或者18位，15位时全为数字，18位前17位为数字，最后一位是校验位，可能为数字或字符X
  const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
  if (!idCardReg.test(idCard)) {
    return false
  }

  // 如果是15位身份证
  if (idCard.length === 15) {
    return true
  }

  // 如果是18位身份证，需要校验最后一位校验位
  if (idCard.length === 18) {
    const idCardWi = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2] // 加权因子
    const idCardY = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'] // 校验字符
    let idCardWiSum = 0 // 用来保存前17位各自乘加权因子后的总和

    for (let i = 0; i < 17; i++) {
      idCardWiSum += parseInt(idCard.substring(i, i + 1)) * idCardWi[i]
    }

    const idCardMod = idCardWiSum % 11 // 计算出校验码所在位置
    const idCardLast = idCard.substring(17) // 得到最后一位身份证号码

    // 如果等于2，则说明校验码是x，否则是数字
    if (idCardMod === 2) {
      return idCardLast === 'X' || idCardLast === 'x'
    } else {
      return idCardLast === idCardY[idCardMod]
    }
  }

  return false
}

/**
 * 身份证号校验函数 - 用于Element UI表单验证
 * @param {Object} rule - 验证规则对象
 * @param {string} value - 要验证的值
 * @param {Function} callback - 回调函数
 */
export function idCardValidator(rule, value, callback) {
  if (!value) {
    callback(new Error('请输入身份证号'))
  } else if (!validateIdCard(value)) {
    callback(new Error('请输入正确的身份证号'))
  } else {
    callback()
  }
}

// 校验input输入框必须是数字或者字符串数字
/**
 * 校验输入值是否为数字或字符串数字
 * @param {string|number} value - 要校验的值
 * @returns {boolean} - 是否为有效数字
 */
export function validateNumber(value) {
  if (value === '' || value === null || value === undefined) {
    return false
  }

  // 转换为字符串进行校验
  const strValue = String(value).trim()

  // 使用正则表达式校验是否为数字（包括小数、负数）
  const numberRegex = /^-?\d+(\.\d+)?$/

  return numberRegex.test(strValue)
}

/**
 * 数字输入框校验函数 - 用于Element UI表单验证
 * @param {Object} rule - 验证规则对象
 * @param {string|number} value - 要验证的值
 * @param {Function} callback - 回调函数
 */
export function numberValidator(rule, value, callback) {
  if (!value && value !== 0) {
    callback(new Error('请输入数字'))
  } else if (!validateNumber(value)) {
    callback(new Error('请输入正确的数字格式'))
  } else {
    callback()
  }
}

/**
 * 正整数校验函数
 * @param {string|number} value - 要校验的值
 * @returns {boolean} - 是否为正整数
 */
export function validatePositiveInteger(value) {
  if (value === '' || value === null || value === undefined) {
    return false
  }

  const strValue = String(value).trim()
  const positiveIntegerRegex = /^[1-9]\d*$/

  return positiveIntegerRegex.test(strValue)
}

/**
 * 非负整数校验函数（包括0）
 * @param {string|number} value - 要校验的值
 * @returns {boolean} - 是否为非负整数
 */
export function validateNonNegativeInteger(value) {
  if (value === '' || value === null || value === undefined) {
    return false
  }

  const strValue = String(value).trim()
  const nonNegativeIntegerRegex = /^(0|[1-9]\d*)$/

  return nonNegativeIntegerRegex.test(strValue)
}

// 校验只能输入数字
export function validateOnlyNumber(value) {
  // 清除非数字和小数点的字符
  const cleaned = value
    .replace(/[^\d.]/g, '') // 移除非数字和小数点
    .replace(/^0+(?=\d)/, '') // 去除开头多余的 0（可选）
    .replace(/^\./, '') // 不能以点开头
    .replace(/\.{2,}/g, '.') // 多个点只保留一个
    .replace(/(\.\d*)\./g, '$1') // 保留第一个点，其余移除

  return cleaned
}

/**
 * 拆分 URL，返回协议+域名 和 剩余路径
 * @param {string} url - 完整 URL 地址
 * @returns {{ origin: string, path: string }} - 拆分后的结果
 */
export function splitUrl(url) {
  const match = url.match(/^(https?:\/\/[^\/]+)(\/.*)?$/)
  // const match = url.match(/^(https?:\/\/[^\/]+\/)(.*)$/)
  const { path } = match
    ? {
      origin: match[1], // 协议 + 域名
      path: match[2] || '/' // 剩余路径（没有就返回 /）
    }
    : {
      origin: '',
      path: ''
    }
  return path
}

export function getFullUrl(path = '') {
  const { origin } = window.location // 自动获取当前域名和协议，

  // 去除 path 开头的 `/`，避免拼接出双斜杠
  const normalizedPath = path.replace(/^\/+/, '')

  return `${origin}/${normalizedPath}`
}

// 设备码是否展示
export function showDeviceCode(path) {
  const supportedPaths = ['/receptionCenter/patientReception', '/receptionCenter/patientExamination']
  return supportedPaths.includes(path)
}

export function getDeviceCode(path, data) {
  if (!data || !data.archiveId) {
    return ''
  }

  const supportedPaths = ['/receptionCenter/patientReception', '/receptionCenter/patientExamination']

  return supportedPaths.includes(path) ? `${data.archiveId}` : ''
}

// 将 base64 转换为 file
export function convertBase64ToFormData(base64Str, filename = 'image.jpg', mimeType = 'image/jpeg') {
  const byteString = atob(base64Str.split(',')[1]) // 去掉 data:image/jpeg;base64, 前缀
  const ab = new ArrayBuffer(byteString.length)
  const ia = new Uint8Array(ab)
  for (let i = 0; i < byteString.length; i++) {
    ia[i] = byteString.charCodeAt(i)
  }

  const blob = new Blob([ab], { type: mimeType })

  const formData = new FormData()
  formData.append('file', blob, filename)
  return formData
}

// 性别转换
export function genderTransform(gender) {
  if (gender === undefined || gender === null || gender === '') return ''
  return GENDER_ENUM.find((item) => item.value === gender).label || ''
}
