/* eslint-disable max-len */
import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // progress bar style
import { getToken, getUserId } from '@/utils/auth' // get token from cookie
import Layout from '@/layout'

const _import = require('./router/_import_development')

const routemenu = [
  {
    path: '/archives',
    component: Layout,
    meta: {
      title: '居民档案'
    },
    children: [
      {
        path: '/archives/detail',
        component: () => import('@/views/archives/deatil/index'),
        meta: {
          title: '个人概况'
        }
      }
    ]
  },

  {
    path: '/screenList',
    component: Layout,
    meta: {
      title: '人群筛查'
    },
    children: [
      {
        name: 'screenListDetail',
        path: 'detail',
        component: () => import('@/views/screenList/detail'),
        meta: {
          title: '详情'
        }
      }
    ]
  },

  {
    path: '/followUp/followUpCenter',
    component: Layout,
    meta: {
      title: '随访中心'
    },
    children: [
      {
        name: 'followUpCenterDetail',
        path: 'detail',
        component: () => import('@/views/followUp/followUpCenter/detail'),
        meta: {
          title: '详情'
        }
      }
    ]
  },

  {
    path: '/followUp/additionalVisitEvaluation',
    component: Layout,
    meta: {
      title: '随访评估'
    },
    children: [
      {
        name: 'additionalVisitEvaluationDetail',
        path: 'detail',
        component: () => import('@/views/followUp/additionalVisitEvaluation/detail'),
        meta: {
          title: '详情'
        }
      }
    ]
  },

  {
    path: '/qualityControl/ultrasonicQualityControl',
    component: Layout,
    meta: {
      title: '超声质控'
    },
    children: [
      {
        name: 'ultrasonicQualityControlDetail',
        path: 'detail',
        component: () => import('@/views/qualityControl/ultrasonicQualityControl/detail'),
        meta: {
          title: '详情'
        }
      }
    ]
  },

  {
    path: '/receptionCenter',
    component: Layout,
    meta: {
      title: '接诊中心'
    },
    children: [
      {
        name: 'patientReception',
        path: 'patientReception',
        component: () => import('@/views/receptionCenter/patientReception/index.vue'),
        meta: {
          title: '患者接诊'
        }
      },
      {
        name: 'managePatient',
        path: 'managePatient',
        component: () => import('@/views/receptionCenter/managePatient/index.vue'),
        meta: {
          title: '管理患者'
        }
      },
      {
        name: 'patientExamination',
        path: 'patientExamination',
        component: () => import('@/views/receptionCenter/patientExamination/index.vue'),
        meta: {
          title: '患者检查'
        }
      },
      {
        name: 'printGuidanceSheet',
        path: 'printGuidanceSheet',
        component: () => import('@/views/receptionCenter/reportPrint/printGuidanceSheet.vue'),
        meta: {
          title: '引导单打印'
        }
      }
    ]
  },

  {
    path: '/regionalMedical/transfer',
    component: Layout,
    meta: {
      title: '区域转诊'
    },
    children: [
      {
        name: 'transferApply',
        path: 'transferApply',
        meta: {
          title: '转诊申请'
        },
        component: () => import('@/views/regionalMedical/transfer/transferApply.vue')
      }
    ]
  },

  {
    path: '/ocrRecognition',
    component: () => import('@/components/ocrRecognition/index.vue'),
    hidden: true
  },

  {
    path: '/healthRecordReport',
    component: () => import('@/views/healthRecordReport/index'),
    hidden: true
  },

  {
    path: '/messageNotification',
    component: Layout,
    meta: {
      title: '消息通知'
    },
    children: [
      {
        path: 'allMessage',
        component: () => import('@/views/messageNotification/allMessage/index'),
        meta: {
          title: '全部消息'
        }
      }
    ]
  },

  { path: '*', redirect: '/404', hidden: true }
]
const havepath = (obj) => {
  let str = obj.path
  if (obj.children && obj.children[0].path) {
    str += `/${havepath(obj.children[0])}`
  }
  return str
}
NProgress.configure({ showSpinner: false }) // NProgress Configuration

const whiteList = ['/login']

const noTokenList = []

// 只有一级菜单的页面
const onlyOneLevelMenu = ['/home', '/patientCreate', '/archives', '/screenList']

// eslint-disable-next-line space-before-function-paren
router.beforeEach(async (to, from, next) => {
  // start progress bar
  NProgress.start()

  // determine whether the user has logged in
  const hasToken = getToken()
  if (hasToken && !noTokenList.includes(to.path)) {
    if (to.path === '/login') {
      next({ path: '/' })

      NProgress.done() // hack: https://github.com/PanJiaChen/vue-element-admin/pull/2939
    } else if (
      window.basePar.newAndOld &&
      window.basePar.filterNav.some((v) => {
        return v === to.path
      })
    ) {
      const envModule = require('@/utils/env')
      let key = envModule.getEnv()
      key =
        window.location.hostname.includes('localhost') ||
        (window.location.hostname.includes('192.168') && !window.location.hostname.includes('**************'))
          ? 'local'
          : key
      const title = envModule.genUrlEnum().TYPE
      const hostname = window.basePar.oldHref[title][key]
      const { path } = to
      window.location.href = `${hostname}/starnet/#${path}?token=${hasToken}&userId=${getUserId()}`
    } else {
      // determine whether the user has obtained his permission roles through getInfo
      const hasRoles = store.getters.roles && store.getters.roles.length > 0
      if (hasRoles) {
        if (router.options.routes[0].path !== '/main' && to.name === 'dashboard') {
          if (router.options.routes[0].path === '/redirect') {
            next({ path: '/' })
          } else {
            next({ path: havepath(router.options.routes[0]) })
          }
        } else {
          next()
        }
      } else {
        try {
          await store.dispatch('user/getInfo')
          const menus = formatRoutes(store.getters.menus) // <=== 修改部分
          menus.push(...routemenu)
          global.antRouter = menus // <=== 修改部分
          router.options.routes = menus
          router.addRoutes(menus)
          next({ ...to, replace: true })
        } catch (error) {
          // remove token and go to login page to re-login
          await store.dispatch('user/resetToken')
          Message.error(error || 'Has Error')
          next(`/login`)
          NProgress.done()
        }
      }
    }
  } else if (whiteList.indexOf(to.path) !== -1) {
    // in the free login whitelist, go directly
    next()
  } else {
    // other pages that do not have permission to access are redirected to the login page.
    next(`/login`)
    NProgress.done()
  }
})

router.afterEach(() => {
  // finish progress bar
  NProgress.done()
})

function formatRoutes(routes) {
  return routes.map((route) => {
    if (onlyOneLevelMenu.includes(route.path)) {
      return {
        name: '',
        path: '',
        component: Layout,
        redirect: route.path === '/home' ? '/home' : '',
        hidden: route.hidden || false,
        children: [
          {
            name: route.componentName || '',
            path: route.path || '',
            component: _import(route.component),
            hidden: route.hidden || false
          }
        ]
      }
    }

    const formattedRoute = {
      name: route.componentName || '',
      path: route.path || '',
      component: route.component === 'Layout' ? Layout : _import(route.component),
      hidden: route.hidden || false,
      meta: {
        title: route.meta.title || '',
        icon: route.meta.icon || ''
      }
    }

    if (route.children && route.children.length > 0) {
      formattedRoute.children = formatRoutes(route.children)
    }

    return formattedRoute
  })
}
