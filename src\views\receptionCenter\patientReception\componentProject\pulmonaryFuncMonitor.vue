<!-- 肺功能仪监测 -->
<template>
  <div class="pulmonary-func-monitor">
    <el-form :model="form" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="FEV1：">
            <custom-input-number v-model="form.fev1" @input="calculateFev1FvcRate" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="FVC：">
            <custom-input-number v-model="form.fvc" @input="calculateFev1FvcRate" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="FEV1/FVC：">
            <custom-input-number v-model="form.fev1FvcRate" readonly>
              <template #append>%</template>
            </custom-input-number>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p style="margin-top: 16px; color: red; font-weight: bold; font-size: 14px">
      参数说明：备注：FEV1/FVC < 70%，确诊为慢阻肺。
    </p>
  </div>
</template>

<script>
export default {
  name: 'PulmonaryFuncMonitor',
  data() {
    return {
      form: {
        fev1: '',
        fvc: '',
        fev1FvcRate: ''
      }
    }
  },
  methods: {
    initData({ id, data }) {
      this.form = {
        fev1: data && data.fev1,
        fvc: data && data.fvc,
        fev1FvcRate: data && data.fev1FvcRate,
        itemId: id,
        itemDetailId: data && data.id
      }
      // 初始化后也要计算一次比率
      this.calculateFev1FvcRate()
    },
    calculateFev1FvcRate() {
      const { fev1, fvc } = this.form
      if (fev1 && fvc && !isNaN(fev1) && !isNaN(fvc) && Number(fvc) !== 0) {
        // 计算FEV1/FVC的百分比值，保留2位小数
        this.form.fev1FvcRate = ((Number(fev1) / Number(fvc)) * 100).toFixed(2)
      } else {
        this.form.fev1FvcRate = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pulmonary-func-monitor {
  padding: 16px;
}
</style>
