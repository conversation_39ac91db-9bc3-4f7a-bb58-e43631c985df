<template>
  <div class="patient-list">
    <el-input
      v-model="queryParams.keyword"
      prefix-icon="el-icon-search"
      placeholder="请输入姓名/身份证"
      style="width: 350px; margin-bottom: 10px"
      @input="handleInput"
    />
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      :height="500"
      row-key="id"
      :columns="columns"
      :total="total"
      :page-info="queryParams"
      @pagination-change="handlePaginationChange"
    >
      <template #disease="{ row }">
        <ChronicDiseaseType :record="row" />
      </template>
      <template #radio="{ row }">
        <el-radio v-model="selectedRow" :label="row.id">{{ '' }}</el-radio>
      </template>
    </base-table>
  </div>
</template>

<script>
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import { getPatientList } from '@/api/receptionWorkbench'
import ChronicDiseaseType from '@/components/chronicDiseaseType'
import { throttle } from 'lodash'

export default {
  name: 'PatientList',
  components: {
    BaseTable,
    ChronicDiseaseType
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        keyword: '',
        disease: this.$route.path === '/followUp' ? 'fangchan' : ''
      },
      columns: [
        { prop: 'radio', label: '', width: 50, slot: 'radio' },
        { type: 'index', width: 80 },
        { prop: 'name', label: '姓名' },
        { prop: 'age', label: '年龄' },
        // { prop: 'idCard', label: '身份证号' },
        { prop: 'disease', label: '慢病病种', slot: 'disease' },
        { prop: 'departName', label: '医疗机构' }
      ],
      selectedRow: null,
      throttledSearch: null
    }
  },
  created() {
    this.throttledSearch = throttle(() => {
      this.handleSearch()
    }, 1000)
  },
  methods: {
    async getTableList(params) {
      return await getPatientList(params)
    },
    handleInput() {
      this.throttledSearch()
    }
  }
}
</script>
