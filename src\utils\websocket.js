/* eslint-disable space-before-function-paren */
/* eslint-disable no-shadow */

import { getUserId } from '@/utils/auth'
import { URL_ENUM } from '@/utils/enum'
import { messageHandle } from './websocketMessaging'

// 构建 WebSocket 地址
const url = URL_ENUM.WSS_ENV
let ws
let tt
let lockReconnect = false // 避免重复连接
const clientId = getUserId() // 缓存中取出客户端id

const websocket = {
  Init(clientId) {
    if ('WebSocket' in window) {
      ws = new WebSocket(url + clientId)
    } else if ('MozWebSocket' in window) {
      // eslint-disable-next-line no-undef
      ws = new MozWebSocket(url + clientId)
    } else {
      // console.log('您的浏览器不支持 WebSocket!（星网计划连接后台---WebSocket）')
      return
    }

    ws.onmessage = function (e) {
      if (JSON.parse(e.data).subModule !== '网络质量') {
        console.log(`接收消息:${e.data}`)
      }
      window.dispatchEvent(
        new CustomEvent('onmessageWs', {
          detail: {
            data: e.data
          }
        })
      )
      heartCheck.start()
      if (e.data === 'ok') {
        // 心跳消息不做处理
        return
      }
      messageHandle(e.data)
    }

    ws.onclose = function () {
      reconnect(clientId)
    }

    ws.onopen = function () {
      heartCheck.start()
    }

    ws.onerror = function (e) {
      reconnect(clientId)
    }
  },
  Send(sender, reception, body, flag) {
    const data = {
      sender,
      reception,
      body,
      flag
    }
    const msg = JSON.stringify(data)
    console.log(`发送消息：${msg}`)
    ws.send(msg)
  },
  getWebSocket() {
    return ws
  },
  close() {
    lockReconnect = true
    ws.close()
  },
  getStatus() {
    if (`${ws.readyState}` === '0') {
      return '未连接'
    } else if (`${ws.readyState}` === '1') {
      return '已连接'
    } else if (`${ws.readyState}` === '2') {
      return '连接正在关闭'
    } else if (`${ws.readyState}` === '3') {
      return '连接已关闭'
    }
  }
}

export default websocket

function reconnect(sname) {
  if (lockReconnect) {
    return
  }
  lockReconnect = true
  // 没连接上会一直重连，设置延迟避免请求过多
  tt && clearTimeout(tt)
  tt = setTimeout(() => {
    // console.log('执行断线重连...（星网计划连接后台---WebSocket）')
    websocket.Init(sname)
    lockReconnect = false
  }, 4000)
}

// 心跳检测
let heartCheck = {
  timeout: 1000 * 5 * 1,
  timeoutObj: null,
  serverTimeoutObj: null,
  start() {
    const self = this
    this.timeoutObj && clearTimeout(this.timeoutObj)
    this.serverTimeoutObj && clearTimeout(this.serverTimeoutObj)
    this.timeoutObj = setTimeout(() => {
      ws.send(`HeartBeat:${clientId}`)
      self.serverTimeoutObj = setTimeout(() => {
        if (`${ws.readyState}` !== '1') {
          ws.close()
        } else {
          heartCheck.start()
        }
      }, self.timeout)
    }, this.timeout)
  }
}
