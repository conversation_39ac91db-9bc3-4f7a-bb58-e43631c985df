<!-- 药物监测 -->
<template>
  <div class="drug-monitoring">
    <el-form ref="formRef" :model="form" label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="治疗建议：">
            <el-input v-model="form.suggest" type="textarea" :rows="3" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="药物治疗说明：">
            <el-input v-model="form.description" type="textarea" :rows="3" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注：">
            <el-input v-model="form.remark" type="textarea" :rows="3" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-col :span="24">
      <flag-component
        :title="'药物清单'"
        desc="药品名称、每次剂量、给药频率、使用方法为必填项，任何一项为空均视为无效数据。"
      />
      <el-table :data="tableData" border style="margin-top: 16px">
        <el-table-column prop="medicalId" label="药品名称" align="center">
          <template #default="scope">
            <el-select
              v-model="scope.row.medicalId"
              placeholder="请选择"
              style="width: 100%"
              @change="handlemedicalChange(scope.row)"
            >
              <el-option v-for="item in drugManagementList" :key="item.id" :label="item.medicalName" :value="item.id" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="medicalType" label="药品类型" align="center">
          <template #default="scope">
            <el-select v-model="scope.row.medicalType" placeholder="请选择" style="width: 100%">
              <el-option label="降压药" :value="1" />
              <el-option label="胰岛素" :value="2" />
              <el-option label="降糖药" :value="3" />
              <el-option label="调脂药" :value="4" />
              <el-option label="房颤" :value="5" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="useCount" label="每次剂量" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.useCount" placeholder="请输入" />
          </template>
        </el-table-column>
        <el-table-column prop="useUnit" label="计量单位" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.useUnit" placeholder="请输入" />
          </template>
        </el-table-column>
        <el-table-column prop="useRate" label="给药频率" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.useRate" placeholder="请输入" />
          </template>
        </el-table-column>
        <el-table-column prop="useWay" label="使用方法" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.useWay" placeholder="请输入" />
          </template>
        </el-table-column>
        <el-table-column prop="beginDate" label="开始用药日期" align="center">
          <template #default="scope">
            <el-date-picker
              v-model="scope.row.beginDate"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="请选择"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column prop="endDate" label="结束用药日期" align="center">
          <template #default="scope">
            <el-date-picker
              v-model="scope.row.endDate"
              value-format="yyyy-MM-dd"
              type="date"
              placeholder="请选择"
              style="width: 100%"
            />
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center">
          <template #default="scope">
            <el-input v-model="scope.row.remark" placeholder="请输入" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template #default="scope">
            <span v-if="scope.$index === tableData.length - 1">
              <i class="el-icon-plus" style="color: #41a1d4; cursor: pointer" @click="handleAdd" />
            </span>
            <span>
              <i
                class="el-icon-delete"
                style="color: red; margin-left: 16px; cursor: pointer"
                @click="handleDelete(scope.$index)"
              />
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-col>
  </div>
</template>

<script>
import flagComponent from '@/components/flagComponent/index.vue'
import { getDrugManagementList } from '@/api/drugManagement'

export default {
  name: 'DrugMonitoring',
  components: { flagComponent },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        suggest: '',
        description: '',
        remark: ''
      },
      tableData: [
        {
          medicalType: '',
          medicalId: '',
          useCount: '',
          useUnit: '',
          useRate: '',
          useWay: '',
          beginDate: '',
          endDate: '',
          remark: ''
        }
      ],
      drugManagementList: []
    }
  },
  created() {
    this.getDrugManagementListForSelect()
  },
  methods: {
    initData(data) {
      if (data) {
        this.form = data.medicalMonitor
        this.tableData = data.itemList
      }
    },

    async getDrugManagementListForSelect() {
      const res = await getDrugManagementList({
        pageNo: 1,
        pageSize: 1000
      })
      if (res.code === 200) {
        this.drugManagementList = res.data.list
      }
    },

    handleAdd() {
      this.tableData.push({
        medicalType: '',
        medicalId: '',
        useCount: '',
        useUnit: '',
        useRate: '',
        useWay: '',
        beginDate: '',
        endDate: '',
        remark: ''
      })
    },

    handleDelete(index) {
      this.tableData.splice(index, 1)
    },

    handlemedicalChange(row) {
      const drugManagement = this.drugManagementList.find((item) => item.id === row.medicalId)
      if (drugManagement) {
        row.useUnit = drugManagement.medicalUnit
        row.medicalType = drugManagement.medicalType
      }
    },

    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: true,
        data: {
          data: {
            medicalMonitor: {
              suggest: this.form.suggest,
              description: this.form.description,
              remark: this.form.remark,
              id: this.form.id
            },
            itemList: this.tableData
          },
          itemCode: this.itemTemp.value,
          taItemId: this.$store.getters.therapeuticActionDetail.itemList.find((it) => it.itemCode === this.itemTemp.value)
            .taItemId
        }
      }

      return result
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.drug-monitoring {
  padding: 16px;
}
</style>
