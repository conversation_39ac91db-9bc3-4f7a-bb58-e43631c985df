<template>
  <div class="adverse-event">
    <el-card class="search-card">
      <el-form :model="queryParams" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="医疗机构：" prop="depart">
              <TreeSelect
                v-model="queryParams.departCode"
                :data="departTree"
                :props="{
                  children: 'children',
                  label: 'departName',
                  value: 'departCode'
                }"
                placeholder="请选择"
                @change="handleDepartChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="姓名/身份证：">
              <el-input v-model="queryParams.keyword" />
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="table-card">
      <el-button icon="el-icon-plus" type="primary" style="margin-bottom: 8px" @click="handleAdd">新增</el-button>
      <BaseTable
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        row-key="id"
        height="calc(100% - 90px)"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #sex="{ row }">
          <span>{{ genderTransform(row.sex) }}</span>
        </template>
        <template #operation="{ row }">
          <el-button type="text" size="small" @click="handleView(row)">查看</el-button>
          <el-button type="text" size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" size="small" style="color: red" @click="handleDelete(row)">删除</el-button>
        </template>
      </BaseTable>
    </el-card>

    <ProDialog ref="proDialog" :title="dialogTitle" :visible.sync="addDialogVisible" width="650px">
      <el-form ref="formRef" :model="form" label-width="90px">
        <el-form-item label="医疗机构：" prop="depart">
          <TreeSelect
            v-model="form.departCode"
            :data="departTree"
            :props="{
              children: 'children',
              label: 'departName',
              value: 'departCode'
            }"
            placeholder="请选择"
            :disabled="disableSubmit"
            @change="handleFormDepartChange"
          />
        </el-form-item>
        <el-form-item label="选择人员：">
          <SelectTable
            ref="selectTable"
            v-model="form.patientName"
            method="post"
            :url="'/cspapi/backend/base/user/base/patient/page'"
            :search-key="'keyword'"
            :params="{ departCode: form.departCode }"
            :option-item="{ value: 'id', label: 'name' }"
            :disabled="disableSubmit"
            @select="handleChangePatient"
          />
        </el-form-item>
        <CheckboxGroupField v-model="form.badEvent" :disabled="disableSubmit" :item="badEventList" :form-data="form" />
        <el-form-item label="发生日期：">
          <el-date-picker
            v-model="form.happenDate"
            :disabled="disableSubmit"
            value-format="yyyy-MM-dd"
            type="date"
            placeholder="选择日期"
          />
        </el-form-item>
      </el-form>
      <span v-if="!disableSubmit" slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </span>
    </ProDialog>
  </div>
</template>

<script>
import { getAdverseEventList, addAdverseEvent, deleteAdverseEvent } from '@/api/adverseEvent'
import { getOrgTreeByIdApi } from '@/api/system'
import { getUserId } from '@/utils/auth'
import { genderTransform } from '@/utils/cspUtils'
import { localCache } from '@/utils/cache'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ProDialog from '@/components/ProDialog/index.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'
import TreeSelect from '@/components/TreeSelect/index.vue'
import SelectTable from '@/components/selectTable/index.vue'

export default {
  name: 'AdverseEvent',
  components: {
    BaseTable,
    ProDialog,
    CheckboxGroupField,
    TreeSelect,
    SelectTable
  },
  mixins: [tableMixin],
  data() {
    return {
      dialogTitle: '新增不良事件',
      queryParams: {
        departName: '',
        departCode: '',
        keyword: ''
      },
      form: {
        patientId: '',
        patientName: '',
        departName: '',
        departCode: localCache.getCache('userInfo').departCode,
        happenDate: '',
        badEvent: [],
        badEventRemark1: '',
        badEventRemark2: '',
        badEventRemark3: '',
        badEventRemark4: '',
        badEventRemark5: '',
        badEventRemark6: '',
        badEventRemark7: '',
        badEventRemark8: '',
        badEventRemark9: '',
        badEventRemark10: '',
        badEventRemark11: '',
        badEventRemark12: '',
        badEventRemark13: '',
        badEventBloodLevel: ''
      },
      userList: [],
      departTree: [],
      badEventList: {
        label: '不良事件：',
        type: 'checkbox',
        prop: 'badEvent',
        belong: 'all',
        required: false,
        remark: true, // 是否需要备注
        options: [
          { label: '出血事件', value: '1', badEventRemark1: '', prop: 'badEventBloodLevel', type: 'select' },
          { label: '心衰', value: '2', badEventRemark2: '' },
          { label: 'TIA', value: '3', badEventRemark2: '' },
          { label: '痴呆', value: '4', badEventRemark3: '' },
          { label: '卒中', value: '5', badEventRemark4: '' },
          { label: '冠心病', value: '6', badEventRemark5: '' },
          { label: '失明', value: '7', badEventRemark6: '' },
          { label: '肾功能不全', value: '8', badEventRemark7: '' },
          { label: '周围血管闭塞', value: '9', badEventRemark8: '' },
          { label: '主动脉夹层', value: '10', badEventRemark9: '' },
          { label: '呼吸衰竭', value: '11', badEventRemark10: '' },
          { label: '肺源性心脏病', value: '12', badEventRemark11: '' },
          { label: '肺性脑病', value: '13', badEventRemark12: '' }
        ],
        extraOptions: [
          { label: '少量出血', value: 1 },
          { label: '中等出血', value: 2 },
          { label: '大出血或致命性出血', value: 3 }
        ]
      },
      columns: [
        { label: '姓名', prop: 'patientName' },
        { label: '性别', prop: 'sex', slot: 'sex' },
        { label: '年龄', prop: 'age' },
        { label: '医疗机构', prop: 'departName' },
        { label: '不良事件', prop: 'badEventDesc', width: 160, showOverflowTooltip: true },
        { label: '发生日期', prop: 'happenDate' },
        { label: '操作', prop: 'operation', width: 160, slot: 'operation' }
      ]
    }
  },
  created() {
    this.getDepartTree()
  },
  methods: {
    genderTransform,
    async getTableList(params) {
      return await getAdverseEventList(params)
    },

    // 机构树
    async getDepartTree() {
      const res = await getOrgTreeByIdApi({
        patientId: getUserId()
      })
      this.departTree = res.data
    },
    // 查询表单部门选择变化
    handleDepartChange(value, data, node) {
      this.queryParams.departCode = value
      this.queryParams.departName = data ? data.departName : ''
    },

    // 表单部门选择变化
    handleFormDepartChange(value, data, node) {
      this.form.departCode = value
      this.form.departName = data ? data.departName : ''
    },

    handleChangePatient(record) {
      this.form.patientId = record.id
      this.form.patientName = record.name
    },

    // 新增
    handleAdd() {
      this.dialogTitle = '新增不良事件'
      this.form = {
        departCode: localCache.getCache('userInfo').departCode
      }
      this.addDialogVisible = true
      this.disableSubmit = false
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate()
      })
    },

    // 查看
    handleView(row) {
      this.dialogTitle = '查看不良事件'
      this.form = {
        ...row,
        badEvent: row.badEvent ? row.badEvent.split(',') : []
      }
      this.disableSubmit = true
      this.addDialogVisible = true
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑不良事件'
      this.form = {
        ...row,
        badEvent: row.badEvent ? row.badEvent.split(',') : []
      }
      this.disableSubmit = false
      this.addDialogVisible = true
    },

    // 删除
    handleDelete(row) {
      this.handleConfirmDelete({
        params: {
          id: row.id
        },
        deleteApi: deleteAdverseEvent,
        message: '确认删除该条数据吗？'
      })
    },

    handleSave() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 提交表单
          const params = {
            ...this.form,
            badEvent: this.form.badEvent.join(',')
          }
          addAdverseEvent(params).then((res) => {
            if (res.code === 200) {
              this.$message.success('保存成功')
              this.addDialogVisible = false
              this.handleSearch()
            }
          })
        } else {
          console.log('表单验证失败')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.adverse-event {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-card {
    margin: 16px;
    height: 77px;
  }
  .table-card {
    flex: 1;
    margin: 0 16px;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
}
</style>
