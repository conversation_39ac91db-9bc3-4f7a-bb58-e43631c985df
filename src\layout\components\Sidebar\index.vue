<template>
  <!-- eslint-disable vue/no-use-v-if-with-v-for -->
  <div id="sidebar" :class="{ 'has-logo': showLogo }">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper" style="height: calc(100% - 100px)">
      <el-menu
        :default-openeds="openeds"
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="false"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in routes"
          v-if="route.meta && route.meta.roles && route.meta.roles.includes(role)"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  components: { SidebarItem, Logo },
  data() {
    return {
      openeds: ['/outpatient/treatment']
    }
  },
  computed: {
    ...mapGetters(['permission_routes', 'sidebar', 'menus']),
    routes() {
      return this.$router.options.routes
    },
    role() {
      return localStorage.getItem('role')
    },
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  watch: {
    $route() {
      this.addTags()
    }
  },
  created() {},
  methods: {
    addTags() {
      const { name } = this.$route
      if (name) {
        this.$store.dispatch('tagsView/addView', this.$route)
      }
      return false
    }
  }
}
</script>
<style scoped lang="scss">
// #sidebar {
//   box-shadow: 0 0 5px rgba(0, 0, 0, 0.5);
// }
::v-deep .el-menu-item {
  height: 45px;
  line-height: 45px;
}
</style>
