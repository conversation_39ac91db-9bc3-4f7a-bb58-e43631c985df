import Vue from 'vue'
import Print from 'vue-print-nb'
import tool from '@/utils/tool'
import htmlToPdf from '@/utils/html2pdf'
import install from '@/directive/select-scroll-down'
import hasPermission from '@/utils/hasPermission'
import Mixin from '@/mixins'

// 注册第三方插件和工具
const registerPlugins = () => {
  // 注册插件
  Vue.use(install)
  Vue.use(htmlToPdf)
  Vue.use(Print) // 注册打印
  Vue.use(tool)
  Vue.use(hasPermission)

  // 注册全局混入
  Vue.mixin(Mixin)
}

export default registerPlugins
