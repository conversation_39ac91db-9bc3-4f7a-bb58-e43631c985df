<!-- eslint-disable vue/max-attributes-per-line -->
<template>
  <div class="upload-video-container">
    <!-- 左侧：多个视频预览 -->
    <div v-for="(video, index) in videoList" :key="video" class="video-preview-area">
      <video :src="video" class="video-preview" controls preload="metadata" />
      <div v-if="!disabled" class="video-controls">
        <el-tooltip content="删除" placement="top">
          <i class="el-icon-close" style="color: red" @click.stop="handleDelete(index)" />
        </el-tooltip>
      </div>
    </div>

    <!-- 上传区域 -->
    <el-upload
      ref="upload"
      drag
      multiple
      :disabled="disabled"
      :action="action"
      :headers="headers"
      :accept="accept"
      :show-file-list="false"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :on-progress="handleProgress"
      :before-upload="beforeUpload"
      :data="data"
      :auto-upload="autoUpload"
      :limit="limit"
      class="upload-area"
    >
      <div class="upload-content">
        <i class="el-icon-video-camera" />
        <div class="el-upload__text">点击上传视频</div>
      </div>

      <!-- 上传进度（展示任意一个进度值） -->
      <el-progress
        v-if="Object.values(uploadProgress).some(p => p > 0 && p < 100)"
        :percentage="currentProgress"
        :stroke-width="6"
        :show-text="true"
        class="upload-progress"
      />
    </el-upload>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth'
import { URL_ENUM } from '@/utils/enum'
import { localCache } from '@/utils/cache'
import { splitUrl, getFullUrl } from '@/utils/cspUtils'

export default {
  name: 'UploadVideo',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: Array,
      default: () => []
    },
    action: {
      type: String,
      default: '/cspapi/backend/cos/uploadFile/private2'
    },
    accept: {
      type: String,
      default: '.mp4,.avi,.mov,.wmv,.flv,.rmvb'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: () => ({ folder: 'videoFiles/' })
    },
    autoUpload: {
      type: Boolean,
      default: true
    },
    uploadHeight: {
      type: String,
      default: '200px'
    },
    maxSize: {
      type: Number,
      default: 500
    },
    limit: {
      type: Number,
      default: 5
    }
  },
  data() {
    return {
      videoList: [],
      uploadProgress: {}, // key: file.uid -> percent
      isUploading: false
    }
  },
  computed: {
    headers() {
      return {
        Authorization: `${getToken()}`,
        'X-Tenant-Id': localCache.getCache('tenantId')
      }
    },
    HTTP_URL() {
      return process.env.NODE_ENV === 'production'
        ? window.location.href.match(/^https?:\/\/[^/]+\/?/)[0]
        : URL_ENUM.FILE_HTTP_ENV
    },
    acceptText() {
      return this.accept.replace(/\./g, '').toUpperCase()
    },
    currentProgress() {
      // 获取第一个不为100的进度值
      const progressList = Object.values(this.uploadProgress).filter((p) => p < 100)
      return progressList.length ? progressList[0] : 0
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (Array.isArray(newVal)) {
          this.videoList = newVal.map((url) => getFullUrl(url))
        } else {
          this.videoList = []
        }
      }
    }
  },
  methods: {
    beforeUpload(file) {
      const isLtMaxSize = file.size / 1024 / 1024 < this.maxSize
      if (!isLtMaxSize) {
        this.$message.error(`上传视频大小不能超过 ${this.maxSize}MB!`)
        return false
      }

      const fileExtension = file.name.slice(file.name.lastIndexOf('.'))
      const isValidType = this.accept.includes(fileExtension.toLowerCase())
      if (!isValidType) {
        this.$message.error(`请上传 ${this.acceptText} 格式的视频!`)
        return false
      }

      this.isUploading = true
      return true
    },
    handleProgress(event, file) {
      this.$set(this.uploadProgress, file.uid, Math.floor(event.percent))
    },
    handleUploadSuccess(response, file) {
      this.isUploading = false
      this.$set(this.uploadProgress, file.uid, 100)

      if (response && response.data) {
        const urls = response.data.map((item) => splitUrl(item.fullFileUrl))
        this.videoList = [...this.videoList, ...urls]
        this.$emit('input', this.videoList)
        this.$emit('upload-success', response, file)
      }
    },
    handleUploadError(err, file, fileList) {
      this.isUploading = false
      this.$delete(this.uploadProgress, file.uid)
      this.$message.error('视频上传失败，请重试')
      this.$emit('upload-error', err, file, fileList)
    },
    handleDelete(index) {
      this.videoList.splice(index, 1)
      this.$emit('input', this.videoList)

      if (this.$refs.upload) {
        this.$refs.upload.clearFiles()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.upload-video-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  width: 100%;
}

.video-preview-area {
  width: 150px;
  height: 150px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  background: #f5f7fa;

  .video-preview {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background: #000;
  }

  .video-controls {
    display: none;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 0;
    top: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10;

    i {
      color: #ffffff;
      font-size: 24px;
      cursor: pointer;

      &:hover {
        color: #409eff;
      }
    }
  }

  &:hover .video-controls {
    display: flex;
  }
}

.upload-area {
  width: 150px;
  height: 150px;

  ::v-deep .el-upload {
    width: 100%;
    height: 100%;
  }

  ::v-deep .el-upload-dragger {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
  }

  .upload-content {
    text-align: center;

    i {
      font-size: 28px;
      color: #8c939d;
    }

    .el-upload__text {
      color: #606266;
      font-size: 14px;
      margin: 10px 0;
    }
  }
}

.upload-progress {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
}

.upload_disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
