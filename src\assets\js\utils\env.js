module.exports = {
  genUrlEnum() {
    return {
      HTTP_ENV: process.env.VUE_APP_HTTP_ENV
    }
  },
  // 生成 env
  getEnv() {
    return process.env
  },
  getCookieValue(name) {
    // 将document.cookie字符串按照; 分割成数组
    var cookies = document.cookie.split(';')
    // 遍历cookie数组，寻找指定的cookie
    for (var i = 0; i < cookies.length; i++) {
      // 使用=分割cookie的名字和值
      var cookie = cookies[i].split('=')
      // 去除cookie名字两边的空格
      var cookieName = cookie[0].trim()
      // 如果发现了指定的cookie名字，返回它的值
      if (cookieName === name) {
        // 同样去除cookie值两边的空格
        return cookie[1].trim()
      }
    }
    // 如果没有找到指定的cookie，返回null
    return null
  },
  clearCookieValue() {
    var cookies = document.cookie.split(';')
    for (var i = 0; i < cookies.length; i++) {
      var cookie = cookies[i]
      var eqPos = cookie.indexOf('=')
      var cookieName = eqPos > -1 ? cookie.substr(0, eqPos) : cookie
      document.cookie = cookieName + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
    }
  }
}
