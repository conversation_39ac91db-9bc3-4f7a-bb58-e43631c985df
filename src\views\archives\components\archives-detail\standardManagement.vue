<!-- 规范管理记录 -->
<template>
  <div class="standard-management">
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      row-key="id"
      :columns="columns"
      :show-pagination="showPagination"
    >
      <<template #status="{ row }">
        <el-tag v-if="row.status === 1" type="primary">管理中</el-tag>
        <el-tag v-else-if="row.status === 5" type="success">已完成</el-tag>
        <el-tag v-else-if="row.status === 9" type="danger">已取消</el-tag>
      </template>
      <template #operation="{ row }">
        <el-button type="text" size="small" @click="handleDetail(row)">详情</el-button>
      </template>
    </base-table>
  </div>
</template>

<script>
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import { getStandardizedManageHistoryList } from '@/api/standardizedManage'

export default {
  name: 'StandardManagement',
  components: {
    BaseTable
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        patientId: this.$route.query.id
      },
      columns: [
        { label: '管理日期', prop: 'manageDate' },
        { label: '管理医生', prop: 'doctorName' },
        { label: '取消管理原因', prop: 'cancelReason' },
        { label: '管理状态', prop: 'status', slot: 'status' },
        { label: '操作', slot: 'operation' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      return await getStandardizedManageHistoryList(params)
    },
    handleDetail(row) {
      this.$router.push({
        path: '/receptionCenter/managePatient',
        query: { id: row.id }
      })
    }
  }
}
</script>
