import request from '@/utils/request'

// 接诊工作台疾病数量
export const getReceptionWorkbenchDisease = (data) => {
  return request({
    url: '/cspapi/backend/reception/workbenches/disease',
    method: 'post',
    data
  })
}

// 分页查询接诊记录
export const getReceptionWorkbenchList = (data) => {
  return request({
    url: '/cspapi/backend/reception/workbenches/page',
    method: 'post',
    data
  })
}

// 查询接诊历史记录
export const getReceptionWorkbenchHistoryList = (data) => {
  return request({
    url: '/cspapi/backend/reception/workbenches/list/history',
    method: 'post',
    data
  })
}

// 新增接诊
export const addReceptionWorkbench = (data) => {
  return request({
    url: '/cspapi/backend/reception/workbenches/create',
    method: 'post',
    data
  })
}

// 取消接诊
export const cancelReceptionWorkbench = (data) => {
  return request({
    url: '/cspapi/backend/reception/workbenches/cancel',
    method: 'post',
    data
  })
}

// 完成接诊
export const completeReceptionWorkbench = (data) => {
  return request({
    url: '/cspapi/backend/reception/workbenches/complete',
    method: 'post',
    data
  })
}
// 查询患者列表
export const getPatientList = (data) => {
  return request({
    url: '/cspapi/backend/base/user/base/patient/page',
    method: 'post',
    data
  })
}

// 查询接诊记录ById
export const getReceptionWorkbenchById = (data) => {
  return request({
    url: '/cspapi/backend/reception/workbenches/detail',
    method: 'post',
    data
  })
}

// 查询接诊详情By普筛ID
export const getReceptionWorkbenchByCsId = (data) => {
  return request({
    url: '/cspapi/backend/reception/workbenches/detail/csId',
    method: 'post',
    data
  })
}

// 查询接诊详情By patientId
export const getReceptionWorkbenchByPatientId = (data) => {
  return request({
    url: '/cspapi/backend/reception/workbenches/detail/patientId',
    method: 'post',
    data
  })
}

// 问诊信息详情
export const getConsultationInfoDetail = (data) => {
  return request({
    url: '/cspapi/backend/reception/workbenches/consultation/detail',
    method: 'post',
    data
  })
}

// 问诊信息保存
export const saveConsultationInfo = (data) => {
  return request({
    url: '/cspapi/backend/reception/workbenches/consultation/save',
    method: 'post',
    data
  })
}

// 高危筛查列表
export const getHighRiskScreeningList = (data) => {
  return request({
    url: '/cspapi/backend/high/risk/screening/page',
    method: 'post',
    data
  })
}
// 高危筛查详情
export const getHighRiskScreeningDetail = (data) => {
  return request({
    url: '/cspapi/backend/high/risk/screening/detail',
    method: 'post',
    data
  })
}

// 高危筛查保存
export const saveHighRiskScreening = (data) => {
  return request({
    url: '/cspapi/backend/high/risk/screening/save',
    method: 'post',
    data
  })
}

// 病情诊断列表
export const getDiagnosisOfIllnessList = (data) => {
  return request({
    url: '/cspapi/backend/disease/diagnosis/page',
    method: 'post',
    data
  })
}
// 病情诊断保存
export const saveDiagnosisOfIllness = (data) => {
  return request({
    url: '/cspapi/backend/disease/diagnosis/save',
    method: 'post',
    data
  })
}

// 病情诊断详情
export const getDiagnosisOfIllnessDetail = (data) => {
  return request({
    url: '/cspapi/backend/disease/diagnosis/detail',
    method: 'post',
    data
  })
}

// 并发症筛查记录列表
export const getComplicationsScreeningRecordList = (data) => {
  return request({
    url: '/cspapi/backend/complication/screening/page',
    method: 'post',
    data
  })
}

// 保存并发症筛查记录&&评估报告
export const saveComplicationsScreening = (data) => {
  return request({
    url: '/cspapi/backend/complication/screening/save',
    method: 'post',
    data
  })
}

// 并发症项目列表
export const getComplicationsScreeningList = (data) => {
  return request({
    url: '/cspapi/backend/complication/screening/item/list',
    method: 'post',
    data
  })
}

// 并发症筛查项目查询
export const getComplicationsScreeningProject = (data) => {
  return request({
    url: '/cspapi/backend/complication/screening/detail',
    method: 'post',
    data
  })
}

// 添加并发症项目
export const addComplicationsScreeningItem = (data) => {
  return request({
    url: '/cspapi/backend/complication/screening/item/add',
    method: 'post',
    data
  })
}

// 删除并发症项目
export const removeComplicationsScreeningItem = (data) => {
  return request({
    url: '/cspapi/backend/complication/screening/item/remove',
    method: 'post',
    data
  })
}

// 查询接诊步骤
export const getReceptionStepList = (data) => {
  return request({
    url: '/cspapi/backend/reception/workbenches/step/status',
    method: 'post',
    data
  })
}
