<!-- 医学营养 -->
<template>
  <div class="medical-nutrition">
    <div class="operate-button">
      <el-select v-model="templateId" placeholder="请选择系统预置的营养处方" clearable @change="handleTemplateChange">
        <el-option v-for="item in templateList" :key="item.id" :label="item.templateName" :value="item.id" />
      </el-select>
      <el-button type="primary" @click="handleSaveTemplate">保存模板</el-button>
    </div>

    <el-form ref="formRef" :model="form" :rules="rules" label-width="130px">
      <el-form-item label="医学营养建议：" prop="medicalNutritionSuggest">
        <el-input v-model="form.medicalNutritionSuggest" type="textarea" :rows="3" />
      </el-form-item>

      <el-form-item label="营养成分：">
        <el-table :data="tableData1" border>
          <el-table-column label="类型" prop="type" align="center" width="130" />
          <el-table-column label="建议摄入量" prop="suggestion" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.suggestion" />
            </template>
          </el-table-column>
          <el-table-column label="点评" prop="comment" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.comment" />
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item label="推荐饮食：">
        <el-table :data="tableData2" border>
          <el-table-column label="时间" prop="time" align="center" width="100" />
          <el-table-column label="建议(按照推荐份数分别从每类种选择，可自由搭配)" prop="suggestion" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.suggestion" />
            </template>
          </el-table-column>
          <el-table-column label="点评" prop="comment" align="center">
            <template #default="scope">
              <el-input v-model="scope.row.comment" />
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item label="指导建议：" prop="guidanceSuggest">
        <el-input v-model="form.guidanceSuggest" type="textarea" :rows="3" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { saveMedicalNutritionTemplate, getMedicalNutritionTemplate } from '@/api/standardizedManage'
import { mapDataToTable } from '@/utils/cspUtils'
import { cloneDeep } from 'lodash'

export default {
  name: 'MedicalNutrition',
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        medicalNutritionSuggest: '',
        guidanceSuggest: ''
      },
      rules: {
        medicalNutritionSuggest: [{ required: true, message: '请输入医学营养建议' }],
        guidanceSuggest: [{ required: true, message: '请输入指导建议' }]
      },
      tableData1: [
        {
          type: '能量（千卡）',
          suggestion: '',
          comment: '',
          energyCount: 'suggestion',
          energyComment: 'comment'
        },
        {
          type: '碳水化合物(克)',
          suggestion: '',
          comment: '',
          carbonWaterCount: 'suggestion',
          carbonWaterComment: 'comment'
        },
        {
          type: '蛋白质(克)',
          suggestion: '',
          comment: '',
          proteinCount: 'suggestion',
          proteinComment: 'comment'
        },
        {
          type: '脂肪(克)',
          suggestion: '',
          comment: '',
          fatCount: 'suggestion',
          fatComment: 'comment'
        },
        {
          type: '膳食纤维(克)',
          suggestion: '',
          comment: '',
          dietaryFiberCount: 'suggestion',
          dietaryFiberComment: 'comment'
        }
      ],
      tableData2: [
        {
          time: '早上',
          suggestion: '',
          comment: '',
          morningSuggest: 'suggestion',
          morningComment: 'comment'
        },
        {
          time: '中午',
          suggestion: '',
          comment: '',
          noonSuggest: 'suggestion',
          noonComment: 'comment'
        },
        {
          time: '晚上',
          suggestion: '',
          comment: '',
          dinnerSuggest: 'suggestion',
          dinnerComment: 'comment'
        },
        {
          time: '加餐',
          suggestion: '',
          comment: '',
          nightSuggest: 'suggestion',
          nightComment: 'comment'
        }
      ],
      templateList: [],
      templateId: ''
    }
  },
  async created() {
    this.getTemplateList()
  },
  methods: {
    initData(data) {
      if (data) {
        this.tableData1 = mapDataToTable(data, cloneDeep(this.tableData1))
        this.tableData2 = mapDataToTable(data, cloneDeep(this.tableData2))
        this.form = {
          medicalNutritionSuggest: data.medicalNutritionSuggest,
          guidanceSuggest: data.guidanceSuggest,
          id: data.id
        }
      }
    },

    handleTemplateChange(value) {
      if (value) {
        const data = cloneDeep(this.templateList.find((item) => item.id === value))
        delete data.id
        this.initData(data)
      }
    },
    async getTemplateList() {
      const res = await getMedicalNutritionTemplate({})
      if (res.code === 200) {
        this.templateList = res.data
      }
    },

    handleSaveTemplate() {
      this.$prompt('模板名称：', '模板设置', {
        confirmButtonText: '确定',
        cancelButtonText: '关闭',
        inputPattern: /^\S+$/,
        inputErrorMessage: '模板名称不能为空'
      })
        .then(({ value }) => {
          this.handleSave(value)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消输入'
          })
        })
    },

    async handleSave(value = '') {
      const table1Temp = this.extractEyeFields(this.tableData1).reduce((acc, item) => {
        acc = Object.assign(acc, item)
        return acc
      }, {})
      const table2Temp = this.extractEyeFields(this.tableData2).reduce((acc, item) => {
        acc = Object.assign(acc, item)
        return acc
      }, {})

      let result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          data: {
            ...this.form,
            ...table1Temp,
            ...table2Temp,
            id: this.form.id
          },
          itemCode: this.itemTemp.value,
          taItemId: this.$store.getters.therapeuticActionDetail.itemList.find((it) => it.itemCode === this.itemTemp.value)
            .taItemId
        }
      }

      if (value) {
        result = {
          name: this.itemTemp.label,
          success: false,
          data: {
            templateName: value,
            ...this.form,
            ...table1Temp,
            ...table2Temp,
            itemCode: this.itemTemp.value,
            taItemId: this.$store.getters.therapeuticActionDetail.itemList.find(
              (it) => it.itemCode === this.itemTemp.value
            ).taItemId
          }
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
        if (valid && value) {
          const res = await saveMedicalNutritionTemplate(result.data)
          if (res.code === 200) {
            this.$message.success('模板保存成功')
            this.getTemplateList()
          }
        }
        // for (const key in result.data) {
        //   if (result.data[key] === '') {
        //     result.success = false
        //     break
        //   }
        // }
      } catch (err) {
        console.warn('医学营养校验异常', err)
        result.success = false
      }
      return result
    },

    extractEyeFields(data, mappingFields = ['suggestion', 'comment']) {
      return data.map((item) => {
        const result = {}
        for (const key in item) {
          if (mappingFields.includes(item[key])) {
            result[key] = item[item[key]]
          }
        }
        return result
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.medical-nutrition {
  padding: 16px;
  .operate-button {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
  }
}
</style>
