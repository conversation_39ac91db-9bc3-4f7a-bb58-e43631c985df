<template>
  <div class="app-main">
    <div class="app_mainParent">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="cachedViews" :max="10">
          <router-view :key="key" />
        </keep-alive>
      </transition>
    </div>
    <!-- <div class="public_copyright flex_end">
      <p>技术实现：卫软（江苏）科技有限公司</p>
    </div> -->
  </div>
</template>

<script>
const ALWAYS_CACHED_ROUTES = [
  'ExaminationRecord',
  // 'EcgManage',
  'EcgRecycleBin',
  'DeviceCenter',
  // 'Followups',
  'Analyse',
  'DeviceManage'
  // 'Bodycheck',
  // 'Userdb'
]
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      return [...this.$store.state.tagsView.cachedViews, ...ALWAYS_CACHED_ROUTES]
    },
    key() {
      return this.$route.path
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  height: calc(100vh - 95px);
  width: 100%;
  position: relative;
  overflow-x: hidden;
}
.app_mainParent {
  overflow: auto;
  height: calc(100% - 10px);
  overflow-x: hidden;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    height: calc(100vh - 95px);
  }

  .fixed-header + .app-main {
    padding-top: 50px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  padding-right: 0 !important;
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
