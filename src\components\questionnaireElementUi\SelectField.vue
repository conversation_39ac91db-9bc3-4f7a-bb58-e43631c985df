<template>
  <el-form-item :label="item.label" :prop="item.prop">
    <div :class="item.class">
      <el-select v-model="localValue" placeholder="请选择" clearable :disabled="item.disabled">
        <el-option v-for="opt in item.options" :key="opt.value" :label="opt.label" :value="opt.value" />
      </el-select>
    </div>
  </el-form-item>
</template>

<script>
export default {
  name: 'SelectField',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: ['item', 'value'],
  computed: {
    localValue: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  }
}
</script>
