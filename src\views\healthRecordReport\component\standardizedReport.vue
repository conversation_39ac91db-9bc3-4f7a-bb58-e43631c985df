<!-- 规范管理报告 -->
<template>
  <div class="standardized-report">
    <h2 style="color: #4bc0f1">规范管理</h2>
    <div class="content">
      <div class="title">医学营养</div>
      <div class="item">
        <el-table :data="medicalNutritionData.tableData1" border style="margin-top: 10px">
          <el-table-column label="类型" prop="type" align="center" width="130" />
          <el-table-column label="建议摄入量" prop="suggestion" align="center" />
          <el-table-column label="点评" prop="comment" align="center" />
        </el-table>

        <el-table :data="medicalNutritionData.tableData2" border style="margin-top: 10px">
          <el-table-column label="时间" prop="time" align="center" width="100" />
          <el-table-column label="建议(按照推荐份数分别从每类种选择，可自由搭配)" prop="suggestion" align="center" />
          <el-table-column label="点评" prop="comment" align="center" />
        </el-table>

        <el-descriptions :column="1" border style="margin-top: 6px">
          <el-descriptions-item label="医学营养建议">
            {{ medicalNutritionData.medicalNutritionSuggest }}
          </el-descriptions-item>
          <el-descriptions-item label="指导建议">
            {{ medicalNutritionData.guidanceSuggest }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </div>
</template>

<script>
import { mapDataToTable } from '@/utils/cspUtils'
import { cloneDeep } from 'lodash'

export default {
  name: 'StandardizedReport',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData1: [
        {
          type: '能量（千卡）',
          suggestion: '',
          comment: '',
          energyCount: 'suggestion',
          energyComment: 'comment'
        },
        {
          type: '碳水化合物(克)',
          suggestion: '',
          comment: '',
          carbonWaterCount: 'suggestion',
          carbonWaterComment: 'comment'
        },
        {
          type: '蛋白质(克)',
          suggestion: '',
          comment: '',
          proteinCount: 'suggestion',
          proteinComment: 'comment'
        },
        {
          type: '脂肪(克)',
          suggestion: '',
          comment: '',
          fatCount: 'suggestion',
          fatComment: 'comment'
        },
        {
          type: '膳食纤维(克)',
          suggestion: '',
          comment: '',
          dietaryFiberCount: 'suggestion',
          dietaryFiberComment: 'comment'
        }
      ],
      tableData2: [
        {
          time: '早上',
          suggestion: '',
          comment: '',
          morningSuggest: 'suggestion',
          morningComment: 'comment'
        },
        {
          time: '中午',
          suggestion: '',
          comment: '',
          noonSuggest: 'suggestion',
          noonComment: 'comment'
        },
        {
          time: '晚上',
          suggestion: '',
          comment: '',
          dinnerSuggest: 'suggestion',
          dinnerComment: 'comment'
        },
        {
          time: '加餐',
          suggestion: '',
          comment: '',
          nightSuggest: 'suggestion',
          nightComment: 'comment'
        }
      ]
    }
  },
  computed: {
    medicalNutritionData() {
      const data = this.reportInfo.treatmentActionDetailVO
        ? this.reportInfo.treatmentActionDetailVO.itemList.find((item) => item.itemCode === 'MEDICAL_NUTRITION').data
        : {}

      return {
        tableData1: mapDataToTable(data, cloneDeep(this.tableData1)),
        tableData2: mapDataToTable(data, cloneDeep(this.tableData2)),
        medicalNutritionSuggest: data.medicalNutritionSuggest,
        guidanceSuggest: data.guidanceSuggest
      }
    }
  }
}
</script>

<style scoped lang="scss">
.standardized-report {
  padding: 10px;
  .content {
    margin-top: 8px;
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
    }
  }
}
</style>
