#app .public_v2 .ecgDetail_data {
  height: 100%;
  overflow-y: scroll;
}

.transferDetail_moudle {
  position: relative;

  > label {
    padding-left: 0.9rem;
    position: relative;

    &::after {
      position: absolute;
      content: '';
      width: 0.1rem;
      height: 0.6rem;
      background-color: #0a86c8;
      left: 0%;
      top: 50%;
      transform: translate(0%, -50%);
    }
  }
  .transferDetail_syncHis {
    display: flex;
    width: 4rem;
    height: 1rem;
    cursor: pointer;
  }

  .transferDetail_list {
    &.flex_start {
      flex-wrap: wrap;
    }

    > div {
      min-width: 20%;

      label {
        font-weight: 500;
        min-width: 5rem;
        margin-right: 1rem;
        display: inline-block;
      }

      > span {
        width: calc(100% - 5rem);
        display: inline-block;

        &.public_width100 {
          width: 100%;
        }
      }
      @media (max-width: 1280px) {
        label {
          min-width: 4.2rem;
          margin-right: 0.2rem;
        }

        > span {
          width: calc(100% - 4.5rem);
        }
      }
    }
    .transferDetail_annex,
    .transferDetail_annexImg {
      width: 4rem;
      height: 4rem;
    }

    .transferDetail_annex {
      border-radius: 0.2rem;
      border: 1px solid #0a86c8;
      margin-right: 0.5rem;
      position: relative;
      overflow: hidden;
    }
    .transferDetail_annexDel {
      position: absolute;
      top: 0.2rem;
      right: 0.2rem;
      z-index: 9999;
      cursor: pointer;
    }
  }
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 1.4rem;
  color: #bec7c6;
  width: 4rem;
  height: 4rem;
  line-height: 4rem;
  text-align: center;
  background: #f6fdf8;
  border-radius: 2px;
  border: 1px solid #0a86c8;
}

::v-deep.searchFormItemVwAddress.el-form-item {
  width: 100%;
}

.refuseFrom_phrase {
  > span {
    padding: 0.5rem 1rem;
    line-height: 1rem;
    height: 2rem;
    border-radius: 0.4rem;
    box-sizing: border-box;
    display: inline-block;
    cursor: pointer;
    margin-right: 0.5rem;
  }
}
