<template>
  <div class="a4-template" :style="paperStyle">
    <!-- 页眉 -->
    <div class="header">
      <div class="header-content">
        <img src="@/assets/logo/logo-wigroup.png" class="logo-image" alt="logo">
        <div class="header-text">基层慢病筛防中心</div>
      </div>
      <img src="@/assets/cspImg/titlebar.png" class="title-bar" alt="title-bar">
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <slot />
    </div>

    <!-- 页脚 -->
    <div class="footer">
      <div class="page-number">
        <span>{{ pageNumber }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'A4Template',
  props: {
    // 当前页码
    pageNumber: {
      type: [Number, String],
      default: 1
    },
    // 页面方向：portrait-纵向，landscape-横向
    orientation: {
      type: String,
      default: 'portrait',
      validator: (value) => ['portrait', 'landscape'].includes(value)
    }
  },
  computed: {
    paperStyle() {
      return {
        width: this.orientation === 'portrait' ? '210mm' : '297mm',
        height: this.orientation === 'portrait' ? '297mm' : '210mm',
        margin: '0 auto'
      }
    }
  }
}
</script>

<style scoped>
/* A4模板基本样式 */
.a4-template {
  position: relative;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  padding: 5mm;
  box-sizing: border-box;
  font-family: 'Microsoft YaHei', '微软雅黑', 'PingFang SC', 'Arial', sans-serif;
}

/* 页眉样式 */
.header {
  position: relative;
  height: 15mm;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: end;
  height: 100%;
}

.title-bar {
  width: 100%;
  position: relative;
  top: -28px;
}

.logo-image {
  height: 24px;
  object-fit: contain;
}

.header-text {
  font-size: 14px;
  color: #666;
  margin-right: 20px;
}

/* 内容区域样式 */
.content {
  min-height: calc(100% - 35mm); /* 减去页眉和页脚的高度 */
}

/* 页脚样式 */
.footer {
  position: absolute;
  bottom: 10mm;
  left: 0;
  right: 0;
  height: 10mm;
  display: flex;
  justify-content: center;
  align-items: center;
}

.page-number {
  font-size: 12px;
  color: #999;
}

/* 打印时的样式调整 */
@media print {
  .a4-template {
    box-shadow: none;
    padding: 5mm;
  }

  @page {
    size: A4;
    margin: 0;
  }
}
</style>
