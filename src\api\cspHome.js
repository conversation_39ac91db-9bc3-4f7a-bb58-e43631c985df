import request from '@/utils/request'

// 慢病筛查人数
export function getDiseaseScreeningCount(data) {
  return request({
    url: '/cspapi/backend/home/<USER>/screening/count',
    method: 'post',
    data
  })
}

// 规范管理人数
export function getStandardManageCount(data) {
  return request({
    url: '/cspapi/backend/home/<USER>/manage/count',
    method: 'post',
    data
  })
}

// 筛查统计-一筛
export function getScreeningCount(data) {
  return request({
    url: '/cspapi/backend/home/<USER>/count/first',
    method: 'post',
    data
  })
}

// 筛查统计-一筛
export function getScreeningCountSecond(data) {
  return request({
    url: '/cspapi/backend/home/<USER>/count/second',
    method: 'post',
    data
  })
}

// 筛查数据
export function getScreeningData(data) {
  return request({
    url: '/cspapi/backend/home/<USER>/data',
    method: 'post',
    data
  })
}

// 接诊人数
export function getReceptionCount(data) {
  return request({
    url: '/cspapi/backend/home/<USER>/count',
    method: 'post',
    data
  })
}

// 人群构成
export function getPopulationComposition(data) {
  return request({
    url: '/cspapi/backend/home/<USER>/compose',
    method: 'post',
    data
  })
}
