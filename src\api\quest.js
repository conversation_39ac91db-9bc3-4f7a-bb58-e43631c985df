import request from '@/utils/request'

export function getByRecordIdAndPatientIdAndQuestionnaireId(data, token) {
  return request({
    url: `/cspapi/backend/visitCheck/phmquestionnaires/getByRecordIdAndPatientIdAndQuestionnaireId`,
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}

// 获取融合的问卷信息
export function getPatientOfNewQuestApiVisit(data, token) {
  return request({
    url: `/cspapi/backend/visitCheck/phmquestionnaires/getByRecordId`,
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}

// 问卷个人信息新增
export function saveQuestionnaireStatus(data, token) {
  return request({
    url: `/cspapi/backend/bodyCheck/questionnaires/saveQuestionnaireStatus`,
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 问卷个人信息新增
export function addPhmUserinfoApi(data, token) {
  return request({
    url: `/cspapi/backend/bodyCheck/phmUserinfo2`,
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 问卷个人信息更新
export function putPhmUserinfoApi(data, token) {
  return request({
    url: `/cspapi/backend/bodyCheck/phmUserinfo2`,
    method: 'put',
    data,
    headers: {
      Authorization: token
    }
  })
}

// 问卷个人信息新增-随访
export function addPhmUserinfoVisitApi(data, token) {
  return request({
    url: `/cspapi/backend/bodyCheck/phmUserinfo2`,
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 问卷个人信息更新-随访
export function putPhmUserinfoVisitApi(data, token) {
  return request({
    url: `/cspapi/backend/bodyCheck/phmUserinfo2`,
    method: 'put',
    data,
    headers: {
      Authorization: token
    }
  })
}

// 问卷1000对应的信息新增
export function addID1000PhmUserinfoApi(data, token) {
  return request({
    url: `/cspapi/backend/visitCheck/phmquestionnaires/saveOneQuestionnaire`,
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 问卷1000对应的信息更新
export function putID1000PhmUserinfoApi(data, token) {
  return request({
    url: `/cspapi/backend/visitCheck/phmquestionnaires`,
    method: 'put',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 体检老年人+健康体检表 获取
export function getPhmExaminfoApi(data, token) {
  return request({
    url: '/cspapi/backend/bodyCheck/phmExaminfo2/getPatientByBatchIdAndPatientId',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}
// 体检老年人+健康体检表 新增
export function addPhmExaminfoApi(data, token) {
  return request({
    url: `/cspapi/backend/bodyCheck/phmExaminfo2`,
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 体检老年人+健康体检表 更新
export function putPhmExaminfoApi(data, token) {
  return request({
    url: `/cspapi/backend/bodyCheck/phmExaminfo2`,
    method: 'put',
    data,
    headers: {
      Authorization: token
    }
  })
}

// 中医问卷信息获取
export function getPhmZytzinfoApi(data, token) {
  return request({
    url: '/cspapi/backend/bodyCheck/phmZytzinfo/getPatientByBatchIdAndPatientId',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}

// 中医问卷信息新增
export function addPhmZytzinfoApi(data, token) {
  return request({
    url: `/cspapi/backend/bodyCheck/phmZytzinfo`,
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 中医问卷信息更新
export function putPhmZytzinfoApi(data, token) {
  return request({
    url: `/cspapi/backend/bodyCheck/phmZytzinfo`,
    method: 'put',
    data,
    headers: {
      Authorization: token
    }
  })
}

// 根据身份证号获取基公卫信息数据
export function getPhmUserByJGWApi(idCard, token) {
  return request({
    url: '/cspapi/backend/phmInfo/getPhmUser',
    method: 'post',
    data: { idCard },
    headers: {
      Authorization: token
    }
  })
}

// 体检记录健康评价
export function getBodyCheckEvaluateApi(data, token) {
  return request({
    url: '/cspapi/backend/bodyCheck/record/getBodyCheckEvaluate',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}

//
export function getEssentialDataApi(data, token) {
  return request({
    url: '/cspapi/backend/bodyCheck/record/getQuestionRecordInfo',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}

export function getLastMedicineApi(data, token) {
  return request({
    url: '/cspapi/backend/visitCheck/phmquestionnaires/getLastMedicine',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}
// 慢阻肺
export function getGWCOPDLastMedicineApi(data, token) {
  return request({
    url: '/cspapi/backend/visitphm/phmCopdQuestionnaires/getLastMedicine',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}
// 高血压
export function getGWGXYLastMedicineApi(data, token) {
  return request({
    url: '/cspapi/backend/visitphm/phmGxyQuestionnaires/getLastMedicine',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}
// 糖尿病
export function getGWTNBLastMedicineApi(data, token) {
  return request({
    url: '/cspapi/backend/visitphm/phmTnbQuestionnaires/getLastMedicine',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}

//
export function getInputSelectDataApi(name, apiUrl, token) {
  return request({
    url: apiUrl,
    method: 'get',
    params: { name },
    headers: {
      Authorization: token
    }
  })
}

// 获取基公卫高血压随访问卷
export function get207infoApi(data, token) {
  return request({
    url: '/cspapi/backend/visitphm/phmGxyQuestionnaires/getByRecordId',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}

// 基公卫高血压随访问卷新增
export function add207infoVisitApi(data, token) {
  return request({
    url: `/cspapi/backend/visitphm/phmGxyQuestionnaires/saveOneQuestionnaire`,
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 基公卫高血压随访问卷更新
export function put207infoVisitApi(data, token) {
  return request({
    url: `/cspapi/backend/visitphm/phmGxyQuestionnaires`,
    method: 'put',
    data,
    headers: {
      Authorization: token
    }
  })
}

// 获取基公卫糖尿病随访问卷
export function get206infoApi(data, token) {
  return request({
    url: '/cspapi/backend/visitphm/phmTnbQuestionnaires/getByRecordId',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}
// 基公卫糖尿病随访问卷新增
export function add206infoVisitApi(data, token) {
  return request({
    url: `/cspapi/backend/visitphm/phmTnbQuestionnaires/saveOneQuestionnaire`,
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 基公卫糖尿病随访问卷更新
export function put206infoVisitApi(data, token) {
  return request({
    url: `/cspapi/backend/visitphm/phmTnbQuestionnaires`,
    method: 'put',
    data,
    headers: {
      Authorization: token
    }
  })
}

// 获取基公卫慢阻肺随访问卷
export function get205infoApi(data, token) {
  return request({
    url: '/cspapi/backend/visitphm/phmCopdQuestionnaires/getByRecordId',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}
export function get100001infoApi(data, token) {
  return request({
    url: '/cspapi/backend/visitphm/phmCopdQuestionnaires/getByRecordId',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}
// 基公卫慢阻肺问卷新增
export function add205infoVisitApi(data, token) {
  return request({
    url: `/cspapi/backend/visitphm/phmCopdQuestionnaires/saveOneQuestionnaire`,
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 基公卫慢阻肺随访问卷更新
export function put205infoVisitApi(data, token) {
  return request({
    url: `/cspapi/backend/visitphm/phmCopdQuestionnaires`,
    method: 'put',
    data,
    headers: {
      Authorization: token
    }
  })
}

// 基公卫慢阻肺问卷新增
export function add100001infoVisitApi(data, token) {
  return request({
    url: `/cspapi/backend/visitphm/phmCopdQuestionnaires/saveOneQuestionnaire`,
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 基公卫慢阻肺随访问卷更新
export function put100001infoVisitApi(data, token) {
  return request({
    url: `/cspapi/backend/visitphm/phmCopdQuestionnaires`,
    method: 'put',
    data,
    headers: {
      Authorization: token
    }
  })
}
