<template>
  <div id="app">
    <div v-show="1 === 2">
      <textarea id="sdk_message" class="input_textarea" type="text" />
      <select id="enum_list" class="input_select" />
    </div>
    <router-view v-if="isRouterAlive" />
    <ProDialog
      v-if="remoteConsultationVisible"
      :visible="remoteConsultationVisible"
      @close="handleCloseRemoteConsultation"
    >
      <other-doctor-page />
    </ProDialog>
  </div>
</template>

<script>
import OtherDoctorPage from '@/components/remoteConsultation/component/otherDoctorPage.vue'
import ProDialog from '@/components/ProDialog/index.vue'
import { mapState } from 'vuex'

export default {
  name: 'App',

  components: {
    OtherDoctorPage,
    ProDialog
  },
  provide() {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      isRouterAlive: true,
      roomId: ''
    }
  },
  computed: {
    ...mapState('app', ['remoteConsultationVisible'])
  },
  mounted() {
    // window.addEventListener('resize', this.setRem, true)
  },
  methods: {
    setRem() {
      let rem = document.documentElement.clientWidth / 96
      if (document.documentElement.clientWidth < 1300) {
        rem = 20
      }
      document.documentElement.style.fontSize = `${rem}px`
      this.$store.dispatch('app/setFontSize', rem)
      if (document.documentElement.clientWidth > 1400) {
        this.$store.dispatch('app/toggleSideBarClose')
      } else {
        this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
      }
    },
    reload() {
      this.isRouterAlive = false
      this.$nextTick(function() {
        this.isRouterAlive = true
      })
    },
    // 关闭远程会诊弹窗
    handleCloseRemoteConsultation() {
      this.$store.commit('app/SET_REMOTE_CONSULTATION_VISIBLE', false)
    }
  }
}
</script>
<style lang="scss">
.t-loading-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(122, 122, 122, 0.1); /* 半透明背景 */
  z-index: 9999;
}

.t-spinner {
  position: relative;
  width: 50px; /* 轨迹圆的宽度 */
  height: 50px; /* 轨迹圆的高度 */
}

.dot {
  width: 15px; /* 小圆球的直径 */
  height: 15px;
  background-color: #fff; /* 小圆球的颜色 */
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 50%;
  transform-origin: 50% 50%; /* 变换的原点 */
  animation: orbit 1.5s linear infinite;
}

.dot:nth-child(1) {
  background-color: #3869ff;
  animation-delay: -0.3s;
}
.dot:nth-child(2) {
  background-color: #33b68a;
  animation-delay: -0.6s;
}
.dot:nth-child(3) {
  background-color: #0e9eff;
  animation-delay: -0.9s;
}
.dot:nth-child(4) {
  background-color: #3fdfff;
  animation-delay: -1.2s;
}
.dot:nth-child(5) {
  background-color: #1996af;
  animation-delay: -1.5s;
}

@keyframes orbit {
  0% {
    transform: rotate(0deg) translateX(25px); /* 轨迹半径 */
  }
  100% {
    transform: rotate(360deg) translateX(25px); /* 顺时针旋转一圈 */
  }
}

.remove-confirm .el-message-box__status {
  width: 54px;
  height: 54px;
  border-radius: 10px;
  overflow: hidden;
  background-color: #ffdddd;
  color: #eb4f3e;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.remove-confirm .el-message-box__message {
  padding-left: 65px;
}

.el-table__empty-text {
  font-size: 0.6rem;
}

.el-buttom {
  font-size: 0.6rem;
}

.el-empty__description p {
  font-size: 0.6rem;
}

.el-button--medium {
}

.el-radio {
  .el-radio__label {
    font-size: 0.6rem;
  }
}

.el-descriptions__title {
  font-size: 0.65rem;
}

.el-tooltip__popper.is-dark[x-placement^='right'] .popper__arrow::after {
  border-right-color: #5e6e82;
}

.el-tooltip__popper.is-dark {
  background: #5e6e82;
  border: 1px solid #5e6e82;
  color: #fff;
}

.dateArrClass > div ::after {
  content: '';
  position: absolute;
  right: calc(50% - 3px);
  bottom: -7px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #1890ff;
}

.el-big-dialog {
  backdrop-filter: blur(3px);
  -webkit-backdrop-filter: blur(5px);

  .el-dialog {
    width: 1100px;
    // height: 600px;
    border-radius: 10px;
    overflow: hidden;
  }

  .el-dialog__header {
    background-color: #f6f6f6;
    text-align: left;
    color: #333;
    height: 40px;
    padding: 0;
    line-height: 40px;
    border-bottom: 1px solid #dedde2;

    .el-dialog__headerbtn .el-dialog__close {
      color: #333 !important;
    }

    .dialog-title {
      display: flex;
      align-items: center;
      font-size: 0.65rem !important;
      background-color: #f6fdf8;
      color: #0a86c8;
      img {
        width: 22px;
        height: 22px;
        margin: 0 10px;
      }
    }

    .el-dialog__title {
      color: #333;
    }

    .el-dialog__headerbtn {
      top: 12px;

      .el-dialog__close {
        color: #fff;
      }
    }
  }
}

#app {
  background: #f9fffb;
  .el-dialog__wrapper {
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(5px);
    .el-dialog__header {
      border-bottom: 1px solid #dedde2;
      span {
        line-height: 40px;
        height: 40px;
        font-size: 0.8rem !important;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      }
      .el-dialog__close {
        font-size: 0.9rem;
      }
    }
  }
}
</style>
