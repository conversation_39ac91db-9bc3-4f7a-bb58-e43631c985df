<template>
  <!-- eslint-disable vue/no-mutating-props -->
  <content class="singleEditPencilContainer">
    <i class="el-icon-edit-outline" @click="editDialogVisible = true" />

    <el-dialog
      v-el-drag-dialog
      custom-class="applicationCustomDialog noPaddingDialog"
      :visible="editDialogVisible"
      width="20vw"
      :close-on-click-modal="false"
      :before-close="
        done => {
          editDialogVisible = false
          done()
        }
      "
    >
      <!-- 标题 -->
      <span slot="title" class="customDialogTitle customLeftDialogTitle">
        <span>{{ labelName }}编辑</span>
      </span>
      <el-input v-model="mapInner[keyName]" :maxlength="maxlen" class="inputSelf" />
      <div class="confirmButtonBox">
        <el-button class="saveButton" type="primary" plain @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
  </content>
</template>

<script>
import { deepClone } from '@/utils'

export default {
  name: 'WiSingleEditPencil',
  props: {
    labelName: {
      type: String,
      default: ''
    },
    keyName: {
      type: String,
      default: ''
    },
    infoMap: {
      type: Object,
      default: () => {
        return {}
      }
    },
    pattern: {
      type: RegExp,
      default: null
    },
    maxlen: {
      type: Number,
      default: 1024
    }
  },
  data() {
    return {
      editDialogVisible: false,
      mapInner: {}
    }
  },
  watch: {
    editDialogVisible(newV) {
      if (newV) {
        this.mapInner = deepClone(this.infoMap)
      }
    }
  },
  methods: {
    handleSave() {
      const self = this
      if (!self.pattern || self.pattern.test(self.mapInner[self.keyName])) {
        self.$emit('onSave', {
          userInfo: self.mapInner,
          keyName: self.keyName,
          cb: self.closeDialog()
        })
      } else {
        self.$message.error(`请输入正确的${self.labelName}`)
      }
    },
    closeDialog() {
      this.editDialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.singleEditPencilContainer {
  i.el-icon-edit-outline {
    cursor: pointer;
  }
  .confirmButtonBox {
    margin-top: 20px;
    text-align: right;

    .saveButton {
      width: 96px;
      height: 40px;
    }
  }
}
</style>
