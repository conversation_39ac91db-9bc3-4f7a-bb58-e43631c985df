import request from '@/utils/request'
/**
 * 状态
 * 1: 采集中(采集未完成, 项未全部采集)
 * 2: 采集完成(所有的项都已经采集) 对应 待转运
 * 3: 已转运 对应 待分装
 * 4: 待分装 ??? (暂时没用到) 和 3 相呼应的
 * 5: 分装确认(没问题) 对应 待质控
 * 6: 分装确认(有问题)
 * 7: 待质控 ??? (暂时没用到) 和 5 相呼应
 * 8: 质控(没问题) 对应 待入库
 * 9: 质控(有问题)-待处理
 * 10: 已入库
 */
// 分页-样本管理
export function samplePageApi(params) {
  return request({
    url: `/cspapi/backend/sampleCollection/page`,
    method: 'get',
    params
  })
}
// 新增样本采集
export function addsamplePageApi(data) {
  return request({
    url: `/cspapi/backend/sampleCollection`,
    method: 'post',
    data
  })
}
// 单个-样本采集
export function getSampleCollectionApi(id) {
  return request({
    url: `/cspapi/backend/sampleCollection/${id}`,
    method: 'get'
  })
}
// 修改 -- 样本采集  赋值 + 改采集项 + 状态
export function putSamplePageApi(data) {
  return request({
    url: `/cspapi/backend/sampleCollection`,
    method: 'put',
    data
  })
}
// 样本采集-我知道了
export function putbatchDealApi(data) {
  return request({
    url: `/cspapi/backend/sampleCollection/sure/batchDeal`,
    method: 'put',
    data
  })
}
// 批量转运
export function batchUpdateStatusApi(collectionIds) {
  return request({
    url: `/cspapi/backend/sampleCollection/batchUpdateStatus`,
    method: 'put',
    data: { collectionIds, status: 3 }
  })
}
// 批量转运
export function batchWarehouseApi(data) {
  return request({
    url: `/cspapi/backend/sampleCollection/package/batchWarehouse`,
    method: 'put',
    data
  })
}

// 新增-确认  3 => 4
export function ackSampleCollectionApi(arr) {
  return request({
    url: `/cspapi/backend/sampleCollection/sure`,
    method: 'post',
    data: { status: 4, list: arr }
  })
}
// 列表-样本确认(是否处理)
export function getAckSampleCollectionApi() {
  return request({
    url: `/cspapi/backend/sampleCollection/sure/page`,
    method: 'get',
    params: { pageNo: 1, pageSize: 99999, dealStatus: 0 }
  })
}
//  单个-样本分装(根据采集列表的id)
export function getSubpackageDataApi(collectionId) {
  return request({
    url: `/cspapi/backend/sampleCollection/package/getByCollectionId/${collectionId}`,
    method: 'get'
  })
}
//  新增-样本分装
export function addSubpackageDataApi(data) {
  return request({
    url: `/cspapi/backend/sampleCollection/package`,
    method: 'post',
    data
  })
}
//  修改-样本分装
export function putSubpackageDataApi(data) {
  return request({
    url: `/cspapi/backend/sampleCollection/package`,
    method: 'put',
    data
  })
}
//  修改-样本分装
export function addQualityControlApi(data) {
  return request({
    url: `/cspapi/backend/sampleCollection/qualityControl`,
    method: 'post',
    data
  })
}
//  单个-样本质控(根据采集列表的id)
export function getQualityControlApi(collectionId) {
  return request({
    url: `/cspapi/backend/sampleCollection/qualityControl/getByCollectionId`,
    method: 'get',
    params: { collectionId }
  })
}
//  详情-质控处理(根据collectionId)
export function getQualityControlDealtApi(collectionId) {
  return request({
    url: `/cspapi/backend/sampleCollection/qualityControl/deal/getByCollectionId`,
    method: 'get',
    params: { collectionId }
  })
}
//  新增-质控处理
export function addQualityControlDealtApi(data) {
  return request({
    url: `/cspapi/backend/sampleCollection/qualityControl/deal`,
    method: 'post',
    data
  })
}
//  新增-质控处理
export function putQualityControlDealtApi(data) {
  return request({
    url: `/cspapi/backend/sampleCollection/qualityControl/deal`,
    method: 'put',
    data
  })
}
//  样本采集-统计
export function getStatisticGroupByStatus(params) {
  return request({
    url: `/cspapi/backend/sampleCollection/statisticGroupByStatus`,
    method: 'get',
    params
  })
}
//  样本分装-统计
export function getPackageStatisticGroupByStatus(params) {
  return request({
    url: `/cspapi/backend/sampleCollection/package/statisticGroupByStatus`,
    method: 'get',
    params
  })
}
//  样本质控-统计
export function getQualityControlStatisticGroupByStatus(params) {
  return request({
    url: `/cspapi/backend/sampleCollection/qualityControl/statisticGroupByStatus`,
    method: 'get',
    params
  })
}

// 分页-样本管理
export function sampleSubQuestionApi(params) {
  // return request({
  //   url: `/cspapi/backend/device/manage/page`,
  //   method: 'get',
  //   params
  // })
  const list = [
    { name: '张茂发(1011)', questionItems: '血、尿、粪、组织、其他', remark: '样本未收到' },
    { name: '王大志(2418)', questionItems: '血', remark: '采集血量过少' }
  ]
  return new Promise((resolve) => {
    resolve({
      data: {
        list
      }
    })
  })
}

// 分页-待确认样本
export function sampleConfirmListApi(params) {
  // return request({
  //   url: `/cspapi/backend/device/manage/page`,
  //   method: 'get',
  //   params
  // })
  const list = [
    { name: '张茂发(1011)', sex: 1, age: 72, collectItems: '血、尿、粪、组织、其他', remark: '' },
    { name: '王大志(2418)', sex: 1, age: 61, collectItems: '血', remark: '' }
  ]
  return new Promise((resolve) => {
    resolve({
      data: {
        list
      }
    })
  })
}

const detailCollectData = {
  blood: {
    // 空腹 0-否 / 1-是 BLOOD_EMPTY_ENUM
    empty: 1,
    // 状态 0-未抗凝 / 1-抗凝 BLOOD_STATUS_ENUM
    status: 1,
    // 采集时间
    time: '2023-10-24',
    // 采集量
    volume: '15',
    unit: 'ml',
    // 储存条件 0-常温 / 1-4℃ COLLECT_SAVE_CONDITION_ENUM
    saveCondition: 0
  },
  pee: {
    // 是否为晨尿 0-否 / 1-是 PEE_IS_MORNING_ENUM
    morning: 1,
    // 采集时间
    time: '2023/10/24',
    // 采集量
    volume: '15',
    unit: 'ml',
    // 储存条件 0-常温 / 1-4℃
    saveCondition: 0
  },
  shit: {
    // 采集时间
    time: '2023/10/24',
    // 采集量
    volume: '15',
    unit: 'ml',
    // 储存条件 0-常温 / 1-4℃
    saveCondition: 0
  },
  tissue: {
    // 采集时间
    time: '2023/10/24',
    // 采集量
    volume: '15',
    unit: 'ml',
    // 储存条件 0-常温 / 1-4℃
    saveCondition: 0
  },
  other: {
    // 类型
    type: '唾液',
    // 采集时间
    time: '2023/10/24',
    // 采集量
    volume: '15',
    unit: 'ml',
    // 储存条件 0-常温 / 1-4℃
    saveCondition: 0
  }
}

const sampleQcForm = {
  // 0-正常 1-有血丝 2-橙色 3-红色 BLOOD_QALITY_ENUM
  bloodQality: '1',
  bq1Remark: '12.1',
  bq2Remark: '',
  bq3Remark: '',
  // 单管体积 0: &lt;0.5ml / 1: 0.5ml≤&&lt;1ml / 2: ≥1ml SINGLE_VOLUME_ENUM
  singleVolume: '1',
  sv0Remark: '',
  sv1Remark: '0.75',
  sv2Remark: '',
  // 对应编号是否错误 0-否 / 1-是 YES_ONE_NO_ZERO
  numberWrong: '1',
  nw1Remark: '0x0907',
  // 标签是否脱落 0-否 / 1-是
  tagDown: '0',
  td1Remark: '',
  // 封存管破损情况 0-否 / 1-是
  broken: '0',
  broken1Remark: '',
  // 管盖松动情况 0-否 / 1-是
  loose: '0',
  loose1Remark: '',
  // 是否合格 0-否 / 1-是
  qualified: '0',
  qualified0Remark: ''
}

// 分页-样本管理
export function sampleSubpkgPageApi(params) {
  // return request({
  //   url: `/cspapi/backend/device/manage/page`,
  //   method: 'get',
  //   params
  // })
  const list = [
    {
      name: '戴佳红',
      sex: '0',
      age: 72,
      status: '2',
      phone: '13135576224',
      idCard: '270344299210102987',
      qcForm: sampleQcForm,
      collectionForm: detailCollectData,
      collection: { blood: '15ml', pee: '15ml', shit: '-' }
    },
    {
      name: '元素贞',
      sex: '0',
      age: 68,
      status: '2',
      phone: '13135576224',
      idCard: '270344299210102987',
      qcForm: sampleQcForm,
      collectionForm: detailCollectData,
      collection: { blood: '15ml', pee: '-', shit: '20g', tissue: '15ml', other: '-' }
    },
    {
      name: '张瑞红',
      sex: '1',
      age: 71,
      status: '3',
      phone: '13135576224',
      idCard: '270344299210102987',
      qcForm: sampleQcForm,
      collectionForm: detailCollectData,
      collection: { blood: '15ml', pee: '15ml', shit: '20g' }
    },
    {
      name: '张大大',
      sex: '1',
      age: 64,
      status: '3',
      phone: '13135576224',
      idCard: '270344299210102987',
      qcForm: sampleQcForm,
      collectionForm: detailCollectData,
      collection: { blood: '15ml', pee: '15ml', shit: '20g', tissue: '15ml' }
    },
    {
      name: '李谷谷',
      sex: '0',
      age: 88,
      status: '3',
      phone: '13135576224',
      idCard: '270344299210102987',
      qcForm: sampleQcForm,
      collectionForm: detailCollectData,
      collection: { blood: '15ml', pee: '15ml', shit: '20g' }
    },
    {
      name: '尚书',
      sex: '1',
      age: 77,
      status: '4',
      phone: '13135576224',
      idCard: '270344299210102987',
      qcForm: sampleQcForm,
      collectionForm: detailCollectData,
      collection: { blood: '15ml', pee: '15ml', shit: '20g' }
    },
    {
      name: '萧大人',
      sex: '1',
      age: 66,
      status: '5',
      phone: '13135576224',
      idCard: '270344299210102987',
      qcForm: sampleQcForm,
      collectionForm: detailCollectData,
      collection: { blood: '15ml', pee: '15ml', shit: '20g' }
    },
    {
      name: '萧小小',
      sex: '0',
      age: 69,
      status: '6',
      phone: '13135576224',
      idCard: '270344299210102987',
      qcForm: sampleQcForm,
      collectionForm: detailCollectData,
      collection: { blood: '15ml', pee: '15ml', shit: '20g' }
    }
  ]
  return new Promise((resolve) => {
    resolve({
      data: {
        list
      }
    })
  })
}

const sampleSubFormData = {
  blood: {
    plasmaCount: 3,
    plasmaVolume: 50,
    plasmaUnit: 'μL/管',
    serumCount: 3,
    serumVolume: 50,
    serumUnit: 'μL/管',
    redcellCount: 3,
    redcellVolume: 50,
    redcellUnit: 'μL/管'
  },
  pee: {
    count: 1,
    volume: 15,
    unit: 'mL/管'
  },
  shit: {
    count: 1,
    volume: 15,
    unit: 'mL/管'
  },
  tissue: {
    count: 1,
    volume: 15,
    unit: 'mL/管'
  },
  other: {
    // 类型
    count: 1,
    volume: 15,
    unit: 'mL/管'
  }
}

// 分页-样本质控
export function sampleQcPageApi(params) {
  // return request({
  //   url: `/cspapi/backend/device/manage/page`,
  //   method: 'get',
  //   params
  // })
  const list = [
    {
      name: '戴佳红',
      sex: '0',
      age: 72,
      status: '3',
      phone: '13135576224',
      idCard: '270344299210102987',
      collectionForm: detailCollectData,
      subForm: sampleSubFormData,
      collection: { blood: '15ml', pee: '15ml', shit: '-' }
    },
    {
      name: '元素贞',
      sex: '0',
      age: 68,
      status: '3',
      phone: '13135576224',
      idCard: '270344299210102987',
      collectionForm: detailCollectData,
      subForm: sampleSubFormData,
      collection: { blood: '15ml', pee: '-', shit: '20g', tissue: '15ml', other: '-' }
    },
    {
      name: '张瑞红',
      sex: '1',
      age: 71,
      status: '3',
      phone: '13135576224',
      idCard: '270344299210102987',
      collectionForm: detailCollectData,
      subForm: sampleSubFormData,
      collection: { blood: '15ml', pee: '15ml', shit: '20g' }
    },
    {
      name: '张大大',
      sex: '1',
      age: 64,
      status: '4',
      phone: '13135576224',
      idCard: '270344299210102987',
      collectionForm: detailCollectData,
      subForm: sampleSubFormData,
      collection: { blood: '15ml', pee: '15ml', shit: '20g', tissue: '15ml' }
    },
    {
      name: '李谷谷',
      sex: '0',
      age: 88,
      status: '4',
      phone: '13135576224',
      idCard: '270344299210102987',
      collectionForm: detailCollectData,
      subForm: sampleSubFormData,
      collection: { blood: '15ml', pee: '15ml', shit: '20g' }
    },
    {
      name: '尚书',
      sex: '1',
      age: 77,
      status: '4',
      phone: '13135576224',
      idCard: '270344299210102987',
      collectionForm: detailCollectData,
      subForm: sampleSubFormData,
      collection: { blood: '15ml', pee: '15ml', shit: '20g' }
    },
    {
      name: '萧大人',
      sex: '1',
      age: 66,
      status: '5',
      phone: '13135576224',
      idCard: '270344299210102987',
      collectionForm: detailCollectData,
      subForm: sampleSubFormData,
      collection: { blood: '15ml', pee: '15ml', shit: '20g' }
    },
    {
      name: '萧小小',
      sex: '0',
      age: 69,
      status: '5',
      phone: '13135576224',
      idCard: '270344299210102987',
      collectionForm: detailCollectData,
      subForm: sampleSubFormData,
      collection: { blood: '15ml', pee: '15ml', shit: '20g' }
    }
  ]
  return new Promise((resolve) => {
    resolve({
      data: {
        list
      }
    })
  })
}

//
export function addManageDeviceApi(data) {
  return request({
    url: '/cspapi/backend/device/manage/add',
    method: 'post',
    data
  })
}
//
export function editManageDeviceApi(data) {
  return request({
    url: '/cspapi/backend/device/manage',
    method: 'put',
    data
  })
}
//
export function batchOutboundApi(data) {
  return request({
    url: '/cspapi/backend/device/manage/batchOutbound',
    method: 'put',
    data
  })
}
//
export function deleteDeviceApi(deviceId) {
  return request({
    url: `/cspapi/backend/device/manage/${deviceId}`,
    method: 'delete'
  })
}
//
export function deviceDetailApi(deviceId) {
  return request({
    url: `/cspapi/backend/device/manage/${deviceId}`,
    method: 'get'
  })
}
