<template>
  <div v-loading="loading" class="dataReportPage public_v2">
    <div>
      <span class="fontSize_14 color464242 searchTxt">时间选择：</span>
      <el-date-picker
        v-model="searchData.year"
        class="public_inputHeight32 public_datePickerHeight32 marginTop10 public_datePicker"
        type="year"
        placeholder="请选择年份"
        value-format="yyyy"
        format="yyyy"
        :clearable="false"
        @change="handleYearChange"
      />

      <el-button
        v-if="isHealthCommissio"
        v-has="'dataReport:edit'"
        class="fr public_button fontSize_14 bgColor_42C9A300B2DC flex_center"
        @click="saveData"
      >
        <span>一键保存</span>
      </el-button>
    </div>
    <div class="reportTable">
      <el-table :data="tableData" :row-class-name="rowClassName" style="width: 100%" border>
        <el-table-column align="center" label="单位/机构" prop="departName" />
        <el-table-column align="center" label="常住人口数" prop="popularCnt">
          <template slot-scope="scope">
            <el-input
              v-if="scope.row.editing && isHealthCommissio"
              v-model="scope.row.popularCnt"
              size="mini"
              @blur="handleEdit(scope.row)"
            />
            <span v-else @click="handleCellClick(scope.row)">{{ scope.row.popularCnt || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="高血压应管理任务数" prop="gxyCnt">
          <template slot-scope="scope">
            <el-input
              v-if="scope.row.editing && isHealthCommissio"
              v-model="scope.row.gxyCnt"
              size="mini"
              @blur="handleEdit(scope.row)"
            />
            <span v-else @click="handleCellClick(scope.row)">{{ scope.row.gxyCnt || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="糖尿病应管理任务数" prop="tnbCnt">
          <template slot-scope="scope">
            <el-input
              v-if="scope.row.editing && isHealthCommissio"
              v-model="scope.row.tnbCnt"
              size="mini"
              @blur="handleEdit(scope.row)"
            />
            <span v-else @click="handleCellClick(scope.row)">{{ scope.row.tnbCnt || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="慢阻肺应管理任务数" prop="copdCnt">
          <template slot-scope="scope">
            <el-input
              v-if="scope.row.editing && isHealthCommissio"
              v-model="scope.row.copdCnt"
              size="mini"
              @blur="handleEdit(scope.row)"
            />
            <span v-else @click="handleCellClick(scope.row)">{{ scope.row.copdCnt || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="老年人管理任务数" prop="oldManCnt">
          <template slot-scope="scope">
            <el-input
              v-if="scope.row.editing && isHealthCommissio"
              v-model="scope.row.oldManCnt"
              size="mini"
              @blur="handleEdit(scope.row)"
            />
            <span v-else @click="handleCellClick(scope.row)">{{ scope.row.oldManCnt || 0 }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { getListDataApi, saveListDataApi } from '@/api/system'
import { mapGetters } from 'vuex'
import { cloneDeep } from 'lodash'

export default {
  name: 'DataReport',
  data() {
    const time = new Date().getFullYear()
    return {
      loading: false,
      tableData: [],
      searchData: {
        year: `${time}`
      }
    }
  },
  computed: {
    ...mapGetters(['roles']),
    isHealthCommissio() {
      // 是否卫建委
      return this.roles.some((i) => i.roleCode === 'Health Commissio' || i.roleCode === 'admin')
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    getTableData() {
      this.loading = true
      getListDataApi({
        year: this.searchData.year
      })
        .then((res) => {
          if (res.code === 200) {
            this.tableData = res.data
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    rowClassName({ row }) {
      return `row-${row.departCode}`
    },

    handleYearChange(year) {
      this.searchData.year = year
      this.getTableData()
    },
    handleCellClick(row) {
      if (!this.$store.state.user.buttonPermissions.includes('dataReport:edit')) {
        return
      }
      this.tableData.forEach((item) => {
        if (item.departCode !== row.departCode) {
          this.$set(item, 'editing', false)
        }
      })
      // 设置当前行为编辑状态
      this.$set(row, 'editing', true)
      this.$nextTick(() => {
        const input = document.querySelector(`.row-${row.departCode} .el-input__inner`)
        input.focus()
      })
    },
    handleEdit(row) {
      if (!this.validateData(row)) {
        this.$message.error('请输入有效数字')
      }
    },
    validateData(row) {
      const fields = ['popularCnt', 'gxyCnt', 'tnbCnt', 'copdCnt', 'oldManCnt']
      fields.forEach((field) => {
        const value = row[field]
        if (isNaN(value) || value < 0) {
          row[field] = 0
        }
      })
      return true
    },
    // 保存数据
    saveListDataApiFn(tableDataTemp) {
      saveListDataApi({
        year: this.searchData.year,
        departPeopleList: tableDataTemp
      })
        .then((res) => {
          if (res.code === 200) {
            this.$message.success('保存成功')
            this.getTableData()
          }
        })
        .finally(() => {
          this.loading = false
        })
    },

    async saveData() {
      const tableDataTemp = cloneDeep(this.tableData)

      const customTotal = tableDataTemp.reduce(
        (acc, curr, index) => {
          if (index === 0) return acc // 跳过第一项，从第二项开始计算
          acc.popularCnt += Number(curr.popularCnt || 0)
          acc.gxyCnt += Number(curr.gxyCnt || 0)
          acc.tnbCnt += Number(curr.tnbCnt || 0)
          acc.copdCnt += Number(curr.copdCnt || 0)
          acc.oldManCnt += Number(curr.oldManCnt || 0)
          return acc
        },
        { popularCnt: 0, gxyCnt: 0, tnbCnt: 0, copdCnt: 0, oldManCnt: 0 }
      )

      const firstRow = tableDataTemp[0]

      const errorMessages = {
        popularCnt: '常住人口数总数不能大于区总人口数',
        gxyCnt: '高血压应管理任务数总数不能大于区高血压应管理任务数',
        tnbCnt: '糖尿病应管理任务数总数不能大于区糖尿病应管理任务数',
        copdCnt: '慢阻肺应管理任务数总数不能大于区慢阻肺应管理任务数',
        oldManCnt: '老年人管理任务数总数不能大于区老年人管理任务数'
      }

      const warningMessages = {
        popularCnt: '常住人口数总数小于区总人口数；',
        gxyCnt: '高血压应管理任务数总数小于区高血压应管理任务数；',
        tnbCnt: '糖尿病应管理任务数总数小于区糖尿病应管理任务数；',
        copdCnt: '慢阻肺应管理任务数总数小于区慢阻肺应管理任务数；',
        oldManCnt: '老年人管理任务数总数小于区老年人管理任务数；'
      }

      const warningMessageExtra = []

      for (const key in customTotal) {
        if (customTotal[key] > firstRow[key]) {
          this.$message.error(errorMessages[key])
          return
        }
        if (customTotal[key] < firstRow[key]) {
          warningMessageExtra.push(warningMessages[key])
        }
      }

      if (warningMessageExtra.length > 0) {
        warningMessageExtra.push('是否继续保存？')
        console.log('warningMessageExtra', warningMessageExtra)
        this.$confirm(warningMessageExtra.join('<br/>'), '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          customClass: 'my-custom-confirm-reportFill',
          dangerouslyUseHTMLString: true,
          lockScroll: false
        }).then(() => {
          this.saveListDataApiFn(tableDataTemp)
        })
        return
      }
      this.saveListDataApiFn(tableDataTemp)
    }
  }
}
</script>
<style lang="scss" scoped>
.dataReportPage {
  padding: 16px;
  .reportTable {
    margin-top: 16px;
    ::v-deep .el-table__header {
      height: 40px;
    }
    .el-table {
      ::v-deep .el-input {
        width: 100%;
      }

      ::v-deep .el-input__inner {
        padding: 0 5px;
        text-align: center;
      }

      span {
        display: block;
        width: 100%;
        height: 100%;
        cursor: pointer;

        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }
}
::v-deep(.my-custom-confirm) {
  width: 500px !important;
  max-width: 80vw;
}
</style>
