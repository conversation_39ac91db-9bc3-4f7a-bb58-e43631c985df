<template>
  <el-form-item :label="item.label" :prop="item.prop">
    <div :class="item.class">
      <el-checkbox-group v-model="localValue" :disabled="disabled" @change="handleChange">
        <div v-for="opt in item.options" :key="opt.value" style="display: inline-flex; align-items: center">
          <el-checkbox :label="opt.value">{{ opt.label }}</el-checkbox>
          <el-select
            v-if="localValue.includes(opt.value) && opt.type === 'select' && opt.prop"
            v-model="formData[opt.prop]"
            :disabled="disabled"
            style="width: 200px; margin-right: 10px"
            placeholder="请选择"
          >
            <el-option
              v-for="option in item.extraOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>
          <el-input
            v-if="localValue.includes(opt.value) && item.remark"
            :key="`${item.prop}${opt.value}`"
            v-model="formData[`${item.prop}Remark${opt.value}`]"
            :disabled="disabled"
            style="width: 200px; margin-right: 10px"
            placeholder="请输入"
          />
        </div>
      </el-checkbox-group>
    </div>
  </el-form-item>
</template>

<script>
export default {
  name: 'CheckboxGroupField',
  inheritAttrs: false,
  model: {
    prop: 'value',
    event: 'input'
  },
  props: ['item', 'value', 'formData', 'disabled'],
  computed: {
    localValue: {
      get() {
        return this.value || []
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  },
  methods: {
    handleChange(value) {
      if (this.item.mutuallyExclusive) {
        const noneValue = '1'
        let newValue = [...value]

        const currentValue = newValue[newValue.length - 1]

        if (currentValue === noneValue) {
          newValue = [noneValue]
        } else {
          newValue = newValue.filter((v) => v !== noneValue)
        }

        this.$emit('input', newValue)
      }
      if (this.item.remark) {
        this.item.options.forEach((opt) => {
          if (!value.includes(opt.value)) {
            if (this.formData && this.formData[`${this.item.prop}Remark${opt.value}`] !== undefined) {
              this.formData[`${this.item.prop}Remark${opt.value}`] = ''
            }
          }
        })
      }
    }
  }
}
</script>
