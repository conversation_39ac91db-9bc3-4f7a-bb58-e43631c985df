<!-- 病情诊断 -->
<template>
  <div class="diagnosis-of-illness-report">
    <h2 style="color: #4bc0f1">病情诊断</h2>

    <div v-if="reportInfo.diseaseDiagnosisDetailVO && reportInfo.diseaseDiagnosisDetailVO.tnbResult" class="content">
      <div class="title">
        <span>糖尿病诊断结果：</span>
        <span>
          {{
            reportInfo.diseaseDiagnosisDetailVO && reportInfo.diseaseDiagnosisDetailVO.tnbResult
              ? tnbRadioList.find(item => item.value === reportInfo.diseaseDiagnosisDetailVO.tnbResult).label
              : '-'
          }}
        </span>
      </div>
      <div class="content-table">
        <el-table
          :data="reportInfo.diseaseDiagnosisDetailVO && reportInfo.diseaseDiagnosisDetailVO.tnbHrsVOList"
          style="width: 100%"
          border
        >
          <el-table-column align="center" prop="itemName" label="名称" />
          <el-table-column align="center" prop="itemValue" label="测量值" />
          <!-- <el-table-column align="center" prop="range" label="参考值" /> -->
        </el-table>
      </div>
    </div>

    <div v-if="reportInfo.diseaseDiagnosisDetailVO && reportInfo.diseaseDiagnosisDetailVO.gxyResult" class="content">
      <div class="title">
        <span>高血压诊断结果：</span>
        <span>
          {{
            reportInfo.diseaseDiagnosisDetailVO && reportInfo.diseaseDiagnosisDetailVO.gxyResult
              ? gxyRadioList.find(item => item.value === reportInfo.diseaseDiagnosisDetailVO.gxyResult).label
              : '-'
          }}
        </span>
      </div>
      <div class="content-table">
        <el-table
          :data="reportInfo.diseaseDiagnosisDetailVO && reportInfo.diseaseDiagnosisDetailVO.gxyHrsVOList"
          style="width: 100%"
          border
        >
          <el-table-column align="center" prop="itemName" label="名称" />
          <el-table-column align="center" prop="spValue" label="收缩压" />
          <el-table-column align="center" prop="dpValue" label="舒张压" />
          <!-- <el-table-column align="center" prop="range" label="参考值" /> -->
        </el-table>
      </div>
    </div>

    <div v-if="reportInfo.diseaseDiagnosisDetailVO && reportInfo.diseaseDiagnosisDetailVO.copdResult" class="content">
      <div class="title">
        <span>慢阻肺诊断结果：</span>
        <span>
          {{
            reportInfo.diseaseDiagnosisDetailVO && reportInfo.diseaseDiagnosisDetailVO.copdResult
              ? copdRadioList.find(item => item.value === reportInfo.diseaseDiagnosisDetailVO.copdResult).label
              : '-'
          }}
        </span>
      </div>
      <div class="content-table">
        <el-table
          :data="reportInfo.diseaseDiagnosisDetailVO && reportInfo.diseaseDiagnosisDetailVO.copdHrsVOList"
          style="width: 100%"
          border
        >
          <el-table-column align="center" prop="itemName" label="名称" />
          <el-table-column align="center" prop="itemValue" label="检查结果/分值" />
          <!-- <el-table-column align="center" prop="range" label="参考值" /> -->
        </el-table>
      </div>
    </div>

    <div v-if="reportInfo.diseaseDiagnosisDetailVO && reportInfo.diseaseDiagnosisDetailVO.fcResult" class="content">
      <div class="title">
        <span>房颤诊断结果：</span>
        <span>
          {{
            reportInfo.diseaseDiagnosisDetailVO && reportInfo.diseaseDiagnosisDetailVO.fcResult
              ? fcRadioList.find(item => item.value === reportInfo.diseaseDiagnosisDetailVO.fcResult).label
              : '-'
          }}
        </span>
      </div>
      <div class="content-table">
        <el-table
          :data="reportInfo.diseaseDiagnosisDetailVO && reportInfo.diseaseDiagnosisDetailVO.fcHrsVOList"
          style="width: 100%"
          border
        >
          <el-table-column align="center" prop="itemName" label="名称" />
          <el-table-column align="center" prop="itemValue" label="检查结果" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import {
  tnbRadioList,
  gxyRadioList,
  copdRadioList,
  fcRadioList
} from '@/views/receptionCenter/patientReception/component/diagnosisofIllness.js'

export default {
  name: 'DiagnosisOfIllnessReport',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tnbRadioList,
      gxyRadioList,
      copdRadioList,
      fcRadioList
    }
  }
}
</script>
<style lang="scss" scoped>
.diagnosis-of-illness-report {
  padding: 10px;
  .content {
    margin-top: 16px;
    .title {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 16px;
      font-weight: bold;
    }
    .content-table {
      margin-top: 10px;
    }
  }
}
</style>
