export default {
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 20
      },
      // 行数据
      record: {},
      // 排序参数
      sortParams: {},
      // 表格数据
      tableData: [],
      // 加载状态
      loading: false,
      // 选中的行
      selectedRows: [],
      // 总条数
      total: 0,
      // 新增弹窗
      addDialogVisible: false,
      // 是否显示分页
      showPagination: true,
      // 禁用
      disableSubmit: false
    }
  },

  methods: {
    // 获取表格数据
    async fetchData() {
      try {
        this.loading = true
        const params = {
          ...this.queryParams,
          ...this.sortParams
        }
        const res = await this.getTableList(params)
        this.tableData = this.showPagination ? res.data.list : res.data

        this.total = res.data.total || 0
      } catch (error) {
        console.error('获取数据失败:', error)
        this.$message.error('获取数据失败')
      } finally {
        this.loading = false
      }
    },

    // 重置查询参数
    handleReset() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20
      }
      this.sortParams = {}
      this.fetchData()
    },

    // 处理查询
    handleSearch() {
      this.queryParams.pageNo = 1
      this.fetchData()
    },

    // 处理排序
    handleSortChange(sort) {
      this.sortParams = {
        sortField: sort.prop,
        sortOrder: sort.order
      }
      this.fetchData()
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 处理分页变化
    handlePaginationChange({ pageNo, pageSize }) {
      this.queryParams.pageNo = pageNo
      this.queryParams.pageSize = pageSize
      this.fetchData()
    },

    // 刷新表格
    refreshTable() {
      this.fetchData()
    },

    // 新增
    handleAdd() {
      this.addDialogVisible = true
    },

    // 删除
    async handleConfirmDelete({ params, deleteApi, message, afterSuccess }) {
      try {
        await this.$confirm(message, '操作确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'error',
          iconClass: 'el-icon-delete-solid',
          customClass: 'remove-confirm',
          closeOnClickModal: false,
          confirmButtonClass: 'bgred'
        })

        const res = await deleteApi(params)
        if (res.code === 200) {
          this.$message({
            type: 'success',
            message: '删除成功'
          })
          if (typeof afterSuccess === 'function') {
            afterSuccess()
          } else {
            this.queryParams.pageNo = 1
            this.fetchData()
          }
        }
      } catch (error) {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      }
    }
  },

  created() {
    this.fetchData()
  }
}
