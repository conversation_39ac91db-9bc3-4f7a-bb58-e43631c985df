const envModule = require('@/utils/env')

// const taizhouLogo = require('@/assets/login_images/lgoin_v3_logo.png')
const wigroupLogo = require('@/assets/logo/logo-wigroup.png')
const taizhouQR = require('@/assets/logo/qrcode-taizhou.jpg')

const _env = envModule.getEnv()

console.log('loginEnum _env', _env)

const testMap = {
  TITLE: '',
  TEXT: '基层筛防工作站',
  LOGO: wigroupLogo,
  QRURL: taizhouQR,
  QRTXT: '关注微信公众号',
  QRTOOLTXT: '关注泰州人民医院',
  TELEP: '0523-962120'
}
const starcdsMap = {
  TITLE: '',
  TEXT: '基层筛防工作站',
  LOGO: wigroupLogo,
  QRURL: taizhouQR,
  QRTXT: '关注微信公众号',
  QRTOOLTXT: '关注泰州人民医院',
  TELEP: '0523-962120'
}

const settingMap = {
  //  生产环境配置
  prod: starcdsMap,
  test: testMap
}
export const loginSetting = settingMap[_env]
