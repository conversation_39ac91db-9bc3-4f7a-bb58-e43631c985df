<!-- 高危筛查 -->
<template>
  <div v-loading="$store.state.receptionWorkbench.loading" class="high-risk-screening">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="体查" name="physicalExamination" />
      <el-tab-pane label="慢病病种" name="chronicDiseaseTypes" />
    </el-tabs>
    <div v-show="activeName === 'physicalExamination'" class="physicalExamination">
      <div class="physicalExaminationForm">
        <el-form ref="physicalExaminationFormRef" :model="physicalExaminationInfo" label-width="125px" :rules="rules">
          <el-row style="margin: 16px" :gutter="20">
            <flag-component title="体查" />
            <el-col v-for="(item, index) in physicalExaminationForm" :key="index" :span="6">
              <el-form-item :label="item.label" :prop="item.prop">
                <el-input
                  v-model="physicalExaminationInfo[item.prop]"
                  :disabled="item.disabled"
                  @input="val => handleInput(val, item, 'physicalExamination')"
                >
                  <template v-if="item.append" slot="append">{{ item.append }}</template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-row style="margin: 16px" :gutter="20">
          <div style="display: flex; align-items: center; justify-content: space-between">
            <flag-component title="人体成分" />
            <!-- <device-icon device-type="bodyComposition" :socket-connect="true" /> -->
          </div>
          <el-form
            ref="bodyCompositionFormRef"
            :model="bodyCompositionInfo"
            label-width="125px"
            :rules="bodyCompositionRules"
          >
            <el-col v-for="(item, index) in bodyCompositionForm" :key="index" :span="6">
              <el-form-item :label="item.label" :prop="item.prop">
                <el-input
                  v-model="bodyCompositionInfo[item.prop]"
                  @input="val => handleInput(val, item, 'bodyComposition')"
                >
                  <template v-if="item.append" slot="append">{{ item.append }}</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="体型判断：">
                <el-select v-model="bodyCompositionInfo.bodyType" style="width: 100%">
                  <el-option
                    v-for="item in bodyShapeOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="综合评分：" prop="sco">
                <el-input
                  v-model="bodyCompositionInfo.sco"
                  @input="val => handleInput(val, { prop: 'sco' }, 'bodyComposition')"
                />
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </div>
    </div>
    <div v-show="activeName === 'chronicDiseaseTypes'" class="chronicDiseaseTypes">
      <questionnaire-tags :questionnaire-list="questionnaireList" @click="handleQuestionnaireClick" />
      <div v-show="questionnaireId === 'tnb'" class="diabetes">
        <chronic-disease-diabetes ref="chronicDiseaseDiabetesRef" />
      </div>
      <div v-show="questionnaireId === 'gxy'" class="hypertension">
        <chronic-diseases-hype ref="chronicDiseasesHypeRef" />
      </div>
      <div v-show="questionnaireId === 'COPD'" class="chronicObstructivePulmonaryDisease">
        <chronic-disease-copd ref="chronicDiseaseCopdRef" />
      </div>
      <div v-show="questionnaireId === 'fangchan'" class="atrialFibrillation">
        <chronic-disease-fibrillation ref="chronicDiseaseFibrillationRef" />
      </div>
    </div>
  </div>
</template>

<script>
import { physicalExaminationForm, bodyCompositionForm } from './highRriskScreening.js'
import { saveHighRiskScreening, saveDiagnosisOfIllness } from '@/api/receptionWorkbench'
import { cloneDeep } from 'lodash'
import FlagComponent from '@/components/flagComponent/index.vue'
import QuestionnaireTags from '@/components/questionnaireTags/index.vue'
import ChronicDiseaseDiabetes from '../componentDetail/chronicDiseaseDiabetes.vue'
import ChronicDiseasesHype from '../componentDetail/chronicDiseasesHype.vue'
import ChronicDiseaseCopd from '../componentDetail/chronicDiseaseCopd.vue'
import ChronicDiseaseFibrillation from '../componentDetail/chronicDiseaseFibrillation.vue'
// import DeviceIcon from '@/components/deviceIcon/index.vue'

export default {
  name: 'HighRiskScreening',
  components: {
    FlagComponent,
    QuestionnaireTags,
    // DeviceIcon,
    ChronicDiseaseDiabetes, // 慢病病种-糖尿病
    ChronicDiseasesHype, // 慢病病种-高血压
    ChronicDiseaseCopd, // 慢病病种-慢阻肺
    ChronicDiseaseFibrillation // 慢病病种-房颤
  },
  props: {
    historyId: {
      type: String,
      default: ''
    }
  },
  data() {
    const questionnaireList = cloneDeep(this.$store.state.receptionWorkbench.receptionWorkbenchData.diseaseList)

    const physicalExaminationInfo = physicalExaminationForm.reduce((acc, cur) => {
      acc[cur.prop] = ''
      return acc
    }, {})

    const bodyCompositionInfo = bodyCompositionForm.reduce((acc, cur) => {
      acc[cur.prop] = ''
      return acc
    }, {})
    bodyCompositionInfo.sco = ''

    return {
      activeName: 'physicalExamination',
      physicalExaminationInfo,
      bodyCompositionInfo,
      bodyShapeOptions: [
        { value: 0, label: '低脂肪低体重型' },
        { value: 1, label: '低脂肪肌肉型' },
        { value: 2, label: '运动员型' },
        { value: 3, label: '低肌肉低体重型' },
        { value: 4, label: '标准体型' },
        { value: 5, label: '高体重肌肉型' },
        { value: 6, label: '隐性肥胖型' },
        { value: 7, label: '脂肪过量型' },
        { value: 8, label: '肥胖体型' }
      ],
      questionnaireId: '',
      physicalExaminationForm,
      bodyCompositionForm,
      questionnaireList
    }
  },
  computed: {
    rules() {
      const rules = {}
      this.physicalExaminationForm.forEach((item) => {
        rules[item.prop] = [{ required: item.required, message: item.label.slice(0, -1), trigger: 'blur' }]
      })
      return rules
    },

    bodyCompositionRules() {
      const rules = {
        // sco: [{ required: true, message: '综合评分', trigger: 'blur' }]
      }
      this.bodyCompositionForm.forEach((item) => {
        rules[item.prop] = [{ required: item.required, message: item.label.slice(0, -1), trigger: 'blur' }]
      })
      return rules
    }
  },
  created() {
    this.handleInitData()
  },
  methods: {
    async handleInitData() {
      const data = await this.$store.dispatch('receptionWorkbench/getHighRiskScreeningData', {
        rrId: this.historyId || this.$route.query.id
      })
      if (data.itemList && data.itemList.length > 0) {
        // 体查 & 人体成分
        this.initData(data.itemList)

        // 慢病病种-糖尿病
        this.$refs.chronicDiseaseDiabetesRef.initData(data.itemList)
        // 慢病病种-高血压
        this.$refs.chronicDiseasesHypeRef.initData(data.itemList)
        // 慢病病种-慢阻肺
        this.$refs.chronicDiseaseCopdRef.initData(data.itemList)
        // 慢病病种-房颤
        this.$refs.chronicDiseaseFibrillationRef.initData(data.itemList)
      }
    },

    initData(data) {
      const physicalExaminationData = data.find((item) => item.itemCode === 'BODY_INFO') || {}
      const bodyCompositionData = data.find((item) => item.itemCode === 'BODY_COMPOSITION') || {}
      this.physicalExaminationInfo = {
        itemCode: physicalExaminationData.itemCode,
        itemId: physicalExaminationData.id,
        itemDetailId: physicalExaminationData.data && physicalExaminationData.data.id,
        ...this.physicalExaminationForm.reduce((acc, cur) => {
          acc[cur.prop] = physicalExaminationData.data && physicalExaminationData.data[cur.prop]
          return acc
        }, {})
      }

      this.bodyCompositionInfo = {
        itemCode: bodyCompositionData.itemCode,
        itemId: bodyCompositionData.id,
        itemDetailId: bodyCompositionData.data && bodyCompositionData.data.id,
        ...this.bodyCompositionForm.reduce((acc, cur) => {
          acc[cur.prop] = bodyCompositionData.data && bodyCompositionData.data[cur.prop]
          return acc
        }, {}),
        sco: bodyCompositionData.data && bodyCompositionData.data.sco,
        bodyType: bodyCompositionData.data && bodyCompositionData.data.bodyType
      }
    },

    handleClick(item) {
      this.questionnaireId = this.questionnaireList[0].id
    },

    handleQuestionnaireClick(item) {
      this.questionnaireId = item.id
    },

    handleInput(val, item, type) {
      if (item.prop === 'height' && this.physicalExaminationInfo.weight) {
        this.physicalExaminationInfo.bmi = (this.physicalExaminationInfo.weight / (val / 100) ** 2).toFixed(2)
      }

      if (item.prop === 'weight' && this.physicalExaminationInfo.height) {
        this.physicalExaminationInfo.bmi = (val / (this.physicalExaminationInfo.height / 100) ** 2).toFixed(2)
      }

      const onlyNumber = this.$validateOnlyNumber(val)
      if (type === 'physicalExamination') {
        this.$set(this.physicalExaminationInfo, item.prop, onlyNumber) // 用于校验只能是数字
      }
      if (type === 'bodyComposition') {
        this.$set(this.bodyCompositionInfo, item.prop, onlyNumber) // 用于校验只能是数字
      }
    },

    async handleSave() {
      const result = {
        name: '体查',
        success: false,
        data: {
          physicalExamination: {
            ...this.physicalExaminationInfo,
            name: '体查',
            itemCode: 'BODY_INFO'
          },
          bodyComposition: {
            ...this.bodyCompositionInfo,
            sco: this.bodyCompositionInfo.sco,
            name: '人体成分',
            itemCode: 'BODY_COMPOSITION'
          }
        }
      }
      try {
        const valid = await this.$refs.physicalExaminationFormRef.validate()
        result.success = valid
      } catch (err) {
        console.warn('体查校验异常', err)
        result.success = false
      }
      try {
        const valid = await this.$refs.bodyCompositionFormRef.validate()
        result.success = valid
      } catch (err) {
        console.warn('人体成分校验异常', err)
        result.success = false
      }
      return result
    },

    async handleHighRiskScreeningSave(type) {
      const rrId = this.$route.query.id
      const screeningId =
        (this.$store.getters.highRiskScreeningData && this.$store.getters.highRiskScreeningData.highRiskScreening.id) ||
        ''
      let status =
        (this.$store.getters.highRiskScreeningData &&
          this.$store.getters.highRiskScreeningData.highRiskScreening.status) ||
        1

      if (type === 'skip') {
        status = 9
        await saveHighRiskScreening({ rrId, id: screeningId, status })
        // 这里要保存一下病情诊断
        await saveDiagnosisOfIllness({ rrId })
        this.$store.dispatch('receptionWorkbench/getHighRiskScreeningData', { rrId })
        return status
      }

      if (type === 'edit') {
        status = 1
        await saveHighRiskScreening({ rrId, id: screeningId, status })
        this.$store.dispatch('receptionWorkbench/getHighRiskScreeningData', { rrId })
        return status
      }

      if (status === 9) {
        return status
      }

      // ==== 1. 保存各部分数据 ====
      const resultMap = {
        physical: await this.handleSave(),
        tnb: await this.$refs.chronicDiseaseDiabetesRef.handleSave(),
        gxy: await this.$refs.chronicDiseasesHypeRef.handleSave(),
        copd: await this.$refs.chronicDiseaseCopdRef.handleSave(),
        fc: await this.$refs.chronicDiseaseFibrillationRef.handleSave()
      }

      // ==== 2. 默认处理未参与问卷 ====
      const questionnaireIds = new Set(
        this.questionnaireList.map((item) => {
          if (item.id === 'COPD') {
            return 'copd'
          }
          if (item.id === 'fangchan') {
            return 'fc'
          }
          return item.id
        })
      )

      for (const [key, result] of Object.entries(resultMap)) {
        if (!questionnaireIds.has(key) && key !== 'physical') {
          result.success = true
          result.data = {}
        }
      }

      const projectList = {
        ...resultMap.physical.data,
        ...resultMap.tnb.data,
        ...resultMap.gxy.data,
        ...resultMap.copd.data,
        ...resultMap.fc.data
      }

      const params = {
        rrId,
        id: screeningId,
        status,
        bloodSugarScreening: projectList.bloodSugarScreening,
        itemDetailList: Object.keys(projectList).reduce((acc, cur) => {
          if (cur !== 'bloodSugarScreening') {
            acc.push(projectList[cur])
          }
          return acc
        }, [])
      }

      const warnings = []
      for (const [, result] of Object.entries(resultMap)) {
        if (!result.success) {
          warnings.push(`${result.name}存在必填未填项！`)
        }
      }

      if (warnings.length) {
        if (type === 'next') {
          for (const msg of warnings) {
            this.$message.warning(msg)
            // eslint-disable-next-line
            await new Promise(resolve => setTimeout(resolve, 500))
          }
        }
        status = 1
      } else {
        status = 5
      }

      const res = await saveHighRiskScreening({
        ...params,
        status,
        rrId,
        id: screeningId
      })

      if (res.code === 200) {
        if (type === 'save') {
          this.$message.success('保存成功')
        }
        this.handleInitData()
        return status
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.high-risk-screening {
  ::v-deep .el-input-group__append {
    width: 50px !important;
    text-align: center;
    padding: 0 10px;
  }
}
.chronicDiseaseTypes {
  .diabetes,
  .hypertension,
  .chronicObstructivePulmonaryDisease,
  .atrialFibrillation {
    padding: 16px;
  }
}
</style>
