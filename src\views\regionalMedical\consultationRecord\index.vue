<template>
  <div class="consultation-record">
    <el-card class="consultation-record-search">
      <el-form :model="queryParams" label-width="120px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="患者姓名：">
              <el-input v-model="queryParams.keyword" placeholder="请输入患者姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="会诊时间：">
              <el-date-picker
                v-model="queryParams.timeRange"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="consultation-record-table">
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 50px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #disease="{ row }">
          <ChronicDiseaseType :record="row" />
        </template>
        <template #operation="{ row }">
          <el-button type="text" @click="handleDetail(row)">查看</el-button>
        </template>
      </base-table>
    </el-card>

    <consultation-record-modal ref="consultationRecordModal" />
  </div>
</template>

<script>
import { getConsultationRecordsApi, getConsultationVideoApi } from '@/api/remoteConsultation'
import { mapState } from 'vuex'
import BaseTable from '@/components/BaseTable/index.vue'
import ChronicDiseaseType from '@/components/chronicDiseaseType/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ConsultationRecordModal from '@/components/remoteConsultation/component/consultationRecordModal.vue'

export default {
  name: 'ConsultationRecord',
  components: {
    BaseTable,
    ChronicDiseaseType,
    ConsultationRecordModal
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        timeRange: [],
        keyword: ''
      },
      columns: [
        { label: '患者姓名', prop: 'patientName' },
        { label: '慢病病种', prop: 'disease', slot: 'disease' },
        { label: '会诊日期', prop: 'createTime' },
        { label: '申请医生', prop: 'createUsername' },
        { label: '会诊专家', prop: 'participantName' },
        { label: '会诊时长', prop: 'meetingDuration' },
        { label: '操作', prop: 'operation', width: 100, slot: 'operation' }
      ]
    }
  },
  computed: {
    ...mapState('drugManagement', ['consultationRecordDetail'])
  },
  methods: {
    async getTableList(params) {
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }

      return await getConsultationRecordsApi(queryParams)
    },

    handleDetail(row) {
      this.$store.dispatch('drugManagement/getConsultationRecordDetail', {
        roomId: row.id
      })
      this.getConsultationVideoFn(row.id)
    },

    // 获取会诊视频
    async getConsultationVideoFn(roomId) {
      const res = await getConsultationVideoApi({ id: roomId })
      if (res.code === 200 && res.data.mediaInfoSet.length > 0) {
        this.$refs.consultationRecordModal.dialogVisible = true
        this.$nextTick(() => {
          this.$refs.consultationRecordModal.meetingResult = this.meetingResult
          this.$refs.consultationRecordModal.medicalList = this.medicalList
          this.$refs.consultationRecordModal.videoUrl = res.data.mediaInfoSet[0].basicInfo.mediaUrl
        })
      } else {
        this.$message.warning('暂无视频')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';

.consultation-record {
  display: flex;
  flex-direction: column;
  height: 100%;
  .consultation-record-search {
    margin: 16px;
    height: 77px;
  }
  .consultation-record-table {
    margin: 0 16px;
    flex: 1;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
}
</style>
