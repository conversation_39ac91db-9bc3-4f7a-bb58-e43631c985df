// 通用枚举
export const YES_NO_ENUM = { 1: '是', 0: '否' }
// 枚举 - 性别
export const GENDER_ENUM = { 1: '男', 0: '女', 9: '未说明', 2: '未知' }
// 枚举 - 状态
export const TYPE_ENUM = { 1: '启用', 0: '禁用' }

// 枚举 - 就诊体检随访 三个模块对应的module code
export const PATIENT_EXAM_FOLLOWUP_MODULE_CODE_ENUM = {
  REG: '就诊',
  'BODY-CHECK': '体检',
  VISIT: '随访'
}
// 医生在团队里的角色
export const doctorRoleList = [
  {
    name: '副队长',
    id: 1
  },
  {
    name: '专家',
    id: 2
  },
  {
    name: '医生',
    id: 3
  },
  {
    name: '护士',
    id: 4
  },
  {
    name: '健康管家',
    id: 5
  },
  {
    name: '其他',
    id: 6
  }
]
export const OPERATE_FROM_TYPE_ENUM = {
  pc: '网页端',
  app: '手机端',
  pad: '平板端',
  phone: '手机端'
}

// 枚举 - 操作类型 枚举
export const OPERATE_TYPE_ENUM = {
  INSERT: '添加',
  UPDATE: '修改',
  DELETE: '删除'
}
// 特殊人群
export const SPECIALPERSON = [
  {
    label: '偏瘫',
    value: 1
  },
  {
    label: '病危/病重',
    value: 2
  },
  {
    label: '失聪/失语',
    value: 3
  },
  {
    label: '长期在外',
    value: 4
  },
  {
    label: '依从性差/不配合',
    value: 5
  }
]
// 人群类型
export const CROWDTYPES = [
  {
    label: '一般人群',
    value: 0
  },
  {
    label: '儿童',
    value: 1
  },
  {
    label: '老年人',
    value: 2
  },
  {
    label: '孕产妇',
    value: 3
  },
  {
    label: '高血压',
    value: 5
  },
  {
    label: '2型糖尿病',
    value: 6
  },
  {
    label: '严重精神障碍',
    value: 7
  },
  {
    label: '肺结核',
    value: 8
  },
  {
    label: '残疾人',
    value: 9
  },
  {
    label: '建档立卡贫困户',
    value: 10
  },
  {
    label: '计划生育特殊家庭',
    value: 11
  }
]

//与户主关系
export const RELATIONSHIP = [
  {
    label: '本人',
    id: 0
  },
  {
    label: '配偶',
    id: 1
  },
  {
    label: '子',
    id: 2
  },
  {
    label: '女',
    id: 3
  },
  {
    label: '女婿',
    id: 28
  },
  {
    label: '儿媳',
    id: 38
  },
  {
    label: '(外)孙辈',
    id: 4
  },
  {
    label: '父母',
    id: 5
  },
  {
    label: '(外)祖父母',
    id: 6
  },
  {
    label: '兄弟姐妹',
    id: 7
  },
  {
    label: '其他',
    id: 8
  }
]
// 签约记录的状态
export const signStatusEnum = {
  1: '待生效',
  2: '履约中',
  3: '即将到期',
  4: '已到期',
  9: '已解约'
}
// 签约类型
export const signTypeEnum = {
  0: '个人签约',
  1: '家庭签约'
}
// 履约状态
export const performstatusEnum = {
  1: '待执行',
  2: '履约中',
  3: '已完成'
}


// 家庭签约分类
export const familyTypeEnum = [
  {
    id: 1,
    label: '一般普通家庭'
  },
  {
    id: 2,
    label: '建档立卡家庭'
  },
  {
    id: 3,
    label: '计生特殊家庭'
  }
]


