<!-- 检查第一部分: 指尖血糖 餐后两小时尿糖 糖化血红蛋白 空腹静脉血糖 OCTT 24小时动态血压 ACR检查 心电图 -->
<template>
  <div class="inspection-first-part">
    <h2 style="color: #4bc0f1">检验检查</h2>
    <div class="content">
      <div class="title">指尖血糖</div>
      <div class="item">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="血糖类型">
            {{ fingerSugarData.name }}
          </el-descriptions-item>
          <el-descriptions-item label="血糖值"> {{ fingerSugarData.value }} mmol/L </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div v-if="postHourUrineSugarData" class="content">
      <div class="title">餐后两小时尿糖</div>
      <div class="item">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="尿糖（定性）">
            {{ postHourUrineSugarData }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div v-if="hbA1cData" class="content">
      <div class="title">糖化血红蛋白</div>
      <div class="item">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="HbA1C"> {{ hbA1cData }} % </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div v-if="fastingBloodGlucoseData" class="content">
      <div class="title">空腹静脉血糖</div>
      <div class="item">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="空腹静脉血糖"> {{ fastingBloodGlucoseData }} mmol/L </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div class="content">
      <div class="title">OGTT</div>
      <div class="item">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="空腹血糖"> {{ ogttData.kfSugarValue }} mmol/L </el-descriptions-item>
          <el-descriptions-item label="餐后0.5h血糖"> {{ ogttData.chHalfSugarValue }} mmol/L </el-descriptions-item>
          <el-descriptions-item label="餐后1h血糖"> {{ ogttData.chOneSugarValue }} mmol/L </el-descriptions-item>
          <el-descriptions-item label="餐后2h血糖"> {{ ogttData.chTwoSugarValue }} mmol/L </el-descriptions-item>
          <el-descriptions-item label="餐后3h血糖"> {{ ogttData.chThreeSugarValue }} mmol/L </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div class="content">
      <div class="title">24小时动态血压</div>
      <div class="item">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="24小时平均血压">
            {{ dynamicBloodPressureData.allAvgSp }} / {{ dynamicBloodPressureData.allAvgDp }} mmHg
          </el-descriptions-item>

          <el-descriptions-item label="日平均血压">
            {{ dynamicBloodPressureData.dayAvgSp }} / {{ dynamicBloodPressureData.dayAvgDp }} mmHg
          </el-descriptions-item>

          <el-descriptions-item label="夜平均血压">
            {{ dynamicBloodPressureData.nightAvgSp }} / {{ dynamicBloodPressureData.nightAvgDp }} mmHg
          </el-descriptions-item>

          <el-descriptions-item label="夜间血压下降率">
            {{ dynamicBloodPressureData.nightSpDeclineRate }} / {{ dynamicBloodPressureData.nightDpDeclineRate }} %
          </el-descriptions-item>

          <el-descriptions-item label="晨峰血压">
            {{ dynamicBloodPressureData.morningSp }} / {{ dynamicBloodPressureData.morningDp }} mmHg
          </el-descriptions-item>

          <el-descriptions-item label="动态血压系数">
            {{ dynamicBloodPressureData.dynamicCoefficient }}
          </el-descriptions-item>

          <el-descriptions-item label="检查结论">
            {{ dynamicBloodPressureData.bpDescription }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>

    <div class="content">
      <div class="title">ACR检查</div>
      <div class="item">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="尿微量白蛋白"> {{ acrData.uma }} mg/L </el-descriptions-item>
          <el-descriptions-item label="肌酐"> {{ acrData.creatinine }} mg/dL </el-descriptions-item>
          <el-descriptions-item label="尿白蛋白/肌酐"> {{ acrData.umaCreatinine }} mg/g </el-descriptions-item>
          <el-descriptions-item label="糖尿病ACR检查结构">
            {{ acrData.acrResult === 1 ? '正常' : acrData.acrResult === 2 ? '异常' : '' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'InspectionFirstPart',
  props: {
    reportInfo: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    // 指尖血糖数据
    fingerSugarData() {
      const fingerSugarType =
        this.reportInfo.itemList.find((item) => item.itemCode === 'FINGER_SUGAR') &&
        this.reportInfo.itemList.find((item) => item.itemCode === 'FINGER_SUGAR').data.fingerSugarType
      const name =
        fingerSugarType === 1
          ? '空腹血糖'
          : fingerSugarType === 2
            ? '随机血糖'
            : fingerSugarType === 3
              ? '餐后2h血糖'
              : '既往空腹血糖'
      const value =
        this.reportInfo.itemList.find((item) => item.itemCode === 'FINGER_SUGAR') &&
        this.reportInfo.itemList.find((item) => item.itemCode === 'FINGER_SUGAR').data.fingerSugarValue
      return {
        name,
        value
      }
    },

    // 餐后两小时尿糖数据
    postHourUrineSugarData() {
      const value =
        this.reportInfo.itemList.find((item) => item.itemCode === 'CH2H_SUGAR') &&
        this.reportInfo.itemList.find((item) => item.itemCode === 'CH2H_SUGAR').data.chTwoUrineSugar
      return value === 1 ? '阴性' : value === 2 ? '阳性' : ''
    },

    // 糖化血红蛋白数据
    hbA1cData() {
      const value =
        this.reportInfo.itemList.find((item) => item.itemCode === 'SUGAR_HEMOGLOBIN') &&
        this.reportInfo.itemList.find((item) => item.itemCode === 'SUGAR_HEMOGLOBIN').data.hba1cValue
      return value
    },

    // 空腹静脉血糖数据
    fastingBloodGlucoseData() {
      const value =
        this.reportInfo.itemList.find((item) => item.itemCode === 'KF_JM_SUGAR') &&
        this.reportInfo.itemList.find((item) => item.itemCode === 'KF_JM_SUGAR').data.kfJmSugarValue
      return value
    },

    // OGTT数据
    ogttData() {
      return (
        (this.reportInfo.itemList.find((item) => item.itemCode === 'OGTT') &&
          this.reportInfo.itemList.find((item) => item.itemCode === 'OGTT').data) ||
        {}
      )
    },

    // 24小时动态血压数据
    dynamicBloodPressureData() {
      return (
        (this.reportInfo.itemList.find((item) => item.itemCode === 'DYNAMICS_BLOOD_PRESSURE') &&
          this.reportInfo.itemList.find((item) => item.itemCode === 'DYNAMICS_BLOOD_PRESSURE').data) ||
        {}
      )
    },

    // ACR检查数据
    acrData() {
      return (
        (this.reportInfo.itemList.find((item) => item.itemCode === 'ACR') &&
          this.reportInfo.itemList.find((item) => item.itemCode === 'ACR').data) ||
        {}
      )
    }
  }
}
</script>

<style lang="scss">
.inspection-first-part {
  padding: 10px;
  .content {
    margin-top: 8px;
    .title {
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
    }
  }
}
</style>
