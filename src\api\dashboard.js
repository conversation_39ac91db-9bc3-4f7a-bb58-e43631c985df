import request from '@/utils/request'
import { getUserId } from '@/utils/auth'

// 根据用户id查询用户信息(已删除的患者也可以查询)
export function getUserInfoApi(id, token) {
  return request({
    url: `/cspapi/backend/user/patient/noIsDelete/${id}`,
    method: 'get',
    headers: {
      Authorization: token
    }
  })
}
// 平台总用户数
export function statisticUserApi() {
  return request({
    url: '/cspapi/backend/dataOverview/statistic/user',
    method: 'get'
  })
}
// AI风险预警次数
export function statisticAlarmApi() {
  return request({
    url: '/cspapi/backend/dataOverview/statistic/alarm',
    method: 'get'
  })
}
// AI风险预警次数
export function statisticVisitApi() {
  return request({
    url: '/cspapi/backend/dataOverview/statistic/visit',
    method: 'get'
  })
}
// 远程心电采集次数
export function statisticECGApi() {
  return request({
    url: '/cspapi/backend/dataOverview/statistic/ecg',
    method: 'get'
  })
}
// 预警频发时间段
export function statisticAlarmPeriodApi(data) {
  return request({
    url: '/cspapi/backend/dataOverview/statistic/alarm/period',
    method: 'get',
    params: data
  })
}
// 预警风险类型占比(高风险, 中风险)
export function dataOverviewRiskApi() {
  return request({
    url: '/cspapi/backend/dataOverview/scale/alarm/risk',
    method: 'get'
  })
}
// 预警类型占比(房颤, 低心率, 高心率 等)
export function dataOverviewTypeApi() {
  return request({
    url: '/cspapi/backend/dataOverview/scale/alarm/type',
    method: 'get'
  })
}
// 今日预警排行
export function diseaseTypeApi(data) {
  return request({
    url: '/cspapi/backend/dataOverview/statistic/alarm/diseaseType',
    method: 'get',
    params: data
  })
}
// 今日健康监测人数
export function statisticEcgUserApi() {
  return request({
    url: '/cspapi/backend/dataOverview/statistic/ecg/user',
    method: 'get'
  })
}

// 工作台！！！
// 就诊记录列表(分页)
export function getregisteredrecordApi(data) {
  return request({
    url: '/cspapi/backend/registeredrecord/pagePatientWithDeptId',
    method: 'get',
    params: data
  })
}
// 就诊记录列表(分页)
export function registeredrecordApi(data) {
  return request({
    url: '/cspapi/backend/registeredrecord',
    method: 'post',
    data
  })
}
// 病史记录(根据就诊id
export function getdiseasehistorycollectApi(data) {
  return request({
    url: '/cspapi/backend/diseasehistorycollect/listByRegId',
    method: 'get',
    params: data
  })
}
// 新增病史
export function addDiseasehistorycollectApi(data) {
  return request({
    url: '/cspapi/backend/diseasehistorycollect',
    method: 'post',
    data
  })
}

// 诊断记录(根据就诊id)   新增！！！
export function addDiagnosisApi(data, token) {
  return request({
    url: '/cspapi/backend/diagnosis',
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 登录日志
export function Loginlogs(params, token) {
  return request({
    url: '/cspapi/backend/sys/log/opera/page',
    method: 'get',
    params,
    headers: {
      Authorization: token
    }
  })
}
// 获取下拉框
export function Getdropdown(params, token) {
  return request({
    url: '/cspapi/backend/sys/log/opera/module',
    method: 'get',
    params,
    headers: {
      Authorization: token
    }
  })
}

// 修改密码
export function changePasswords(data, token) {
  return request({
    url: '/cspapi/backend/user/password',
    method: 'put',
    data,
    headers: {
      Authorization: token
    }
  })
}

// 处方记录(根据就诊id) 新增！！！！
export function addTakeMedicine(data) {
  return request({
    url: '/cspapi/backend/takeMedicine',
    method: 'post',
    data
  })
}

// 处方记录(根据就诊id)
export function addPrescriptionApi(data) {
  return request({
    url: '/cspapi/backend/prescription',
    method: 'post',
    data
  })
}

// 不良事件
//  某人的不良事件列表
export function getbadEventApiHeader(data, token) {
  return request({
    url: '/cspapi/backend/badEvent/page',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}

// 一体机数据
// 查询一体机的完整数据(根据regId)
export function getKsByRegIdApi(data) {
  return request({
    url: '/cspapi/backend/ks/getKsByRegId',
    method: 'get',
    params: data
  })
}
// 马雪良一键造数据！
export function postmxlzsjApi(data) {
  return request({
    url: '/cspapi/backend/common/konsung/receive',
    method: 'post',
    data
  })
}
// 患者主诉+初步诊断
export function getuserregisteredrecordApi(id, data, token) {
  return request({
    url: `/cspapi/backend/registeredrecord/${id}`,
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}
// 终止随访
export function stopTerminationfollowup(patientId, groupId, queueId, reason, recordId, token) {
  return request({
    url: `/cspapi/backend/visitCheck/groupUser/stopUser`,
    method: 'delete',
    params: { patientId, groupId, queueId, reason, recordId },
    header: {
      Authorization: token
    }
  })
}

// 居家数据
export function Dataacquisitionhome(data, token) {
  return request({
    url: '/cspapi/backend/visitCheck/active2/getByBatchIdAndPatientIdAndType',
    method: 'get',
    params: data,
    header: {
      Authorization: token
    }
  })
}

export function putuserregisteredrecordApi(data) {
  return request({
    url: '/cspapi/backend/registeredrecord',
    method: 'put',
    data
  })
}

// type post新增，put修改
export function putuserdiagnosisApi(data, type) {
  return request({
    url: '/cspapi/backend/diagnosis',
    method: 'post',
    data
  })
}
//  工作台-就诊统计
export function getstatisticCountAPi(data) {
  return request({
    url: '/cspapi/backend/registeredrecord/statistic/count',
    method: 'get',
    params: data
  })
}

// 结束就诊
export function getfinishApi(data) {
  return request({
    url: '/cspapi/backend/registeredrecord',
    method: 'put',
    data
  })
}

// 一体机数据  心电图机
export function getECGByRegIdApi(data) {
  return request({
    url: '/cspapi/backend/ks/heart/getByRegId',
    method: 'get',
    params: data
  })
}

//  获取ECG 中期 地址
export function getECGresultByRegId(data) {
  return request({
    url: '/cspapi/backend/ecgresult/getByRegId',
    method: 'get',
    params: data
  })
}

// 蓝牙设备
//  获取血糖(根据regId
export function getbluetoothSugarApi(data) {
  return request({
    url: '/cspapi/backend/bloodSugarActive/getByRegId',
    method: 'get',
    params: data
  })
}
//  某人的血糖记录列表
export function getbloodSugarApi(id) {
  return request({
    url: '/cspapi/backend/bloodSugarActive/listByUserId',
    method: 'get',
    params: { userId: id }
  })
}
// 某人的异常血糖记录列表
export function getAbnormalBloodSugarApi(data) {
  return request({
    url: '/cspapi/backend/bloodSugarActive/pageByUserId',
    method: 'get',
    params: data
  })
}
//  获取血压值(根据regId)
export function getbluetoothPressureApi(data) {
  return request({
    url: '/cspapi/backend/bloodPressureActive2/getByRegId',
    method: 'get',
    params: data
  })
}
//  获取某人的血压记录列表
export function getbloodPressureApi(id) {
  return request({
    url: '/cspapi/backend/bloodPressureActive2/listByUserId',
    method: 'get',
    params: { userId: id }
  })
}
// 某人的异常血压记录列表(分页)
export function getAbnormalBloodPressureApi(data) {
  return request({
    url: '/cspapi/backend/bloodPressureActive2/pageByUserId',
    method: 'get',
    params: data
  })
}

// 不良事件
//  某人的不良事件列表
export function getbadEventApi(data) {
  return request({
    url: '/cspapi/backend/badEvent/page',
    method: 'get',
    params: data
  })
}
//  新增不良事件
export function postbadEventApi(data) {
  return request({
    url: '/cspapi/backend/badEvent',
    method: 'post',
    data
  })
}
//  新增不良事件
export function getlistByModuleCodeApi(code) {
  return request({
    url: `/cspapi/backend/sys/dictionary/listByModuleCode/${code}`,
    method: 'get'
  })
}

//  就诊页面 小红点
// 查看小红点
export function getredDotApi(regId) {
  return request({
    url: '/cspapi/backend/redDot/getByRegId',
    method: 'get',
    params: { regId }
  })
}
// 小红点已读
export function putRedDotApi(ids) {
  return request({
    url: '/cspapi/backend/redDot/updateByIds',
    method: 'put',
    data: { ids }
  })
}

// 远程会诊
// 创建会诊
export function remoteConsultationApi(data) {
  return request({
    url: '/cspapi/backend/remoteConsultation',
    method: 'post',
    data
  })
}

// 发起房间(房主发起呼叫的时候)
export function remoteConsultationstartApi(data) {
  return request({
    url: '/cspapi/backend/remoteConsultation/start',
    method: 'put',
    data
  })
}

// 结束远程会诊并发送ws消息
export function stopRemoteConsultationApi(data) {
  return request({
    url: '/cspapi/backend/remoteConsultation/stop',
    method: 'post',
    data
  })
}
//  新增或修改处方2(会诊使用)
export function saveOrUpdateByRegIdApi(data) {
  return request({
    url: '/cspapi/backend/prescription/saveOrUpdateByRegId',
    method: 'post',
    data
  })
}
//  修改医生的加入会议状态
export function setupdateByRegIdApi(data) {
  return request({
    url: '/cspapi/backend/remoteUsers/updateByRegId',
    method: 'post',
    data
  })
}
// 拉人入会
export function remoteUsersApi(data) {
  return request({
    url: '/cspapi/backend/remoteUsers',
    method: 'post',
    data
  })
}
// 某医生入会/离会给其他人发通知/
export function putRemoteUsersApi(data) {
  return request({
    url: '/cspapi/backend/remoteUsers',
    method: 'put',
    data
  })
}

// 已完成的就诊记录列表(分页)
export function getMedrecordDataApi(data) {
  return request({
    url: '/cspapi/backend/registeredrecord/pageFinishedRecordByModel',
    method: 'get',
    params: data
  })
}
// 下发检查人员(就诊)
export function sendECGStatusApi(id) {
  return request({
    url: '/cspapi/backend/registeredrecord/changeEcgStatus',
    method: 'get',
    params: { regId: id, status: 0 }
  })
}
// 下发检查人员(就诊)  qds
export function sendQDSStatusApi(id) {
  return request({
    url: '/cspapi/backend/registeredrecord/changeQDSStatus',
    method: 'get',
    params: { regId: id, status: 0 }
  })
}
// 强制重置 用户status
export function resetMeetStatus(params) {
  return request({
    url: '/cspapi/backend/common/resetMeetStatus',
    method: 'get',
    params
  })
}
// 自定义发送WS消息
export function websocketSendMsgApi(data) {
  return request({
    url: '/cspapi/backend/myWebSocket/sendMsg',
    method: 'post',
    data
  })
}

// 就诊 肢体动脉硬化 下发人员
export function getChangeDmyhStatusApi(patientId, recordId, doctorId) {
  return request({
    url: '/cspapi/backend/registeredrecord/status/changeDmyhStatus',
    method: 'get',
    params: { patientId, recordId, doctorId }
  })
}

// 一键呼叫
export function aiCallStart(data) {
  return request({
    url: '/cspapi/backend/ai/call/start',
    method: 'post',
    data
  })
}
// app  下载 list(废弃不用)
export function getAppListApi() {
  return request({
    url: '/cspapi/backend/app/info/list?pageNo=1&pageSize=2000',
    method: 'get'
  })
}

// app 根据包名称下载
export function getAppInfoLastApi(packageName) {
  return request({
    url: `/cspapi/backend/common/app/info/last?packageName=${packageName}`,
    method: 'get'
  })
}

// app  下载 zip 文件
export function getDownloadZipApi(url) {
  return request({
    url,
    method: 'get',
    responseType: 'blob',
    headers: { 'content-type': 'application/x-www-form-urlencoded' }
  })
}
// 直接入随访组 ( 98  基层慢病管理)
export function getHandleJoinFromRegApi(patientId) {
  return request({
    url: '/cspapi/backend/visitCheck/groupUser/handleJoinFromReg',
    method: 'post',
    data: { queueIds: [98], fromCunyi: 1, patientId }
  })
}

// 卫生室信息总览
export function getDashboardSanitaryRoomApi(userId = getUserId()) {
  return request({
    url: '/cspapi/backend/dashboard/sanitaryRoom',
    method: 'get',
    params: { doctorId: userId }
  })
}

// 获取慢病人数
export function getNhcCountPatientGroupDiseaseApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/count/patient/group/disease',
    method: 'get',
    params
  })
}
// ****** 村医医看板接口 start ******
// 获取慢病人数
export function getDashboardOverviewApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/overview',
    method: 'get',
    params
  })
}
// 老年人体检统计
export function getDashboardOldBodyCheckOverviewApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/cunyi/oldBodyCheckOverview',
    method: 'get',
    params
  })
}
// 村医新的new 数据总览
export function getDashboardOverviewNewApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/overviewNew',
    method: 'get',
    params
  })
}

// 随访-近10天待随访人数(往后加10天)
export function getDashboardVisitStatusCountWaitApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/visit/status/count/wait',
    method: 'get',
    params
  })
}

// 随访-近10天已逾期(往前减10天)
export function getDashboardVisitStatusCountOverdueApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/visit/status/count/overdue',
    method: 'get',
    params
  })
}

// 随访-近10天待随访人数(往后加10天)
export function getDashboardVisitStatusCountWaitZBApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/visit/status/count/wait/zb',
    method: 'get',
    params
  })
}

// 随访-近10天已逾期(往前减10天)
export function getDashboardVisitStatusCountOverdueZBApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/visit/status/count/overdue/zb',
    method: 'get',
    params
  })
}

// ****** 村医医看板接口 end ******

// ****** 卫健委看板接口 start ******

// 医疗资源分布情况
export function getDashboardNhcDepartDistributionApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/depart/distribution',
    method: 'get',
    params
  })
}

// 随访进度以及完成率
export function getDashboardNhcVisitGroupDepartApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/visit/group/depart',
    method: 'get',
    params
  })
}

// 区域转诊详情
export function getDashboardNhcTurnDetailApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/turn/detail',
    method: 'get',
    params
  })
}

// 区域会诊-机构-明细
export function getDashboardNhcRemoteGroupDepartDetailApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/remote/group/depart/detail',
    method: 'get',
    params
  })
}

// 区域会诊-医生-明细
export function getDashboardNhcRemoteGroupDoctorDetailApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/remote/group/doctor/detail',
    method: 'get',
    params
  })
}

// 获取家庭医生签约统计
export function getFamilyDoctorSignContractApi(params) {
  return request({
    url: '/cspapi/backend/familyDoctor/statistic/index/wjw/signContract',
    method: 'get',
    params
  })
}
// 获取65岁以上老人体检统计
export function getDashboardNhcBodyCheckApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/bodyCheck',
    method: 'get',
    params
  })
}
// 获取慢病统计
export function getDashboardNhDiseaseGroupDepartApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/disease/group/depart',
    method: 'get',
    params
  })
}
// 治疗率(服药率)（高血压、糖尿病）
export function getDashboardNhcRate3typeApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/rate/3type',
    method: 'get',
    params
  })
}

// 医疗资源分布情况(仅医院)
export function getDashboardNhcDepartDistributionHospitalApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/depart/distribution/hospital',
    method: 'get',
    params
  })
}
// 每个街道下面的建档数+慢病人数
export function getStreetUserCountApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/depart/distribution/street/user/count',
    method: 'get',
    params
  })
}
// 每个街道下每种病的人数
export function getPatientCountGroupByDiseaseCodeByDepartIdApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/patientCountGroupByDiseaseCodeByDepartId',
    method: 'get',
    params
  })
}
// 慢病管理统计（%）就诊率
export function getNhcRateRegApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/rate/reg',
    method: 'get',
    params
  })
}
// 慢病管理统计（%）规范管理率
export function getNhcRateStandManageApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/nhc/rate/stand/manage',
    method: 'get',
    params
  })
}

// ******** 慢病管理统计用到的接口 start********
// 统计率的常量人口数据
export function getStatisticDataRatePopulationApi(params) {
  return request({
    url: '/cspapi/backend/statistic/data/rate/population',
    method: 'get',
    params
  })
}
// 统计率的慢病患者人数
export function getPopulationDiseaseChronicApi(params) {
  return request({
    url: '/cspapi/backend/statistic/data/rate/population/disease/chronic',
    method: 'get',
    params
  })
}
// 统计率的已管理慢病患者人数
export function getPopulationManagedApi(params) {
  return request({
    url: '/cspapi/backend/statistic/data/rate/population/managed',
    method: 'get',
    params
  })
}
// 统计率的规范管理人数
export function getPopulationManagedStandardApi(params) {
  return request({
    url: '/cspapi/backend/statistic/data/rate/population/managed/standard',
    method: 'get',
    params
  })
}
// 统计率的最近一次随访达标人数
export function getPopulationOkApi(params) {
  return request({
    url: '/cspapi/backend/statistic/data/rate/population/ok',
    method: 'get',
    params
  })
}

// 统计率的有服药记录人数
export function getPopulationMedicineTakeApi(params) {
  return request({
    url: '/cspapi/backend/statistic/data/rate/population/medicine/take',
    method: 'get',
    params
  })
}
// 统计率的有就诊记录人数
export function getPopulationRegisteredApi(params) {
  return request({
    url: '/cspapi/backend/statistic/data/rate/population/registered',
    method: 'get',
    params
  })
}
// ******** 慢病管理统计用到的接口 start********

// ****** 卫健委看板接口 end ******

export function getcountVisitPatientByDateRangeApi(params) {
  return request({
    url: '/cspapi/backend/dashboard/countVisitPatientByDateRange',
    method: 'get',
    params
  })
}
