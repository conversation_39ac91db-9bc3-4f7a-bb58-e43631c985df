<template>
  <div class="patient-reception">
    <el-card>
      <patient-info :patient-info="$store.state.receptionWorkbench.receptionWorkbenchData" :module-type="'reception'" />
    </el-card>

    <el-card
      v-if="
        $store.state.receptionWorkbench.receptionWorkbenchData.id &&
          $store.state.receptionWorkbench.receptionWorkbenchData.diseaseList.length > 0
      "
    >
      <div class="reception-step">
        <StepIndicator
          v-if="!$route.query.active"
          :steps="$store.state.receptionWorkbench.stepList"
          :current-step="active"
          @stepClick="handleStepChange"
        />
      </div>
      <div
        v-if="
          $store.state.receptionWorkbench.receptionWorkbenchData.receptionStatus === 1 &&
            !$route.query.active &&
            !historyId
        "
        style="
          position: fixed;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          z-index: 888;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          background-color: #fff;
          padding: 10px;
          border-radius: 4px;
          box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
        "
      >
        <el-button
          v-if="
            $store.state.receptionWorkbench.stepList[active] &&
              $store.state.receptionWorkbench.stepList[active].stepStatus !== 9
          "
          type="success"
          :loading="$store.state.receptionWorkbench.saveBtnLoading"
          style="background-color: #0bae01; border-color: #0bae01"
          @click="handleSave('save')"
        >
          保存
        </el-button>
        <el-button
          v-if="
            $store.state.receptionWorkbench.stepList[active] &&
              $store.state.receptionWorkbench.stepList[active].stepStatus === 9
          "
          :loading="$store.state.receptionWorkbench.saveBtnLoading"
          type="primary"
          @click="handleSave('edit')"
        >
          重新编辑
        </el-button>
        <el-button v-if="active > 0" type="primary" @click="handlePrevious">上一步</el-button>
        <el-button
          v-if="active < 3"
          :loading="$store.state.receptionWorkbench.saveBtnLoading"
          type="primary"
          @click="handleSave('next')"
        >
          下一步</el-button>
        <el-button
          v-if="
            $store.state.receptionWorkbench.stepList[active] &&
              $store.state.receptionWorkbench.stepList[active].stepStatus !== 9
          "
          :loading="$store.state.receptionWorkbench.saveBtnLoading"
          @click="handleSave('skip')"
        >
          跳过任务
        </el-button>
        <el-button
          v-if="active === 3 || active === 1"
          type="primary"
          @click="handlePrintGuidanceSheet"
        >打印引导单</el-button>
      </div>

      <div class="reception-content-wrapper">
        <div
          v-if="
            $store.state.receptionWorkbench.stepList[active] &&
              $store.state.receptionWorkbench.stepList[active].stepStatus === 9 &&
              !$route.query.active
          "
          class="mask"
        />
        <div class="reception-content">
          <consultation-info v-if="active === 0" ref="consultationInfoRef" :history-id="historyId" />
          <high-risk-screening v-if="active === 1" ref="highRiskScreeningRef" :history-id="historyId" />
          <diagnosis-of-illness v-if="active === 2" ref="diagnosisOfIllnessRef" :history-id="historyId" />
          <complications-screening v-if="active === 3" ref="complicationsScreeningRef" :history-id="historyId" />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import patientInfo from '@/components/patientInfo/index.vue'
import ConsultationInfo from './component/consultationInfo.vue'
import HighRiskScreening from './component/highRriskScreening.vue'
import DiagnosisOfIllness from './component/diagnosisofIllness.vue'
import ComplicationsScreening from './component/complicationsScreening.vue'
import StepIndicator from '@/components/customSteps/index.vue'
import { saveComplicationsScreening } from '@/api/receptionWorkbench'
import { mapGetters } from 'vuex'

export default {
  name: 'PatientReception',
  components: {
    patientInfo,
    ConsultationInfo,
    HighRiskScreening,
    DiagnosisOfIllness,
    ComplicationsScreening,
    StepIndicator
  },
  props: {
    historyId: {
      // 远程会诊需要在Modal中打开
      type: String,
      default: ''
    }
  },

  data() {
    return {
      active: 0,
      stepList: []
    }
  },
  computed: {
    ...mapGetters(['highRiskScreeningData', 'complicationsScreeningData'])
  },
  async created() {
    this.$store.dispatch('receptionWorkbench/getReceptionWorkbenchById', { id: this.historyId || this.$route.query.id })
    const idx = await this.$store.dispatch('receptionWorkbench/getReceptionStepListFn', {
      rrId: this.historyId || this.$route.query.id
    })
    if (idx > -1) {
      this.active = idx
    }
    if (this.$route.query.active) {
      this.active = this.$route.query.active
    }
  },
  methods: {
    handleStepChange(step, index) {
      this.active = index
    },

    // 病情诊断调用下一步生成并发症筛查记录
    async saveComplicationsScreeningFn() {
      const res = await saveComplicationsScreening({
        rrId: this.$route.query.id,
        disease: this.$store.state.receptionWorkbench.receptionWorkbenchData.disease
      })
      return res
    },

    async handleSave(type) {
      try {
        this.$store.commit('receptionWorkbench/SET_SAVE_BTN_LOADING', true)
        let status = 1
        if (this.active === 0) {
          // 问诊信息保存
          status = await this.$refs.consultationInfoRef.handleConsultationInfoSave(type)
        } else if (this.active === 1) {
          // 高危筛查保存
          status = await this.$refs.highRiskScreeningRef.handleHighRiskScreeningSave(type)
        } else if (this.active === 2) {
          // 病情诊断保存
          status = await this.$refs.diagnosisOfIllnessRef.handleDiagnosisOfIllnessSave(type)
        } else if (this.active === 3) {
          // 并发症筛查保存
          status = await this.$refs.complicationsScreeningRef.handleComplicationsScreeningSave(type)
        }

        this.$store.dispatch('receptionWorkbench/getReceptionStepListFn', { rrId: this.$route.query.id })

        if ((status === 5 || status === 9) && this.active === 2) {
          const res = await this.saveComplicationsScreeningFn()
          if (res.code !== 200) {
            return
          }
        }

        if (status === 9 || (type === 'next' && status === 9)) {
          if (this.active < 3) {
            this.active++
          }
          return
        }

        if (status === 5 && type === 'next') {
          this.$store.commit('receptionWorkbench/SET_LOADING', true)
          setTimeout(() => {
            this.active++
          }, 1000)
        }
      } catch (error) {
        console.log('error', error)
      } finally {
        this.$store.commit('receptionWorkbench/SET_SAVE_BTN_LOADING', false)
      }
    },
    handlePrevious() {
      this.active--
    },
    handlePrintGuidanceSheet() {
      this.$router.push({
        path: '/receptionCenter/printGuidanceSheet',
        query: {
          id: this.$route.query.id,
          menu: 'patientReception',
          active: this.active,
          archiveId:
            this.active === 1
              ? this.highRiskScreeningData.highRiskScreening.archiveId
              : this.complicationsScreeningData.archiveId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.patient-reception {
  .el-card {
    margin: 16px;
    .reception-step {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      .el-steps {
        width: 600px;
      }
    }
  }
  ::v-deep .el-tabs__new-tab {
    background-color: #41a1d4;
  }

  .reception-content-wrapper {
    position: relative;

    .mask {
      position: absolute;
      top: -8px;
      left: -8px;
      width: calc(100% + 16px);
      height: calc(100% + 16px);
      background: rgba(0, 0, 0, 0.1);
      z-index: 10;
      cursor: not-allowed;
    }

    // .reception-content {
    //   position: relative;
    //   z-index: 2998;
    // }
  }
}
</style>
