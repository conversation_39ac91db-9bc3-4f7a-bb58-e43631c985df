<!-- 血压监测 -->
<template>
  <div class="blood-pressure-monitoring">
    <flag-component :title="'血压监测'" desc="时间和收缩压、舒张压为必填项，任何一项为空均视为无效数据。" />
    <div class="table-container">
      <el-table :data="tableData" border style="margin-top: 16px">
        <el-table-column prop="measureTime" label="时间" align="center">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.measureTime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="选择时间"
            />
          </template>
        </el-table-column>

        <el-table-column prop="sp" label="收缩压" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.sp" />
          </template>
        </el-table-column>
        <el-table-column prop="dp" label="舒张压" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.dp" />
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.remark" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <span v-if="scope.$index === tableData.length - 1">
              <i class="el-icon-plus" style="color: #41a1d4; cursor: pointer" @click="handleAdd" />
            </span>
            <span>
              <i
                class="el-icon-delete"
                style="color: red; margin-left: 16px; cursor: pointer"
                @click="handleDelete(scope.$index)"
              />
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="table-echarts">
      <flag-component title="血压折线图" />
      <blood-pressure-echart ref="bloodPressureEchart" :table-data="tableData" />
    </div>

    <div class="desc">
      <flag-component title="血压监测说明" />
      <el-input v-model="description" type="textarea" :rows="3" style="margin-top: 16px" />
    </div>
  </div>
</template>

<script>
import flagComponent from '@/components/flagComponent/index.vue'
import bloodPressureEchart from '../echarts/bloodPressureEchart.vue'

export default {
  name: 'BloodPressureMonitoring',
  components: { flagComponent, bloodPressureEchart },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: [
        {
          measureTime: '',
          sp: '',
          dp: '',
          remark: ''
        }
      ],
      description: '',
      descriptionId: null
    }
  },
  methods: {
    initData(data) {
      if (data) {
        this.description = data.pressureMonitor.description
        this.descriptionId = data.pressureMonitor.id
        this.tableData = data.itemList
      }
    },

    initEcharts() {
      this.$nextTick(() => {
        this.$refs.bloodPressureEchart.initChart()
      })
    },

    handleAdd() {
      this.tableData.push({
        measureTime: '',
        sp: '',
        dp: '',
        remark: ''
      })
    },
    handleDelete(index) {
      this.tableData.splice(index, 1)
    },

    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: true,
        data: {
          data: {
            pressureMonitor: {
              description: this.description,
              id: this.descriptionId
            },
            itemList: this.tableData
          },
          itemCode: this.itemTemp.value,
          taItemId: this.$store.getters.therapeuticActionDetail.itemList.find((it) => it.itemCode === this.itemTemp.value)
            .taItemId
        }
      }

      return result
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.blood-pressure-monitoring {
  padding: 16px;
  .table-echarts {
    width: 100%;
    margin-top: 16px;
  }
}
</style>
