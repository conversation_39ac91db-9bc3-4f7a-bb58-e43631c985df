<template>
  <div class="search-reset-buttons">
    <el-button
      v-if="showType.includes('reset')"
      class="public_button public_buttonReset bgColor_FFF flex_center"
      icon="el-icon-refresh"
      :disabled="disabledButton"
      @click="handleReset"
    >
      {{ resetText }}
    </el-button>
    <el-tooltip :disabled="!disabledButton" :content="content" placement="top">
      <el-button
        class="public_button bgColor_42C9A300B2DC flex_center"
        type="primary"
        :icon="searchIcon"
        :loading="loading"
        :disabled="disabledButton"
        @click="handleSearch"
      >
        {{ searchText }}
      </el-button>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'SearchResetButton',
  props: {
    showType: {
      type: String,
      default: 'search,reset'
    },
    disabledButton: {
      type: Boolean,
      default: false
    },
    content: {
      type: String,
      default: '当前状态不可查询'
    },
    searchText: {
      type: String,
      default: '搜索'
    },
    resetText: {
      type: String,
      default: '重置'
    },
    searchIcon: {
      type: String,
      default: 'el-icon-search'
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleSearch() {
      this.$emit('search')
    },
    handleReset() {
      this.$emit('reset')
    }
  }
}
</script>

<style lang="scss" scoped>
.search-reset-buttons {
  display: flex;
  margin-top: 30px;
}
</style>
