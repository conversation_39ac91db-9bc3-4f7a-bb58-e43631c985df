import Vue from 'vue'

// 导入全局组件
import noData from '@/components/NoData/index.vue'
import wiTreeInput from '@/components/WiComponents/wiTreeInput/wiTreeInput.vue'
import wiTreeInputNormalizer from '@/components/WiComponents/wiTreeInput/wiTreeInputNormalizer.vue'
import encryptionStr from '@/components/encryptionStr/index.vue'
import iconSvg from '@/components/iconSvg.vue'
import customInputNumber from '@/components/customInputNumber/index.vue'

// 注册全局组件
const registerGlobalComponents = () => {
  Vue.component('NoData', noData)
  Vue.component('EncryptionStr', encryptionStr)
  Vue.component('WiTreeInput', wiTreeInput)
  Vue.component('WiTreeInputNormalizer', wiTreeInputNormalizer)
  Vue.component('IconSvg', iconSvg)
  Vue.component('CustomInputNumber', customInputNumber)
}

export default registerGlobalComponents
