const on_web_start = function() {
	_thermal_enum_printer_()
  _sdk_version_pkg_()
}
localStorage.setItem('printType','0')
//+----------------------------------------------------------------+
//SDK信息
const _sdk_version_pkg_ = function() {
	let sdk_message = document.getElementById("sdk_message")
	if (sdk_message) {
		sdk_message.innerHTML = '正在连接服务器...'
		console.log('正在连接服务器...');
		let onback = function(isok, res) {
			if (!isok) {
        localStorage.setItem('printType','0')
				sdk_message.innerHTML = '连接服务端失败...'; return
			}
			if (res.code !== 0) {
        localStorage.setItem('printType','0')
				sdk_message.innerHTML = res.msg; return
			}

      localStorage.setItem('printType','1')
			let text = ''
			for (let i = 0; i < res.message.length; i++) {
				text += res.message[i]
				if (i < (res.message.length - 1)) { text += '\n' }
			}
			sdk_message.innerHTML = text
		}
		let pack = {
			command: '_sdk_version_pkg_',
			source : 'thermal',
		}
		on_link_device(pack, onback)
	}
}
const on_link_device = function(pack, onback) {
  jqpost('http://localhost:8009/thermal.io?data=', pack, onback)
}
//+----------------------------------------------------------------+
//枚举设备
const _thermal_enum_printer_ = function() {
	let onback = function(isok, res) {
    console.log(isok);
    console.log(res);
		if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
		if (res.code !== 0) { output_control(res.msg); return }
		let enum_list = document.getElementById('enum_list')
		if (enum_list.length > 0) { enum_list.clear() }
		
		if (res.device.length > 0) {
			for (let i = 0; i < res.device.length; i++) {
				let opt = document.createElement('option')
				opt.text= res.device[i]
				enum_list.options.add(opt)
			}
		} else {
			output_control(res.message)
		}
	}
	let pack = {
		command: '_thermal_enum_printer_',
		require: 'usb',
	}
	on_link_device(pack, onback)
}
//+----------------------------------------------------------------+
//获取设备状态(4字节解析)
const _thermal_get_printer_status_ = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			//output_control('设备状态:' + JSON.stringify(res.state))
			output_control(JSON.stringify(res))
			
			//十六进制字符串转二进制数据
			let data = _hex2bin(res.recv_data);
			let dataleng = data.length;
			let byte1 = data[dataleng-4], byte2 = data[dataleng-3], byte3 = data[dataleng-2], byte4 = data[dataleng-1];
			
			//二进制数据转十六进制字符串
			output_control(_bin2hex(data))
			
			if ((byte1 & 0x02) == 0x02) {
				output_control('卡纸')
			}
			if ((byte1 & 0x10) != 0x10) {
				output_control('非联机')
			}
			if ((byte1 & 0x20) == 0x20) {
				output_control('缺纸')
			}
			if ((byte1 & 0x40) == 0x40) {
				output_control('切刀出错')
			}
			if ((byte1 & 0x80) == 0x80) {
				output_control('设备忙')
			}
			if ((byte2 & 0x08) == 0x08) {
				output_control('缓冲非空')
			}
			if ((byte3 & 0x01) == 0x01) {
				output_control('碳带出错')
			}
			if ((byte3 & 0x02) == 0x02) {
				output_control('打印头抬起')
			}
						
			if ((byte3 & 0x04) == 0x04) {
				output_control('标签纸')
			}
			else {
				output_control('连续纸')
			}
						
			if ((byte3 & 0x08) == 0x08) {
				output_control('热敏')
			}
			else {
				output_control('热转印')
			}
						
			if ((byte3 & 0x10) == 0x10) {
				output_control('切刀开')
			}
			else {
				output_control('切刀关')
			}
						
			if ((byte3 & 0x20) == 0x20) {
				output_control('电源复位')
			}
			if ((byte3 & 0x40) == 0x40) {
				output_control('打印头过热')
			}
		}
		//接口详情使用，可参考thermal_web_api.txt文件
		let pack = {
			command: "_thermal_cmd_engine_controler_",
			device : device,
			func   : "thermal_get_printer_status"
		}
		on_link_device(pack, onback)
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+
const output_control = function(out) {
	let item = document.getElementById('out_window')
	if (item != null) {
		if (out === 'window clear') {
			item.value = ''
		} else {
			item.value = out + '\r\n' + item.value
		}
	}
}
//+----------------------------------------------------------------+
//黑标定位
const _thermal_label_auto_locate_ = function(mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (mode == 'black_label') {
				output_control('黑标定位成功。')
			}
		}
		let pack = {
			command: '_thermal_cmd_engine_controler_',
			device : device,
			func   :  'thermal_blackmarking_auto_locate',
		}
		on_link_device(pack, onback)
	} else {
		output_control('请先枚举设备后再操作！')
	}
}

//绘图打印条码
const _thermal_barcode_print_ = function(arr,n,back) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		//直接打印（ 打印:'true' / 预览:'false' ）
		let printdirectly  = 'true'
		
		//获取预览图为base64数据（ base64:'true' / 路径:'false' ）
		let get_base64_img = ''
		
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (printdirectly == 'true') {
				output_control('条形码打印成功')
			} else {
				if (get_base64_img == 'true') {
					output_control(res.image_base64)
				} else {
					output_control(res.image_path)
				}
			}
        back(arr,n)
		}
		
		//张纸宽度
		let draw_w = 50
		//张纸长度
		let draw_h = 30
		//横向位置
		let barcode_x = 4
		//纵向位置
		let barcode_y = 3
		//条码宽度
		let barcode_w = 42
		//条码高度
		let barcode_h = 20
		//条码类型
		let barcode_style_let = 20;
		//是否打印注释
    let name = arr[n].name.length > 2?arr[n].name:arr[n].name+'　'
		let barcode_annotation = '　 '+arr[n].name+'　  '+arr[n].sex;
		if(arr[n].age){
			barcode_annotation+=' 　 '+arr[n].age+'岁'
		}
		let barcode_annotation_let = 1;
		//旋转角度
    let barcode_rotion_let = 0;
		//条码内容
		let barcode_data = arr[n].id
    //TSPL打印
    let pack = {
      command: '_thermal_tspl_draw_print_',
      device : device,
      printdirectly,
      measurement_mode: 'mm',
      canvas_size : {"width": draw_w, "height": draw_h},
      get_base64_img ,
      is_label: 'false',
      "print": [
        {"func":"PDSetFont","szFontName":"微软雅黑","fSize":10},
        {"func":"PDSetTextDecorate","iIsLandScape":1,"iIsReverseSequence":0,"iIsAutoLineFeed":0,"iIsLayDown":0},
        {"func":"PDDrawTextW","iX":barcode_x,"iY":2.25,"iWidth":barcode_w, "iHeight":barcode_h,"iIsSetNoAbsoluteBlack":0, "wszText":barcode_annotation},
        {"func":"PDDrawBarCode","iX":barcode_x,"iY":barcode_y + 5.5,"iWidth":barcode_w, "iHeight":barcode_h - 4,"szData":barcode_data},
        {"func":"PDDrawTextW","iX":barcode_x,"iY":22.5,"iWidth":barcode_w, "iHeight":barcode_h,"iIsSetNoAbsoluteBlack":0, "wszText":arr[n].id},
      ]
    }
    if (pack.print.length > 0) {
      on_link_device(pack, onback)
    }
	}else {
			output_control('请先枚举设备后再操作！')
		}
}
//绘图打印条码  区域检验
const _thermal_barcode_print_new_ = function(arr,n,back) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		//直接打印（ 打印:'true' / 预览:'false' ）
		let printdirectly  = 'true'
		
		//获取预览图为base64数据（ base64:'true' / 路径:'false' ）
		let get_base64_img = ''
		
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (printdirectly == 'true') {
				output_control('条形码打印成功')
			} else {
				if (get_base64_img == 'true') {
					output_control(res.image_base64)
				} else {
					output_control(res.image_path)
				}
			}
        back(arr,n)
		}
		
		//张纸宽度
		let draw_w = 50
		//张纸长度
		let draw_h = 30
		//横向位置
		let barcode_x = 8
		//纵向位置
		let barcode_y = 3
		//条码宽度
		let barcode_w = 42
		//条码高度
		let barcode_h = 20
		//条码类型
		let barcode_style_let = 20;
		//是否打印注释
    let name = arr[n].name.length > 2?arr[n].name:arr[n].name+'　'
		let barcode_annotation = arr[n].name+'　  '+arr[n].sex;
		if(arr[n].age){
			barcode_annotation+=' 　 '+arr[n].age
		}
		let barcode_annotation_let = 1;
		//旋转角度
    let barcode_rotion_let = 0;
		//条码内容
		let barcode_data = arr[n].id
    //TSPL打印
    let pack = {
      command: '_thermal_tspl_draw_print_',
      device : device,
      printdirectly,
      measurement_mode: 'mm',
      canvas_size : {"width": draw_w, "height": draw_h},
      get_base64_img ,
      is_label: 'false',
      "print": [
        {"func":"PDSetFont","szFontName":"微软雅黑","fSize":9},
        {"func":"PDSetTextDecorate","iIsLandScape":1,"iIsReverseSequence":0,"iIsAutoLineFeed":0,"iIsLayDown":0},
        {"func":"PDDrawTextW","iX":barcode_x,"iY":2.25,"iWidth":barcode_w, "iHeight":barcode_h,"iIsSetNoAbsoluteBlack":0, "wszText":barcode_annotation},
        {"func":"PDDrawBarCode","iX":barcode_x,"iY":barcode_y + 5.5,"iWidth":barcode_w, "iHeight":barcode_h - 4,"szData":barcode_data},
        // {"func":"PDDrawTextW","iX":barcode_x,"iY":22.5,"iWidth":barcode_w, "iHeight":barcode_h,"iIsSetNoAbsoluteBlack":0, "wszText":arr[n].id},
      ]
    }
    if (pack.print.length > 0) {
      on_link_device(pack, onback)
    }
	}else {
			output_control('请先枚举设备后再操作！')
		}
}