<!-- 空腹静脉血糖 -->
<template>
  <div class="fasting-venous-blood">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="150px">
      <el-form-item label="空腹静脉血糖：" prop="kfJmSugarValue">
        <custom-input-number v-model="form.kfJmSugarValue" style="width: 30%">
          <template #append>mmol/L</template>
        </custom-input-number>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'FastingVenousBlood',
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        kfJmSugarValue: ''
      },
      rules: {
        kfJmSugarValue: [{ required: true, message: '请输入空腹静脉血糖' }]
      }
    }
  },
  methods: {
    initData(data) {
      this.form = {
        kfJmSugarValue: data.kfJmSugarValue,
        id: data.id
      }
    },
    async handleSave() {
      const result = {
        name: `${this.itemTemp.label}`,
        success: false,
        data: {
          kfJmSugarValue: this.form.kfJmSugarValue,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    }
  }
}
</script>
