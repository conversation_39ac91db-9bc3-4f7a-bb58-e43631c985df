<!-- 检验检查记录 -->
<template>
  <div class="inspection-record">
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      row-key="id"
      :columns="columns"
      :show-pagination="showPagination"
    >
      <template #status="{ row }">
        <el-tag v-if="row.status === 1" type="primary">检查中</el-tag>
        <el-tag v-else-if="row.status === 5" type="success">已完成</el-tag>
        <el-tag v-else-if="row.status === 9" type="danger">已取消</el-tag>
      </template>
      <template #operation="{ row }">
        <el-button type="text" size="small" @click="handleDetail(row)">详情</el-button>
      </template>
    </base-table>
  </div>
</template>

<script>
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import { getInspectionTestingHistoryList } from '@/api/examination'

export default {
  name: 'InspectionRecord',
  components: {
    BaseTable
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        patientId: this.$route.query.id
      },
      columns: [
        { label: '检查日期', prop: 'inspectDate' },
        { label: '检查医生', prop: 'doctorName' },
        { label: '取消检查原因', prop: 'cancelReason' },
        { label: '检查状态', prop: 'status', slot: 'status' },
        { label: '操作', slot: 'operation' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      return await getInspectionTestingHistoryList(params)
    },
    handleDetail(row) {
      this.$router.push({
        path: '/receptionCenter/patientExamination',
        query: { id: row.id }
      })
    }
  }
}
</script>
