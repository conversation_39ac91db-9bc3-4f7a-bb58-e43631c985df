<template>
  <div class="gateway-config">
    <div v-if="!gatewayList.length" class="gateway-img">
      <img src="@/assets/cspImg/noGateway.png" alt="网关配置">
      <el-button type="primary" class="add-gateway-btn" @click="dialogVisible = true">绑定网关</el-button>
    </div>

    <el-table v-else :data="gatewayList" border style="width: 100%">
      <el-table-column align="center" prop="departmentName" label="部门名称" />
      <el-table-column align="center" prop="createTime" label="绑定时间" />
      <el-table-column align="center" prop="boxSn" label="序列号" />
      <el-table-column align="center" prop="boxMac" label="蓝牙MAC地址" />
      <el-table-column align="center" prop="status" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 1" type="success">在线</el-tag>
          <el-tag v-else type="danger">离线</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="handleUnbind(scope.row)">解绑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <ProDialog title="绑定网关" :visible.sync="dialogVisible" width="600px">
      <div class="gateway-config-content">
        <el-form ref="formRef" :model="form" label-width="130px" :rules="rules">
          <el-form-item label="序列号：" prop="boxSn">
            <el-input v-model="form.boxSn" placeholder="请输入序列号" />
          </el-form-item>
          <el-form-item label="蓝牙MAC地址：" prop="boxMac">
            <el-input v-model="form.boxMac" placeholder="请输入蓝牙MAC地址" />
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </span>
    </ProDialog>
  </div>
</template>

<script>
import { bindGatewayApi, getGatewayListApi, unbindGatewayApi } from '@/api/system'
import { localCache } from '@/utils/cache'
import ProDialog from '@/components/ProDialog/index.vue'

export default {
  name: 'GatewayConfig',
  components: {
    ProDialog
  },

  data() {
    return {
      dialogVisible: false,
      gatewayList: [],
      form: {
        boxSn: '',
        boxMac: ''
      },
      queryParams: {
        departmentId: localCache.getCache('userInfo').departId || ''
      },
      rules: {
        boxSn: [{ required: true, message: '请输入序列号', trigger: 'blur' }],
        boxMac: [{ required: true, message: '请输入蓝牙MAC地址', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getGatewayList()
  },
  methods: {
    async getGatewayList() {
      const res = await getGatewayListApi(this.queryParams)
      if (res.code === 200) {
        this.gatewayList = res.data || []
      }
    },

    async handleConfirm() {
      const valid = await this.$refs.formRef.validate()
      if (valid) {
        const params = {
          ...this.form,
          departmentId: localCache.getCache('userInfo').departId || '',
          departmentName: localCache.getCache('userInfo').departName || ''
        }
        const res = await bindGatewayApi(params)
        if (res.code === 200) {
          this.$message.success('绑定成功')
          this.dialogVisible = false
          this.getGatewayList()
        } else {
          this.$message.error(res.message)
        }
      }
    },

    handleUnbind(row) {
      this.$confirm('确定解绑该网关吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        const res = await unbindGatewayApi({
          departmentId: row.departmentId,
          boxSn: row.boxSn,
          boxMac: row.boxMac
        })
        if (res.code === 200) {
          this.$message.success('解绑成功')
          this.getGatewayList()
        } else {
          this.$message.error(res.message)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.gateway-config {
  position: relative;
  padding: 16px;
  width: 100%;
  height: 100%;
  .gateway-img {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    .add-gateway-btn {
      position: absolute;
      bottom: 80px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
</style>
