.marginLeft20{
  margin-left: 1rem;
}
.dfx-ac{
  display: flex;
  align-items: center;
  .el-form-item__content{
    margin-left: 0 !important;
    .el-radio{
      height: 1rem;
    }
  }
}
.familyDoctorCenter{
  .searchDiv.itemDiv{
    width: 15rem;
    height: 100%;
    margin-bottom: 0;
    float: left;
    .oh{
      height: 100%;
      overflow: auto;
      .title{
        font-weight: 400;
        font-size: .7rem;
        color: #666666;
        line-height: .8rem;
        padding: 0.5rem 0;
      }
      .serviceTree{
        max-height: calc(100% - 3rem);
        overflow: auto;
        border: 1px solid #dfdfdf;
      }
      .searchList{
        width: 100% !important;
      }
    }
    &+div{
      width: calc(100% - 16rem);
      float: right;
      box-shadow: 0 0.1rem 0.4rem 0 var(--searchBoxColor);
      border-radius: 0.5rem;
      background-color: var(--bjColor);
      padding: 0.6rem 0.75rem .6rem;
      height: 100%;
      .tableTopDiv{
        display: flex;
        .btnLinear {
          width: 5rem;
          padding: 0;
        }
        .inputKeywordDiv{
          flex: 1;
          text-align: right;
          line-height: 1.8rem;
          .inputKeyword{
            width: 16rem;
            .el-input__inner{
              //height: 1.6rem !important;
            }
          }
        }
      }
    }
  }
}
.doctorPop{
  .el-form-item {
    margin-bottom: 0.8rem;
  }
  .el-dialog{
    height: 70vh;
    .el-dialog__body{
      height: calc(100% - 40px - 3.1rem);
      overflow: auto;
    }
  }
  .el-input__inner{
    height: 1.8rem;
  }
  .el-form-item__label,.el-form-item__content{
    line-height: 1.8rem;
  }
  .el-upload-dragger{
    width: 100%;
  }
  &.doctorPopMin{
    .el-dialog{
      height: auto;
    }
  }
  &.newPopTable {
    .el-table  .el-table__body-wrapper .cell {
      line-height: 2rem;
    }
  }
}


  .familyDoctorCenter {
    .el-table.signStatisTable .el-table__header .cell{
      line-height: 1.5 !important;
      padding-top: .5vh;
      padding-bottom: .5vh;
      font-weight: 500;
      font-size: 0.7rem;
    }
  }
