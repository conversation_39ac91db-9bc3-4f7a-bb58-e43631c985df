<template>
  <div class="health-record-report">
    <el-button v-print="printObj" type="primary" style="position: fixed; top: 200px; right: 40px">打印</el-button>
    <div id="print-content" class="health-record-report-content">
      <div v-for="(pageConfig, index) in pageConfigs" :key="`page-${index}`" class="a4-wrapper">
        <a4-template :page-number="pageConfig.pageNumber">
          <component :is="pageConfig.component" v-bind="pageConfig.props" />
        </a4-template>
      </div>
    </div>
  </div>
</template>

<script>
import { getUserDbDetail, getUserDbReportDetail } from '@/api/archives'
import A4Template from './component/A4Template.vue'
import NewsCoverage from './component/newsCoverage.vue'
import ManageObject from './component/manageObject.vue'
import HealthRecords from './component/healthRecords.vue'
import DiagnosisOfIllnessReport from './component/diagnosisOfIllnessReport.vue'
import InspectionFirstPart from './component/inspectionFirstPart.vue'
import InspectionSecondPart from './component/inspectionSecondPart.vue'
import InspectionThirdPart from './component/inspectionThirdPart.vue'
import InspectionFourthPart from './component/inspectionFourthPart.vue'
import InspectionFifthPart from './component/inspectionFifthPart.vue'
import InspectionSixthPart from './component/inspectionSixthPart.vue'
import InspectionSeventhPart from './component/inspectionSeventhPart.vue'
import InspectionEigthPart from './component/inspectionEigthPart.vue'
import InspectionNinthPart from './component/inspectionNinthPart.vue'
import InspectionTenthPart from './component/inspectionTenthPart.vue'
import InspectionEleventhPart from './component/inspectionelEventhPart.vue'
import StandardizedReport from './component/standardizedReport.vue'
import StandardizedSecondReport from './component/standardizedSecondReport.vue'

export default {
  name: 'HealthRecordReport',
  components: {
    A4Template,
    NewsCoverage, // 封面
    ManageObject, // 管理目标
    HealthRecords, // 健康档案
    DiagnosisOfIllnessReport, // 诊断报告
    InspectionFirstPart, // 检验检查第一部分
    InspectionSecondPart, // 检验检查第二部分
    InspectionThirdPart, // 检验检查第三部分
    InspectionFourthPart, // 检验检查第四部分
    InspectionFifthPart, // 检验检查第五部分
    InspectionSixthPart, // 检验检查第六部分
    InspectionSeventhPart, // 检验检查第七部分
    InspectionEigthPart, // 检验检查第八部分
    InspectionNinthPart, // 检验检查第九部分
    InspectionTenthPart, // 检验检查第十部分
    InspectionEleventhPart, // 检验检查第十一部分
    StandardizedReport, // 规范管理
    StandardizedSecondReport // 规范管理第二部分
  },
  props: {
    userInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      printObj: {
        id: '#print-content'
      },
      baseInfo: {},
      reportInfo: {},
      diagnosisOfIllnessReportShow: false,
      inspectionShow: false,
      standardizedShow: false
    }
  },
  computed: {
    // 页面配置数组
    pageConfigs() {
      const pages = [
        {
          component: 'NewsCoverage',
          props: { patientInfo: this.userInfo },
          show: true
        },
        {
          component: 'HealthRecords',
          props: { baseInfo: this.baseInfo },
          show: true
        },
        {
          component: 'ManageObject',
          props: { patientInfo: this.userInfo, reportInfo: this.reportInfo },
          show: true
        },
        {
          component: 'DiagnosisOfIllnessReport',
          props: { reportInfo: this.reportInfo },
          show: this.diagnosisOfIllnessReportShow
        },
        {
          component: 'InspectionFirstPart',
          props: { reportInfo: this.reportInfo },
          show: this.inspectionFirstPartShow
        },
        {
          component: 'InspectionSecondPart',
          props: { reportInfo: this.reportInfo },
          show: this.inspectionSecondPartShow
        },
        {
          component: 'InspectionThirdPart',
          props: { reportInfo: this.reportInfo },
          show: this.inspectionThirdPartShow
        },
        {
          component: 'InspectionFourthPart',
          props: { reportInfo: this.reportInfo },
          show: this.inspectionFourthPartShow
        },
        {
          component: 'InspectionFifthPart',
          props: { reportInfo: this.reportInfo },
          show: this.inspectionFifthPartShow
        },
        {
          component: 'InspectionSixthPart',
          props: { reportInfo: this.reportInfo },
          show: this.inspectionSixthPartShow
        },
        {
          component: 'InspectionSeventhPart',
          props: { reportInfo: this.reportInfo },
          show: this.inspectionSeventhPartShow
        },
        {
          component: 'InspectionEigthPart',
          props: { reportInfo: this.reportInfo },
          show: this.inspectionEigthPartShow
        },
        {
          component: 'InspectionNinthPart',
          props: { reportInfo: this.reportInfo },
          show: this.inspectionNinthPartShow
        },
        {
          component: 'InspectionTenthPart',
          props: { reportInfo: this.reportInfo },
          show: this.inspectionTenthPartShow
        },
        {
          component: 'InspectionEleventhPart',
          props: { reportInfo: this.reportInfo },
          show: this.inspectionEleventhPartShow
        },
        {
          component: 'StandardizedReport',
          props: { reportInfo: this.reportInfo },
          show: this.standardizedShow
        },
        {
          component: 'StandardizedSecondReport',
          props: { reportInfo: this.reportInfo },
          show: this.standardizedShow
        }
      ]

      // 过滤显示的页面并添加动态页码
      return pages
        .filter((page) => page.show)
        .map((page, index) => ({
          ...page,
          pageNumber: index + 1
        }))
    }
  },
  created() {
    this.getUserDbDetailFn()
    this.getUserDbReportDetailFn()
  },
  methods: {
    async getUserDbDetailFn() {
      const res = await getUserDbDetail({ patientId: this.$route.query.id })
      if (res.code === 200) {
        this.baseInfo = res.data
      }
    },
    async getUserDbReportDetailFn() {
      const res = await getUserDbReportDetail({ patientId: this.$route.query.id })
      if (res.code === 200) {
        this.reportInfo = res.data
        // 判断是否显示病情报告
        this.diagnosisOfIllnessReportShow =
          res.data.diseaseDiagnosisDetailVO &&
          (res.data.diseaseDiagnosisDetailVO.tnbResult ||
            res.data.diseaseDiagnosisDetailVO.gxyResult ||
            res.data.diseaseDiagnosisDetailVO.copdResult ||
            res.data.diseaseDiagnosisDetailVO.fcResult)
        // 检验检查是否显示
        this.inspectionShow = res.data.itemList.length > 0
        this.inspectionFirstPartShow = this.hasInspectionItemData([
          'FINGER_SUGAR',
          'CH2H_SUGAR',
          'SUGAR_HEMOGLOBIN',
          'KF_JM_SUGAR',
          'OGTT',
          'DYNAMICS_BLOOD_PRESSURE',
          'ACR'
        ])

        this.inspectionSecondPartShow = this.hasInspectionItemData(['ECG', 'URINE_ROUTINE'])
        this.inspectionThirdPartShow = this.hasInspectionItemData(['BIOCHEMISTRY'])
        this.inspectionFourthPartShow = this.hasInspectionItemData(['BLOOD_ROUTINE'])
        this.inspectionFifthPartShow = this.hasInspectionItemData(['BNP', 'CAROTID_ULTRASOUND', 'ARTERIOSCLEROSIS'])
        this.inspectionSixthPartShow = this.hasInspectionItemData(['EMG'])
        this.inspectionSeventhPartShow = this.hasInspectionItemData(['EYE_GROUND'])
        this.inspectionEigthPartShow = this.hasInspectionItemData(['VIBRATION_THRESHOLD', 'PAO'])
        this.inspectionNinthPartShow = this.hasInspectionItemData(['DYNAMICS_ECG'])
        this.inspectionTenthPartShow = this.hasInspectionItemData(['ECHOCARDIOGRAM'])
        this.inspectionEleventhPartShow = this.hasInspectionItemData(['ECHOCARDIOGRAM'])

        // 规范管理是否显示
        this.standardizedShow =
          res.data.treatmentActionDetailVO &&
          res.data.treatmentActionDetailVO.itemList &&
          res.data.treatmentActionDetailVO.itemList.length > 0
      }
    },

    hasInspectionItemData(itemCodes) {
      return itemCodes.some((item) => {
        if (this.reportInfo.itemList.length > 0) {
          const idx = this.reportInfo.itemList.findIndex((it) => it.itemCode === item)
          return idx !== -1
        }
        return false
      })
    }
  }
}
</script>

<style lang="scss">
.health-record-report {
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: #f5f7fa;
  position: relative;
}

.a4-wrapper {
  margin-bottom: 20px;
}

@media print {
  .health-record-report {
    background-color: white !important;
    overflow: visible !important;
    height: auto !important;
  }

  .health-record-report-content {
    overflow: visible !important;
    max-height: none !important;
  }

  .health-record-report-content .a4-wrapper {
    page-break-after: always;
    break-after: page;
    margin-bottom: 0;
  }

  .a4-template {
    box-shadow: none !important;
    background: white !important;
  }

  /* 强制显示表格背景颜色和边框 */

  .el-table th {
    background-color: #bdc0c3 !important;
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  .el-table td.el-table__cell .cell {
    line-height: 0.8rem;
  }

  .el-table--border {
    border: 2px solid #dfe6ec !important;
  }

  html,
  body {
    height: auto !important;
    overflow: visible !important;
  }

  @page {
    size: A4 portrait;
    margin: 0;
  }
}
</style>
