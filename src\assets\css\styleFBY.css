/* 公用样式
 * 要求
 *  1、一个模块的样式需要注释
 *  2、禁止使用id选择器
 *  3、使用rem，勿用px
 *  4、使用有意义的命名（类似 .a  .ccc .ssss ...  禁止使用）
 *  5、驼峰命名 （personName、personTel、perTel、renYanXinXi、）
 */

/* 公用样式 Start */
.mr5 {
    margin-right: .25rem;
}

.white-text {
    color: #fff;
}

.btn-gradient-ramp {
    background: linear-gradient(131deg, #43C9A3 0%, #01B3DD 100%);
}

.flex-box {
    display: flex;
    align-items: center;
}

.flex-box.between {
    justify-content: space-between;
}

.text-btn {
    font-weight: 500;
    font-size: 0.75rem;
    color: #0A86C8;
    line-height: 1.1rem;
    cursor: pointer;
    margin-right: 0.5rem;
    user-select: none;
}

.text-btn.red {
    color: #ee5757;
}

.text-btn.disabled {
    color: #c0c4cc;
}

.inbl-box {
    display: inline-block;
}

.tag-green {
    padding: 0.2rem 0.5rem;
    background: #C4F3E1;
    border-radius: 0.2rem;
    color: #02BF72;
    font-size: .7rem;
}

.tag-orange {
    padding: 0.2rem 0.5rem;
    background: #FFE3D4;
    border-radius: 0.2rem;
    color: #FF7A3F;
    font-size: .7rem;
}

.tag-yellow {
    padding: 0.2rem 0.5rem;
    background: #FDE7CD;
    border-radius: 0.2rem;
    color: #FB961A;
    font-size: .7rem;
}

.tag-gray {
    padding: 0.2rem 0.5rem;
    background: #F0F0F0;
    border-radius: 0.2rem;
    color: #999999;
    font-size: .7rem;
}

.mt20 {
    margin-top: 1rem;
}

.blue-before-line::before {
    content: '';
    display: inline-block;
    width: .1rem;
    height: .6rem;
    background: #0A86C8;
    border-radius: .05rem;
    margin-right: 0.75rem;
}
