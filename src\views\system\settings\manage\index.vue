<!-- eslint-disable vue/html-indent -->
<!-- eslint-disable vue/max-attributes-per-line -->
<!-- eslint-disable space-before-function-paren -->
<!-- eslint-disable vue/html-closing-bracket-newline -->
<template>
  <ProTwoColumnsLayout>
    <ProTwoColumnsLayoutLeft slot="left">
      <div class="left">
        <div class="public_label_list flex_columnStart">
          <ProInput v-model="filterText" placeholder="输入关键字进行过滤" />
        </div>
        <div class="public_label_list marginTop30 paddingTop20 flex_columnStart public_label_listBorderTop">
          <span class="fontSize_14 color666 public_label_customTime">组织架构：</span>
          <el-scrollbar class="public_scrollbar scrollbar_area public_width100">
            <el-tree
              ref="tree"
              :props="{
                children: 'children',
                label: 'departName'
              }"
              :data="treeData"
              node-key="departCode"
              highlight-current
              :default-expanded-keys="treeDefaultKeys"
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
            >
              <span slot-scope="{ node }" class="custom-tree-node">
                <span style="display: flex; align-items: center; font-size: 0.7rem"> {{ node.label }}</span>
              </span>
            </el-tree>
          </el-scrollbar>
        </div>
      </div>
    </ProTwoColumnsLayoutLeft>
    <ProTwoColumnsLayoutRight slot="right">
      <div class="ecgDetail_data public_boxShadow bgColor_FFF borderRadius6 public_width100">
        <el-form :inline="true" :model="searchData" class="flex_start">
          <div style="width: 80%; display: flex; align-items: center">
            <el-form-item label="姓名：" style="margin-bottom: 0">
              <ProInput
                v-model="searchData.keyword"
                placeholder=""
                size="mini"
                @debounce="
                  searchData.pageNo = 1
                  getTableData()
                "
              />
            </el-form-item>
            <el-form-item label="状态：" style="margin-bottom: 0">
              <el-select
                v-model="searchData.status"
                placeholder=""
                size="mini"
                @change="
                  searchData.pageNo = 1
                  getTableData()
                "
              >
                <el-option label="全部" :value="''" />
                <el-option
                  v-for="(label, key) of ROLE_STATUS_ENUM"
                  :key="'enum-' + key"
                  :label="label"
                  :value="parseInt(key)"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="角色：" style="margin-bottom: 0">
              <el-select v-model="searchData.roleCode" placeholder="" size="mini" @change="getTableData()">
                <el-option label="全部" :value="''" />
                <el-option v-for="item in role" :key="item.roleCode" :label="item.roleName" :value="item.roleCode" />
              </el-select>
            </el-form-item>
            <el-form-item label="科室：" style="margin-bottom: 0">
              <el-select v-model="searchData.departmentCode" placeholder="" size="mini" @change="getTableData()">
                <el-option label="全部" :value="''" />
                <el-option v-for="item in departmentList" :key="item.code" :label="item.value" :value="item.code" />
              </el-select>
            </el-form-item>
          </div>

          <el-button
            class="public_button fontSize_14 bgColor_42C9A300B2DC flex_center v2icon_search"
            icon="el-icon-search"
            @click="resetPageNo"
          >
            <span>搜索</span>
          </el-button>

          <el-button
            class="public_button fontSize_14 bgColor_42C9A300B2DC flex_center v2icon_search"
            icon="el-icon-plus"
            @click="addUser"
          >
            <span>新增用户</span>
          </el-button>
        </el-form>
      </div>
      <div class="public_boxShadow bgColor_FFF public_width100 ecg_table marginTop8 borderRadius6">
        <div id="followTable" class="table-container">
          <el-table
            v-loading="tableLOading"
            :data="tableData"
            stripe
            class="public_table analysis_talbe"
            :max-height="tableHeight"
          >
            <template slot="empty">
              <noData />
            </template>

            <el-table-column label="序号" type="index" width="80" align="center" />
            <el-table-column prop="name" label="姓名" align="center" min-width="80">
              <template slot-scope="{ row }">
                <span>{{ row.name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="idCard" label="手机号码" width="200" align="center">
              <template slot-scope="{ row }">
                <encryptionStr v-if="row.phoneReplace" :cipher-text="row.phone" :replace="row.phoneReplace" />
                <span v-else>-</span>
              </template>
            </el-table-column>

            <el-table-column prop="departName" label="所属机构" align="center" min-width="120">
              <template slot-scope="{ row }">
                <el-tooltip v-if="row.departName" effect="dark" :content="row.departName" placement="top">
                  <span>{{ row.departName }}</span>
                </el-tooltip>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column prop="roles" label="角色" align="center" :min-width="fz * 12">
              <template slot-scope="{ row }">
                <ProTag :tips-arr="row.roleNamesArr" />
              </template>
            </el-table-column>
            <el-table-column prop="userDepartmentNames" label="科室" align="center" :min-width="fz * 12">
              <template slot-scope="{ row }">
                <ProTag :tips-arr="row.userDepartmentNamesArr" />
              </template>
            </el-table-column>
            <el-table-column prop="address" label="状态" width="100" align="center">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.status == 1" type="success">{{ ROLE_STATUS_ENUM[scope.row.status] }}</el-tag>
                <el-tag v-if="scope.row.status == 0" type="danger">{{ ROLE_STATUS_ENUM[scope.row.status] }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="120">
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.roleNames && scope.row.roleNames.indexOf('患者') === -1"
                  type="text"
                  size="mini"
                  @click="editClick(scope.row)"
                  >编辑</el-button
                >
                <el-button type="text" style="color: #f67f7d" size="mini" @click="deleteUser(scope.row.id)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align: right; margin: 0.8rem 0 0"
            :current-page="searchData.pageNo"
            :page-sizes="tablePageSizes"
            :page-size="searchData.pageSize"
            :layout="tablePaginationLayout"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </ProTwoColumnsLayoutRight>

    <!-- 新增用户  -->
    <ProDialog :visible.sync="addUserShow" :before-close="addUserClose" width="700px" :top="'10vh'">
      <span slot="title" class="dialog-title">
        <span>新增用户</span>
      </span>
      <el-form ref="addUserForm" :model="addUserForm" :rules="rules" label-width="80px">
        <el-form-item prop="name" label="姓名">
          <el-input v-model="addUserForm.name" maxlength="10" show-word-limit placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item prop="phone" label="手机号码">
          <el-input v-model.trim="addUserForm.phone" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item prop="password" label="密码">
          <el-input v-model.trim="addUserForm.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item prop="checkPass" label="确认密码">
          <el-input v-model.trim="addUserForm.checkPass" type="password" placeholder="请重新输入密码" />
        </el-form-item>
        <el-form-item prop="departCode" label="组织">
          <!-- noneed -->
          <el-cascader
            v-model="addUserForm.departCode"
            :options="treeData"
            :props="cascaderProps"
            clearable
            filterable
          />
        </el-form-item>
        <el-form-item prop="roleCodeList" label="角色">
          <el-select
            v-model="addUserForm.roleCodeList"
            multiple
            placeholder="请选择角色"
            class="public_slectMultiple"
            @change="onRoleCodesChange"
          >
            <el-option v-for="item in addRoleArr" :key="item.roleCode" :label="item.roleName" :value="item.roleCode" />
          </el-select>
        </el-form-item>
        <el-form-item prop="status" label="状态">
          <el-select v-model="addUserForm.status" placeholder="请选择状态">
            <el-option
              v-for="(label, key) of ROLE_STATUS_ENUM"
              :key="'enum-' + key"
              :label="label"
              :value="parseInt(key)"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="userDepartmentCodes" label="科室">
          <el-select
            v-model="addUserForm.userDepartmentCodes"
            placeholder="请选择科室"
            multiple
            class="public_slectMultiple"
          >
            <el-option v-for="item in departmentList" :key="item.code" :label="item.value" :value="item.code" />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer flex_end">
        <el-button class="public_dialogButton2 color333 fontSize_14 bgColor_FFF flex_center" @click="addUserClose"
          >取消
        </el-button>
        <el-button
          class="public_dialogButton colorFFF fontSize_14 bgColor_42C9A300B2DC flex_center"
          @click="SubmitForm()"
          >保存</el-button
        >
      </span>
    </ProDialog>
    <!-- 编辑用户  -->
    <ProDialog :visible.sync="editUserShow" :before-close="closeDialog" width="700px" :top="'10vh'">
      <span slot="title" class="dialog-title">
        <span>编辑用户</span>
      </span>
      <el-form
        ref="editUserForm"
        :model="editUserForm"
        maxlength="10"
        show-word-limit
        :rules="editrules"
        label-width="80px"
      >
        <el-form-item prop="name" label="姓名">
          <el-input v-model="editUserForm.name" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号码">
          <el-input
            v-model="editUserForm.phone"
            placeholder="请输入手机号码"
            :disabled="
              editUserForm.roleCodeList ? (editUserForm.roleCodeList.includes('patient') ? false : true) : true
            "
          />
        </el-form-item>
        <el-form-item prop="departCode" label="组织">
          <el-cascader
            v-model="editUserForm.departCode"
            :options="treeData"
            :props="cascaderProps"
            clearable
            filterable
          />
        </el-form-item>
        <el-form-item prop="roleCodeList" label="角色">
          <el-select
            v-model="editUserForm.roleCodeList"
            multiple
            placeholder="请选择角色"
            class="public_slectMultiple"
            @change="onRoleCodesChange"
          >
            <el-option v-for="item in editRoleArr" :key="item.roleCode" :label="item.roleName" :value="item.roleCode" />
          </el-select>
        </el-form-item>
        <el-form-item prop="status" label="状态">
          <el-select v-model="editUserForm.status" placeholder="请选择状态">
            <el-option
              v-for="(label, key) of ROLE_STATUS_ENUM"
              :key="'enum-' + key"
              :label="label"
              :value="parseInt(key)"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="userDepartmentCodes" label="科室">
          <el-select
            v-model="editUserForm.userDepartmentCodes"
            placeholder="请选择科室"
            multiple
            class="public_slectMultiple"
          >
            <el-option v-for="item in departmentList" :key="item.code" :label="item.value" :value="item.code" />
          </el-select>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model.trim="editUserForm.password" type="password" placeholder="请输入密码" />
        </el-form-item>
      </el-form>

      <span slot="footer" class="dialog-footer flex_end">
        <el-button class="public_dialogButton2 color333 fontSize_14 bgColor_FFF flex_center" @click="closeDialog"
          >取消
        </el-button>
        <el-button
          class="public_dialogButton colorFFF fontSize_14 bgColor_42C9A300B2DC flex_center"
          @click="SubmitEditForm()"
          >保存</el-button
        >
      </span>
    </ProDialog>
  </ProTwoColumnsLayout>
</template>
<script>
import { mapGetters } from 'vuex'
import elDragDialog from '@/directive/el-drag-dialog'
import { REGEX_MOBILE } from '@/utils/enum'
import { deepClone } from '@/utils'
import ProDialog from '@/components/ProDialog/index.vue'

import {
  // hospitalAllApi,
  departUserApi,
  getRole,
  postuserApi,
  deleteuserApi,
  putuserApi,
  getuserInfoApi,
  getUserByPhoneApi,
  getOrgTreeByIdApi
} from '@/api/system'
import { getDictionaryValApi } from '@/api/dict'
import { getUserId } from '@/utils/auth'
import ProTag from '@/components/ProTag/index.vue'
import ProInput from '@/components/ProInput/index.vue'
import ProTwoColumnsLayout from '@/components/ProTwoColumnsLayout/parent.vue'
import ProTwoColumnsLayoutLeft from '@/components/ProTwoColumnsLayout/left.vue'
import ProTwoColumnsLayoutRight from '@/components/ProTwoColumnsLayout/right.vue'

export default {
  name: 'Manage',
  components: { ProDialog, ProTag, ProInput, ProTwoColumnsLayout, ProTwoColumnsLayoutLeft, ProTwoColumnsLayoutRight },
  directives: {
    elDragDialog
  },
  data() {
    const checkPwd = (rule, value, callback) => {
      if (value.trim().length === 0) {
        callback(new Error('请确认密码不能为空'))
      } else if (value.length < 6) {
        callback(new Error('密码长度不能少于6个字符'))
      } else if (value.length > 16) {
        callback(new Error('密码长度不能超过16个字符'))
      } else {
        callback()
      }
    }
    const checkPwd2 = (rule, value, callback) => {
      if (value.trim().length === 0) {
        callback(new Error('请确认密码不能为空'))
      } else if (`${value}` !== `${this.addUserForm.password}`) {
        callback(new Error('俩次密码不一致'))
      } else {
        callback()
      }
    }
    const validatePhone = (rule, value, callback) => {
      this.getUserByPhone(value).then((res) => {
        if (res && !res.data) {
          callback(new Error('手机号已被占用。'))
        } else {
          callback()
        }
      })
    }
    return {
      // enum
      ROLE_STATUS_ENUM: { 1: '启用', 0: '禁用' },
      treeDefaultKeys: [], // 默认展开的树节点
      treeData: null,
      searchData: {
        departCode: '',
        pageNo: 1,
        pageSize: 20,
        keyword: '',
        status: '',
        roleCode: '',
        departmentCode: ''
      },
      cascaderProps: {
        // 级联组件配置
        label: 'departName',
        children: 'children',
        value: 'departCode',
        checkStrictly: true,
        emitPath: false
      },
      tableData: [],
      tableLOading: false,
      total: 0,
      editUserShow: false,
      editUserForm: {},
      addUserShow: false,
      addRoleArr: [], // 新增角色时使用的下拉数据
      editRoleArr: [], // 编辑角色时使用的下拉数据
      role: [],
      addUserForm: {
        name: '',
        phone: '',
        password: '',
        roleCodeList: [],
        checkPass: '',
        status: 1,
        userDepartmentCodes: []
      },
      rules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: REGEX_MOBILE, message: '手机号码格式不正确', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        password: [{ validator: checkPwd, trigger: 'blur' }],
        checkPass: [{ validator: checkPwd2, trigger: 'blur' }],
        roleCodeList: [{ required: true, message: '请选择角色', trigger: ['blur', 'change'] }],
        departCode: [{ required: true, message: '请选择组织', trigger: ['blur', 'change'] }]
      },
      editrules: {
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: REGEX_MOBILE, message: '手机号码格式不正确', trigger: 'blur' }
        ],
        password: [{ validator: checkPwd, trigger: 'blur' }],
        checkPass: [{ validator: checkPwd2, trigger: 'blur' }],
        roleCodeList: [{ required: true, message: '请选择角色', trigger: ['blur', 'change'] }],
        departCode: [{ required: true, message: '请选择组织', trigger: ['blur', 'change'] }]
      },
      loading: false,
      filterText: '', // 筛选树节点关键字
      departmentList: [],
      tableHeight: 0
    }
  },
  computed: {
    ...mapGetters(['hospital_code'])
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getTreeData()
    this.getrole()
    this.getDepartmentList()
  },
  mounted() {
    this.getTableHeight()
    window.addEventListener('resize', this.getTableHeight)
  },
  destroyed() {
    window.removeEventListener('resize', this.getTableHeight)
  },

  methods: {
    getTableHeight() {
      function getElementTop(element) {
        const rect = element.getBoundingClientRect()
        const scrollTop = window.scrollY || document.documentElement.scrollTop
        return rect.top + scrollTop // 下面的padding 有个30
      }

      function getListHeight(elId = 'table-pro', hasPagination = true) {
        const element = document.getElementById(elId)
        let topDistance = getElementTop(element)
        if (hasPagination) topDistance += 80 // 如果是表格默认把分页的高度加上
        return `${window.innerHeight - Math.floor(topDistance) - 20}`
      }

      this.$nextTick(() => {
        this.tableHeight = getListHeight('followTable')
      })
    },

    /**
     * @description: 关闭弹窗
     * @author: LiSuwan
     * @Date: 2025-01-16 11:32:11
     */
    closeDialog() {
      this.editUserShow = false
    },
    async getDepartmentList() {
      const res = await getDictionaryValApi('department')
      if (res.code === 200) {
        this.departmentList = res.data
      }
    },
    filterNode(value, data) {
      if (!value) return true
      return data.departName.indexOf(value) !== -1
    },
    addUserClose() {
      this.$refs.addUserForm.resetFields()
      this.addUserShow = false
      this.addUserForm = Object.assign({}, this.$options.data().addUserForm)
      this.loading = false
    },
    onRoleCodesChange(v) {
      for (let vIdx = 0; vIdx < v.length; vIdx++) {
        const currRole = this.role.find((i) => `${i.roleCode}` === `${v[vIdx]}`)
        // 如果有 【胸痛专家】 角色，自动赋值 【远程专家】 角色
        if (currRole.roleCode === 'chest_pain_expert') {
          const remoteRole = this.role.find((i) => i.roleCode === 'remoteExpert')
          const remoteroleCode = remoteRole ? remoteRole.roleCode : ''
          if (!v.includes(remoteroleCode)) {
            this.$message.warning('胸痛专家角色需同时是远程专家。')
            if (this.addUserShow) {
              this.addUserForm.roleCodeList.push(remoteroleCode)
            }
            if (this.editUserShow) {
              this.editUserForm.roleCodeList.push(remoteroleCode)
            }
          }
        }
      }
    },
    async getUserByPhone(phone) {
      const res = await getUserByPhoneApi({ phone })
      if (res.code === 200 && res.data) {
        return res.data
      }
    },
    async editClick(row) {
      const res = await getuserInfoApi(row.id)
      this.getrole()
      const { id, name, originalPhone, status, roleCodeList, userDepartmentCodes, departCode } = res.data
      this.editUserForm = {
        id,
        name,
        status,
        roleCodeList,
        phone: originalPhone,
        userDepartmentCodes,
        departCode
      }
      this.editUserShow = true
    },
    async editputUser() {
      const params = deepClone(this.editUserForm)

      const res = await putuserApi(params)
      if (`${res.code}` === '200') {
        this.$message.success('编辑成功')
      }
      this.editUserShow = false
      this.resetPageNo()
    },
    deleteUser(id) {
      this.$confirm('此操作将永久删除该用户, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          const res = await deleteuserApi(id)
          if (`${res.code}` === '200') {
            this.$message.success('删除成功')
          }
          this.resetPageNo()
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async getrole() {
      const res = await getRole()
      this.editRoleArr = res.data.filter((val) => {
        return val.roleCode !== 'patient'
      })

      this.addRoleArr = res.data.filter((val) => {
        return val.roleCode !== 'patient' && val.roleCode !== 'doctor'
      })

      this.role = res.data
    },
    handleChange(val) {},
    SubmitEditForm() {
      this.$refs.editUserForm.validate((valid) => {
        if (valid) {
          if (!this.editUserForm.password || this.editUserForm.password === '') {
            delete this.editUserForm.password
            this.editputUser()
          } else if (this.editUserForm.password.length < 6) {
            this.$message.warning('密码不能小于六位数')
          } else if (this.editUserForm.password.length > 16) {
            this.$message.warning('密码不能大于十六位')
          } else {
            this.editputUser()
          }
        } else {
          return false
        }
      })
    },
    SubmitForm() {
      this.$refs.addUserForm.validate((valid) => {
        if (valid) {
          this.loading = true
          this.addpostUser()
        } else {
          return false
        }
      })
    },
    async addpostUser() {
      const res = await postuserApi({
        hospitalCode: this.hospital_code,
        ...this.addUserForm
      })
      if (`${res.code}` === '200') {
        this.$message.success('新增成功')
        this.addUserClose()
        this.resetPageNo()
      } else {
        this.$message.error(res.msg && '新增失败')
        this.loading = false
      }
    },
    async getTreeData() {
      const res = await getOrgTreeByIdApi({
        patientId: getUserId()
      })
      this.treeData = res.data

      this.treeDefaultKeys.push(this.treeData[0].departCode) // 默认展开的树节点

      this.searchData.departCode = this.treeData[0].departCode

      this.resetPageNo()
    },
    // 遍历树结构
    addDisabled(dropdownList) {
      const list = []
      try {
        dropdownList.forEach((e, index) => {
          let e_new = deepClone(e)
          if (!e.serviceStation) {
            e_new = { ...e_new, disabled: true }
          }
          if (e.children && e.children.length) {
            const children = this.addDisabled(e.children)
            e_new = { ...e_new, children }
          } else {
            e_new.children = null
          }
          list.push(e_new)
        })
      } catch (error) {
        return []
      }
      return list
    },

    addUser() {
      this.addUserShow = true
      this.getrole()
    },
    handleNodeClick(data) {
      this.searchData.departCode = data.departCode
      this.resetPageNo()
    },
    async getTableData() {
      this.tableLOading = true
      const res = await departUserApi(this.searchData)
      if (res.code === 200) {
        res.data.list.forEach((val) => {
          val.userDepartmentNamesArr = (val.userDepartmentNames && val.userDepartmentNames.split(',')) || []
          val.roleNamesArr = (val.roleNames && val.roleNames.split(',')) || []
        })
        this.tableData = res.data.list
        this.total = res.data.total
        this.tableLOading = false
      }
    },
    handleSizeChange(val) {
      this.searchData.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.searchData.pageNo = val
      this.getTableData()
    },
    treeDataChange(data) {
      for (let i = 0; i < data.length; i++) {
        if (data[i].children && data[i].children.length === 0) {
          delete data[i].children
        }
        if (data[i].children) {
          this.treeDataChange(data[i].children)
        }
      }
      return data
    },
    // 重置页码参数
    resetPageNo() {
      this.searchData.pageNo = 1
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.manage {
  margin: 30px auto 0;
  width: 97%;
  background-color: #fff;
  padding: 15px;
  border-radius: 15px;
  display: flex;

  ::v-deep .el-button {
    padding: 0 1rem !important;
  }
  .title {
    color: #333;
    font-size: 0.65rem;
    padding-left: 15px;
  }

  .left {
    width: 19%;
    height: calc(100vh - 155px);
    border-radius: 15px;
    border: 1px solid #d9d9d9;
    margin-right: 15px;
    padding: 0 15px;

    ::v-deep .el-scrollbar__wrap {
      margin-bottom: -15px !important;
      margin-right: -15px !important;
    }
  }

  .right {
    width: 80%;

    .top {
      width: 100%;
      height: 90px;
      border-radius: 15px;
      border: 1px solid #d9d9d9;
      display: flex;
      align-items: center;
      // justify-content: space-between;
      padding: 0 35px 0 15px;
    }

    .content {
      margin-top: 15px;
      border-radius: 15px;
      border: 1px solid #d9d9d9;
      overflow-y: auto;
    }
  }
}
::v-deep .el-select,
::v-deep .el-cascader {
  width: 100%;
}
::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}

// ::v-deep .el-tree-node__content {
//   height: 45px;
// }

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.7rem;
  padding-right: 8px;

  .spanbtn {
    display: none;
  }
}

::v-deep .el-form--inline .el-form-item {
  width: 46%;
  margin-bottom: 10px;
  display: inline-block;

  .el-form-item__label {
    font-size: 0.7rem;
    line-height: 1.8rem !important;
    color: #333;
    font-weight: 400;
  }

  .el-form-item__content {
    width: calc(100% - 70px);
    padding-right: 0rem;
    div {
      width: 100%;
    }
  }
}

.scrollbar_area {
  width: 100%;
  max-height: 70vh;
  border: 1px solid #dfdfdf;
  margin: 0.6rem 0 0rem;
  overflow: auto;
  padding: 0.5rem;
}
@media (max-width: 1280px) {
  .scrollbar_area {
    max-height: 55vh;
  }
}

::v-deep .el-pager li {
  margin: 0px 5px;
}
</style>
