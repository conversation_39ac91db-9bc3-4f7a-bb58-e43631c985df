<!-- 评估结果 -->
<template>
  <div class="evaluation-results">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <div v-for="item in questionnaireList.filter(it => it.id !== 'pgjg')" :key="item.id">
        <flag-component :title="`${item.name}筛查结果`" />
        <RiskAssessment v-if="item.id === 'fangchan'" ref="riskAssessment" />
        <el-form-item label="评估结果：" :prop="`${item.id === 'fangchan' ? 'fcResult' : `${item.id}Result`}`">
          <el-radio-group v-model="form[`${item.id === 'fangchan' ? 'fcResult' : `${item.id}Result`}`]">
            <el-radio :label="1">低风险</el-radio>
            <el-radio :label="2">高风险</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="评估说明：">
          <el-input
            v-model="form[`${item.id === 'fangchan' ? 'fcDescription' : `${item.id}Description`}`]"
            type="textarea"
          />
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import FlagComponent from '@/components/flagComponent/index.vue'
import RiskAssessment from '@/views/receptionCenter/managePatient/component/riskAssessment.vue'

export default {
  name: 'EvaluationResults',
  components: {
    FlagComponent,
    RiskAssessment
  },
  props: {
    questionnaireList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        tnbResult: '',
        tnbDescription: '',
        gxyResult: '',
        gxyDescription: '',
        fcResult: '',
        fcDescription: ''
      },
      rules: {
        tnbResult: [{ required: true, message: '请选择评估结果', trigger: 'change' }],
        gxyResult: [{ required: true, message: '请选择评估结果', trigger: 'change' }],
        fcResult: [{ required: true, message: '请选择评估结果', trigger: 'change' }]
      }
    }
  },
  methods: {
    initData(data) {
      Object.keys(this.form).forEach((key) => {
        this.form[key] = data[key] || ''
      })
      this.$refs.riskAssessment && this.$refs.riskAssessment[0].initData(data)
    },
    async handleSave() {
      const riskAssessmentData = this.questionnaireList.find((it) => it.id === 'fangchan')
        ? await this.$refs.riskAssessment[0].handleSave()
        : {}
      const result = {
        name: '评估结果',
        success: false,
        data: {
          ...this.form,
          ...riskAssessmentData
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn('评估结果校验异常', err)
        result.success = false
      }
      return result
    }
  }
}
</script>
