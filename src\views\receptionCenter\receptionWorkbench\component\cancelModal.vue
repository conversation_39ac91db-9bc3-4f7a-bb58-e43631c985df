<template>
  <ProDialog ref="cancelDialog" title="取消" :visible.sync="visible" top="10vh" width="600px">
    <div>
      <el-form :model="form" label-width="100px">
        <el-form-item label="取消原因：">
          <el-select v-model="form.cancelReason" placeholder="请选择" style="width: 100%">
            <el-option label="家属/患者认为不需要医生管理" value="家属/患者认为不需要医生管理" />
            <el-option label="填写表格内容过多" value="填写表格内容过多" />
            <el-option label="经济因素" value="经济因素" />
            <el-option label="有固定长期随访医生" value="有固定长期随访医生" />
            <el-option label="不信任医生" value="不信任医生" />
            <el-option label="不想暴露过多个人隐私" value="不想暴露过多个人隐私" />
            <el-option label="其他原因" value="其他原因" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </span>
  </ProDialog>
</template>

<script>
import ProDialog from '@/components/ProDialog/index.vue'

export default {
  name: 'CancelModal',
  components: {
    ProDialog
  },
  data() {
    return {
      visible: false,
      form: {
        cancelReason: ''
      }
    }
  },
  methods: {
    handleClose() {
      this.visible = false
      this.form.cancelReason = ''
    },
    handleConfirm() {
      this.$emit('confirm', this.form.cancelReason)
      this.handleClose()
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
</style>
