<template>
  <div class="patient-info">
    <div class="patient-info-item">
      <img src="@/assets/cspImg/patient.png" alt="头像">
      <div class="patient-info-item-name">{{ patientInfo.name || patientInfo.patientName }}</div>
    </div>
    <div class="patient-info-item">
      <div class="patient-info-item-label">年龄：</div>
      <div class="patient-info-item-value">{{ patientInfo.age }}</div>
    </div>
    <div class="patient-info-item">
      <div class="patient-info-item-label">性别：</div>
      <div class="patient-info-item-value">{{ genderTransform(patientInfo.sex) }}</div>
    </div>

    <div class="patient-info-item">
      <el-tag
        v-for="tag in diseaseTags"
        :key="tag"
        :type="tag === '高血压' ? 'danger' : tag === '糖尿病' ? 'success' : tag === '慢阻肺' ? 'warning' : ''"
      >{{ tag }}</el-tag>
    </div>

    <div v-if="$route.path === '/archives/detail'" class="patient-info-item">
      <div class="patient-info-item-custom-label patient-info-item-label">建档时间：</div>
      <div class="patient-info-item-value">{{ patientInfo.registerDate }}</div>
    </div>

    <div
      v-if="
        $route.path === '/receptionCenter/patientReception' &&
          $store.state.receptionWorkbench.receptionWorkbenchData.receptionStatus === 1 &&
          !$route.query.active
      "
      class="patient-info-item"
    >
      <el-button type="danger" @click="handleCancel">取消接诊</el-button>
      <el-button type="primary" @click="handleFinish">完成接诊</el-button>
      <el-button type="primary" @click="handleManage">规范管理</el-button>
    </div>

    <div
      v-if="
        $route.path === '/receptionCenter/managePatient' && $store.state.managePatient.managePatientData.status === 1
      "
      class="patient-info-item"
    >
      <el-button type="danger" @click="handleCancel">取消管理</el-button>
      <el-button type="primary" @click="handleFinishManage">完成管理</el-button>
      <el-button type="primary" @click="handleCheck">检验检查</el-button>
      <el-button type="primary" @click="handleFamilyDoctor">服务包签订</el-button>
    </div>

    <div
      v-if="
        $route.path === '/receptionCenter/patientExamination' &&
          $store.state.patientExamination.patientExaminationData.status === 1
      "
      class="patient-info-item"
    >
      <el-button type="danger" @click="handleCancel">取消检查</el-button>
      <el-button type="primary" @click="handleFinishCheck">完成检查</el-button>
    </div>

    <div class="patient-info-item">
      <remoteConsultation
        v-if="
          !$route.query.active &&
            $route.path !== '/qualityControl/ultrasonicQualityControl/detail' &&
            $route.path !== '/archives/detail'
        "
        :module-type="moduleType"
        :patient-info="patientInfo"
      />
    </div>

    <div v-if="$route.path === '/qualityControl/ultrasonicQualityControl/detail'" class="patient-info-item">
      <span style="font-size: 16px; font-weight: 400">检查项目：{{ patientInfo.itemName }}</span>
    </div>

    <CancelModal ref="cancelDialog" @confirm="handleCancelConfirm" />
  </div>
</template>

<script>
import { cancelReceptionWorkbench, completeReceptionWorkbench } from '@/api/receptionWorkbench'
import {
  cancelStandardizedManage,
  completeStandardizedManage,
  getStandardizedManageDetailByPatientId
} from '@/api/standardizedManage'
import { addInspectionTesting, cancelCheck, completeCheck } from '@/api/examination'
import { genderTransform } from '@/utils/cspUtils'
import remoteConsultation from '@/components/remoteConsultation/index.vue'
import CancelModal from '@/views/receptionCenter/receptionWorkbench/component/cancelModal.vue'

export default {
  name: 'PatientInfo',
  components: {
    remoteConsultation,
    CancelModal
  },
  props: {
    moduleType: {
      type: String,
      default: ''
    },
    patientInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  computed: {
    diseaseTags() {
      return (
        this.patientInfo.disease &&
        this.patientInfo.disease.split(',').map((item) => {
          if (item === 'gxy') {
            return '高血压'
          } else if (item === 'tnb') {
            return '糖尿病'
          } else if (item === 'fangchan') {
            return '房颤'
          } else {
            return '慢阻肺'
          }
        })
      )
    }
  },
  methods: {
    genderTransform,
    handleCancel() {
      this.$refs.cancelDialog.form.cancelReason = ''
      this.$refs.cancelDialog.visible = true
    },

    async handleCancelConfirm(reason) {
      let res = {}
      if (this.$route.path === '/receptionCenter/patientReception') {
        res = await cancelReceptionWorkbench({
          id: this.patientInfo.id,
          cancelReason: reason
        })
      } else if (this.$route.path === '/receptionCenter/managePatient') {
        res = await cancelStandardizedManage({
          id: this.patientInfo.id,
          cancelReason: reason
        })
      } else if (this.$route.path === '/receptionCenter/patientExamination') {
        res = await cancelCheck({
          id: this.patientInfo.id,
          cancelReason: reason
        })
      }
      if (res.code === 200) {
        this.$message.success('取消成功')
        this.$refs.cancelDialog.visible = false
        this.$router.go(-1)
      }
    },

    async handleFinish() {
      const res = await completeReceptionWorkbench({
        id: this.patientInfo.id
      })
      if (res.code === 200) {
        this.$message.success('接诊完成')
        this.$router.go(-1)
      }
    },

    async handleManage() {
      this.$store.commit('managePatient/SET_MANAGE_PATIENT_DATA', {})
      const res = await getStandardizedManageDetailByPatientId({
        patientId: this.patientInfo.patientId
      })
      if (res.code === 200) {
        this.$router.push({
          path: '/receptionCenter/managePatient',
          query: {
            id: res.data.id
          }
        })
      }
    },

    async handleFinishManage() {
      const res = await completeStandardizedManage({
        id: this.patientInfo.id
      })
      if (res.code === 200) {
        this.$message.success('完成管理')
        this.$router.go(-1)
      }
    },

    async handleCheck() {
      this.$store.commit('patientExamination/SET_PATIENT_EXAMINATION_DATA', {})
      const res = await addInspectionTesting({
        patientId: this.patientInfo.patientId
      })
      if (res.code === 200) {
        this.$router.push({
          path: '/receptionCenter/patientExamination',
          query: {
            id: res.data.id
          }
        })
      }
    },

    async handleFinishCheck() {
      const res = await completeCheck({
        id: this.patientInfo.id
      })
      if (res.code === 200) {
        this.$message.success('完成检查')
        this.$router.go(-1)
      }
    },

    handleFamilyDoctor() {
      this.$router.push({
        path: '/servicePackageManage/individualSigning'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.code {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
  .bottom-code-text {
    font-size: 16px;
    font-weight: 500;
  }
}
.patient-info {
  display: flex;
  gap: 80px;
  .patient-info-item {
    display: flex;
    align-items: center;
    gap: 6px;
    .patient-info-item-name {
      font-size: 20px;
    }
    .patient-info-item-label,
    .patient-info-item-value {
      font-size: 16px;
      font-weight: 400;
    }
    .patient-info-item-label {
      width: 80px;
    }
  }
}

@media only screen and (max-width: 1600px) {
  .patient-info {
    gap: 25px !important;

    .patient-info-item {
      .patient-info-item-label {
        width: 65px !important;
      }
      .patient-info-item-custom-label {
        width: 80px !important;
      }
    }
  }
}
</style>
