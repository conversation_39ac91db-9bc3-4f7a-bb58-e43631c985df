<!-- Vue SFC -->
<template>
  <div :class="`wiTreeInputContainer type-${treeType} applicationCustomScrollBar`">
    <treeselect
      ref="treeselectRef"
      v-model="value"
      :multiple="multiple"
      :disabled="disabled"
      :options="options"
      :close-on-select="true"
      :clearable="clearable"
      :flat="true"
      :default-expand-level="defaultExpandLevel"
      :placeholder="placeholder"
      no-results-text="无匹配项"
      :no-options-text="noOptionsText"
      no-children-text="无子项"
      clear-all-text="清除全部"
      clear-value-text="清除"
      @select="treeSelectChange"
      @open="treeSelectOpen"
      @close="treeSelectClose"
    />
  </div>
</template>

<script>
// import the component
import Treeselect from '@riophae/vue-treeselect'
// import the styles
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  // register the component
  components: { Treeselect },
  props: {
    options: {
      type: Array,
      default: () => {
        return []
      }
    },
    disabled: Boolean,
    clearable: Boolean,
    defaultExpandLevel: {
      type: Number,
      default: 0
    },
    defaultValue: {
      type: String,
      default: ''
    },
    treeType: {
      type: String,
      default: 'normal'
    },
    placeholder: {
      type: String,
      default: '请选择地点'
    },
    noOptionsText: {
      type: String,
      default: '无可选项'
    },
    multiple: Boolean
  },
  data() {
    return {
      // define the default value
      value: null
    }
  },
  watch: {
    value(v) {
      console.log('wiTreeInput')
      console.log('-------------------------------------')
      console.log(v)

      if (!v) {
        this.handleClear()
      }
    }
  },
  mounted() {
    const targetDomEl = this.$refs.treeselectRef.$el.getElementsByClassName(
      'vue-treeselect__control-arrow-container'
    )[0]
    targetDomEl.innerHTML = `<i class="el-icon-arrow-down"></i>`
  },
  methods: {
    treeSelectChange(val) {
      this.$nextTick(() => {
        if (this.clearable) {
          const clearEl = this.$refs.treeselectRef.$el.getElementsByClassName('vue-treeselect__x-container')[0]
          clearEl.innerHTML = `<i class="el-icon-circle-close"></i>`
        }

        this.treeSelectClose()
      })
      this.$emit('onChange', val)
    },
    handleClear() {
      console.log('=>>>>>  onClearonClear')
      this.$emit('onClear')
      this.$nextTick(() => {
        this.treeSelectClose()
      })
    },
    treeSelectOpen() {
      const targetDomEl = this.$refs.treeselectRef.$el.getElementsByClassName(
        'vue-treeselect__control-arrow-container'
      )[0]
      targetDomEl.innerHTML = `<i class="el-icon-arrow-down rotateOpen"></i>`
    },
    treeSelectClose() {
      const targetDomEl = this.$refs.treeselectRef.$el.getElementsByClassName(
        'vue-treeselect__control-arrow-container'
      )[0]
      targetDomEl.innerHTML = `<i class="el-icon-arrow-down"></i>`
    },
    setValue(val) {
      this.value = val
    }
  }
}
</script>

<style lang="scss">
.wiTreeInputContainer {
  .vue-treeselect__menu-container {
    z-index: 3090 !important;
  }
  .rotateOpen {
    rotate: -180deg;
  }
  &.type-normal {
    .vue-treeselect {
      &.vue-treeselect--disabled {
        .vue-treeselect__input-container {
          display: none !important;
        }
        background-color: #f5f7fa;
        border-color: #dfe4ed;
        color: #c0c4cc;
        cursor: not-allowed;
        .vue-treeselect__single-value {
          color: #c0c4cc !important;
        }
      }
      .vue-treeselect__control {
        padding: 0 5px 0 5px;
        background: transparent;

        .vue-treeselect__value-container {
          .vue-treeselect__single-value {
            height: 38px;
            font-size: 0.6rem;
            line-height: 38px;
            padding: 0 10px !important;
            color: #606266;
          }
          .vue-treeselect__placeholder {
            height: 38px;
            font-size: 0.7rem;
            line-height: 38px;
            padding: 0 10px;
          }
          .vue-treeselect__input-container {
            height: 38px;
            padding: 0 10px !important;
            display: flex;
            font-size: 0.7rem;
            align-items: center;
            .vue-treeselect__input,
            .vue-treeselect__input::placeholder {
              height: 38px;
              font-size: 0.7rem;
            }
          }
          .vue-treeselect__multi-value {
            margin-bottom: 0 !important;
          }
        }
        .vue-treeselect__control-arrow-container {
          width: 25px;
          color: #c0c4cc;
          font-size: 0.7rem;
          i {
            transition-duration: 0.3s;
          }
          .vue-treeselect__control-arrow {
            width: 14px;
            height: 9px;
          }
        }
      }

      .vue-treeselect__menu-container {
        z-index: 3090 !important;

        .vue-treeselect__menu {
          backdrop-filter: blur(5px);
          .vue-treeselect__label {
            color: #999999;
          }

          .vue-treeselect__option {
            .vue-treeselect__option-arrow-container {
              .vue-treeselect__option-arrow {
                width: 14px;
                height: 9px;
              }
            }
            &--selected {
              .vue-treeselect__option-arrow {
                width: 14px;
                height: 9px;
              }
            }
          }
        }
      }
    }
  }
  &.type-dark {
    .vue-treeselect {
      .vue-treeselect__tip-text {
        color: #ffffff;
      }
      .vue-treeselect__control {
        border: 1px solid #27a8f1;
        padding: 0 5px 0 5px;
        background: transparent;

        .vue-treeselect__value-container {
          margin-top: 5px;
          .vue-treeselect__single-value {
            height: 35px;
            font-size: 0.6rem;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #27a8f1;
            line-height: 35px;
          }
          .vue-treeselect__placeholder {
            height: 35px;
            font-size: 0.6rem;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #27a8f1;
            line-height: 35px;
          }
          .vue-treeselect__input-container {
            .vue-treeselect__input {
              height: 40px !important;
              color: #27a8f1;
            }
          }
          .vue-treeselect__multi-value {
            margin-bottom: 0 !important;
            .vue-treeselect__multi-value-item {
              background: #31a0df;
              color: #ffffff;
              .vue-treeselect__multi-value-label {
                color: #ffffff;
              }
              .vue-treeselect__icon.vue-treeselect__value-remove {
                color: #ffffff;
                fill: #ffffff;
                border-left: 1px solid #fff;
                &:hover {
                  color: #dfdfdf;
                  fill: #dfdfdf;
                }
              }
            }
          }
        }
        .vue-treeselect__control-arrow-container {
          .vue-treeselect__control-arrow {
            fill: #27a8f1 !important;
            width: 14px;
            height: 9px;
            &:hover {
              fill: #49caf3 !important;
            }
          }
        }
      }

      .vue-treeselect__menu-container {
        z-index: 3090 !important;

        .vue-treeselect__menu {
          background: linear-gradient(360deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.3) 100%);
          border: 1px solid rgba(0, 214, 215, 0.5);
          backdrop-filter: blur(5px);

          .vue-treeselect__option {
            .vue-treeselect__option-arrow-container {
              .vue-treeselect__option-arrow {
                fill: #ffffff;
                width: 14px;
                height: 9px;
                &:hover {
                  fill: #ffffff;
                }
              }
            }
            .vue-treeselect__label {
              color: #ffffff;
            }
            &--selected {
              background: #046f7f;
              .vue-treeselect__option-arrow {
                fill: #08fffe !important;
                width: 14px;
                height: 9px;
                &:hover {
                  fill: #08fffe !important;
                }
              }
              .vue-treeselect__label {
                color: #08fffe;
              }
            }
            &--highlight {
              background: #268f9f;
            }
          }
        }
      }
    }
  }
}
</style>
