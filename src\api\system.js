import request from '@/utils/request'

// 批量导出
export function exportOneDataApi(params, exportOneUrl) {
  return request({
    // url: '/cspapi/backend/familyDoctor/doctor/export/one',
    url: exportOneUrl,
    method: 'get',
    responseType: 'blob',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params
  })
}

// 批量导出
export function exportDataApi(params, exportUrl) {
  return request({
    // url: `/cspapi/backend/familyDoctor/doctor/export/page`,
    url: exportUrl,
    method: 'get',
    responseType: 'blob',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params
  })
}

// 角色权限与管理 ！！！！！！！！！！！！！！！！！

// 查询所有角色
export function getRole(params) {
  return request({
    url: '/cspapi/backend/role/list',
    method: 'get',
    params
  })
}

// 增加角色
export function addRole(data) {
  return request({
    url: '/cspapi/backend/role/',
    method: 'post',
    data
  })
}

//  根据角色id查询用户列表
export function getqueryUserByRole(id) {
  return request({
    url: `/cspapi/backend/user/role/${id}`,
    method: 'get'
  })
}
//  删除角色
export function getdeleteUser(id) {
  return request({
    url: `/cspapi/backend/role/${id}`,
    method: 'delete'
  })
}
//  查看某个角色对应的菜单以及操作权限
export function getroleMenuUser(id) {
  return request({
    url: `/cspapi/backend/role/menu/${id}`,
    method: 'get'
  })
}
//  查看某个角色对应的菜单以及操作权限
export function postroleMenuApi(data) {
  return request({
    url: '/cspapi/backend/role/menu',
    method: 'post',
    data
  })
}

//   递归查询用户所具有的左侧菜单列表

// export function getQueryMenuPermissionByTree(id) {
//   return request({
//     url: '/cspapi/backend/opPermission/' + id,
//     method: 'get',
//   })
// }

//   递归查询所有菜单

export function getQueryMenuPermissionTree() {
  return request({
    url: '/cspapi/backend/menu/tree',
    method: 'get'
  })
}

//  根据角色id查询所有角色职位

export function getlistByRoleId(id) {
  return request({
    url: `/cspapi/backend/rolePosition/${id}`,
    method: 'get'
  })
}

// 增加职位(与角色关联)
export function addJob(data) {
  return request({
    url: '/cspapi/backend/rolePosition/',
    method: 'post',
    data
  })
}
// 删除 角色-职位 关系
export function deleteJob(id) {
  return request({
    url: `/cspapi/backend/rolePosition/${id}`,
    method: 'delete'
  })
}

//  (树)递归查询所有菜单

export function getqueryUserPermissionId(id) {
  return request({
    url: `/cspapi/backend/menu/user/${id}`,
    method: 'get'
  })
}

//  根据菜单id查询操作权限列表

export function getOpPermissiondId(id) {
  return request({
    url: `/cspapi/backend/opPermission/${id}`,
    method: 'get'
  })
}

// 设置用户权限(用这个)
export function setUserPermission(data) {
  return request({
    url: '/cspapi/backend/menu/userPermission',
    method: 'post',
    data
  })
}

// 我的菜单  仅自己可见 ！！！！！！！！！
//  新增菜单
export function addmenuApi(data) {
  return request({
    url: '/cspapi/backend/menu/',
    method: 'post',
    data
  })
}
//  新增菜单
export function deletemenuApi(id) {
  return request({
    url: `/cspapi/backend/menu/${id}`,
    method: 'delete'
  })
}
//  修改菜单
export function putmenuApi(data) {
  return request({
    url: '/cspapi/backend/menu/',
    method: 'put',
    data
  })
}
//  新增操作权限
export function postOpPermission(data) {
  return request({
    url: '/cspapi/backend/opPermission',
    method: 'post',
    data
  })
}

// 订单管理 菜单管理
export function getOrderApi(data) {
  return request({
    url: '/cspapi/backend/order/',
    method: 'get',
    params: data
  })
}
// 1.2.3 查看同步记录列表(分页)
export function getLogApi(data) {
  return request({
    url: '/cspapi/backend/app/user/syncTime/list',
    method: 'get',
    params: data
  })
}
export function getAuthApi() {
  return request({
    url: '/cspapi/backend/redis/pattern/auth',
    method: 'get'
  })
}

// 病种管理 ！！！！！
//   病种列表(条件查询)
export function diseaseApi(data) {
  return request({
    url: '/cspapi/backend/disease/page',
    method: 'get',
    params: data
  })
}

//    增加病种
export function addDiseaseApi(data) {
  return request({
    url: '/cspapi/backend/disease/',
    method: 'post',
    data
  })
}

//    删除病种
export function deleteDiseaseApi(id) {
  return request({
    url: `/cspapi/backend/disease/${id}`,
    method: 'delete'
  })
}

//     修改病种
export function putDiseaseApi(data) {
  return request({
    url: '/cspapi/backend/disease/',
    method: 'put',
    data
  })
}

// 用户管理 ！！！！！
// 查询医院基础信息
export function hospitalAllApi() {
  return request({
    url: '/cspapi/backend/depart2/tree',
    method: 'get'
  })
}

// 分页查询某科室下的人员
export function departUserApi(data) {
  return request({
    url: '/cspapi/backend/depart/user/page',
    method: 'post',
    data
  })
}
// 查询医生
export function listDistinctManageDoctor(data) {
  return request({
    url: '/cspapi/backend/user/listDistinctManageDoctor',
    method: 'get',
    params: data
  })
}

// 查询医院基础信息
export function hospitalListApi(id) {
  return request({
    url: '/cspapi/backend/hospital/getById',
    method: 'get',
    params: { hospitalId: id }
  })
}
// 修改医院基础信息
export function hospitalModApi(data) {
  return request({
    url: '/cspapi/backend/hospital/',
    method: 'put',
    data
  })
}

// 查询单个部门信息
export function depart2Api(id) {
  return request({
    url: `/cspapi/backend/depart2/${id}`,
    method: 'get'
  })
}

// 修改部门
export function putDepart2Api(data) {
  return request({
    url: '/cspapi/backend/depart2/',
    method: 'put',
    data
  })
}

// 新增部门
export function postDepart2Api(data) {
  return request({
    url: '/cspapi/backend/depart2/',
    method: 'post',
    data
  })
}

export function deleteDepart2Api(id) {
  return request({
    url: `/cspapi/backend/depart2/${id}`,
    method: 'delete'
  })
}

export function getuserInfoApi(id) {
  return request({
    url: `/cspapi/backend/user/${id}`,
    method: 'get'
  })
}

// 新增用户
export function postuserApi(data) {
  return request({
    url: '/cspapi/backend/user',
    method: 'post',
    data
  })
}

// 删除用户
export function deleteuserApi(id) {
  return request({
    url: `/cspapi/backend/user/${id}`,
    method: 'delete'
  })
}

//  修改用户  医生
export function putuserApi(data) {
  return request({
    url: '/cspapi/backend/user/',
    method: 'put',
    data
  })
}
//  修改用户  患者
export function putPatientApi(data) {
  return request({
    url: '/cspapi/backend/user/patient',
    method: 'put',
    data
  })
}

//  查询所有健康顾问 / 医生
export function listByTypeApi(data) {
  return request({
    url: '/cspapi/backend/user/listByType',
    method: 'get',
    params: data
  })
}

//  根据健康顾问id获取关联的医生列表
export function userdoctorApi(id) {
  return request({
    url: `/cspapi/backend/im/user/doctor/${id}`,
    method: 'get'
  })
}

//  绑定健康顾问和医生关联(单个)
export function relationAddApi(data) {
  return request({
    url: '/cspapi/backend/im/user/relation/addAll',
    method: 'post',
    data
  })
}

//  根据手机号查询用户信息
export function getUserByPhoneApi(data) {
  return request({
    url: '/cspapi/backend/base/user/doctor/phone',
    method: 'post',
    data
  })
}

// 机构名称配置 ！！！！
// 查询服务站点树
export function getserviceStationApi() {
  return request({
    url: '/cspapi/backend/serviceStation/tree',
    method: 'get'
  })
}

export function exportVisitCheck(queueId) {
  return request({
    url: '/cspapi/backend/visitCheck/status/export',
    method: 'get',
    responseType: 'blob',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: { queueId }
  })
}
//  新增服务站点
export function addServiceStationApi(data) {
  return request({
    url: '/cspapi/backend/serviceStation',
    method: 'post',
    data
  })
}
//  新增服务站点  新增服务站点(独立)  单个
export function addServiceStationNewApi(data) {
  return request({
    url: '/cspapi/backend/serviceStation/add/alone',
    method: 'post',
    data
  })
}
//  删除服务站点
export function removeServiceStationApi(id) {
  return request({
    url: '/cspapi/backend/serviceStation',
    method: 'delete',
    params: { id }
  })
}
//  查询接入的服务点(根据父节点)  全部的
export function getServiceStationListApi(pid) {
  return request({
    url: '/cspapi/backend/depart2/listStationByPid',
    method: 'get',
    params: { pid }
  })
}

//  查询服务站点(list) 已有的
export function getServiceStationHasListApi(id) {
  return request({
    url: `/cspapi/backend/serviceStation/list/${id}`,
    method: 'get'
  })
}

// 知情同意书 ！！！！！！！！
// 列表分页-知情同意书
export function getInformedConsentListApi(params) {
  return request({
    url: `/cspapi/backend/visit/informedConsent/page`,
    method: 'get',
    params
  })
}
// 新增知情同意书
export function addInformedConsentListApi(data) {
  return request({
    url: `/cspapi/backend/visit/informedConsent`,
    method: 'post',
    data
  })
}
// 编辑  知情同意书
export function putInformedConsentListApi(data) {
  return request({
    url: `/cspapi/backend/visit/informedConsent`,
    method: 'put',
    data
  })
}
// 编辑  知情同意书
export function removeInformedConsentListApi(id) {
  return request({
    url: `/cspapi/backend/visit/informedConsent/${id}`,
    method: 'delete'
  })
}
// 查看详情  知情同意书
export function getInformedConsentDetailsApi(id, token) {
  return request({
    url: `/cspapi/backend/visit/informedConsent/${id}`,
    method: 'get',
    headers: {
      Authorization: token
    }
  })
}
// 验证是否签署知情同意书
export function getSignStatusApi(params, token) {
  return request({
    url: `/cspapi/backend/visitCheck/groupUser/getSignStatusByGroupIdIdAndUserId`,
    method: 'get',
    params,
    headers: {
      Authorization: token
    }
  })
}
// 根据姓名模糊查询用户信息
export function getUserlistByNameApi(name) {
  return request({
    url: `/cspapi/backend/user/listByName`,
    method: 'get',
    params: { name, roleCode: 'patient' }
  })
}
// ********* 日志列表
export function logPageByModel(params) {
  return request({
    url: `/cspapi/backend/active/log/pageByModel`,
    method: 'get',
    params
  })
}
// 30.23.2 识别语音质量
export function getVoiceApi(data) {
  return request({
    url: `/cspapi/backend/wr/voice`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

// 健康科普

//  分页查询科普文章列表
export function getHealthQueryArticle(data) {
  return request({
    url: '/cspapi/backend/healthArticle/page',
    method: 'get',
    params: data
  })
}

//  添加科普文章
export function getHealthArticleAdd(data) {
  return request({
    url: '/cspapi/backend/healthArticle/add',
    method: 'post',
    data
  })
}

//  修改科普文章
export function getHealthArticleSet(data) {
  return request({
    url: '/cspapi/backend/healthArticle/update',
    method: 'put',
    data
  })
}

//   查看科普文章详情
export function getHealthArticle(id) {
  return request({
    url: `/cspapi/backend/healthArticle/${id}`,
    method: 'get'
  })
}

//   删除科普文章（支持批量）
export function getHealthArticledel(str) {
  return request({
    url: `/cspapi/backend/healthArticle/del?ids=${str}`,
    method: 'delete'
  })
}

// 查看健康科普文章
export function getArticleApi(id) {
  return request({
    url: `/cspapi/backend/common/article/${id}`,
    method: 'get'
  })
}

// 随访审核 ！！！
// 审核列表
export function getExamListApi(params) {
  return request({
    url: `/cspapi/backend/visitCheck/exam/list`,
    method: 'get',
    params
  })
}
// 审核统计
export function getExamStatisticsApi() {
  return request({
    url: `/cspapi/backend/visitCheck/exam/statistics`,
    method: 'get'
  })
}
// 无需审核列表
export function getNotexamListApi(params) {
  return request({
    url: `/cspapi/backend/visitCheck/notexam/list`,
    method: 'get',
    params
  })
}
// 获取需要审核的详细信息
export function getExamUserInfoApi(id) {
  return request({
    url: `/cspapi/backend/visitCheck/exam/${id}`,
    method: 'get'
  })
}
// 审核
export function getExamUserCheckApi(data) {
  return request({
    url: `/cspapi/backend/visitCheck/exam/check`,
    method: 'post',
    data
  })
}

// 删除无需审核
export function getRemoveNotexamApi(idCard) {
  return request({
    url: `/cspapi/backend/visitCheck/notexam/del`,
    method: 'delete',
    params: { idCard }
  })
}

export function getmanageDoctorApi(data) {
  return request({
    url: `/cspapi/backend/base/user/base/doctor/page`,
    method: 'post',
    data
  })
}

// 获取登录信息
export function postLastLoginInfoApi(data) {
  return request({
    url: '/cspapi/backend/sys/log/opera/lastLoginInfo',
    method: 'get',
    params: data
  })
}

/**
 * @description: 解密手机号和身份照等（显示原文）
 * @param {*} data
 * @return {*}
 * @author: LiSuwan
 * @Date: 2025-01-16 17:02:56
 */
export function backendParamDecryptApi(data) {
  return request({
    url: '/cspapi/backend/param/decrypt',
    method: 'get',
    params: data
  })
}

// 获取数据填报列表数据
export function getListDataApi(data) {
  return request({
    url: '/cspapi/backend/depart/people/list',
    method: 'post',
    data
  })
}
// 获取数据填报列表数据
export function getCardDataApi(data) {
  return request({
    url: '/cspapi/backend/depart/people/statistics',
    method: 'post',
    data
  })
}

// 保存数据填报列表数据
export function saveListDataApi(data) {
  return request({
    url: '/cspapi/backend/depart/people/save/batch',
    method: 'post',
    data
  })
}

// 服务日志
export function getServiceLogApi(data) {
  return request({
    url: '/cspapi/backend/sys/duration/getSysDurationPage',
    method: 'post',
    data
  })
}

// csp查询组织架构
export function getOrgTreeApi(data) {
  return request({
    url: '/cspapi/backend/depart/query/tree',
    method: 'post',
    data
  })
}

// csp保存组织架构
export function saveOrgTreeApi(data) {
  return request({
    url: '/cspapi/backend/depart/save',
    method: 'post',
    data
  })
}

// csp删除组织架构
export function deleteOrgTreeApi(data) {
  return request({
    url: '/cspapi/backend/depart/remove',
    method: 'post',
    data
  })
}

// 根据id查询组织架构
export function getOrgTreeByIdApi(data) {
  return request({
    url: `/cspapi/backend/depart/query/tree/user`,
    method: 'post',
    data
  })
}

// 字典查询
export function getDictApi(code) {
  return request({
    url: `/cspapi/backend/sys/dictionary/listByModuleCode/${code}`,
    method: 'get'
  })
}

// ocr识别
export function getOcrApi(data) {
  return request({
    url: '/cspapi/backend/ocr/gaopaiyi/base64str',
    method: 'post',
    data
  })
}

// 查询患者信息By身份证号
export function getPatientInfoByIdCardApi(data) {
  return request({
    url: `/cspapi/backend/base/user/patient/idCard`,
    method: 'post',
    data
  })
}

// 绑定网关
export function bindGatewayApi(data) {
  return request({
    url: '/cspapi/backend/boxinfo/box/bind',
    method: 'post',
    data
  })
}

// 网关查询
export function getGatewayListApi(data) {
  return request({
    url: '/cspapi/backend/boxinfo/box/list',
    method: 'post',
    data
  })
}

// 解绑网关
export function unbindGatewayApi(data) {
  return request({
    url: '/cspapi/backend/boxinfo/box/unbind',
    method: 'post',
    data
  })
}

// 文件上传
export function fileUoload(data) {
  return request({
    url: '/cspapi/backend/cos/uploadFile/private2',
    method: 'post',
    data
  })
}
