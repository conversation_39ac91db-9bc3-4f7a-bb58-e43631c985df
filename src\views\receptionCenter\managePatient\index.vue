<template>
  <!-- eslint-disable vue/first-attribute-linebreak -->
  <div class="manage-patient">
    <el-card>
      <patient-info :patient-info="$store.state.managePatient.managePatientData" :module-type="'inspect'" />
    </el-card>

    <el-card
      v-if="
        $store.state.managePatient.managePatientData.id &&
          !!$store.state.managePatient.managePatientData.disease &&
          stepList.length > 0
      "
    >
      <div class="manage-step">
        <StepIndicator :steps="stepList" :current-step="active" @stepClick="handleStepChange" />
      </div>

      <div class="manage-content">
        <risk-assessment v-if="stepList.length === 5 && active === 0" ref="riskAssessmentRef" :history-id="historyId" />
        <manage-by-objectives
          v-if="stepList.length === 5 ? active === 1 : active === 0"
          ref="manageByObjectives"
          :history-id="historyId"
        />
        <therapeutic-action
          v-if="stepList.length === 5 ? active === 2 : active === 1"
          ref="therapeuticActionRef"
          :history-id="historyId"
        />
        <target-inspection
          v-if="stepList.length === 5 ? active === 3 : active === 2"
          ref="targetInspectionRef"
          :history-id="historyId"
        />
        <effect-evaluation
          v-if="stepList.length === 5 ? active === 4 : active === 3"
          ref="effectEvaluationRef"
          :history-id="historyId"
        />
      </div>

      <div
        v-if="$store.state.managePatient.managePatientData.status === 1 && !historyId"
        style="
          position: fixed;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          z-index: 888;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 100%;
          background-color: #fff;
          padding: 10px;
          border-radius: 4px;
          box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
        "
      >
        <!-- 规范管理最后一步去掉保存按钮 -->
        <el-button
          v-if="stepList.length === 5 ? active < 4 : active < 3"
          type="success"
          style="background-color: #0bae01; border-color: #0bae01"
          :loading="$store.state.managePatient.saveBtnLoading"
          @click="handleSave('save')"
        >
          保存
        </el-button>
        <el-button v-if="active > 0" type="primary" @click="handlePrevious">上一步</el-button>
        <el-button
          v-if="stepList.length === 5 ? active < 4 : active < 3"
          type="primary"
          :loading="$store.state.managePatient.saveBtnLoading"
          @click="handleSave('next')"
        >
          下一步
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  saveStandardizedManageTarget,
  saveTherapeuticAction,
  getStandardizedManageStepStatus
} from '@/api/standardizedManage'
import patientInfo from '@/components/patientInfo/index.vue'
import StepIndicator from '@/components/customSteps/index.vue'
import ManageByObjectives from './component/manageByObjectives.vue'
import TherapeuticAction from './component/therapeuticAction.vue'
import TargetInspection from './component/targetInspection.vue'
import EffectEvaluation from './component/effectEvaluation.vue'
import RiskAssessment from './component/riskAssessment.vue'

export default {
  name: 'ManagePatient',
  components: {
    patientInfo,
    StepIndicator,
    ManageByObjectives,
    TherapeuticAction,
    TargetInspection,
    EffectEvaluation,
    RiskAssessment
  },
  props: {
    historyId: {
      // 远程会诊需要在Modal中打开
      type: String,
      default: ''
    }
  },
  data() {
    return {
      active: 0,
      stepList: []
    }
  },
  async created() {
    this.$store.dispatch('managePatient/getStandardizedManageDetail', { id: this.historyId || this.$route.query.id })
    const idx = await this.getStepStatus()
    if (idx > -1) {
      this.active = idx
    }
  },
  methods: {
    async getStepStatus() {
      const res = await getStandardizedManageStepStatus({ smrId: this.historyId || this.$route.query.id })
      this.stepList = res.data.map((item) => {
        return {
          ...item,
          title: item.stepName
        }
      })
      const idx = this.stepList.findIndex((it) => it.stepStatus === 0 || it.stepStatus === 1)
      return idx
    },
    handleStepChange(step, index) {
      this.active = index
    },
    async handleSave(type) {
      try {
        this.$store.commit('managePatient/SET_SAVE_BTN_LOADING', true)
        let params = {}
        let status = 1

        // 风险评估
        if (this.stepList.length === 5 && this.active === 0) {
          status = await this.$refs.riskAssessmentRef.handleSave(type)
        }

        // 目标管理
        if (this.stepList.length === 5 ? this.active === 1 : this.active === 0) {
          const result = await this.$refs.manageByObjectives.handleSave(type)

          if (!result.success) {
            this.$message.warning(`${result.name}存在必填项未填写`)
          } else {
            status = 5
          }
          params = {
            ...result.data,
            smrId: this.$route.query.id,
            status
          }
          const res = await saveStandardizedManageTarget(params)
          if (res.code === 200) {
            if (type === 'save') {
              this.$message.success('保存成功')
              await this.$store.dispatch('managePatient/getStandardizedManageTargetDetailFn', {
                smrId: this.$route.query.id,
                patientId: this.$store.state.managePatient.managePatientData.patientId
              })
            }
          }
        }

        // 治疗动作
        if (this.stepList.length === 5 ? this.active === 2 : this.active === 1) {
          status = await this.$refs.therapeuticActionRef.handleSave(type)
        }

        // 目标检验
        if (this.stepList.length === 5 ? this.active === 3 : this.active === 2) {
          status = await this.$refs.targetInspectionRef.handleSave(type)
        }

        // 生成治疗动作
        if (this.stepList.length === 5 ? this.active === 1 : this.active === 0 && type === 'next' && status === 5) {
          const res = await saveTherapeuticAction({
            smrId: this.$route.query.id,
            disease: this.$store.state.managePatient.managePatientData.disease
          })
          if (res.code !== 200) {
            return
          }
        }

        if (status === 5 && type === 'next') {
          this.$store.commit('managePatient/SET_LOADING', true)
          setTimeout(() => {
            this.active++
          }, 1000)
        }
      } catch (error) {
        console.log('error', error)
      } finally {
        this.$store.commit('managePatient/SET_SAVE_BTN_LOADING', false)
        this.getStepStatus()
      }
    },
    handlePrevious() {
      this.active--
    }
  }
}
</script>

<style lang="scss" scoped>
.manage-patient {
  .el-card {
    margin: 16px;
    .manage-step {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      .el-steps {
        width: 600px;
      }
    }
  }
  ::v-deep .el-tabs__new-tab {
    background-color: #41a1d4;
  }
}
</style>
