<template>
  <el-input
    class="fontSize_12 public_inputPlaceholder"
    v-bind="finalProps"
    style="width: 100%"
    v-on="finalListeners"
    @input="debouncedInput"
  >
    <!-- 通过 v-slot 正确传递作用域数据 -->
    <template #default="{ item }">
      <slot :item="item" />
    </template>
  </el-input>
</template>

<script>
import ElInput from 'element-ui/lib/input'
import debounce from 'lodash/debounce'
// import { debounce } from '@/utils'

export default {
  name: 'ProInput',
  components: { ElInput },
  inheritAttrs: false, // 禁止自动绑定 attrs
  // props: ProInput.props, // 继承 el-input 的所有 props
  props: {
    // 继承 ProInput 的其他 props
    ...ElInput.props,
    suffixIcon: {
      type: String,
      default: 'el-icon-search'
    },
    clearable: {
      type: Boolean,
      default: true
    },
    debounceDelay: {
      type: Number,
      default: 1000 // 防抖延迟时间，单位 ms
    }
  },
  data() {
    return {
      debouncedInput: null
    }
  },
  computed: {
    // 合并 attrs 和 props，保证 props 优先级高
    finalProps() {
      return {
        ...this.$attrs,
        ...this.$props
      }
    },
    // 自动绑定所有事件监听
    finalListeners() {
      return {
        ...this.$listeners
      }
    }
  },
  created() {
    // 初始化防抖方法
    this.debouncedInput = debounce(this.handleInput, this.debounceDelay)
  },
  beforeDestroy() {
    // 组件销毁时取消防抖
    this.debouncedInput.cancel()
  },
  methods: {
    handleInput(value) {
      this.$emit('debounce', value) // 手动触发 input 事件
    }
  }
}
</script>

<style></style>
