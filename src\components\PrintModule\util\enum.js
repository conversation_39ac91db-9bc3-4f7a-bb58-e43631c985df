// 样例代码//

import { GENDER_ENUM } from '@/utils/enum'

export const renderContent = (item, sizeObj) => {
  const { id, name, sex, age, deptName } = item
  let width = 40
  let height = 20
  let textX = 1
  let barWidth = 38
  let marginX = 1
  let marginY = 1
  let fontSize = 3
  let barFontSize = 2
  let textLineHeight = 3
  let barCodeOffsetX = 1
  if (sizeObj) {
    width = sizeObj.width
    height = sizeObj.height
    marginX = sizeObj.marginX
    textX = sizeObj.textX
    barWidth = sizeObj.barWidth
    marginY = sizeObj.marginY
    fontSize = sizeObj.fontSize
    barFontSize = sizeObj.barFontSize
    textLineHeight = sizeObj.textLineHeight
    barCodeOffsetX = sizeObj.barCodeOffsetX
  }
  return [
    {
      // top 基础信息
      type: 'text',
      json: {
        x: textX,
        y: marginY,
        height: textLineHeight,
        width: width - marginX * 2,
        value: `${deptName} ${name}  ${GENDER_ENUM[sex]}  ${age}岁`,
        fontFamily: '宋体',
        rotate: 0,
        fontSize,
        textAlignHorizonral: 1,
        textAlignVertical: 1,
        letterSpacing: 0.6,
        lineSpacing: 0.3,
        lineMode: 6,
        fontStyle: [false, false, false, false]
      }
    },

    {
      type: 'barCode',
      json: {
        x: textX + barCodeOffsetX,
        y: marginY + textLineHeight + 1,
        height: 13,
        width: barWidth,
        value: id,
        codeType: 20,
        rotate: 0,
        textAlignHorizonral: 1,
        textAlignVertical: 1
      }
    },

    // bottom text
    {
      type: 'text',
      json: {
        x: textX,
        y: height - textLineHeight - 1,
        height: textLineHeight,
        width: width - marginX * 2,
        value: id,
        fontFamily: '宋体',
        rotate: 0,
        fontSize: barFontSize,
        textAlignHorizonral: 1,
        textAlignVertical: 1,
        letterSpacing: 0.7,
        lineMode: 6,
        fontStyle: [false, false, false, false]
      }
    }
  ]
}
