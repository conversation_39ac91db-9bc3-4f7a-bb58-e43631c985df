<!-- OGTT -->
<template>
  <div class="OGTT">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="空腹血糖：" prop="kfSugarValue">
        <custom-input-number v-model="form.kfSugarValue" style="width: 30%">
          <template #append>mmol/L</template>
        </custom-input-number>
      </el-form-item>

      <el-form-item label="餐后0.5h血糖：">
        <custom-input-number v-model="form.chHalfSugarValue" style="width: 30%">
          <template #append>mmol/L</template>
        </custom-input-number>
      </el-form-item>

      <el-form-item label="餐后1h血糖：">
        <custom-input-number v-model="form.chOneSugarValue" style="width: 30%">
          <template #append>mmol/L</template>
        </custom-input-number>
      </el-form-item>

      <el-form-item label="餐后2h血糖：" prop="chTwoSugarValue">
        <custom-input-number v-model="form.chTwoSugarValue" style="width: 30%">
          <template #append>mmol/L</template>
        </custom-input-number>
      </el-form-item>

      <el-form-item label="餐后3h血糖：">
        <custom-input-number v-model="form.chThreeSugarValue" style="width: 30%">
          <template #append>mmol/L</template>
        </custom-input-number>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'OGTT',
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        kfSugarValue: '',
        chHalfSugarValue: '',
        chOneSugarValue: '',
        chTwoSugarValue: '',
        chThreeSugarValue: ''
      },
      rules: {
        kfSugarValue: [{ required: true, message: '请输入空腹血糖' }],
        chTwoSugarValue: [{ required: true, message: '请输入餐后2h血糖' }]
      }
    }
  },
  methods: {
    initData(data) {
      this.form = {
        kfSugarValue: data.kfSugarValue,
        chHalfSugarValue: data.chHalfSugarValue,
        chOneSugarValue: data.chOneSugarValue,
        chTwoSugarValue: data.chTwoSugarValue,
        chThreeSugarValue: data.chThreeSugarValue,
        id: data.id
      }
    },
    async handleSave() {
      const result = {
        name: `${this.itemTemp.label}`,
        success: false,
        data: {
          ...this.form,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    }
  }
}
</script>
