<!-- 外周动脉 -->
<template>
  <div class="peripheral-artery">
    <div v-if="showDeviceCode($route.path)" class="device-barcode">
      <Barcode :code="`${getDeviceCode($route.path, getSourceData())}`" />
    </div>
    <el-table :data="tableData" style="width: 100%; margin-bottom: 16px" border :span-method="mergeCells">
      <el-table-column prop="part" label="位置" align="center" />
      <el-table-column prop="type" label="" align="center" />
      <el-table-column prop="left" label="左" align="center">
        <template slot-scope="scope">
          <custom-input-number v-model="scope.row.left" />
        </template>
      </el-table-column>
      <el-table-column prop="right" label="右" align="center">
        <template slot-scope="scope">
          <custom-input-number v-model="scope.row.right" />
        </template>
      </el-table-column>
    </el-table>

    <el-form ref="formRef" :model="form" label-width="120px">
      <el-form-item label="检查所见：">
        <el-input v-model="form.checkResult" type="textarea" :rows="3" />
      </el-form-item>
      <el-form-item label="医生意见：">
        <el-input v-model="form.diagnosis" type="textarea" :rows="3" />
      </el-form-item>
      <el-form-item label="上传报告：">
        <custom-upload v-model="form.imagePath" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { mapDataToTable, getDeviceCode, showDeviceCode } from '@/utils/cspUtils'
import { cloneDeep } from 'lodash'
import CustomUpload from '@/components/customUpload/index.vue'
import Barcode from '@/components/barcode/barcode.vue'

export default {
  name: 'PeripheralArtery',
  components: {
    CustomUpload,
    Barcode
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      tableData: [
        { part: '上臂（肱动脉）', type: 'mmHg', left: '', right: '', sbprb: 'right', sbplb: 'left' },
        { part: '上臂（肱动脉）', type: 'index', left: '', right: '', indexRb: 'right', indexLb: 'left' },
        { part: '脚踝（胫后动脉）', type: 'mmHg', left: '', right: '', sbprapta: 'right', sbplapta: 'left' },
        { part: '脚踝（胫后动脉）', type: 'index', left: '', right: '', indexRb: 'right', indexLapta: 'left' },
        { part: '脚踝（足背动脉）', type: 'mmHg', left: '', right: '', sbprafa: 'right', sbplafa: 'left' },
        { part: '脚踝（足背动脉）', type: 'index', left: '', right: '', indexRafa: 'right', indexLafa: 'left' }
      ],
      form: {
        checkResult: '',
        diagnosis: '',
        imagePath: ''
      }
    }
  },
  methods: {
    showDeviceCode,
    getDeviceCode,
    getSourceData() {
      return this.$route.path === '/receptionCenter/patientReception'
        ? this.$store.getters.complicationsScreeningData
        : this.$store.state.patientExamination.patientExaminationData
    },
    mergeCells({ rowIndex, columnIndex }) {
      // columnIndex：0=part, 1=type, 2=left, 3=right
      if (columnIndex === 0) {
        if (rowIndex % 2 === 0) {
          return { rowspan: 2, colspan: 1 }
        } else {
          return { rowspan: 0, colspan: 0 }
        }
      }
    },

    initData(data) {
      this.tableData = mapDataToTable(data, cloneDeep(this.tableData))
      this.form = {
        checkResult: data.checkResult,
        diagnosis: data.diagnosis,
        imagePath: data.imagePath,
        id: data.id
      }
    },

    async handleSave() {
      const resultData = this.extractTableData(this.tableData)
      const result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          ...resultData,
          ...this.form,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    },
    extractTableData(tableData) {
      const result = {}

      tableData.forEach((row) => {
        for (const key in row) {
          if (key !== 'part' && key !== 'type' && (row[key] === 'left' || row[key] === 'right')) {
            result[key] = row[row[key]] // row['left'] 或 row['right']
          }
        }
      })

      return result
    }
  }
}
</script>
<style scoped lang="scss">
.device-barcode {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
