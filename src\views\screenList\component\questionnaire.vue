<!-- 糖尿病和高血压问卷 -->
<template>
  <div class="qusetion">
    <el-form :model="detail" label-position="top">
      <div v-for="item in questionList" :key="item.prop" class="question-item">
        <div class="question-item-options">
          <RadioGroupField v-if="item.type === 'radio'" v-model="detail[item.prop]" :item="item" />
          <CheckboxGroupField v-if="item.type === 'checkbox'" v-model="detail[item.prop]" :item="item" />
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import { massScreeningQuestion } from './questionnaireAnswer'
import RadioGroupField from '@/components/questionnaireElementUi/RadioGroupField.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'

export default {
  name: 'Questionnaire',
  components: {
    RadioGroupField,
    CheckboxGroupField
  },
  props: {
    detail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  computed: {
    questionList() {
      return this.getQuestionsByDiseases(this.detail.disease || '')
    }
  },
  methods: {
    getQuestionsByDiseases(diseaseKeysStr) {
      const diseases = diseaseKeysStr.split(',').map((item) => {
        if (item === 'fangchan') return 'fc'
        else if (item === 'COPD') return 'copd'
        else return item
      })

      return massScreeningQuestion.filter((q) => {
        if (q.belong === 'all') return true
        const questionBelongs = q.belong.split(',')
        return questionBelongs.some((disease) => diseases.includes(disease))
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
