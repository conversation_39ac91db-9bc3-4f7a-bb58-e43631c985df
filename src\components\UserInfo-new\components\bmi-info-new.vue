<!-- eslint-disable vue/html-self-closing -->
<template>
  <div class="bmi-info public_v2">
    <div class="bmi_info_content flex_spaceAround">
      <div class="bmi_left flex_spaceAroundStart">
        <div class="bmi_3d flex_column">
          <div class="bmi_3dSexImg">
            <img
              :src="
                userInfo.sex == 0
                  ? require('@/assets/BMI/newImgs/3D_woman.png')
                  : require('@/assets/BMI/newImgs/3D_man.png')
              "
              alt="示意图"
              class="bmi_3dSexImg"
            />
            <p
              v-for="(item, index) in diseases"
              :key="'diseases_' + index"
              :class="['diseasesList', userInfo.sex == 0 ? 'man' : '', item.code]"
            >
              {{ item.name }}
            </p>
          </div>
          <div class="bmi_weight flex_spaceBetween fontSize_14">
            <img src="@/assets/BMI/newImgs/weight.png" alt="体重" class="bmi_weightImg" />
            <span>体重</span>
            <span class="fontWeight600 fontSize_18 bmi_weightNum">{{ baseInfoData.weight || '-/-' }}</span>
            <span>kg</span>
          </div>
        </div>
        <div class="bmi_height flex_center">
          <img src="@/assets/BMI/newImgs/male_line.png" alt="体重" class="bmi_heightImg" />
          <div class="flex_center">
            <span class="fontWeight600 fontSize_18 bmi_weightNum">{{ baseInfoData.height || '-/-' }}</span>
            <span class="fontSize_14">cm</span>
          </div>
        </div>
      </div>
      <div class="bmi_right">
        <div class="bmi_header">
          <div class="bmi_headerList flex_spaceAround">
            <!-- bmiType:0 未定义 1 偏瘦 2 标准 3 偏胖 -->
            <div class="flex_column" :class="baseInfoData.bmiType === 1 ? '' : 'opacity3'">
              <img
                :src="
                  userInfo.sex == 0
                    ? require('@/assets/BMI/newImgs/woman_thin.png')
                    : require('@/assets/BMI/newImgs/man_thin.png')
                "
                alt="体重"
                class="bmi_figureImg"
              />
              <span class="bmi_header_line bmi_header_lineThin">
                <img
                  v-if="baseInfoData.bmiType === 1"
                  src="@/assets/BMI/newImgs/face_1.png"
                  alt="脸形"
                  class="bmi_header_faceImg"
                />
              </span>
            </div>
            <div class="flex_column" :class="baseInfoData.bmiType === 2 ? '' : 'opacity3'">
              <img
                :src="
                  userInfo.sex == 0
                    ? require('@/assets/BMI/newImgs/woman.png')
                    : require('@/assets/BMI/newImgs/man.png')
                "
                alt="体重"
                class="bmi_figureImg"
              />
              <span class="bmi_header_line bmi_header_lineStandard">
                <img
                  v-if="baseInfoData.bmiType === 2"
                  src="@/assets/BMI/newImgs/face_2.png"
                  alt="脸形"
                  class="bmi_header_faceImg"
                />
              </span>
            </div>
            <div class="flex_column" :class="baseInfoData.bmiType === 3 ? '' : 'opacity3'">
              <img
                :src="
                  userInfo.sex == 0
                    ? require('@/assets/BMI/newImgs/woman_fat.png')
                    : require('@/assets/BMI/newImgs/man_fat.png')
                "
                alt="体重"
                class="bmi_figureImg"
              />
              <span class="bmi_header_line bmi_header_lineFat">
                <img
                  v-if="baseInfoData.bmiType === 3"
                  src="@/assets/BMI/newImgs/face_3.png"
                  alt="脸形"
                  class="bmi_header_faceImg"
                />
              </span>
            </div>
          </div>
        </div>
        <div class="bmi_rightContent flex_column">
          <div class="flex_center bmi_rightContent_status">
            <span class="fontSize_14">您当前状态：</span>
            <span class="fontWeight600 fontSize_18 bmi_weightNum">{{ baseInfoData.bmiStr }}</span>
          </div>
          <div class="bmi_rightContent_status flex_center bmi_rightContent_waist fontSize_14">
            <img src="@/assets/BMI/newImgs/waist.png" alt="腰围" />
            <span class="fontSize_14">腰围</span>
            <span class="fontWeight600 fontSize_18 bmi_weightNum">{{ baseInfoData.waistline || '-/-' }}</span>
            <span>cm</span>
          </div>
          <div class="bmi_rightContent_status flex_center bmi_rightContent_waist fontSize_14">
            <img src="@/assets/BMI/newImgs/BMI.png" alt="BMI" />
            <span>体质指数 (BMI)</span>
            <span class="fontWeight600 fontSize_18 bmi_weightNum">{{ baseInfoData.bmi || '-/-' }}</span>
            <span>kg/㎡</span>
          </div>
          <div class="bmi_rightContent_status fontSize_14">
            解读：BMI指数在18.5至23.9kg/m属于正常，低于18.5kg/m属于偏瘦，高于23.9kg/m表示超重或肥胖。
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'BmiInfo',
  props: {
    baseInfoData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    userInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    diseases: {
      type: Array,
      default: () => {
        return []
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.bmi-info {
  width: 100%;
  .bmi_info_content {
    .bmi_left {
      height: 100%;
      width: 13.85rem;
    }
    .bmi_right {
      width: 23.5rem;
      margin-left: 2rem;
    }
    .bmi_3d {
      width: calc(3.85rem + 5.8rem);
      .bmi_3dSexImg {
        position: relative;
      }
      img.bmi_3dSexImg {
        width: auto;
        height: 13.15rem;
      }
      .bmi_weight {
        margin-top: 0.5rem;
        width: 7.3rem;
        color: #666;
      }
      img.bmi_weightImg {
        width: 1.2rem;
        height: 0.7rem;
      }
    }
    .bmi_heightImg,
    .bmi_height {
      width: 1.5rem;
      height: 13.15rem;
    }
    .bmi_height {
      position: relative;
      .bmi_weightNum {
        margin-right: 0.4rem;
      }
      .bmi_heightImg {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
      }
    }

    .bmi_weightNum {
      color: #222;
    }
    .bmi_right {
      .bmi_headerList,
      .bmi_header {
        width: 100%;
      }

      .bmi_header_line {
        width: 5.9rem;
        height: 0.4rem;
        display: block;
        border-radius: 0.2rem;

        margin-top: 1.25rem;
        position: relative;
        &.bmi_header_lineThin {
          background-color: #6cd2f3;
        }
        &.bmi_header_lineStandard {
          background-color: #6cd46c;
        }
        &.bmi_header_lineFat {
          background-color: #f04a62;
        }
        .bmi_header_faceImg {
          width: 1.6rem;
          height: 1.6rem;
          border-radius: 50%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translateX(-50%) translateY(-50%);
        }
      }
      .bmi_figureImg {
        width: auto;
        height: 4.5rem;
      }

      .bmi_rightContent_status {
        color: #666;
        margin-top: 1rem;
        .bmi_weightNum {
          margin-right: 0rem;
          margin-left: 0.6rem;
        }
      }
      .bmi_rightContent_waist {
        img {
          width: 1.2rem;
          height: 1.2rem;
          margin-right: 0.75rem;
        }
        span {
          display: block;

          &:nth-child(2) {
            width: 5rem;
            margin-right: 0.5rem;
          }
          &:nth-child(3) {
            width: 3rem;
            margin-right: 0.5rem;
            margin-left: 0rem;
          }
          &:nth-child(4) {
            width: 2rem;
          }
        }
      }

      .opacity3 {
        opacity: 0.3;
      }
    }
  }
}

@media (max-width: 1280px) {
  .bmi-info .bmi_info_content {
    .bmi_left {
      width: 8rem;
      .bmi_3d {
        width: calc(3.85rem + 2.8rem);
        .bmi_weight {
          width: 5.3rem;
        }
      }
    }
    .bmi_right {
      width: 17rem;
      .bmi_header_line {
        width: 4rem;
        .bmi_header_faceImg {
          width: 1.3rem;
          height: 1.3rem;
        }
      }
    }
  }
}
.diseasesList {
  position: absolute;
  width: 65%;
  line-height: 0.85rem;
  display: inline-block;
  right: -70%;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 0.6rem;
  color: #666666;
  border-radius: 2px;
  background-color: #eaeaea;
  text-align: center;
  &::after {
    content: '';
    height: 2px;
    display: inline-block;
    position: absolute;
    right: -36%;
    top: 50%;
    width: 36%;
    background-color: #6cd46c;
  }
  &::before {
    content: '';
    width: 0.4rem;
    height: 0.4rem;
    border-radius: 0.4rem;
    display: inline-block;
    position: absolute;
    right: -38%;
    top: calc(50% - 0.2rem);
    background-color: #6cd46c;
  }
  &.gxy {
    top: 6rem;
    left: -80%;
    &::after {
      background-color: #9f00e2;
    }
    &::before {
      background-color: #9f00e2;
    }
  }
  &.COPD {
    top: 2.2rem;
    left: -80%;
    &::after {
      width: 90%;
      right: -90%;
      background-color: #684949;
    }
    &::before {
      right: -90%;
      background-color: #684949;
    }
  }
  &.tnb {
    top: 11.8rem;
    &::after {
      width: 70%;
      left: -70%;
      background-color: #db4548;
    }
    &::before {
      left: -73%;
      background-color: #db4548;
    }
  }
  &.nzz {
    top: 0rem;
    &::after {
      width: 80%;
      left: -80%;
      background-color: #3fabfd;
    }
    &::before {
      left: -83%;
      background-color: #3fabfd;
    }
  }
  &.xlsj {
    top: 3.2rem;
    left: -80%;
    &::after {
      width: 96%;
      right: -96%;
      background-color: #7b36ff;
    }
    &::before {
      right: -96%;
      background-color: #7b36ff;
    }
  }
  &.fangchan {
    top: 2.6rem;
    &::after {
      width: 83%;
      left: -83%;
      background-color: #9f00e2;
    }
    &::before {
      left: -83%;
      background-color: #9f00e2;
    }
  }
  &.guanxinbing {
    top: 3.6rem;
    &::after {
      width: 83%;
      left: -83%;
      background-color: #ffa700;
    }
    &::before {
      left: -83%;
      background-color: #ffa700;
    }
  }
}
</style>
