<template>
  <!-- eslint-disable vue/no-mutating-props -->
  <div class="navbar">
    <div class="leftLogoBox" @click="navigateTo">
      <img v-if="targetEnv.LOGO" :src="targetEnv.LOGO" class="logoImg">

      <div class="logoTitleBox">
        <div class="topTitle">{{ targetEnv.TITLE }}</div>
      </div>
    </div>

    <navDiv />

    <div class="right-menu">
      <MenuNotice />

      <div class="line" />

      <el-dropdown trigger="hover">
        <div class="avatar-container right-menu-item hover-effect">
          <div class="avatar-wrapper">
            <el-image :src="doctorImg" class="user-avatar" style="width: 40px; height: 40px" />
            <span>欢迎您，{{ name }} </span>
          </div>
        </div>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="handleGo">个人信息</el-dropdown-item>
          <el-dropdown-item @click.native="handleQrCode">我的二维码</el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span style="display: block">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <ProDialog :visible.sync="qrCodeDialogVisible" title="我的二维码" width="25%" :before-close="handleClose">
      <div class="qrcode-container" style="text-align: center">
        <div id="qrcode" ref="qrcode" style="width: 128px; height: 128px; margin: 0 auto" />
        <div class="doctor-info" style="margin-top: 15px">
          <p>{{ departName }} {{ name }}</p>
        </div>
      </div>
    </ProDialog>
  </div>
</template>

<script>
import { loginSetting as targetEnv } from '@/layout/components/Sidebar/LogoEnum'
import { mapGetters } from 'vuex'
import { getUserId } from '@/utils/auth'
import navDiv from '@/layout/components/navDiv'
import MenuNotice from '@/views/messageNotification/menuNotice/index.vue'
import doctorImg from '@/assets/common_images/doctorhand.png'
import ProDialog from '@/components/ProDialog/index.vue'
import QRCode from 'qrcodejs2'

export default {
  components: {
    navDiv,
    ProDialog,
    MenuNotice
  },
  props: {
    hamburgerShow: {
      type: Boolean,
      default: true
    },
    roles: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      targetEnv,
      roleId: '',
      doctorImg,
      qrCodeDialogVisible: false
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'avatar', 'device', 'name', 'hospitalId', 'homeRole', 'bigFont', 'departName']),
    routeName() {
      return this.$route.name
    },
    moduleName() {
      return this.$route.path
    },
    routePath() {
      return this.$route.path
    }
  },
  created() {
    if (this.roles.length > 0) {
      this.roleId = this.homeRole
    }

    this.$store.dispatch('app/setBigFont', localStorage.getItem('bigFont'))
  },
  methods: {
    navigateTo() {
      this.$router.push({
        name: 'Home'
      })
    },
    handleGo() {
      this.$router.push('/userInfo/index')
    },
    async handleChange(val) {
      await this.$store.dispatch('user/changeHomeRole', val)
      this.$emit('roleIdChange', val)
    },
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    setBigFont(val) {
      if (!val) {
        localStorage.removeItem('bigFont')
      } else {
        localStorage.setItem('bigFont', val)
      }
      this.$store.dispatch('app/setBigFont', val)
    },
    logout() {
      this.$confirm('你确定要退出登录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          console.log('this.$store.getters.userId', this.$store.getters.userId)
          this.resetMeetStatus(getUserId())
          await this.$store.dispatch('user/logout')
          this.$router.push(`/login`)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消退出'
          })
        })
    },
    handleQrCode() {
      this.qrCodeDialogVisible = true
      this.$nextTick(() => {
        this.generateQRCode()
      })
    },
    handleClose() {
      this.qrCodeDialogVisible = false
    },
    generateQRCode() {
      const id = getUserId()
      const qrcode = new QRCode(this.$refs.qrcode, {
        text: `https://minicsp.starcds.cn/?doctor_id=${id}`,
        width: 128,
        height: 128,
        colorDark: '#000000',
        colorLight: '#ffffff',
        correctLevel: QRCode.CorrectLevel.H
      })
      console.log('qrcode', qrcode)
    }
  }
}
</script>
<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  // position: relative;
  background-color: #9eafd5;
  background: linear-gradient(133deg, #00b2dd 0%, #0a86c8 100%);
  color: #fff;
  // z-index: 1;
  display: flex;

  .hamburger-container {
    line-height: 50px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }
  .line {
    display: inline-block;
    margin: 0 0.5rem;
    border: 1px dashed #fff;
    height: 30px;
  }
  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .leftLogoBox {
    float: left;
    cursor: pointer;
    height: 100%;
    margin-left: 1rem;
    display: flex;
    justify-content: flex-start;
    flex-wrap: nowrap;
    align-items: center;
    width: 15rem;
    .logoImg {
      height: 35px;
    }

    .logoTitleBox {
      margin-left: 12px;
      height: 40px;
      // line-height: 20px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .topTitle {
        width: 100%;
        font-size: 0.9rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #fff;
        margin: 0;
        letter-spacing: 0.05rem;
      }
      .bottomText {
        width: 100%;
        font-size: 0.6rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #fff;
        margin: 0;
      }
    }
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    position: relative;
    display: flex;
    align-items: center;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 0.9rem;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
    .optical-disk {
      display: flex;
      align-items: center;
      img {
        width: 1.3rem;
        height: 1.3rem;
        margin-right: 0.3rem;
      }
      ::v-deep .el-button {
        color: #fff;
        font-size: 0.7rem;
      }
    }

    .bigFontToggleBox {
      font-size: 0.6rem;
      border: 1px solid #ddd;
      border-radius: 0.2vw;
      display: inline-block;
      width: 1.75rem;
      height: 35px;
      line-height: 35px;
      text-align: center;
      background: rgba(255, 255, 255, 0.3);
      // position: relative;
      // top: -17px;
      cursor: pointer;

      .bigIcon {
        color: #fff;
      }
      &.bigFontTrueBox {
        background-color: #0a86c8;
        border: 1px solid #0a86c8;

        .smallIcon {
          color: #fff;
        }
      }
      &:hover {
        background-color: #0a86c8;

        .bigIcon {
          color: #fff;
        }
      }
    }

    .avatar-container {
      margin-right: 10px;

      .avatar-wrapper {
        // margin-top: 5px;
        position: relative;
        display: flex;
        align-items: center;

        .user-avatar {
          cursor: pointer;
          width: 2rem;
          height: 2rem;
          border-radius: 50%;
          line-height: 50px;
        }

        span {
          margin-left: 0.5rem;
          line-height: 50px;
          font-size: 0.7rem;
          color: #fff;
        }
        .cp {
          cursor: pointer;
        }

        // .el-icon-caret-bottom {
        //   cursor: pointer;
        //   position: absolute;
        //   right: -20px;
        //   top: 25px;
        //   font-size: 0.6rem;
        // }
      }
    }
  }
}
::v-deep .el-dropdown {
  display: flex;
}
// 登录上的 横线 ！！
::v-deep .el-dropdown-menu--medium .el-dropdown-menu__item.el-dropdown-menu__item--divided:before {
  display: none !important;
}

::v-deep .el-dropdown-menu__item--divided:before {
  display: none;
}

::v-deep .el-dropdown-menu__item--divided {
  border-top: 0;
}
.followupImg {
  width: 20px !important;
  cursor: pointer;
}
</style>
