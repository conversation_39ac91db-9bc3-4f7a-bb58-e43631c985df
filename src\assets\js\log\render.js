/* eslint-disable max-len */
export const renderLogValueTag = (itemCode, valueData, itemName) => {
  let renderHTML = ''
  if (itemCode === 'bloodPressure' && valueData) {
    renderHTML = `
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem"> 收缩压: <span class="valueBox">${valueData.sbp}</span> mmHg </span>
    </span>
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem"> 舒张压: <span class="valueBox">${valueData.dbp}</span> mmHg </span>
    </span>
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem"> 脉率: <span class="valueBox">${valueData.pr}</span> 次/分钟 </span>
    </span>
    `
  }
  if (itemCode === 'bloodGlucose' && valueData) {
    renderHTML = `
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem">
        血糖: <span class="valueBox">${valueData.value}</span> mmol/L
      </span>
    </span>
    `
  }
  // {"pef":"1","datetime":"2023-10-31 19:17:30","fvc":"3","fev1":"2","fev_fvc":"4"}
  if (itemCode === 'lung' && valueData) {
    renderHTML = `
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem">
        PEF: <span class="valueBox">${valueData.PEF || valueData.pef}</span> L/min
      </span>
    </span>
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem">
        FEV1: <span class="valueBox">${valueData.FEV1 || valueData.fev1}</span> L
      </span>
    </span>
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem">
        FVC: <span class="valueBox">${valueData.FVC || valueData.fvc}</span> L
      </span>
    </span>
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem">
        FEV/FVC: <span class="valueBox">${valueData.FEV_FVC || valueData.fev_fvc}</span> %
      </span>
    </span>
    `
  }
  if (itemCode === 'HandW' && valueData) {
    renderHTML = `
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
          身高: <span class="valueBox">${valueData.height}</span> cm
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
          体重: <span class="valueBox">${valueData.weight}</span> kg
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
          腰围: <span class="valueBox">${valueData.waistline || '- / -'}</span> cm
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        体质指数（BMI）: <span class="valueBox">${valueData.bmi}</span> kg/m²
        </span>
      </span>
    `
  }
  if (itemCode === 'ksData' && valueData) {
    renderHTML = `
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
          总胆固醇（TC）: <span class="valueBox">${valueData.tC}</span> mmol/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
          甘油三酯（TG）: <span class="valueBox">${valueData.tG}</span> mmol/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
          低密度脂蛋白（LDL-C）: <span class="valueBox">${valueData.lDL_C}</span> mmol/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
          高密度脂蛋白（HDL-C）: <span class="valueBox">${valueData.hDL_C}</span> mmol/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
          葡萄糖（GLU）: <span class="valueBox">${valueData.gLU}</span> mmol/L
        </span>
      </span>
      `
  }
  if (itemCode === 'BNP' && valueData) {
    renderHTML = `
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        BNP: <span class="valueBox">${valueData.bnp || '- / -'}</span> ng/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
          NT-proBNP: <span class="valueBox">${valueData.bnpNt || '- / -'}</span> ng/L
        </span>
      </span>
      `
  }
  if (itemCode === 'bodyTemperature' && valueData) {
    renderHTML = `
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        体温: <span class="valueBox">${valueData.value}</span> ℃
        </span>
      </span>
      `
  }
  if (itemCode === 'Arteriosclerosis' && valueData) {
    renderHTML = `
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem">
      右上臂: <span class="valueBox">收缩压-${convertMinusEmpty(valueData.SBPRB)}, 平均-${convertMinusEmpty(valueData.MBPRB)}, 舒张压-${convertMinusEmpty(valueData.DBPRB)}, 脉压-${convertMinusEmpty(valueData.PPRB)}</span> 
      </span>
    </span>
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem">
      左上臂: <span class="valueBox">收缩压-${convertMinusEmpty(valueData.SBPLB)}, 平均-${convertMinusEmpty(valueData.MBPLB)}, 舒张压-${convertMinusEmpty(valueData.DBPLB)}, 脉压-${convertMinusEmpty(valueData.PPLB)}</span> 
      </span>
    </span>
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem">
      右脚踝: <span class="valueBox">收缩压-${convertMinusEmpty(valueData.SBPRA)}, 平均-${convertMinusEmpty(valueData.MBPRA)}, 舒张压-${convertMinusEmpty(valueData.DBPRA)}, 脉压-${convertMinusEmpty(valueData.PPRA)}</span> 
      </span>
    </span>
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem">
      左脚踝: <span class="valueBox">收缩压-${convertMinusEmpty(valueData.SBPLA)}, 平均-${convertMinusEmpty(valueData.MBPLA)}, 舒张压-${convertMinusEmpty(valueData.DBPLA)}, 脉压-${convertMinusEmpty(valueData.PPLA)}</span> 
      </span>
    </span>
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem">
      左侧: <span class="valueBox">ABI-${convertMinusEmpty(valueData.ABIL)}, PWV-${convertMinusEmpty(valueData.PWVL)}</span>
      </span>
    </span>
    <span class="changeTagBoxRender el-tag el-tag--light">
      <span class="valueItem">
      右侧: <span class="valueBox">ABI-${convertMinusEmpty(valueData.ABIR)}, PWV-${convertMinusEmpty(valueData.PWVR)}</span>
      </span>
    </span>
      `
  }
  if (itemCode === 'hba1c' && valueData) {
    renderHTML = `
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        糖化血红蛋白: <span class="valueBox">${valueData.value}</span>
        </span>
      </span>
      `
  }
  if (itemCode === 'QDS' && valueData) {
    renderHTML = `
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        肌钙蛋白: <span class="valueBox">${valueData.value}</span> ng/mL
        </span>
      </span>
      `
  }
  if (itemCode === 'd2Polymers' && valueData) {
    renderHTML = `
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        D2 聚体: <span class="valueBox">${valueData.d2 || '- / -'}</span> mg/L
        </span>
      </span>
      `
  }
  if (itemCode === 'uri' && valueData) {
    renderHTML = `
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        尿微量白蛋白/肌酐比: <span class="valueBox">${valueData.ucr || '- / -'}</span> mg/g
        </span>
      </span>
      `
  }
  if (itemCode === 'bloodRoutine' && valueData) {
    renderHTML = `
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        血红蛋白: <span class="valueBox">${valueData.hgb || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        红细胞: <span class="valueBox">${valueData.rbc || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        白细胞: <span class="valueBox">${valueData.wbc || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        中性粒细胞: <span class="valueBox">${valueData.pmn || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        单核细胞: <span class="valueBox">${valueData.mono || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        血小板: <span class="valueBox">${valueData.plt || '- / -'}</span> g/L
        </span>
      </span>
      `
  }
  if (itemName === '肾功能' && valueData) {
    renderHTML = `
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        肌酐: <span class="valueBox">${valueData.ua || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        尿素氮: <span class="valueBox">${valueData.nh3 || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        尿酸: <span class="valueBox">${valueData.cr || '- / -'}</span> g/L
        </span>
      </span>
      `
  }
  if (itemName === '电解质' && valueData) {
    renderHTML = `
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        血钾: <span class="valueBox">${valueData.k || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        血钠: <span class="valueBox">${valueData.na || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        血氯: <span class="valueBox">${valueData.oxygen || '- / -'}</span> g/L
        </span>
      </span>
      `
  }
  if (itemName === '肝功能' && valueData) {
    renderHTML = `
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        谷草转氨酶: <span class="valueBox">${valueData.ast || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        谷丙转氨酶: <span class="valueBox">${valueData.alt || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        碱性磷酸酶: <span class="valueBox">${valueData.alp || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        谷氨酰转肽酶: <span class="valueBox">${valueData.ggt || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        总胆红: <span class="valueBox">${valueData.tbil || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        直接胆红: <span class="valueBox">${valueData.dbil || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        间接胆红: <span class="valueBox">${valueData.ibil || '- / -'}</span> g/L
        </span>
      </span>
      <span class="changeTagBoxRender el-tag el-tag--light">
        <span class="valueItem">
        白蛋白: <span class="valueBox">${valueData.alb || '- / -'}</span> g/L
        </span>
      </span>
      `
  }
  return renderHTML
}

// 小于零 转换为""
function convertMinusEmpty(val) {
  let result = val
  if (val < 0) {
    result = '""'
  }
  return result
}
