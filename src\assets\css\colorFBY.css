body {
    /*演示*/
    /* 我是注释 */
    /* 我下面是内容 */
    --textWhiteColor: #fff;
    --noColor: rgba(0, 0, 0, 0);
    --actTextColor: #0A86C8;
    --pageBgColor: #f9fffb;
    --tabsBgColor: #e7e7e7;
    --tabsTextColor: #0A86C8;
    --actTabsBgColor: #0A86C8;
    --actTabsTextColor: #fff;
    --titleColor: #222;
    --borderColor: #999;
    --subTitleColor: #666;
    --myRadioBgColor: #f4fffd;
    --myRadioTextColor: #666;
    --myRadioActBgColor: #e0fdf8;
    --myRadioActTextColor: #1fbfa7;
    --searchTextColoc: #3c405c;
    --videoTimeLabelColor: #fff;
    --videoTimelineBgColor: #D7D7D7;
    --videoTimelineCurrentBgColor: linear-gradient(135deg, #4CE1CB 0%, #0A86C8 100%);
    --videoTimelineBlockBgColor: #fff;
    --videoTimelineBlockBgActColor: #FF7A3F;
    --judgeTrueBgColor: #02BF72;
    --judgeFalseBgColor: #F34A4A;
    --btnDisabledBgColor: #C7C7C7;

    /* xzs */
    --colorBtn: #fff;
    --tableTitleBgColor: linear-gradient(180deg, #eee 0%, #f0f0f0 100%);
    --tableCellBgColor: #F9FFFB;
    --btnPrimaryBgColor: #0A86C8;
    --paginationActiveBgColor: linear-gradient(135deg, #8BD2FF 0%, #22B2FF 100%);
    --colorBtnHover: #0A86C8
}
