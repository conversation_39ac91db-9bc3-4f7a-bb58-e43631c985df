<!-- eslint-disable no-undef -->
<script>
// import { TITLE_SVG_ICON_ENUMS } from '@/layout/util/enum'

export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  render(h, context) {
    const { icon, title } = context.props
    const vnodes = []

    if (icon) {
      if (icon.includes('el-icon')) {
        vnodes.push(<i class={[icon, 'sub-el-icon']} />)
      } else {
        vnodes.push(<svg-icon icon-class={icon} />)
      }
    }

    // if (TITLE_SVG_ICON_ENUMS[title]) {
    //   vnodes.push(<svg-icon icon-class={TITLE_SVG_ICON_ENUMS[title]} />)
    // }

    if (title) {
      vnodes.push(<span slot='title'>{title}</span>)
    }
    return vnodes
  }
}
</script>

<style lang="scss" scoped>
path,
svg {
  color: currentColor !important;
  fill: currentColor !important;
  stroke: currentColor !important;
}
svg.svg-icon {
  height: 1.3em !important;
  margin-right: 0.618em !important;
}
span {
  font-family: PingFangSC-Medium, PingFang SC;
}
.sub-el-icon {
  color: currentColor;
  width: 1em;
  height: 1em;
}
</style>
