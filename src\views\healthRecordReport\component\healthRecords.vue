<template>
  <div class="health-records">
    <h2 style="color: #4bc0f1">健康档案</h2>
    <div style="margin-top: 16px; font-size: 16px">基本信息</div>
    <el-descriptions class="detail-info" :column="2" border>
      <el-descriptions-item label="机构名称"> {{ baseInfo.departName }}</el-descriptions-item>
      <el-descriptions-item label="慢病病种">
        {{ baseInfo.diseaseList ? baseInfo.diseaseList.join('、') : '' }}
      </el-descriptions-item>
      <el-descriptions-item label="建档医生">{{ baseInfo.doctorName }}</el-descriptions-item>
      <el-descriptions-item label="建档日期">{{ baseInfo.registerDate }}</el-descriptions-item>
      <el-descriptions-item label="责任医生">{{ baseInfo.manageDoctorName }}</el-descriptions-item>
      <el-descriptions-item label="患者姓名">{{ baseInfo.name }}</el-descriptions-item>
      <el-descriptions-item label="证件类型">
        {{ baseInfo.idType === 'ID_CARD' ? '身份证' : '护照' }}
      </el-descriptions-item>
      <el-descriptions-item label="证件号码">{{ baseInfo.originalIdCard }}</el-descriptions-item>
      <el-descriptions-item label="性别">{{ genderTransform(baseInfo.sex) }}</el-descriptions-item>
      <el-descriptions-item label="出生日期">{{ baseInfo.birthday }}</el-descriptions-item>
      <el-descriptions-item label="常驻类型">
        {{ baseInfo.registerType === 1 ? '非户籍' : baseInfo.registerType === 2 ? '户籍' : '' }}
      </el-descriptions-item>
      <el-descriptions-item label="本人电话">{{ baseInfo.originalPhone }}</el-descriptions-item>
      <el-descriptions-item label="现居住址">{{ baseInfo.currentAddress }}</el-descriptions-item>
      <el-descriptions-item label="婚姻状况">
        {{ baseInfo.marital ? maritalList.find(item => item.value === baseInfo.marital).label : '' }}
      </el-descriptions-item>
      <el-descriptions-item label="文化程度">
        {{
          baseInfo.educationLevel ? educationLevelList.find(item => item.value === baseInfo.educationLevel).label : ''
        }}
      </el-descriptions-item>
      <el-descriptions-item label="职业">
        {{ baseInfo.career ? careerList.find(item => item.value === baseInfo.career).label : '' }}
      </el-descriptions-item>

      <el-descriptions-item label="紧急联系人">
        <div class="contact-item">
          <span>姓名：{{ baseInfo.contactName1 }}</span>
          <span>
            关系：{{
              baseInfo.contactRelation1
                ? contactRelationList.find(item => item.value === baseInfo.contactRelation1).label
                : ''
            }}
          </span>
          <span>电话：{{ baseInfo.contactPhone1 }}</span>
        </div>
        <div class="contact-item">
          <span>姓名：{{ baseInfo.contactName2 }}</span>
          <span>
            关系：{{
              baseInfo.contactRelation2
                ? contactRelationList.find(item => item.value === baseInfo.contactRelation2).label
                : ''
            }}
          </span>
          <span>电话：{{ baseInfo.contactPhone2 }}</span>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import { genderTransform } from '@/utils/cspUtils'
import { maritalList, careerList, educationLevelList, contactRelationList } from './enum'

export default {
  name: 'HealthRecords',
  props: {
    baseInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      maritalList,
      careerList,
      educationLevelList,
      contactRelationList
    }
  },
  methods: {
    genderTransform
  }
}
</script>

<style lang="scss" scoped>
.detail-info {
  margin-top: 16px;
}
.contact-item {
  display: flex;
  align-items: center;
  gap: 30px;
}
</style>
