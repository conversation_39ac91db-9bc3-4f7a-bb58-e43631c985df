const tnbForm = {
  tnbResult: '',
  tnbDescription: ''
}

const gxyForm = {
  gxyResult: '',
  gxyDescription: ''
}

const copdForm = {
  copdResult: '',
  copdDescription: ''
}

const fcForm = {
  fcResult: '',
  fcDescription: ''
}

const tnbRadioList = [
  {
    label: '正常',
    value: 1
  },
  {
    label: '糖尿病前期',
    value: 2
  },
  {
    label: '糖尿病',
    value: 3
  }
]

const gxyRadioList = [
  {
    label: '正常',
    value: 1
  },
  {
    label: '高血压前期',
    value: 2
  },
  {
    label: '高血压',
    value: 3
  }
]

const copdRadioList = [
  {
    label: '正常',
    value: 1
  },
  {
    label: '慢阻肺',
    value: 2
  }
]

const fcRadioList = [
  {
    label: '正常',
    value: 1
  },
  {
    label: '房颤',
    value: 2
  }
  // {
  //   label: '阵发性房颤',
  //   value: 2
  // },
  // {
  //   label: '持续性房颤',
  //   value: 3
  // },
  // {
  //   label: '长程持续性房颤',
  //   value: 4
  // },
  // {
  //   label: '永久性房颤',
  //   value: 5
  // }
]

const tnbTableObject = {
  tableData: [],
  tableColumns: [
    {
      type: 'index',
      label: '序号'
    },
    {
      prop: 'itemName',
      label: '名称'
    },
    {
      prop: 'itemValue',
      label: '测量值',
      slot: 'itemValue'
    },
    {
      prop: 'range',
      label: '参考值'
    }
  ]
}
const gxyTableObject = {
  tableData: [],
  tableColumns: [
    {
      type: 'index',
      label: '序号'
    },
    {
      prop: 'itemName',
      label: '名称'
    },
    {
      prop: 'spValue',
      label: '收缩压',
      slot: 'spValue'
    },
    {
      prop: 'dpValue',
      label: '舒张压',
      slot: 'dpValue'
    },
    {
      prop: 'range',
      label: '参考值'
    }
  ]
}

const copdTableObject = {
  tableData: [],
  tableColumns: [
    {
      type: 'index',
      label: '序号'
    },
    {
      prop: 'itemName',
      label: '名称'
    },
    {
      prop: 'itemValue',
      label: '检查结果/分值',
      slot: 'itemValue'
    },
    {
      prop: 'range',
      label: '参考值'
    }
  ]
}

const fcTableObject = {
  tableData: [],
  tableColumns: [
    {
      type: 'index',
      label: '序号'
    },
    {
      prop: 'itemName',
      label: '名称'
    },
    {
      prop: 'itemValue',
      label: '检查结果'
    }
  ]
}

const positiveData = [
  {
    label: '是否有房颤家族史：',
    type: 'checkbox',
    required: true,
    mutuallyExclusive: true, // 互斥
    prop: 'fcFamilyHistory',
    belong: 'fc',
    options: [
      { label: '无', value: '1' },
      { label: '一级亲属(父母、子女及兄弟姐妹)', value: '2' },
      { label: '二级亲属(叔、伯、姑、姨、祖父母等)', value: '3' },
      { label: '三级亲属(表兄妹、堂兄妹)', value: '4' }
    ]
  },
  {
    label: '是否有高血压：',
    type: 'radio',
    prop: 'gxyFlag',
    required: true,
    belong: 'fc',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '是否有糖尿病：',
    type: 'radio',
    prop: 'tnbFlag',
    required: true,
    belong: 'fc',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '是否有心悸，气短，乏力，晕厥等：',
    type: 'radio',
    prop: 'palpitationFlag',
    required: true,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否心力衰竭或左室收缩功能障碍：',
    type: 'radio',
    prop: 'heartFailureFlag',
    required: false,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否有脑卒中、短暂性脑缺血发作、血栓栓塞史：',
    type: 'radio',
    prop: 'strokeFlag',
    required: false,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否有血管疾病(外周动脉疾病、心肌梗死、主动脉斑块)：',
    type: 'radio',
    prop: 'bloodFlag',
    required: false,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否有超重和肥胖，BMI≥24kg/m2，或中心性肥胖(男性腰围≥90cm，女性腰围≥85cm)：',
    type: 'radio',
    prop: 'fatFlag',
    required: false,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否有慢性阻塞性肺病(COPD)：',
    type: 'radio',
    prop: 'copdFlag',
    required: false,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否有睡眠呼吸暂停综合征患者：',
    type: 'radio',
    prop: 'sleepFlag',
    required: false,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  }
]

export {
  tnbForm,
  gxyForm,
  copdForm,
  fcForm,
  tnbRadioList,
  gxyRadioList,
  copdRadioList,
  fcRadioList,
  tnbTableObject,
  gxyTableObject,
  copdTableObject,
  fcTableObject,
  positiveData
}
