<template>
  <el-dialog v-el-drag-dialog destroy-on-close custom-class="pro_dialog" v-bind="finalProps" v-on="finalListeners">
    <!-- 默认插槽 -->
    <slot />

    <!-- 具名插槽：title -->
    <template v-if="$slots.title" slot="title">
      <slot name="title" />
    </template>

    <!-- 具名插槽：footer -->
    <template v-if="$slots.footer" slot="footer">
      <slot name="footer" />
    </template>
  </el-dialog>
</template>

<script>
import ElDialog from 'element-ui/lib/dialog'

export default {
  name: 'ProDialog',
  components: { ElDialog },
  inheritAttrs: false, // 禁止自动绑定 attrs
  // props: ElDialog.props, // 继承 el-dialog 的所有 props
  props: {
    // 继承 el-dialog 的其他 props
    ...ElDialog.props,
    // 在 props 中直接定义 width/top/closeOnClickModal 默认值
    width: {
      type: String,
      default: window.innerWidth < 1400 ? '85vw' : '70vw' /* fixed不要动默认配置 */
    },
    top: {
      type: String,
      default: window.innerWidth < 1400 ? '7vh' : '10vh' /* fixed不要动默认配置 */
    },
    closeOnClickModal: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 合并 attrs 和 props，保证 props 优先级高
    finalProps() {
      return {
        ...this.$attrs,
        ...this.$props
      }
    },
    // 自动绑定所有事件监听
    finalListeners() {
      return {
        ...this.$listeners
      }
    }
  }
}
</script>

<style>
#app .el-dialog__wrapper .pro_dialog .el-dialog__header span {
  font-size: 0.9rem !important;
}
</style>

<style lang="scss">
.pro_dialog {
  position: relative;
  .el-dialog__header {
    height: 2rem;
    background: #eefaff;
    border-radius: 16px 16px 0px 0px;
    position: relative;
    font-weight: 500;
    font-size: 0.9rem !important;
    color: #0a86c8;
    line-height: normal;
    padding: 20px;
    display: flex;
    align-items: center;
    .el-dialog__headerbtn {
      top: 12px;
    }
  }
  .el-dialog__body {
    max-height: 90vh;
    overflow: auto;
    padding: 12px 20px; /* fixed不要动默认配置 */
  }
}
</style>
