import { login, logout, getInfo, getLoginPcByWxApi, loginBySms } from '@/api/user'
import { getToken, setToken, removeToken, getUserId, setUserId, removeUserId, removeLoginLog } from '@/utils/auth'
import router, { resetRouter } from '@/router'
import websocket from '@/utils/websocket'
import { Notification } from 'element-ui'
import { deepClone } from '@/utils'
import { cloneDeep } from 'lodash'
import { localCache } from '@/utils/cache'

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_INTRODUCTION: (state, introduction) => {
    state.introduction = introduction
  },
  SET_NAME: (state, name) => {
    state.name = name
  },
  SET_AVATAR: (state, avatar) => {
    state.avatar = avatar
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_ROLE: (state, role) => {
    state.role = role
  },
  SET_HOME_ROLE: (state, homeRole) => {
    state.homeRole = homeRole
  },
  SET_USERID: (state, id) => {
    state.userId = id
  },
  SET_MENUS: (state, menus) => {
    state.menus = filterTypeB(menus)
  },
  SET_PERMISSION: (state, permission) => {
    state.permission = permission
  },
  SET_TELPHONE: (state, telPhone) => {
    state.telPhone = telPhone
  },
  SET_HOSPITALID: (state, hospitalId) => {
    state.hospitalId = hospitalId
  },
  SET_SEX: (state, sex) => {
    state.sex = sex
  },
  SET_TYPE: (state, type) => {
    state.type = type
  },
  SET_DEPARTMENTID: (state, departCode) => {
    state.departCode = departCode
  },
  SET_DEPARTMENTNAME: (state, departName) => {
    state.departName = departName
  },

  SET_HOSPITAL_CODE: (state, hospital_code) => {
    state.hospital_code = hospital_code
  },
  SET_PATIENT_INFO: (state, patientInfo) => {
    if (patientInfo) {
      state.patientInfo = deepClone(patientInfo)
    }
  },
  SET_SERVICE_STATION: (state, serviceStation) => {
    if (serviceStation) {
      state.serviceStation = deepClone(serviceStation)
    }
  },
  SET_STATIONID: (state, stationId) => {
    if (stationId) {
      state.stationId = stationId
    }
  },
  SET_USERINFO: (state, userInfo) => {
    if (userInfo) {
      state.userInfo = userInfo
    }
  },
  SET_BUTTON_PERMISSIONS: (state, buttonPermissions) => {
    state.buttonPermissions = buttonPermissions
  }
}

// 过滤掉 type 为 'B' 的项
function filterTypeB(menus) {
  if (!Array.isArray(menus)) return []

  return menus
    .filter((item) => item.type !== 'B') // 过滤掉 type 为 'B' 的项
    .map((item) => {
      const filteredChildren = filterTypeB(item.children || []) // 递归处理 children
      const newItem = { ...item } // 复制 item 避免修改原数据

      if (filteredChildren.length > 0) {
        newItem.children = filteredChildren // 只有当 children 非空时才保留
      } else {
        delete newItem.children // 删除空的 children
      }

      return newItem
    })
}

const actions = {
  // user LOGIN
  async LOGIN({ commit }, userInfo) {
    const { phone, password, hospital_code } = userInfo
    return new Promise((resolve, reject) => {
      login({ phone, password, hospitalCode: hospital_code, deviceName: 'chrome,', platform: 'pc' })
        .then((response) => {
          resolve(response)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  async SMS_LOGIN({ commit }, userInfo) {
    const { phone, smsCode } = userInfo
    return new Promise((resolve, reject) => {
      loginBySms({ phone, smsCode, platform: 'pc' })
        .then((response) => {
          resolve(response)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  async scanCodeLogin({ commit }, obj) {
    return new Promise((resolve, reject) => {
      getLoginPcByWxApi(obj)
        .then((res) => {
          const { data } = res
          commit('SET_TOKEN', data.access_token)
          commit('SET_USERID', data.userid)
          setToken(data.access_token)
          setUserId(data.userid)
          router.push({ name: 'Home' })

          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  fakeLogin({ commit }, tokenInfo) {
    const { token, userId } = tokenInfo
    return new Promise((resolve, reject) => {
      commit('SET_TOKEN', token)
      commit('SET_USERID', userId)
      setToken(token)
      setUserId(userId)
    })
  },

  // get user info
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo(getUserId())
        .then((response) => {
          // let typearr = ['患者', '医生', '村医', '管理员']
          const { data } = response
          if (!data) {
            reject(new Error('验证失败，请重新登录。'))
          }
          if (!data.introduction) {
            data.introduction = '该用户未编辑'
          }
          if (!data.avatar) {
            if (data.type) {
              if (`${data.sex}` === '0') {
                data.avatar = 'womandoctorhand'
              } else {
                data.avatar = 'doctorhand'
              }
            } else {
              data.avatar = 'doctorhand'
            }
          }
          const {
            id,
            roles,
            name,
            avatar,
            introduction,
            type,
            menusNew: menus,
            opPermissions,
            opPermissionsNew,
            telPhone,
            hospitalId,
            sex,
            departCode,
            departName,
            hospitalCode,
            serviceStation,
            stationId
          } = data

          const temp = cloneDeep(data)
          delete temp.menusNew
          delete temp.roles
          delete temp.opPermissionsNew
          localCache.setCache('userInfo', temp)

          commit('SET_ROLES', roles)
          commit('SET_NAME', name)
          commit('SET_AVATAR', avatar)
          commit('SET_INTRODUCTION', introduction)
          commit('SET_MENUS', menus)
          commit('SET_BUTTON_PERMISSIONS', opPermissionsNew)
          commit('SET_PERMISSION', opPermissions)
          commit('SET_TELPHONE', telPhone)
          commit('SET_HOSPITALID', hospitalId)
          commit('SET_SEX', sex)
          commit('SET_TYPE', type)
          commit('SET_DEPARTMENTID', departCode)
          commit('SET_DEPARTMENTNAME', departName)
          commit('SET_HOSPITAL_CODE', hospitalCode)
          commit('SET_SERVICE_STATION', serviceStation)
          commit('SET_STATIONID', stationId)
          commit('SET_USERINFO', data)
          websocket.Init(id)
          resolve(data)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // user logout
  logout({ commit, state, dispatch }) {
    return new Promise((resolve, reject) => {
      logout(state.token)
        .then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          removeToken()
          resetRouter()
          removeUserId()
          removeLoginLog() // 删除缓存中的登录日志
          websocket.close()
          // 清除所有右下角消息通知
          Notification.closeAll()
          // reset visited views and cached views
          // to fixed https://github.com/PanJiaChen/vue-element-admin/issues/2485
          dispatch('tagsView/delAllViews', null, { root: true })

          resolve()
        })
        .catch((error) => {
          reject(error)
        })
    })
  },

  // remove token
  resetToken({ commit }) {
    return new Promise((resolve) => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resolve()
    })
  },

  // dynamically modify permissions
  async changeRoles({ commit, dispatch }, role) {
    const token = `${role}-token`

    commit('SET_TOKEN', token)
    setToken(token)

    const { roles } = await dispatch('getInfo')

    resetRouter()

    // generate accessible routes map based on roles
    const accessRoutes = await dispatch('permission/generateRoutes', roles, { root: true })
    // dynamically add accessible routes
    router.addRoutes(accessRoutes)

    // reset visited views and cached views
    dispatch('tagsView/delAllViews', null, { root: true })
  },

  changeHomeRole({ commit }, val) {
    return new Promise((resolve) => {
      commit('SET_HOME_ROLE', val)
      resolve()
    })
  },

  setPatient({ commit }, patientInfo) {
    commit('SET_PATIENT_INFO', patientInfo)
  }
}

const state = {
  userInfo: '',
  token: getToken(),
  name: '',
  userId: '',
  avatar: '',
  introduction: '',
  roles: [],
  role: 'admin',
  homeRole: '',
  menus: [],
  permission: [],
  telPhone: '',
  hospitalId: '',
  sex: '',
  type: '',
  departCode: '',
  departName: '',
  hospital_code: '',
  patientInfo: null,
  buttonPermissions: []
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
