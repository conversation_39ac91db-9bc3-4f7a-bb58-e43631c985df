<template>
  <div class="adverse-event-statistics">
    <el-card class="search-card">
      <el-form :model="queryParams" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="医疗机构：" prop="depart">
              <TreeSelect
                v-model="queryParams.departCode"
                :data="departTree"
                :props="{
                  children: 'children',
                  label: 'departName',
                  value: 'departCode'
                }"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="table-card">
      <el-table v-loading="loading" :data="tableData" style="width: 100%">
        <el-table-column align="center" prop="departName" width="200" label="机构名称" />
        <el-table-column align="center" label="不良事件名称">
          <el-table-column align="center" prop="bloodCount" label="出血总数" />
          <el-table-column align="center" prop="littleBloodCount" label="少量出血" />
          <el-table-column align="center" prop="middleBloodCount" label="中等出血" />
          <el-table-column align="center" prop="bigBloodCount" label="大量出血或致命性出血" />
          <el-table-column align="center" prop="heartCount" label="心衰" />
          <el-table-column align="center" prop="tiaCount" label="TIA" />
          <el-table-column align="center" prop="dementiaCount" label="痴呆" />
          <el-table-column align="center" prop="strokeCount" label="卒中" />
          <el-table-column align="center" prop="coronaryCount" label="冠心病" />
          <el-table-column align="center" prop="blindCount" label="失明" />
          <el-table-column align="center" prop="kidneyCount" label="肾功能不全" />
          <el-table-column align="center" prop="vascularOcclusionCount" label="周围血管闭塞" />
          <el-table-column align="center" prop="aadCount" label="主动脉夹层" />
          <el-table-column align="center" prop="breatheCount" label="呼吸衰竭" />
          <el-table-column align="center" prop="lungHeartCount" label="肺源性心脏病" />
          <el-table-column align="center" prop="lungHeadCount" label="肺性脑病" />
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getAdverseEventStatistics } from '@/api/adverseEvent'
import { getOrgTreeByIdApi } from '@/api/system'
import { getUserId } from '@/utils/auth'
import { localCache } from '@/utils/cache'
import TreeSelect from '@/components/TreeSelect/index.vue'

export default {
  name: 'AdverseEventStatistics',
  components: {
    TreeSelect
  },
  data() {
    return {
      loading: false,
      tableData: [],
      queryParams: {
        departCode: localCache.getCache('userInfo').departCode || ''
      },
      departTree: []
    }
  },
  created() {
    this.getDepartTree()
    this.getAdverseEventStatisticsFn()
  },
  methods: {
    // 机构树
    async getDepartTree() {
      const res = await getOrgTreeByIdApi({
        patientId: getUserId()
      })
      this.departTree = res.data
    },
    // 获取不良事件统计
    getAdverseEventStatisticsFn() {
      this.loading = true
      getAdverseEventStatistics(this.queryParams)
        .then((res) => {
          this.tableData = res.data
        })
        .finally(() => {
          this.loading = false
        })
    },

    // 重置
    handleReset() {
      this.queryParams = {
        departCode: localCache.getCache('userInfo').departCode || ''
      }
      this.getAdverseEventStatisticsFn()
    },
    // 查询
    handleSearch() {
      this.getAdverseEventStatisticsFn()
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.adverse-event-statistics {
  padding: 16px;
  .search-card {
    margin-bottom: 16px;
  }
  .table-card {
    padding: 0;
  }
}
</style>
