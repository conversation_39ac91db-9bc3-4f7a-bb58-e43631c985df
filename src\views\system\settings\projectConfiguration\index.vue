<template>
  <div class="project-configuration">
    <el-card class="project-configuration-search">
      <el-form :model="queryParams" label-width="120px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="机构名称：">
              <TreeSelect
                v-model="queryParams.departCode"
                :data="departTree"
                :props="{
                  children: 'children',
                  label: 'departName',
                  value: 'departCode'
                }"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="模板名称：">
              <el-input v-model="queryParams.templateName" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>

          <el-col :span="6" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="project-configuration-table">
      <el-button type="primary" style="margin-bottom: 8px" @click="handleAdd">新增</el-button>

      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 90px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #operation="{ row }">
          <el-button type="text" @click="handleEdit(row)">编辑</el-button>
          <el-button type="text" style="color: red" @click="handleDelete(row)">删除</el-button>
        </template>
      </base-table>
    </el-card>

    <ProDialog :visible.sync="addDialogVisible" :title="title" width="1000px">
      <el-form ref="formRef" :model="form" label-width="120px" :rules="rules">
        <el-row>
          <el-col :span="24">
            <el-form-item label="模板名称：" prop="templateName">
              <el-input v-model="form.templateName" placeholder="请输入模板名称" style="width: 360px" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="机构名称：" prop="departCode">
              <TreeSelect
                v-model="form.departCode"
                style="width: 360px"
                :data="departTree"
                :props="{
                  children: 'children',
                  label: 'departName',
                  value: 'departCode'
                }"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="项目名称：" prop="itemCode">
              <el-select v-model="form.itemCode" placeholder="请选择项目名称" style="width: 360px">
                <el-option label="生化" value="BIOCHEMISTRY" />
                <el-option label="尿常规" value="URINE_ROUTINE" />
                <el-option label="血常规" value="BLOOD_ROUTINE" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-table :data="configTableData" style="width: 100%" height="300px" border>
        <el-table-column align="center" label="名称" prop="name">
          <template slot-scope="scope">
            <el-input v-model="scope.row.name" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="单位" prop="unit">
          <template slot-scope="scope">
            <el-input v-model="scope.row.unit" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="参考范围" prop="range">
          <template slot-scope="scope">
            <el-input v-model="scope.row.range" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="120">
          <template #default="scope">
            <span @click="handleConfigAdd">
              <i class="el-icon-plus" style="color: #41a1d4; cursor: pointer" />
            </span>
            <span v-if="configTableData.length > 1" @click="handleConfigDelete(scope.$index)">
              <i class="el-icon-delete" style="color: red; margin-left: 16px; cursor: pointer" />
            </span>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <el-button @click="handleCancel">关闭</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </template>
    </ProDialog>
  </div>
</template>

<script>
import {
  getProjectConfigurationList,
  saveProjectConfiguration,
  deleteProjectConfiguration,
  getProjectConfigurationDetail
} from '@/api/projectConfiguration'
import { getOrgTreeByIdApi } from '@/api/system'
import { getUserId } from '@/utils/auth'
import { cloneDeep } from 'lodash'
import TreeSelect from '@/components/TreeSelect/index.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ProDialog from '@/components/ProDialog/index.vue'

const addData = {
  name: '',
  unit: '',
  range: ''
}

export default {
  name: 'ProjectConfiguration',
  components: {
    TreeSelect,
    BaseTable,
    ProDialog
  },
  mixins: [tableMixin],
  data() {
    return {
      departTree: [],
      queryParams: {
        departCode: '',
        templateName: ''
      },
      columns: [
        { label: '模板名称', prop: 'templateName' },
        { label: '机构名称', prop: 'departName' },
        { label: '项目名称', prop: 'itemName' },
        { label: '创建时间', prop: 'createTime' },
        { label: '操作', prop: 'operation', width: 120, slot: 'operation' }
      ],
      title: '新增',
      form: {
        id: '',
        templateName: '',
        departCode: '',
        itemCode: ''
      },
      rules: {
        templateName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
        departCode: [{ required: true, message: '请选择机构名称', trigger: 'change' }],
        itemCode: [{ required: true, message: '请选择项目名称', trigger: 'change' }]
      },
      configTableData: [cloneDeep(addData)]
    }
  },
  created() {
    this.getDepartTree()
  },
  methods: {
    async getTableList(params) {
      return await getProjectConfigurationList(params)
    },

    // 机构树
    async getDepartTree() {
      const res = await getOrgTreeByIdApi({
        patientId: getUserId()
      })
      this.departTree = res.data
    },

    handleAdd() {
      this.addDialogVisible = true
      this.title = '新增'
      this.form = {
        id: '',
        templateName: '',
        departCode: '',
        itemCode: ''
      }
      this.configTableData = [cloneDeep(addData)]
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate()
      })
    },

    async handleEdit(row) {
      const res = await getProjectConfigurationDetail({ id: row.id })
      if (res.code === 200) {
        this.form = res.data
        this.configTableData = res.data.itemList
        this.addDialogVisible = true
        this.title = '编辑'
      }
    },

    handleDelete(row) {
      this.handleConfirmDelete({
        params: { id: row.id },
        deleteApi: deleteProjectConfiguration,
        message: '确定删除该模板吗？'
      })
    },

    handleConfigAdd() {
      this.configTableData.push(cloneDeep(addData))
    },

    handleConfigDelete(index) {
      this.configTableData.splice(index, 1)
    },

    async handleSubmit() {
      const valid = await this.$refs.formRef.validate()
      if (!valid) return
      // 校验列表必填项
      if (!this.validateConfigTable()) {
        // 校验未通过时终止提交
        return
      }
      const params = {
        ...this.form,
        itemList: cloneDeep(this.configTableData)
      }
      const res = await saveProjectConfiguration(params)
      if (res.code === 200) {
        this.$message.success('保存成功')
        this.addDialogVisible = false
        this.handleSearch()
      }
    },

    /**
     * 校验配置表格数据必填项
     * @returns {Boolean} 校验是否通过
     */
    validateConfigTable() {
      // 遍历校验每一行必填项
      const EMPTY_TIP = '表格中的“名称”为必填项，请完善后再提交'
      const isValid = this.configTableData.every((item) => {
        return item.name
      })
      if (!isValid) {
        this.$message.error(EMPTY_TIP)
      }
      return isValid
    },

    handleCancel() {
      this.addDialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.project-configuration {
  height: 100%;
  display: flex;
  flex-direction: column;
  .project-configuration-search {
    margin: 16px;
    height: 77px;
  }
  .project-configuration-table {
    margin: 0 16px;
    flex: 1;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
}
</style>
