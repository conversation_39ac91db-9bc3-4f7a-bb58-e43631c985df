<template>
  <div class="ultrasonic-quality-control">
    <el-card class="ultrasonic-quality-control-search">
      <el-form :model="queryParams" label-width="90px">
        <el-row :gutter="20">
          <el-col :span="5">
            <el-form-item label="医疗机构：" prop="depart">
              <TreeSelect
                v-model="queryParams.departCode"
                :data="departTree"
                :props="{
                  children: 'children',
                  label: 'departName',
                  value: 'departCode'
                }"
                placeholder="请选择"
                @change="handleDepartChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="项目：">
              <el-select v-model="queryParams.itemCode" placeholder="请选择" style="width: 100%">
                <el-option label="超声心动图" value="ECHOCARDIOGRAM" />
                <el-option label="颈动脉超声" value="CAROTID_ULTRASOUND" />
                <!-- <el-option label="心脏彩超" value="ECHOCARDIOGRAPHY" /> -->
                <!-- <el-option label="肺部CT" value="COPD_CT" /> -->
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="质控状态：">
              <el-select v-model="queryParams.status" placeholder="请选择" style="width: 100%">
                <el-option label="待质控" :value="1" />
                <el-option label="通过" :value="5" />
                <el-option label="未通过" :value="9" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="姓名/身份证：" label-width="110px">
              <el-input v-model="queryParams.keyword" />
            </el-form-item>
          </el-col>
          <el-col :span="4" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="ultrasonic-quality-control-table">
      <BaseTable
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 55px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #patientName="{ row }">
          <span style="cursor: pointer; color: #41a1d4" @click="handleView(row)">{{ row.patientName }}</span>
        </template>
        <template #sex="{ row }">
          {{ genderTransform(row.sex) }}
        </template>
        <template #status="{ row }">
          <el-tag
            style="width: 56px"
            :type="row.status === 1 ? 'warning' : row.status === 5 ? 'success' : row.status === 9 ? 'danger' : 'info'"
          >
            {{ row.status === 1 ? '待质控' : row.status === 5 ? '通过' : row.status === 9 ? '未通过' : '' }}
          </el-tag>
        </template>
        <template #attachmentPhotoUrlCount="{ row }">
          <div class="attachment-photo-url-count">
            <div v-if="row.photoCount" class="attachment-photo-url-count-item">
              <svg-icon icon-class="smallPic" />
              <span>{{ row.photoCount }}</span>
            </div>
            <div v-if="row.videoCount" class="attachment-photo-url-count-item">
              <svg-icon icon-class="smallVedio" />
              <span>{{ row.videoCount }}</span>
            </div>
          </div>
        </template>
        <template #operation="{ row }">
          <el-button type="text" size="small" @click="handleView(row)">{{
            row.status === 1 ? '审核' : '查看'
          }}</el-button>
        </template>
      </BaseTable>
    </el-card>
  </div>
</template>

<script>
import { getOrgTreeByIdApi } from '@/api/system'
import { getQualityControlList } from '@/api/qualityControl'
import { localCache } from '@/utils/cache'
import { getUserId } from '@/utils/auth'
import { genderTransform } from '@/utils/cspUtils'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import TreeSelect from '@/components/TreeSelect/index.vue'

export default {
  name: 'UltrasonicQualityControl',
  components: {
    BaseTable,
    TreeSelect
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        departCode: localCache.getCache('userInfo').departCode || '',
        keyword: '',
        itemCode: 'ECHOCARDIOGRAM',
        status: ''
      },
      departTree: [],
      columns: [
        { label: '姓名', prop: 'patientName', slot: 'patientName' },
        { label: '性别', prop: 'sex', slot: 'sex' },
        { label: '年龄', prop: 'age' },
        { label: '质控状态', prop: 'status', slot: 'status' },
        // { label: '未通过原因', prop: 'auditResult', width: 200, showOverflowTooltip: true },
        { label: '审核人', prop: 'auditName' },
        { label: '上传机构', prop: 'departName', width: 170 },
        { label: '上传人员', prop: 'createUsername' },
        { label: '创建时间', prop: 'createTime', width: 160 },
        { label: '审核时间', prop: 'auditTime', width: 160 },
        { label: '来源', prop: 'moduleName', width: 110 },
        { label: '项目', prop: 'itemName', width: 120 },
        { label: '图片视频数量', prop: 'attachmentPhotoUrlCount', width: 160, slot: 'attachmentPhotoUrlCount' },
        { label: '质控等待时间', prop: 'waitDuration', width: 160 },
        {
          label: '操作',
          prop: 'operation',
          width: 160,
          slot: 'operation',
          fixed: window.innerWidth < 1600 ? 'right' : false
        }
      ]
    }
  },
  created() {
    this.getDepartTree()
  },
  methods: {
    genderTransform,
    // 机构树
    async getDepartTree() {
      const res = await getOrgTreeByIdApi({
        patientId: getUserId()
      })
      this.departTree = res.data
    },

    async getTableList(params) {
      return await getQualityControlList(params)
    },

    handleDepartChange(value) {
      this.queryParams.departCode = value
    },
    handleView(row) {
      this.$router.push({
        path: '/qualityControl/ultrasonicQualityControl/detail',
        query: { id: row.id, itemCode: row.itemCode }
      })
    },
    handleReset() {
      this.queryParams = {
        departCode: localCache.getCache('userInfo').departCode || '',
        keyword: '',
        itemCode: 'ECHOCARDIOGRAM',
        status: '',
        pageNo: 1,
        pageSize: 10
      }
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.ultrasonic-quality-control {
  display: flex;
  flex-direction: column;
  height: 100%;
  .ultrasonic-quality-control-search {
    margin: 16px;
    height: 77px;
  }
  .ultrasonic-quality-control-table {
    flex: 1;
    margin: 0 16px 16px 16px;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
  .attachment-photo-url-count {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    .attachment-photo-url-count-item {
      display: flex;
      align-items: center;
      gap: 5px;
    }
  }
}
</style>
