<!-- eslint-disable no-irregular-whitespace -->
<template>
  <div v-loading="loadingPageType" class="consentConfig">
    <div class="top" style="justify-content: flex-end">
      <div class="right">
        <el-button type="primary" size="small" icon="el-icon-circle-plus-outline" @click="addBtnClick">新增</el-button>
      </div>
    </div>
    <div class="table-list">
      <el-table :data="tableData" style="width: 100%" class="public_table">
        <el-table-column prop="title" label="配置类型" />
        <el-table-column prop="title" label="接口名称" />
        <el-table-column prop="title" label="接口地址" />
        <el-table-column prop="title" label="视图名称" />
        <el-table-column prop="title" label="数据库地址" />
        <el-table-column prop="title" label="数据库用户名" />
        <el-table-column prop="title" label="数据库密码" />
      </el-table>
      <div class="pagination">
        <el-pagination
          :current-page="searchData.pageNo"
          :page-sizes="tablePageSizes"
          :page-size="searchData.pageSize"
          :layout="tablePaginationLayout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <!-- 知情同意书 新增/编辑 -->
    <el-dialog
      v-el-drag-dialog
      :title="dialogTitle"
      :visible.sync="informedConsentShow"
      width="600px"
      top="7vh"
      class="el-big-dialog"
      :close-on-click-modal="false"
      :before-close="informedConsentClose"
    >
      <span slot="title" class="dialog-title">
        <span style="margin-left: 15px">{{ dialogTitle }}</span>
      </span>
      <div v-loading="loadingType" class="hisPostInfoDiv">
        <p class="titleRadio">
          <el-radio v-model="popPar.type" label="1">URL配置</el-radio>
          <el-radio v-model="popPar.type" label="2">视图配置</el-radio>
        </p>
        <template v-if="popPar.type === '1'">
          <p class="list">
            <span class="txt require">接口名称</span>
            <el-input
              v-model="popPar.urlName"
              placeholder="请输入接口名称"
              :class="['val', errorType.urlName ? 'error' : '']"
            />
          </p>
          <p class="list">
            <span class="txt require">接口地址</span>
            <el-input
              v-model="popPar.url"
              placeholder="请输入接口地址"
              :class="['val', errorType.url ? 'error' : '']"
            />
          </p>
        </template>
        <template v-else>
          <p class="list">
            <span class="txt require">视图名称</span>
            <el-input
              v-model="popPar.viewName"
              placeholder="请输入视图名称"
              :class="['val', errorType.viewName ? 'error' : '']"
            />
          </p>
          <p class="list">
            <span class="txt require">数据库地址</span>
            <el-input
              v-model="popPar.viewDBAddress"
              placeholder="请输入数据库地址"
              :class="['val', errorType.viewDBAddress ? 'error' : '']"
            />
          </p>
          <p class="list">
            <span class="txt require">数据库用户名</span>
            <el-input
              v-model="popPar.viewDBName"
              placeholder="请输入数据库用户名"
              :class="['val', errorType.viewDBName ? 'error' : '']"
            />
          </p>
          <p class="list">
            <span class="txt require">数据库密码</span>
            <el-input
              v-model="popPar.viewDBPwd"
              placeholder="请输入数据库密码"
              :class="['val', errorType.viewDBPwd ? 'error' : '']"
            />
          </p>
        </template>
      </div>
      <span slot="footer" class="dialog-footer" style="display: flex; justify-content: flex-end">
        <el-button class="appCustomBtnStyle" type="primary" plain @click="informedConsentClose">取消 </el-button>
        <el-button class="appCustomBtnStyle" type="primary" @click="SubmitForm()">保存 </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { parseTime } from '@/utils'
import elDragDialog from '@/directive/el-drag-dialog'
import { getToken } from '@/utils/auth'

export default {
  name: 'His',
  components: {},
  directives: {
    elDragDialog
  },
  data() {
    return {
      searchData: {
        pageNo: 1,
        pageSize: 10,
        tag: '',
        title: ''
      },
      popPar: {
        type: '1',
        urlName: '',
        url: '',
        viewDBAddress: '',
        viewDBName: '',
        viewDBPwd: '',
        viewName: ''
      },
      errorType: {
        type: false,
        urlName: false,
        url: false,
        viewDBAddress: false,
        viewDBName: false,
        viewDBPwd: false,
        viewName: false
      },
      radio: '1',
      tableData: [],
      total: 0,
      tagList: [],
      dialogTitle: '',
      informedConsentShow: false,
      informedConsentData: {
        title: '',
        content: '',
        tags: [],
        originalFile: {
          name: '',
          url: '',
          size: ''
        }
      },
      editor: null,
      previewShow: false,
      previewData: {
        title: '',
        content: '',
        tags: [],
        originalFile: {
          name: '',
          url: '',
          size: ''
        }
      },
      loadingPageType: false,
      loadingType: false
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    SubmitForm() {
      if (this.loadingType) return
      let a = false
      // eslint-disable-next-line guard-for-in
      for (const key in this.popPar) {
        this.errorType[key] = this.popPar[key] === ''
      }
      if (this.popPar.type === '1') {
        this.errorType.viewDBAddress = false
        this.errorType.viewDBName = false
        this.errorType.viewDBPwd = false
        this.errorType.viewName = false
      } else {
        this.errorType.urlName = false
        this.errorType.url = false
      }
      // eslint-disable-next-line guard-for-in
      for (const key in this.errorType) {
        a = this.errorType[key] ? true : a
      }
      if (a) return
      this.loadingType = true
      setTimeout(() => {
        this.informedConsentShow = false
        this.loadingType = false
        this.$message.error('保存失败，未检测到接口信息')
        this.loadingPageType = true
        setTimeout(() => {
          this.loadingPageType = false
        }, 3000)
      }, 6000)
    },
    addBtnClick() {
      this.dialogTitle = 'HIS配置'
      this.informedConsentShow = true
      this.initEditor()
    },
    initEditor() {
      this.popPar = {
        type: '1',
        urlName: '',
        url: '',
        viewDBAddress: '',
        viewDBName: '',
        viewDBPwd: '',
        viewName: ''
      }
    },
    informedConsentClose() {
      this.informedConsentShow = false
      this.informedConsentData = {
        title: '',
        content: '',
        tags: [],
        originalFile: {
          name: '',
          url: '',
          size: ''
        }
      }
      this.editor.txt.html('')
    },
    getTableData() {},
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.searchData.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.searchData.pageNo = val
      this.getTableData()
    },
    parseTime,
    getToken
  }
}
</script>
<style lang="scss" scoped>
.hisPostInfoDiv {
  .titleRadio {
    margin-bottom: 1rem;
    .el-radio {
      margin-right: 2.5rem;
    }
  }
  .list {
    margin-top: 1rem;
    .txt {
      width: 6rem;
      display: inline-block;
      font-size: 0.7rem;
      &.require {
        &::before {
          content: '*';
          color: red;
        }
      }
    }
    .val {
      width: calc(100% - 6rem);
      max-width: 20rem;
      &.error {
        ::v-deep .el-input__inner {
          border-color: red;
        }
        position: relative;
        &::after {
          content: '该项目为必填项';
          color: red;
          font-size: 12px;
          position: absolute;
          left: 0;
          bottom: -10px;
          line-height: 0;
        }
      }
      ::v-deep .el-input__inner {
        height: 1.6rem !important;
      }
    }
  }
}
.consentConfig {
  width: calc(100% - 60px);
  height: calc(100% - 60px);
  margin: 30px auto;
  .top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .left {
      display: flex;
      align-items: center;
      .input-box {
        display: flex;
        align-items: center;
        margin-right: 30px;
        span {
          font-size: 0.75rem;
          font-weight: 500;
          color: #666666;
          white-space: nowrap;
        }
        ::v-deep .el-button {
          width: 90px;
        }
      }
    }
    .right {
      ::v-deep .el-button {
        width: 100px;
      }
    }
  }
  .table-list {
    margin-top: 30px;
    overflow: hidden;
    .pagination {
      padding-right: 20px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: right;
    }
  }
}
.label-box {
  color: #666666;
  font-size: 0.7rem;
  line-height: 40px;
  padding-right: 50px;
}
.content {
  background: #f8f8f8;
  border-radius: 10px;
  border: 1px solid #dedede;
  padding: 30px;
}
::v-deep .el-form-item__label {
  font-weight: 500;
}
::v-deep .el-form--inline .el-form-item {
  margin-right: 30px;
}
::v-deep .el-dialog__body {
  max-height: 70vh;
  overflow: auto;
}
</style>
