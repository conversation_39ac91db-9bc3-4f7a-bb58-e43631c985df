<template>
  <div class="general-diagnosis-of-ollness">
    <flag-component title="病情诊断" />

    <el-form ref="formRef" :model="formData" label-width="130px" :rules="rules" style="margin-top: 22px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="病情诊断结果：" :prop="resultField">
            <el-radio-group v-model="formData[resultField]" @change="handleRadioChange">
              <el-radio v-for="item in radioList" :key="item.value" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>

            <!-- <el-button
              v-if="formData[resultField] === 2 && desc === '慢阻肺'"
              style="margin-left: 16px"
              type="primary"
              size="small"
              @click="handleReferral"
            >
              去转诊
            </el-button> -->
          </el-form-item>
        </el-col>
        <el-col
          v-if="formData[resultField] === 2 && desc === '房颤'"
          :span="6"
          :pull="innerWidth > 1600 ? 5 : 3"
          style="margin-left: 16px"
        >
          <el-form-item label="" label-width="0">
            <el-select v-model="formData.fcType" placeholder="请选择" style="margin-left: 16px">
              <el-option label="阵发性房颤" :value="1" />
              <el-option label="持续性房颤" :value="2" />
              <el-option label="长程持续性房颤" :value="3" />
              <el-option label="永久性房颤" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="病情诊断说明：">
            <el-input v-model="formData[descField]" type="textarea" :rows="4" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <p style="margin: 20px 0; font-size: 16px; font-weight: bold; color: red">高危筛查情况</p>
    <div style="margin: 0 auto">
      <base-table :table-data="tableObject.tableData" :columns="tableObject.tableColumns" :show-pagination="false">
        <template #itemValue="{ row }">
          <span :style="{ color: row.abnormalFlag === 2 ? 'red' : '' }">
            {{ `${row.itemValue}${row.itemName === 'FEV1/FVC' ? '%' : ''} ${row.arrowFlag || ''}` }}
          </span>
        </template>
        <template #spValue="{ row }">
          <span :style="{ color: row.abnormalFlag === 2 ? 'red' : '' }">
            {{ `${row.spValue} ${row.arrowFlag || ''}` }}
          </span>
        </template>
        <template #dpValue="{ row }">
          <span :style="{ color: row.abnormalFlag === 2 ? 'red' : '' }">
            {{ `${row.dpValue} ${row.arrowFlag || ''}` }}
          </span>
        </template>
      </base-table>
    </div>

    <p v-if="desc === '房颤'" style="margin: 20px 0; font-size: 16px; font-weight: bold; color: red">问诊阳性数据</p>

    <div v-if="desc === '房颤'" style="margin: 0 auto">
      <el-form ref="positiveFormRef" :model="positiveForm" :rules="positiveRules" label-width="180px">
        <el-row>
          <el-col v-for="item in positiveData" :key="item.label" :span="24">
            <RadioGroupField v-if="item.type === 'radio'" v-model="positiveForm[item.prop]" :item="item" />
            <CheckboxGroupField v-else v-model="positiveForm[item.prop]" :item="item" />
          </el-col>
        </el-row>
      </el-form>
    </div>

    <ProDialog ref="referralDialog" title="转诊" :visible.sync="referralDialogVisible" width="90%">
      <!-- <Referral /> -->
      <TransferApply type="referral" />
    </ProDialog>
  </div>
</template>

<script>
import { positiveData } from '../component/diagnosisofIllness'
import FlagComponent from '@/components/flagComponent/index.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import ProDialog from '@/components/ProDialog/index.vue'
// import Referral from '../componentDetail/referral.vue'
import TransferApply from '@/views/regionalMedical/transfer/transferApply.vue'
import RadioGroupField from '@/components/questionnaireElementUi/RadioGroupField.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'

export default {
  components: { FlagComponent, BaseTable, ProDialog, TransferApply, RadioGroupField, CheckboxGroupField },
  props: {
    form: {
      type: Object,
      default: () => ({})
    },
    resultField: {
      type: String,
      default: 'result'
    },
    descField: {
      type: String,
      default: 'description'
    },
    radioList: {
      type: Array,
      default: () => []
    },
    tableObject: {
      type: Object,
      default: () => ({})
    },
    desc: {
      type: String,
      default: ''
    }
  },
  data() {
    const positiveForm = {}
    const positiveRules = {}
    positiveData.forEach((item) => {
      positiveForm[item.prop] = ''
      if (item.required) {
        positiveRules[item.prop] = [{ required: true, message: '请选择', trigger: 'change' }]
      }
    })
    return {
      positiveData, // 问诊阳性数据
      positiveForm,
      positiveRules,
      formData: {
        ...this.form,
        fcType: ''
      },
      rules: {
        [this.resultField]: [{ required: true, message: '请选择病情诊断结果', trigger: 'change' }]
      },
      referralDialogVisible: false,
      innerWidth: window.innerWidth
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initData(data) {
      this.formData = {
        [this.resultField]: data[this.resultField] || '',
        [this.descField]: data[this.descField],
        fcType: data.fcType || ''
      }

      this.positiveForm = {
        ...Object.keys(this.positiveForm).reduce((acc, key) => {
          if (key === 'fcFamilyHistory') {
            acc[key] = data[key] ? data[key].split(',') : []
          } else {
            acc[key] = data[key] || ''
          }
          return acc
        }, {})
      }

      const tableField =
        this.desc === '高血压'
          ? 'gxyHrsVOList'
          : this.desc === '糖尿病'
            ? 'tnbHrsVOList'
            : this.desc === '慢阻肺'
              ? 'copdHrsVOList'
              : 'fcHrsVOList'
      this.tableObject.tableData = data[tableField] || []
    },
    async handleSave() {
      const result = {
        name: `${this.desc}-病情诊断`,
        success: false,
        data: {
          ...this.formData,
          ...this.positiveForm,
          fcFamilyHistory: this.positiveForm.fcFamilyHistory.join(',')
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        const valid2 = this.desc === '房颤' ? await this.$refs.positiveFormRef.validate() : true
        result.success = valid && valid2
      } catch (err) {
        console.warn('病情诊断校验异常', err)
        result.success = false
      }

      return result
    },
    handleReferral() {
      this.referralDialogVisible = true
    },
    handleResize() {
      this.innerWidth = window.innerWidth
    },
    handleRadioChange(value) {
      if (value === 1 && this.desc === '房颤') {
        this.formData.fcType = ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.general-diagnosis-of-ollness {
  padding: 16px;
  ::v-deep .long-label .el-form-item__label {
    width: auto !important;
    margin-left: 60px;
  }
}
</style>
