// 导入所有插件模块
import registerGlobalComponents from './global-components'
import setupViewer from './viewer'
import registerFilters from './filters'
import registerGlobalProperties from './global-properties'
import setupRouterInterceptor from './router-interceptor'
import importStyles from './styles'
import registerPlugins from './plugins'

// 统一初始化函数
const initializeApp = () => {
  // 导入样式
  importStyles()

  // 注册全局组件
  registerGlobalComponents()

  // 配置 Viewer
  setupViewer()

  // 注册全局过滤器
  registerFilters()

  // 注册全局属性和方法
  registerGlobalProperties()

  // 配置路由拦截器
  setupRouterInterceptor()

  // 注册第三方插件
  registerPlugins()
}

export default initializeApp
