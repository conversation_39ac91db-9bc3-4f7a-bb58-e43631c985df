import request from '@/utils/request'
// import data from '@/views/pdf/content'

// 获取用户信息
export function getUserInfoApi(id, token) {
  return request({
    url: `/cspapi/backend/user/${id}`,
    method: 'get',
    headers: {
      Authorization: token
    }
  })
}

// 新增问卷
export function questionnaireApi(data) {
  return request({
    url: '/cspapi/backend/questionnaire/',
    method: 'post',
    data
  })
}

// 30.37.1 根据code查询单条记录
export function getSettingGetByCodeApi(data) {
  return request({
    url: '/cspapi/backend/sys/param/setting/getByCode',
    method: 'get',
    params: data
  })
}

// 导出-历史体检记录(new-bashboard)
export function getDownloadBodyCheckStatusApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/groupUser/exportVisitExcel',
    method: 'get',
    responseType: 'blob',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: data
  })
}

// 查看问卷详情
export function lookquestionnaireApi(id, token) {
  return request({
    url: `/cspapi/backend/questionnaire/${id}`,
    method: 'get',
    headers: {
      Authorization: token
    }
  })
}
// 删除问卷
export function deletequestionnaireApi(id) {
  return request({
    url: `/cspapi/backend/questionnaire/${id}`,
    method: 'delete'
  })
}
// 删除问卷
export function putquestionnaireApi(data) {
  return request({
    url: '/cspapi/backend/questionnaire/',
    method: 'put',
    data
  })
}

// 查看问卷列表
export function getquestionnaireApi(data) {
  return request({
    url: '/cspapi/backend/questionnaire/',
    method: 'get',
    params: data
  })
}
// 查看问卷 tree  列表
export function getquestionnaireTreeApi(data) {
  return request({
    url: '/cspapi/backend/questionnaire/treeTable',
    method: 'get',
    params: data
  })
}

// 查看问卷详情
export function postPhotoApi(data, token) {
  return request({
    url: '/cspapi/backend/bodyCheck/questionnaires/pc2Phone',
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}

//  查看所有策略
export function getpolicyApi(data) {
  return request({
    url: '/cspapi/backend/visit/policy/',
    method: 'get',
    params: data
  })
}

//  新增策略
export function postpolicyApi(data) {
  return request({
    url: '/cspapi/backend/visit/policy/',
    method: 'post',
    data
  })
}
//  修改策略
export function putpolicyApi(data) {
  return request({
    url: '/cspapi/backend/visit/policy/',
    method: 'put',
    data
  })
}
//  删除策略
export function deletepolicyApi(id) {
  return request({
    url: `/cspapi/backend/visit/policy/${id}`,
    method: 'delete'
  })
}

//  查看所有记录列表(分页)   患者随访！！11
export function getrecordApi(data) {
  return request({
    url: '/cspapi/backend/visit/record/',
    method: 'get',
    params: data
  })
}

//   用户答题
export function postrecordApi(data) {
  return request({
    url: '/cspapi/backend/visit/record/',
    method: 'post',
    data
  })
}

//  新增随访批次
export function addbatchApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/batch',
    method: 'post',
    transformRequest: [
      function(val, headers) {
        // 去除post请求默认的Content-Type
        delete headers.post['Content-Type']
        return val
      }
    ],
    // headers:{
    //   "Content-Type": "multipart/form-data"
    // },
    data
  })
}
// 修改随访批次
export function putbatchApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/batch/updateById',
    method: 'post',
    // transformRequest: [function(data, headers) {
    //   // 去除post请求默认的Content-Type
    //   delete headers.post['Content-Type']
    //   return data
    // }],
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}
// 查看批次列表
export function getbatchListApi() {
  return request({
    url: '/cspapi/backend/visitphm/batch/list',
    method: 'get'
  })
}
// 查看随访批次(根据id)
export function getbatchApi(id) {
  return request({
    url: `/cspapi/backend/visitphm/batch/${id}`,
    method: 'get'
  })
}
// 删除随访批次(根据id)
export function deleteBatchApi(id) {
  return request({
    url: `/cspapi/backend/visitphm/batch/${id}`,
    method: 'delete'
  })
}
// 患者刷卡报到
export function postUserExamineApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/visit/startVisit',
    method: 'get',
    params: { ...data, platform: 'pc' }
  })
}

//  随访中 + 已完成的人数
export function getExamineStatisticApi(id, serviceStationId, sortNo, patientName, data) {
  return request({
    url: '/cspapi/backend/visitphm/status/statistic',
    method: 'get',
    params: { rootQueueId: id, serviceStationId, sortNo, patientName, ...data }
  })
}

//  当前医生所负责的指标人员列表
export function getlistPatientsByBatchIdAndDutyIdApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/duty/listByDoctorId',
    method: 'get',
    params: data
  })
}
//  查看当前患者的随访数据
export function getByBatchIdAndUserIdApi(batchId, patientId) {
  return request({
    url: '/cspapi/backend/visitphm/active/getByBatchIdAndPatientIdAndType',
    method: 'get',
    params: { batchId, patientId }
  })
}
// 查看当前患者的体征数据
export function getvisitCheckActiveApi(batchId, patientId) {
  return request({
    url: '/cspapi/backend/visitCheck/active2/getByBatchIdAndPatientIdAndType',
    method: 'get',
    params: { recordId: batchId, patientId, module: 'PHMVISIT' }
  })
}
// 新增随访记录(保存某项随访指标的数据)
export function postvisitCheckRecordApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/active',
    method: 'post',
    data
  })
}
// 新增/修改体征数据
export function postvisitCheckActiveApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/active',
    method: 'post',
    data
  })
}
//  随访-问卷-糖尿病详情
export function getT2dmByBatchIdAndPatientIdApi(batchId, patientId) {
  return request({
    url: '/cspapi/backend/visitphm/questionnaire/t2dm/getByBatchIdAndPatientId',
    method: 'get',
    params: { batchId, patientId }
  })
}
// 随访-问卷-糖尿病新增     修改
export function postT2dmByBatchIdAndPatientIdApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/questionnaire/t2dm',
    method: 'post',
    data
  })
}
//  随访-问卷-糖尿病详情
export function getHtnByBatchIdAndPatientIdApi(batchId, patientId) {
  return request({
    url: '/cspapi/backend/visitphm/questionnaire/htn/getByBatchIdAndPatientId',
    method: 'get',
    params: { batchId, patientId }
  })
}
// 随访-问卷-糖尿病新增     修改
export function postHtnByBatchIdAndPatientIdApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/questionnaire/htn',
    method: 'post',
    data
  })
}

// 新增随访记录(保存草稿不改变状态)
export function postSaveDraftApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/record/saveDraft',
    method: 'post',
    data
  })
}
// 查看某个医生所负责的指标
export function getlistItemByDoctorIdApi(batchId, doctorId) {
  return request({
    url: '/cspapi/backend/visitphm/duty/listByDoctorId',
    method: 'get',
    params: { batchId, doctorId }
  })
}
// 查看这个队列  的检查项
export function getItemsByRecordIdApi(recordId) {
  return request({
    url: '/cspapi/backend/visitphm/queue/getItemsByRecordId',
    method: 'get',
    params: { recordId }
  })
}

// 根据随访记录id获得队列信息
export function getQueueInfoByRecordIdApi(recordId) {
  return request({
    url: '/cspapi/backend/visitphm/queue/getQueueInfoByRecordId',
    method: 'get',
    params: { recordId }
  })
}

// 某患者已经做过的指标
export function getlistByBatchIdAndPatientIdApi(batchId, patientId) {
  return request({
    url: '/cspapi/backend/visitphm/status/listByBatchIdAndPatientId',
    method: 'get',
    params: { recordId: batchId, patientId }
  })
}
// 随访必填项（基础慢病管理）
export function getVisitMustItemApi(batchId, patientId) {
  return request({
    url: '/cspapi/backend/visitphm/visit/getVisitMustItem',
    method: 'get',
    params: { recordId: batchId, patientId }
  })
}

// 根据医生id获得最近的一个批次
export function getlastByDoctorIdApi(id) {
  return request({
    url: '/cspapi/backend/visitphm/batch/lastByDoctorId',
    method: 'get',
    params: { doctorId: id }
  })
}
// 某患者的历史随访记录
export function getRecordByPatientIdApi(id) {
  return request({
    url: '/cspapi/backend/visitphm/record/listByPatientId',
    method: 'get',
    params: { patientId: id }
  })
}
// 某批次的历史随访记录
export function getRecordByBatchIdApi(id) {
  return request({
    url: '/cspapi/backend/visitphm/record/listByBatchId',
    method: 'get',
    params: { batchId: id }
  })
}
// 下载模板
export function getPhysical_examinationApi() {
  return request({
    url: '/cspapi/backend/excel/template/followup',
    method: 'get'
  })
}
// 工作台-左侧患者随访列表
export function getpagePatientByBatchIdApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/user/pagePatientByBatchId',
    method: 'get',
    params: data
  })
}
// 新建档案
export function postNewUserbatchApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/groupUser/handleJoin',
    method: 'post',
    data
  })
}

// 查询所有健康顾问
export function getListByType() {
  return request({
    url: '/cspapi/backend/user/listByType',
    method: 'get',
    params: { type: 2, departCode: '' }
  })
}
// 查看体征数据(随访)  血压  血糖
export function getByBatchIdAndPatientIdAndTypeApi(data) {
  return request({
    url: '/cspapi/backend/visitCheck/active2/getByBatchIdAndPatientIdAndType',
    method: 'get',
    params: data
  })
}
// 根据用户身份证查询用户信息
export function getUserByIdCardeApi(idCard) {
  return request({
    url: '/cspapi/backend/user/getUserByIdCard',
    method: 'get',
    params: { idCard }
  })
}
// 手动入组
export function getsingleByHandApi(patientId, batchId) {
  return request({
    url: '/cspapi/backend/visitphm/batch/singleByHand',
    method: 'post',
    params: { patientId, batchId }
  })
}
//  查看队列树
export function getqueueTreeApi() {
  return request({
    url: '/cspapi/backend/visitphm/queue/tree',
    method: 'get'
  })
}

//   新增队列
export function postvisitCheckApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/queue',
    method: 'post',
    data
  })
}
//   删除队列
export function removeVisitCheckApi(queueId) {
  return request({
    url: `/cspapi/backend/visitphm/queue/${queueId}`,
    method: 'delete'
  })
}
//  条件查询随访人员
export function getgroupUserApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/groupUser',
    method: 'get',
    params: data
  })
}
//  查看每个医生设置的检查项
export function getvisitCheckDutyApi() {
  return request({
    url: '/cspapi/backend/visitphm/duty',
    method: 'get'
  })
}
//  新增或修改检查项
export function postvisitCheckDutyApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/duty',
    method: 'post',
    data
  })
}
//  根据左侧的分组树查询相应的患者 ==>   30.16.5.0 随访工作台(list tree)
export function postvisitCheckGroupUserApi(data) {
  return request({
    // url: '/cspapi/backend/visitphm/visit/listStudioPatient',
    url: '/cspapi/backend/visitphm/visit/dashboard/tree',
    method: 'get',
    params: data
  })
}

//  换组
export function postChangeGrouprApi(data) {
  return request({
    url: `/cspapi/backend/visitphm/groupUser/changeGroup?patientId=${data.patientId}&newGroupId=${data.newGroupId}`,
    method: 'put'
  })
}
//  查看某个队列/分组的详情
export function getQueueApi(id) {
  return request({
    url: `/cspapi/backend/visitCheck/queue/getById`,
    method: 'get',
    params: { id }
  })
}

//  修改某个队列信息
export function putQueueApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/queue',
    method: 'put',
    data
  })
}
//  结束随访
export function putfinishVisitApi(id) {
  return request({
    url: `/cspapi/backend/visitphm/visit/${id}`,
    method: 'put'
  })
}

//  专病查看某人的历史随访记录
export function getSpecializedListByPatientIdApi(
  id,
  status,
  statusList,
  groupId,
  minVisitCount,
  maxVisitCount,
  startVisitDate,
  endVisitDate
) {
  return request({
    url: '/cspapi/backend/visitCheck/visit/listByPatientId',
    method: 'get',
    params: { patientId: id, status, statusList, groupId, minVisitCount, maxVisitCount, startVisitDate, endVisitDate }
  })
}

//  公卫查看某人的历史随访记录
export function getListByPatientIdApi(
  id,
  status,
  statusList,
  groupId,
  minVisitCount,
  maxVisitCount,
  startVisitDate,
  endVisitDate
) {
  return request({
    url: '/cspapi/backend/visitphm/visit/listByPatientId',
    method: 'get',
    params: { patientId: id, status, statusList, groupId, minVisitCount, maxVisitCount, startVisitDate, endVisitDate }
  })
}

//  公卫查看某人的历史随访记录
export function getListByPatientIdApi2(id, statusList, groupId) {
  return request({
    url: '/cspapi/backend/visitphm/visit/listByPatientId',
    method: 'get',
    params: { patientId: id, statusList, groupId }
  })
}

//  查看某人的历史随访记录-随访
export function getListByPatientIdVisitApi(id, token) {
  return request({
    url: '/cspapi/backend/bodyCheck/phmUserinfo/listByPatientId',
    method: 'get',
    params: { patientId: id },
    headers: {
      Authorization: token
    }
  })
}

//   获取患者要做的问卷
export function getListQuestionnaireApi(id, token) {
  return request({
    url: '/cspapi/backend/visitphm/questionnaires/listByVisitIdAndStatus',
    method: 'get',
    params: { recordId: id },
    headers: {
      Authorization: token
    }
  })
}

//   随访-获取患者要做的问卷
export function getVISITListQuestionnaireApi(id, token) {
  return request({
    url: '/cspapi/backend/visitphm/phmquestionnaires',
    method: 'get',
    params: { recordId: id },
    headers: {
      Authorization: token
    }
  })
}
// 提交问卷
export function postQuestionnaireApi(data, token) {
  return request({
    url: '/cspapi/backend/visitphm/questionnaires/saveOneQuestionnaire',
    method: 'post',
    data,
    headers: {
      Authorization: token
    }
  })
}
// 查看某患者某条随访记录所做的问卷以及答案
export function getAnswersApi(data, token) {
  return request({
    url: '/cspapi/backend/visitphm/questionnaires/listByVisitIdAndStatus',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}
// 查看某患者做过的某张问卷的答案
export function getQuestionnairesAnswersApi(data, token) {
  return request({
    url: '/cspapi/backend/visitphm/questionnaires/getByRecordIdAndPatientIdAndQuestionnaireId',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}
// 提前获取问卷信息（是否有老数据）
export function getQuestionInfoApi(data, token) {
  return request({
    url: '/cspapi/backend/visitphm/phmGxyQuestionnaires/getQuestionInfo',
    method: 'get',
    params: data,
    headers: {
      Authorization: token
    }
  })
}
// ecg  zhuangtai
export function changeEcgStatus(patientId, recordId, doctorId) {
  return request({
    url: '/cspapi/backend/visitphm/status/changeEcgStatus',
    method: 'get',
    params: { patientId, recordId, doctorId }
  })
}
// bps  下发 zhuangtai
export function sendQDSStatusApi(patientId, recordId, doctorId) {
  return request({
    url: '/cspapi/backend/visitphm/status/changeQDSStatus',
    method: 'get',
    params: { patientId, recordId, doctorId }
  })
}
// 肢体动脉硬化 下发人员
export function getChangeDmyhStatusApi(patientId, recordId, doctorId) {
  return request({
    url: '/cspapi/backend/visitphm/status/changeDmyhStatus',
    method: 'get',
    params: { patientId, recordId, doctorId }
  })
}

// 专病删除队列下的某个人员
export function deleteSpecializedUserByQueueIdApi(patientId, groupId, queueId, reason, recordId) {
  return request({
    url: '/cspapi/backend/visitCheck/groupUser/deleteUserByQueueId',
    method: 'delete',
    params: { patientId, groupId, queueId, reason, recordId }
  })
}

// 公卫删除队列下的某个人员
export function deleteUserByQueueIdApi(patientId, groupId, queueId, reason, recordId) {
  return request({
    url: '/cspapi/backend/visitphm/groupUser/deleteUserByQueueId',
    method: 'delete',
    params: { patientId, groupId, queueId, reason, recordId }
  })
}
// 任务导出列表(分页)
export function getDownloadPageApi(params) {
  return request({
    url: '/cspapi/backend/task/download/page',
    method: 'get',
    params
  })
}
//  穿戴数据导出 文件流
export function getExportZipByUserIdsApi(data) {
  return request({
    url: '/cspapi/backend/health/diary/exportZipByUserIds',
    method: 'post',
    // responseType: 'blob',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data
  })
}
//  测量文件  导出  预下载 传入到下载列表
export function getMeasurementFileApi(params) {
  return request({
    url: '/cspapi/backend/visit/export/minio/files',
    method: 'get',
    params
  })
}
//  标签标注数据导出 文件流
export function getExportZipBytagRemarkApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/tagRemark/export',
    method: 'post',
    responseType: 'blob',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    data
  })
}
//   导出随访人员的Excel(心衰+房颤)    测量数据导出
export function getExportExcelApi(params) {
  return request({
    url: '/cspapi/backend/visitphm/visit/exportExcel',
    method: 'get',
    responseType: 'blob',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params
  })
}

//  新增/修改体征数据(整改后-用这个)
export function setActiveDataApi(data) {
  return request({
    url: '/cspapi/backend/visitCheck/active2',
    method: 'post',
    data
  })
}

//   新增/修改体征数据 颈动脉超声  修改
export function setActiveDataCduApi(data) {
  return request({
    url: '/cspapi/backend/active/cdu',
    method: 'put',
    data
  })
}
//   新增/修改体征数据 下肢动脉超声  修改
export function setActiveDataLeusApi(data) {
  return request({
    url: '/cspapi/backend/active/leus',
    method: 'put',
    data
  })
}

// 删除某项指标的某条记录
export function removeActiveDataApi(data) {
  return request({
    url: '/cspapi/backend/visitCheck/active2/deleteByItemRecordId',
    method: 'delete',
    data
  })
}

// 录音
export function getRecordingListApi(params) {
  return request({
    url: '/cspapi/backend/questionnaire/recording/getByRecordId',
    method: 'get',
    params
  })
}
// 录音
export function getRecordingListApi2(params) {
  return request({
    url: '/cspapi/backend/questionnaire/recording/getByRecordId',
    method: 'get',
    params
  })
}
// 查看3级队列
export function getQueueListLevel3Api(params) {
  return request({
    url: '/cspapi/backend/visitCheck/queue/list/level3',
    method: 'get',
    params
  })
}
//  条件查询随访人员(TableTree)
export function getTableTreeByModelApi(params, url) {
  return request({
    url: url || '/cspapi/backend/visitphm/groupUser/tableTreeByModel',
    method: 'get',
    params
  })
}
//  列表-标签备注(某人在某个队列)
export function getTagRemarkListApi(params) {
  return request({
    url: '/cspapi/backend/visitphm/tagRemark',
    method: 'get',
    params
  })
}
//   新增-标签备注
export function addTagRemarkListApi(data) {
  return request({
    url: '/cspapi/backend/visitphm/tagRemark',
    method: 'post',
    data
  })
}
//  查询某个患者所在的队列信息
export function getQueueByPatientIdApi(patientId) {
  return request({
    url: '/cspapi/backend/visitphm/groupUser/getQueueByPatientId',
    method: 'get',
    params: { patientId }
  })
}
//   根据id查询信息  知情同意书
export function getGroupUserApi(id) {
  return request({
    url: '/cspapi/backend/visitphm/groupUser/getById',
    method: 'get',
    params: { id }
  })
}

// 上传  随访问卷 录音问卷 拿到url
export function uploadRecorderApi(data) {
  return request({
    url: '/cspapi/backend/cos/uploadFile/private2',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
    // headers:{Content-Type:'multipart/form-data'}
  })
}
// 10.1.8 修改患者人脸信息
export function updateUserIdFace(data) {
  return request({
    url: '/cspapi/backend/user/updateUserId/face',
    method: 'post',
    data
  })
}

// 上传  随访问卷 录音
export function uploadrecordingApi(data) {
  return request({
    url: '/cspapi/backend/questionnaire/recording',
    method: 'post',
    data
    // headers:{Content-Type:'multipart/form-data'}
  })
}

// 终止随性
export function stopAccompanying(data) {
  return request({
    url: '/cspapi/backend/visitphm/groupUser/stopUser',
    method: 'delete',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 问卷分类
// 19.1.1-1 问卷分类-分页
export function getQuestionnaireCategoryApi(params) {
  return request({
    url: '/cspapi/backend/questionnaire/category/page',
    method: 'get',
    params
  })
}
// 19.1.1-1 问卷分类-列表
export function getQuestionnaireCategoryListApi() {
  return request({
    url: '/cspapi/backend/questionnaire/category/list',
    method: 'get'
  })
}
// 根据moduleCode查询字典值list
export function getDictionaryValApi(code, token) {
  return request({
    url: `/cspapi/backend/sys/dictionary/listByModuleCode/${code}`,
    method: 'get',
    headers: {
      Authorization: token
    }
  })
}
//  问卷分类-新建
export function getAddQuestionnaireCategoryApi(data) {
  return request({
    url: '/cspapi/backend/questionnaire/category',
    method: 'post',
    data
  })
}
//  问卷分类-新建
export function getPutQuestionnaireCategoryApi(data) {
  return request({
    url: '/cspapi/backend/questionnaire/category',
    method: 'put',
    data
  })
}
//  问卷分类-新建
export function getRemoveQuestionnaireCategoryApi(id) {
  return request({
    url: `/cspapi/backend/questionnaire/category/${id}`,
    method: 'delete'
  })
}
//  随访记录详情(根据id)
export function getVisitCheckDetalApi(id) {
  return request({
    url: `/cspapi/backend/visitphm/visit/getById`,
    method: 'get',
    params: { id }
  })
}
export function getVisitCheckDetalApi2(id) {
  return request({
    url: `/cspapi/backend/visitphm/visit/getById`,
    method: 'get',
    params: { id }
  })
}

//  随访记录详情(根据id)
export function getMedicineIsChangedApi(params) {
  return request({
    url: `/cspapi/backend/visitphm/phmquestionnaires/medicineIsChanged`,
    method: 'get',
    params
  })
}
//  随访记录详情(根据id)
export function getQuestionnairesTemporaryApi(params) {
  return request({
    url: `/cspapi/backend/visitphm/questionnaires/page/groupByPatientId`,
    method: 'get',
    params
  })
}
//  分页查询不在这个队列的人员信息
export function getPageUserNotInGroupId(params) {
  return request({
    url: `/cspapi/backend/visitphm/groupUser/pageUserNotInGroupId`,
    method: 'get',
    params
  })
}

//  多用户档案(手动随访入档)
export function getVisitGroupUserApi(data) {
  return request({
    url: `/cspapi/backend/visitphm/groupUser/handleMultiJoin`,
    method: 'post',
    data
  })
}

export function exportVisitCheck(queueId) {
  return request({
    url: '/cspapi/backend/visitphm/status/export',
    method: 'get',
    responseType: 'blob',
    headers: { 'content-type': 'application/x-www-form-urlencoded' },
    params: { queueId }
  })
}

// 专病终止随访
export function stopSpecializedAccompanying(patientId, groupId, queueId, reason, recordId, token) {
  return request({
    url: '/cspapi/backend/visitCheck/groupUser/stopUser',
    method: 'delete',
    params: { patientId, groupId, queueId, reason, recordId },
    header: {
      Authorization: token
    }
  })
}

// 公卫终止随访
export function stopTerminationfollowup(patientId, groupId, queueId, reason, recordId, token) {
  return request({
    url: `/cspapi/backend/visitphm/groupUser/stopUser`,
    method: 'delete',
    params: { patientId, groupId, queueId, reason, recordId },
    header: {
      Authorization: token
    }
  })
}

// 健康档案AI解读
export function getdiagnosticInfoApi(data) {
  return request({
    url: '/cspapi/backend/ollama/chat',
    method: 'get',
    params: data
  })
}

// 星网健康报告结论
export function getConclusionApi(data) {
  return request({
    url: '/cspapi/backend/ai/healReport/Conclusion',
    method: 'get',
    params: data
  })
}
// 心血管结论
export function getBloodVesselConclusionApi(data) {
  return request({
    url: '/cspapi/backend/ai/healReport/bloodVesselConclusion',
    method: 'get',
    params: data
  })
}
