<template>
  <div class="sidebar-logo-container">
    <!-- <div class="sidebar-logo-container" :class="{ collapse: collapse }"> -->
    <transition name="sidebarLogoFade">
      <!-- <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="targetEnv.LOGO" :src="targetEnv.LOGO" class="sidebar-logo" />
        <h1 v-else class="sidebar-title">{{ targetEnv.TITLE }}</h1>
      </router-link> -->
      <router-link key="expand" class="sidebar-logo-link" to="/">
        <img v-if="targetEnv.LOGO" :src="targetEnv.LOGO" class="sidebar-logo">
        <section class="sidebar-title">
          <div class="topTitle">{{ targetEnv.TITLE }}</div>
          <div v-if="targetEnv.TEXT" class="subTitle">{{ targetEnv.TEXT }}</div>
        </section>
      </router-link>
    </transition>
  </div>
</template>

<script>
import { loginSetting as targetEnv } from './LogoEnum'

export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      targetEnv
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 60px;
  line-height: 60px;
  text-align: center;
  overflow: hidden;
  color: #5e6e82;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    -webkit-user-drag: none;

    & .sidebar-logo {
      width: auto;
      height: 40px;
      vertical-align: middle;
      margin-right: 12px;
      -webkit-user-drag: none;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      font-weight: 400;
      line-height: 20px;
      font-size: 0.7rem;
      font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
      vertical-align: middle;
      text-align: left;
      .topTitle {
        font-size: 0.8rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #222222;
        margin: 0;
      }
      .subTitle {
        font-size: 0.6rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #666666;
        margin: 0;
      }
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
      -webkit-user-drag: none;
    }
  }
}
</style>
