<!-- 服务包签订统计 -->
<template>
  <div class="service-package-sign-statistics">
    <el-card class="service-package-sign-statistics-search">
      <SearchForm
        ref="searchForm"
        :query-params="queryParams"
        :query-criteria="['timeRange', 'departCode']"
        @search="handleSearch"
        @reset="handleReset"
      />
    </el-card>

    <el-card class="service-package-sign-statistics-table">
      <BaseTable
        ref="baseTable"
        :columns="columns"
        :loading="loading"
        :table-data="tableData"
        :show-pagination="showPagination"
      />
    </el-card>
  </div>
</template>

<script>
import { getServicePackageStatistics } from '@/api/statisticalReport'
import { localCache } from '@/utils/cache'
import SearchForm from '../component/searchForm.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'

export default {
  name: 'ServicePackageSignStatistics',
  components: {
    SearchForm,
    BaseTable
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        timeRange: [],
        departCode: localCache.getCache('userInfo').departCode || ''
      },
      columns: [
        { prop: 'departName', label: '机构名称' },
        { prop: 'count', label: '签约总人数' },
        { prop: 'moneyStr', label: '签约金额' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }
      return await getServicePackageStatistics(queryParams)
    },

    handleReset() {
      this.queryParams.timeRange = []
      this.queryParams.departCode = localCache.getCache('userInfo').departCode || ''
      this.fetchData()
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/form-overrides.scss';

.service-package-sign-statistics {
  padding: 16px;
  .service-package-sign-statistics-search {
    margin-bottom: 16px;
  }
}
</style>
