const tnbGxyCopdFcQuestion = [
  {
    label: '婚姻状况：',
    type: 'radio',
    prop: 'marriage',
    required: true,
    belong: 'all',
    options: [
      { label: '未婚', value: 1 },
      { label: '已婚', value: 2 },
      { label: '丧偶', value: 3 },
      { label: '离婚', value: 4 },
      { label: '未说明婚姻状况', value: 5 }
    ]
  },
  {
    label: '是否饮酒：',
    type: 'radio',
    prop: 'drink',
    required: true,
    belong: 'all',
    options: [
      { label: '从不', value: 1 },
      { label: '偶尔', value: 2 },
      { label: '经常', value: 3 },
      { label: '每天', value: 4 },
      { label: '已戒酒', value: 5 }
    ]
  },
  {
    label: '是否吸烟：',
    type: 'radio',
    prop: 'smoke',
    required: true,
    belong: 'all',
    options: [
      { label: '从不吸烟', value: 1 },
      { label: '已戒烟', value: 2 },
      { label: '吸烟', value: 3 }
    ]
  },
  {
    label: '职业：',
    type: 'radio',
    prop: 'career',
    required: true,
    belong: 'all',
    options: [
      { label: '国家机关、党群组织、企业、事业单位负责人', value: 1 },
      { label: '专业技术人员', value: 2 },
      { label: '办事人员和有关人员', value: 3 },
      { label: '社会生产服务和生活服务人员', value: 4 },
      { label: '农、林、牧、渔、水利业生产人员', value: 5 },
      { label: '生产、运输设备操作人员及有关人员', value: 6 },
      { label: '军人', value: 7 },
      { label: '不便分类的其他从业人员', value: 8 },
      { label: '无职业', value: 9 }
    ]
  },
  {
    label: '职业类型：',
    type: 'radio',
    prop: 'careerType',
    required: false,
    belong: 'all',
    options: [
      { label: '轻劳动职业(如办公、操作一控、控制、查体运输等)', value: 1 },
      { label: '中等劳动职业(如建筑搬运、挖掘等)', value: 2 },
      { label: '重度劳动职业(如大强度搬运重物、挖掘等)', value: 3 },
      { label: '极重劳动职业(如大强度搬运重物、挖掘等)', value: 4 }
    ]
  },
  {
    label: '每日蔬菜摄入量：',
    type: 'radio',
    prop: 'vegetableIntake',
    required: true,
    belong: 'tnb,gxy',
    options: [
      { label: '非常少（<100g）', value: 1 },
      { label: '少（100-200g）', value: 2 },
      { label: '正常（300-500g）', value: 3 },
      { label: '多（>500g）', value: 4 }
    ]
  },
  {
    label: '是否高盐/高脂：',
    type: 'radio',
    prop: 'highSaltFatFlag',
    required: true,
    belong: 'tnb,gxy',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '是否缺乏体力活动：',
    type: 'radio',
    prop: 'labourFlag',
    required: true,
    belong: 'tnb,gxy',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '是否长期精神紧张：',
    type: 'radio',
    prop: 'workOvertimeFlag',
    required: true,
    belong: 'tnb,gxy',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '主诉：',
    type: 'text',
    prop: 'gxyComplained',
    required: false,
    belong: 'tnb,gxy',
    placeholder: '请输入主诉'
  },
  {
    label: '现病史：',
    type: 'text',
    prop: 'gxyXbs',
    required: false,
    belong: 'tnb,gxy',
    placeholder: '请输入现病史'
  },
  {
    label: '既往史：',
    type: 'checkbox',
    required: true,
    mutuallyExclusive: true, // 互斥
    prop: 'gxyJws',
    belong: 'tnb,gxy',
    options: [
      { label: '无', value: '1' },
      { label: '高血压', value: '2' },
      { label: '糖尿病', value: '3' },
      { label: '冠心病', value: '4' },
      { label: '慢阻肺', value: '5' },
      { label: '恶性肿瘤', value: '6' },
      { label: '脑卒中', value: '7' },
      { label: '严重精神障碍', value: '8' },
      { label: '结核病', value: '9' },
      { label: '肝炎', value: '10' },
      { label: '其他法定传染病', value: '11' },
      { label: '职业病', value: '12' },
      { label: '其他', value: '13' }
    ]
  },
  {
    label: '高血压分级：',
    type: 'select',
    prop: 'gxyLevel',
    placeholder: '请选择',
    belong: 'tnb,gxy',
    required: false,
    options: [
      { label: '1级', value: '1' },
      { label: '2级', value: '2' },
      { label: '3级', value: '3' }
    ]
  },
  {
    label: '既往史备注：',
    type: 'text',
    prop: 'gxyJwsRemark',
    required: false,
    belong: 'tnb,gxy',
    placeholder: '请输入既往史备注'
  },
  {
    label: '糖尿病家族史：',
    type: 'checkbox',
    required: true,
    mutuallyExclusive: true, // 互斥
    prop: 'tnbFamilyHistory',
    belong: 'tnb,gxy',
    options: [
      { label: '无', value: '1' },
      { label: '一级亲属(父母、子女及兄弟姐妹)', value: '2' },
      { label: '二级亲属(叔、伯、姑、姨、祖父母等)', value: '3' },
      { label: '三级亲属(表兄妹、堂兄妹)', value: '4' }
    ]
  },
  {
    label: '用药情况：',
    type: 'MedicationTable',
    prop: 'medicationList',
    belong: 'all'
  },
  // {
  //   label: '不良事件：',
  //   type: 'checkbox',
  //   prop: 'badEvent',
  //   belong: 'all',
  //   required: false,
  //   remark: true, // 是否需要备注
  //   options: [
  //     { label: '出血事件', value: '1', badEventRemark1: '', prop: 'badEventBloodLevel', type: 'select' },
  //     { label: '心衰', value: '2', badEventRemark2: '' },
  //     { label: 'TIA', value: '3', badEventRemark2: '' },
  //     { label: '痴呆', value: '4', badEventRemark3: '' },
  //     { label: '卒中', value: '5', badEventRemark4: '' },
  //     { label: '冠心病', value: '6', badEventRemark5: '' },
  //     { label: '失明', value: '7', badEventRemark6: '' },
  //     { label: '肾功能不全', value: '8', badEventRemark7: '' },
  //     { label: '周围血管闭塞', value: '9', badEventRemark8: '' },
  //     { label: '主动脉夹层', value: '10', badEventRemark9: '' },
  //     { label: '呼吸衰竭', value: '11', badEventRemark10: '' },
  //     { label: '肺源性心脏病', value: '12', badEventRemark11: '' },
  //     { label: '肺性脑病', value: '13', badEventRemark12: '' }
  //   ],
  //   extraOptions: [
  //     { label: '少量出血', value: 1 },
  //     { label: '中等出血', value: 2 },
  //     { label: '大出血或致命性出血', value: 3 }
  //   ]
  // },
  {
    label: '高血压家族史：',
    type: 'checkbox',
    prop: 'gxyFamilyHistory',
    belong: 'tnb,gxy',
    required: true,
    mutuallyExclusive: true, // 互斥
    options: [
      { label: '无', value: '1' },
      { label: '一级亲属(父母、子女及兄弟姐妹)', value: '2' },
      { label: '二级亲属(叔、伯、姑、姨、祖父母等)', value: '3' },
      { label: '三级亲属(表兄妹、堂兄妹)', value: '4' }
    ]
  },
  {
    label: '过敏史：',
    type: 'checkbox',
    required: false,
    mutuallyExclusive: true, // 互斥
    prop: 'allergyHistory',
    belong: 'tnb,gxy',
    options: [
      { label: '无', value: '1' },
      { label: '抗生素', value: '2' },
      { label: '解热镇痛药', value: '3' },
      { label: '镇定安眠药', value: '4' },
      { label: '麻醉用药', value: '5' },
      { label: '血清制剂', value: '6' },
      { label: '其他', value: '7' }
    ]
  },
  {
    label: '已随访次数：',
    type: 'text',
    prop: 'followUpTimes',
    placeholder: '请输入次数',
    belong: 'tnb,gxy',
    required: true
  },

  {
    label: '慢阻肺家族史：',
    type: 'checkbox',
    prop: 'copdFamilyHistory',
    belong: 'copd',
    required: true,
    mutuallyExclusive: true, // 互斥
    options: [
      { label: '无', value: '1' },
      { label: '一级亲属(父母、子女及兄弟姐妹)', value: '2' },
      { label: '二级亲属(叔、伯、姑、姨、祖父母等)', value: '3' },
      { label: '三级亲属(表兄妹、堂兄妹)', value: '4' }
    ]
  },

  {
    label: '气短频率：',
    type: 'radio',
    prop: 'pantFlag',
    belong: 'copd',
    required: true,
    options: [
      { label: '从未感觉气短', value: 1 },
      { label: '很少感觉气短', value: 2 },
      { label: '有时感觉气短', value: 3 },
      { label: '经常感觉气短', value: 4 },
      { label: '总是感觉气短', value: 5 }
    ]
  },

  {
    label: '是否咳痰：',
    type: 'radio',
    prop: 'coughFlag',
    belong: 'copd',
    required: true,
    options: [
      { label: '从未咳出', value: 1 },
      { label: '是的，但仅在偶尔感冒或胸部感染时咳出', value: 2 },
      { label: '是的，每月都咳几天', value: 3 },
      { label: '是的，大多数日子都咳', value: 4 },
      { label: '是的，每天都咳', value: 5 }
    ]
  },

  {
    label: '呼吸困难,活动减少：',
    type: 'radio',
    prop: 'breatheFlag',
    belong: 'copd',
    required: true,
    options: [
      { label: '强烈反对', value: 1 },
      { label: '反对', value: 2 },
      { label: '不确定', value: 3 },
      { label: '同意', value: 4 },
      { label: '非常同意', value: 5 }
    ]
  },

  {
    label: '吸烟超百支：',
    type: 'radio',
    prop: 'hundredSmokeFlag',
    belong: 'copd',
    required: true,
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 },
      { label: '不知道', value: 3 }
    ]
  },

  {
    label: '今年多少岁：',
    type: 'radio',
    prop: 'ageFlag',
    belong: 'copd',
    required: true,
    options: [
      { label: '35-49岁', value: 1 },
      { label: '50-59岁', value: 2 },
      { label: '60-69岁', value: 3 },
      { label: '≥70岁', value: 4 }
    ]
  },

  {
    label: '主诉：',
    type: 'text',
    prop: 'copdComplained',
    required: false,
    belong: 'copd',
    placeholder: '请输入主诉'
  },

  {
    label: '现病史：',
    type: 'text',
    prop: 'copdXbs',
    required: false,
    belong: 'copd',
    placeholder: '请输入现病史'
  },

  {
    label: '既往史：',
    type: 'checkbox',
    required: true,
    mutuallyExclusive: true, // 互斥
    prop: 'copdJws',
    belong: 'copd',
    options: [
      { label: '无', value: '1' },
      { label: '鼻炎', value: '2' },
      { label: '哮喘', value: '3' },
      { label: '慢性支气管炎', value: '4' },
      { label: '肺气肿', value: '5' },
      { label: '慢性阻塞性肺疾病', value: '6' },
      { label: '慢性肺源性心脏病', value: '7' },
      { label: '肺癌', value: '8' },
      { label: '其它恶性肿瘤', value: '9' },
      { label: '支气管扩张症', value: '10' },
      { label: '肺结核', value: '11' },
      { label: '冠心病', value: '12' },
      { label: '脑卒中', value: '13' },
      { label: '高血压', value: '14' },
      { label: '糖尿病', value: '15' },
      { label: '其他肺部疾病', value: '16' }
    ]
  },

  {
    label: '既往史备注：',
    type: 'text',
    prop: 'copdJwsRemark',
    required: false,
    belong: 'copd',
    placeholder: '请输入既往史备注'
  },

  {
    label: '是否进行确诊：',
    type: 'radio',
    prop: 'diagnoseFlag',
    required: false,
    belong: 'copd',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '诊断结果：',
    type: 'checkbox',
    prop: 'diagnoseResult',
    required: false,
    belong: 'copd',
    options: [
      { label: '慢阻肺', value: '1' },
      { label: '肺气肿', value: '2' },
      { label: '慢性支气管炎', value: '3' },
      { label: '哮喘', value: '4' },
      { label: '其他', value: '5' }
    ]
  },
  {
    label: '诊断日期：',
    type: 'date',
    prop: 'diagnoseDate',
    required: false,
    belong: 'copd'
  },
  {
    label: '诊断医院：',
    type: 'input',
    prop: 'diagnoseHospital',
    required: false,
    belong: 'copd'
  },
  {
    label: '诊断医院级别：',
    type: 'radio',
    prop: 'diagnoseHospitalLevel',
    required: false,
    belong: 'copd',
    options: [
      { label: '三级医院', value: 1 },
      { label: '二级医院', value: 2 },
      { label: '一级医院', value: 3 }
    ]
  },
  {
    label: '判断依据：',
    type: 'upload',
    prop: 'attachmentUrl',
    required: false,
    belong: 'copd'
  },
  {
    label: '是否有房颤家族史：',
    type: 'checkbox',
    required: true,
    mutuallyExclusive: true, // 互斥
    prop: 'fcFamilyHistory',
    belong: 'fc',
    options: [
      { label: '无', value: '1' },
      { label: '一级亲属(父母、子女及兄弟姐妹)', value: '2' },
      { label: '二级亲属(叔、伯、姑、姨、祖父母等)', value: '3' },
      { label: '三级亲属(表兄妹、堂兄妹)', value: '4' }
    ]
  },
  {
    label: '是否身体锻炼：',
    type: 'radio',
    prop: 'exerciseFlag',
    required: false,
    belong: 'fc',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '是否有高血压：',
    type: 'radio',
    prop: 'gxyFlag',
    required: true,
    belong: 'fc',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },
  {
    label: '是否有糖尿病：',
    type: 'radio',
    prop: 'tnbFlag',
    required: true,
    belong: 'fc',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否有心悸，气短，乏力，晕厥等：',
    type: 'radio',
    prop: 'palpitationFlag',
    required: true,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否心力衰竭或左室收缩功能障碍：',
    type: 'radio',
    prop: 'heartFailureFlag',
    required: false,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否有脑卒中、短暂性脑缺血发作、血栓栓塞史：',
    type: 'radio',
    prop: 'strokeFlag',
    required: false,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否有血管疾病(外周动脉疾病、心肌梗死、主动脉斑块)：',
    type: 'radio',
    prop: 'bloodFlag',
    required: false,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否有超重和肥胖，BMI≥24kg/m2，或中心性肥胖(男性腰围≥90cm，女性腰围≥85cm)：',
    type: 'radio',
    prop: 'fatFlag',
    required: false,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否有慢性阻塞性肺病(COPD)：',
    type: 'radio',
    prop: 'copdFlag',
    required: false,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '是否有睡眠呼吸暂停综合征患者：',
    type: 'radio',
    prop: 'sleepFlag',
    required: false,
    belong: 'fc',
    labelClass: 'long-label',
    options: [
      { label: '是', value: 1 },
      { label: '否', value: 2 }
    ]
  },

  {
    label: '主诉：',
    type: 'text',
    prop: 'fcComplained',
    required: false,
    belong: 'fc',
    placeholder: '请输入主诉'
  },
  {
    label: '现病史：',
    type: 'text',
    prop: 'fcXbs',
    required: false,
    belong: 'fc',
    placeholder: '请输入现病史'
  },
  {
    label: '既往史：',
    type: 'checkbox',
    required: true,
    mutuallyExclusive: true, // 互斥
    prop: 'fcJws',
    belong: 'fc',
    options: [
      { label: '无', value: '1' },
      { label: '高血压', value: '2' },
      { label: '糖尿病', value: '3' },
      { label: '血脂异常', value: '4' },
      { label: '心肌梗死', value: '5' },
      { label: '脑卒中', value: '6' },
      { label: '心功能衰竭', value: '7' },
      { label: '周围动脉疾病', value: '8' }
    ]
  },
  {
    label: '既往史备注：',
    type: 'text',
    prop: 'fcJwsRemark',
    required: false,
    belong: 'fc',
    placeholder: '请输入既往史备注'
  }
]

export { tnbGxyCopdFcQuestion }
