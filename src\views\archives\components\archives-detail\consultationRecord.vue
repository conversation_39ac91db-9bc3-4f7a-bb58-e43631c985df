<!-- 会诊记录 -->
<template>
  <div class="consultation-record">
    <base-table
      ref="baseTable"
      :table-data="tableData"
      :loading="loading"
      :stripe="true"
      row-key="id"
      :columns="columns"
      :show-pagination="showPagination"
    >
      <template #participant="{ row }">
        <span>{{
          JSON.parse(row.participantJson)
            .map(item => item.name)
            .join(',')
        }}</span>
      </template>

      <template #operation="{ row }">
        <el-button type="text" size="mini" @click="handleDetail(row)">查看回放</el-button>
      </template>
    </base-table>
    <consultation-record-modal ref="consultationRecordModal" />
  </div>
</template>

<script>
import { getConsultationRecordApi, getConsultationVideoApi } from '@/api/remoteConsultation'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ConsultationRecordModal from '@/components/remoteConsultation/component/consultationRecordModal.vue'

export default {
  name: 'ConsultationRecord',
  components: {
    BaseTable,
    ConsultationRecordModal
  },
  mixins: [tableMixin],
  data() {
    return {
      showPagination: false,
      queryParams: {
        patientId: this.$route.query.id
      },
      columns: [
        { label: '云端编号', prop: 'id' },
        { label: '会诊时间', prop: 'meetingStartTime' },
        { label: '申请会诊医生', prop: 'createUsername' },
        { label: '远程会诊专家', prop: 'participantJson', slot: 'participant' },
        { label: '操作', prop: 'operation', slot: 'operation' }
      ]
    }
  },
  methods: {
    async getTableList(params) {
      delete params.pageNo
      delete params.pageSize
      return await getConsultationRecordApi(params)
    },

    handleDetail(row) {
      this.$store.dispatch('drugManagement/getConsultationRecordDetail', {
        roomId: row.id
      })
      this.getConsultationVideoFn(row.id)
    },

    // 获取会诊视频
    async getConsultationVideoFn(roomId) {
      const res = await getConsultationVideoApi({ id: roomId })
      if (res.code === 200 && res.data.mediaInfoSet.length > 0) {
        this.$refs.consultationRecordModal.dialogVisible = true
        this.$nextTick(() => {
          this.$refs.consultationRecordModal.meetingResult = this.meetingResult
          this.$refs.consultationRecordModal.medicalList = this.medicalList
          this.$refs.consultationRecordModal.videoUrl = res.data.mediaInfoSet[0].basicInfo.mediaUrl
        })
      } else {
        this.$message.warning('暂无视频')
      }
    }
  }
}
</script>
