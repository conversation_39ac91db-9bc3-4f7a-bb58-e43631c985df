import request from '@/utils/request'

// 获取院区列表
export function getHospitalList(data) {
  return request({
    url: '/cspapi/backend/his/try/hospital/area/query',
    method: 'post',
    data
  })
}

// 获取科室列表
export function getDepartmentList(data) {
  return request({
    url: '/cspapi/backend/his/try/dept/query',
    method: 'post',
    data
  })
}

// 查询排班余号
export function getScheduleVacancy(data) {
  return request({
    url: '/cspapi/backend/his/try/scheduling/num/query',
    method: 'post',
    data
  })
}

// 查询医生排班信息
export function getDoctorSchedule(data) {
  return request({
    url: '/cspapi/backend/his/try/scheduling/query',
    method: 'post',
    data
  })
}

// 预约时间明细
export function getAppointmentTimeDetail(data) {
  return request({
    url: '/cspapi/backend/his/try/preTime/query',
    method: 'post',
    data
  })
}

// 同步就诊号
export function syncVisitNumber(data) {
  return request({
    url: '/cspapi/backend/his/try/patient/query',
    method: 'post',
    data
  })
}

// 保存挂号信息
export function saveRegistrationInfo(data) {
  return request({
    url: '/cspapi/backend/his/try/outpatient/lock',
    method: 'post',
    data
  })
}

// 查询门诊预约信息列表
export function getOutpatientAppointmentList(data) {
  return request({
    url: '/cspapi/backend/his/try/outpatient/query/page',
    method: 'post',
    data
  })
}

// 取消门诊预约-退号
export function returnNumber(data) {
  return request({
    url: '/cspapi/backend/his/try/outpatient/unlock',
    method: 'post',
    data
  })
}
