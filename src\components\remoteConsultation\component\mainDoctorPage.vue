<!-- 发起会议医生页面 -->
<template>
  <div class="doctorPage">
    <div v-if="!showVideo" class="online-doctor">
      <div class="online-doctor-left">
        <el-checkbox-group v-model="selectedDoctors">
          <el-checkbox
            v-for="doctor in displayDoctors"
            :key="doctor.id"
            :label="doctor.id"
            :disabled="doctor.status === 2 || doctor.status === 4"
          >
            <div class="doctor-info">
              <div class="doctor-info-name">
                <span>{{ doctor.name }}</span>
              </div>
              <el-badge v-if="doctor.status === 3" is-dot type="success" />
              <el-badge v-if="doctor.status === 2" is-dot type="danger" />
              <el-badge v-if="doctor.status === 4" is-dot type="info" />
            </div>
          </el-checkbox>
        </el-checkbox-group>
        <div v-if="showMoreButton" class="show-more" @click="handleShowMore">
          <span>展开</span>
          <i class="el-icon-arrow-down" />
        </div>
        <div v-if="showAll && onlineDoctors.length > limit" class="show-more" @click="handleCollapse">
          <span>收起</span>
          <i class="el-icon-arrow-up" />
        </div>
      </div>
      <div class="online-doctor-right">
        <el-button type="primary" :loading="loading" @click="handleCreateRoom">{{
          `连线（${selectedDoctors.length}）`
        }}</el-button>
      </div>
    </div>

    <!-- 会诊记录 -->
    <div v-if="!showVideo" class="consultation-record">
      <h2>会诊记录</h2>
      <el-table border :data="consultationRecord" style="width: 100%; margin-top: 16px" height="420">
        <el-table-column prop="id" label="云端编号" align="center" />
        <el-table-column prop="meetingStartTime" label="会诊时间" align="center" />
        <el-table-column prop="createUsername" label="医生" align="center" />
        <el-table-column prop="participant" label="会诊专家" align="center" />
        <el-table-column prop="operation" label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="handleView(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 视频组件 -->
    <remote-video
      v-if="showVideo"
      type="main"
      :room-id="roomId"
      :participating-doctors="participatingDoctors"
      @meeting-ended="handleMeetingEnded"
    />

    <el-button
      v-if="showVideo"
      type="primary"
      style="float: right; margin-top: 10px"
      @click="handleSave"
    >保存</el-button>
    <!-- 诊治意见 -->
    <div v-if="showVideo" class="diagnosis-opinion">
      <h3>诊治意见</h3>
      <el-input
        v-model="meetingResult"
        type="textarea"
        :rows="4"
        placeholder="请输入诊治意见"
        style="margin-top: 8px"
      />
    </div>

    <!-- 处方 -->
    <div v-if="showVideo" class="prescription">
      <h3>处方</h3>
      <prescription-table v-model="medicalList" style="width: 100%; margin-top: 8px" />
    </div>

    <consultation-record-modal ref="consultationRecordModal" />
  </div>
</template>

<script>
import {
  getOnlineDoctorsApi,
  createRoomApi,
  getConsultationRecordApi,
  saveConsultationRecordApi,
  getConsultationVideoApi
} from '@/api/remoteConsultation'
import { mapState } from 'vuex'
import RemoteVideo from './remoteVideo.vue'
import PrescriptionTable from '@/components/prescriptionTable/index.vue'
import ConsultationRecordModal from './consultationRecordModal.vue'

export default {
  components: {
    RemoteVideo,
    PrescriptionTable,
    ConsultationRecordModal
  },
  props: {
    moduleType: {
      type: String,
      default: ''
    },
    patientInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      onlineDoctors: [],
      selectedDoctors: [],
      showAll: false,
      limit: 10,
      timer: null,
      roomId: '',
      showVideo: false,
      participatingDoctors: [],
      consultationRecord: [], // 会诊记录
      meetingResult: '',
      loading: false,
      medicalList: []
    }
  },
  computed: {
    displayDoctors() {
      if (this.showAll || this.onlineDoctors.length <= this.limit) {
        return this.onlineDoctors
      } else {
        return this.onlineDoctors.slice(0, this.limit)
      }
    },
    showMoreButton() {
      return !this.showAll && this.onlineDoctors.length > this.limit
    },
    ...mapState('drugManagement', ['consultationRecordDetail'])
  },
  watch: {
    consultationRecordDetail: {
      handler(newVal) {
        this.meetingResult = newVal.remoteRoom ? newVal.remoteRoom.meetingResult : ''
        this.medicalList = newVal.medicalList ? newVal.medicalList : []
      },
      deep: true
    }
  },
  created() {
    this.getOnlineDoctorsFn()
    this.startTimer()
    this.getConsultationRecordFn()
    if (this.roomId) {
      this.$store.dispatch('drugManagement/getConsultationRecordDetail', {
        roomId: this.roomId
      })
    }
  },
  beforeDestroy() {
    this.clearTimer()
  },
  methods: {
    startTimer() {
      this.clearTimer()
      this.timer = setInterval(() => {
        if (!this.showVideo) {
          this.getOnlineDoctorsFn()
        }
      }, 10000)
    },

    clearTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },

    // 获取会诊记录列表
    async getConsultationRecordFn() {
      const res = await getConsultationRecordApi({
        patientId: this.patientInfo.patientId
      })
      if (res.code === 200) {
        this.consultationRecord = res.data.map((item) => {
          return {
            ...item,
            participant: JSON.parse(item.participantJson)
              .map((it) => it.name)
              .join(',')
          }
        })
      }
    },

    // 获取在线医生
    async getOnlineDoctorsFn() {
      const res = await getOnlineDoctorsApi({
        roleCode: 'remoteExpert'
      })
      if (res.code === 200) {
        this.onlineDoctors = res.data
      }
    },

    // 展开
    handleShowMore() {
      this.showAll = true
    },

    // 收起
    handleCollapse() {
      this.showAll = false
    },

    // 处理会议结束事件
    handleMeetingEnded() {
      // 重置状态
      this.showVideo = false
      this.roomId = ''
      this.participatingDoctors = []
      this.selectedDoctors = []
      this.getConsultationRecordFn()
    },

    // 连线
    async handleCreateRoom() {
      if (this.selectedDoctors.length === 0) {
        this.$message.warning('请选择医生')
        return
      }
      this.meetingResult = ''
      this.medicalList = []
      this.loading = true
      try {
        const doctorList = this.selectedDoctors.map((item) => {
          const doctor = this.onlineDoctors.find((it) => it.id === item)
          return doctor
        })

        const params = {
          platform: 'pc',
          module: this.moduleType,
          recordId: this.patientInfo.id,
          patientId: this.patientInfo.patientId,
          doctorList
        }

        const res = await createRoomApi(params)
        if (res.code === 200) {
          this.roomId = res.data.id
          this.participatingDoctors = doctorList
          // 显示视频组件
          this.showVideo = true
        }
      } catch (error) {
        console.error('创建房间失败', error)
        this.$message.error(`创建房间失败: ${error.message}`)
      } finally {
        this.loading = false
      }
    },

    // 保存
    async handleSave() {
      const params = {
        roomId: this.roomId,
        meetingResult: this.meetingResult,
        medicalList: this.medicalList
      }
      const res = await saveConsultationRecordApi(params)
      if (res.code === 200) {
        this.$message.success('保存成功')
      }
    },

    // 查看会诊记录
    handleView(row) {
      this.$store.dispatch('drugManagement/getConsultationRecordDetail', {
        roomId: row.id
      })
      this.getConsultationVideoFn(row.id)
    },

    // 获取会诊视频
    async getConsultationVideoFn(roomId) {
      const res = await getConsultationVideoApi({ id: roomId })
      if (res.code === 200 && res.data.mediaInfoSet.length > 0) {
        this.$refs.consultationRecordModal.dialogVisible = true
        this.$nextTick(() => {
          this.$refs.consultationRecordModal.meetingResult = this.meetingResult
          this.$refs.consultationRecordModal.medicalList = this.medicalList
          this.$refs.consultationRecordModal.videoUrl = res.data.mediaInfoSet[0].basicInfo.mediaUrl
        })
      } else {
        this.$message.warning('暂无视频')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.doctorPage {
  width: 100%;
  height: 100%;
  padding: 10px;

  .online-doctor {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #e5e5e5;
    padding: 10px;
    border-radius: 4px;
    .show-more {
      margin-left: 10px;
      cursor: pointer;
      color: #0a86c8;
      font-size: 14px;
    }
    .doctor-info {
      display: flex;
      align-items: center;
      margin: 8px 0;

      &-name {
        margin-right: 10px;
      }
    }
  }

  .consultation-record {
    margin-top: 16px;
  }

  .diagnosis-opinion {
    margin-top: 16px;
  }

  .prescription {
    margin-top: 16px;
  }
}
</style>
