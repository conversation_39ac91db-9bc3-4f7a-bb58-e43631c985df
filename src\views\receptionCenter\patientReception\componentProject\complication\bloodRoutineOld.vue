<!-- 血常规 -->

<template>
  <div class="bloodRoutine">
    <device-icon :socket-connect="deviceConnect" style="margin-bottom: 8px; display: flex; justify-content: flex-end" />
    <div class="content">
      <div class="form-container">
        <el-form ref="formRef" :model="form" label-width="200px">
          <el-row>
            <el-col v-for="item in bloodRoutine" :key="item.label" :span="24">
              <el-form-item :label="item.label">
                <template #label>
                  <el-tooltip v-if="item.label.length > 15" :content="item.label" placement="top">
                    <span>{{ item.label.slice(0, 15) + '...' }}</span>
                  </el-tooltip>
                  <span v-else>{{ item.label }}</span>
                </template>
                <custom-input-number v-model="form[item.prop]" :placeholder="item.placeholder" style="width: 85%">
                  <template #append>{{ item.append }}</template>
                </custom-input-number>
                <range-tooltip style="margin-left: 8px" :range="item.range" :description="item.description" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="ocr-recognition">
        <ocr-recognition v-if="activeTab === 'BLOOD_ROUTINE'" type="bt" />
      </div>
    </div>
  </div>
</template>

<script>
import { bloodRoutine } from '@/views/receptionCenter/patientReception/component/complicationsScreening.js'
import { mapGetters } from 'vuex'
import DeviceIcon from '@/components/deviceIcon/index.vue'
import OcrRecognition from '@/components/ocrRecognition/index.vue'
import RangeTooltip from '@/components/RangeTooltip/index.vue'

export default {
  name: 'BloodRoutine',
  components: {
    DeviceIcon,
    OcrRecognition,
    RangeTooltip
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    },
    activeTab: {
      type: String,
      default: ''
    }
  },
  data() {
    const form = {}
    bloodRoutine.forEach((item) => {
      form[item.prop] = ''
    })
    return {
      bloodRoutine,
      form
    }
  },
  computed: {
    ...mapGetters(['deviceConnect'])
  },
  methods: {
    initData(data) {
      Object.keys(this.form).forEach((key) => {
        this.form[key] = data[key]
      })
      this.form.id = data.id
    },
    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          ...this.form,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    }
  }
}
</script>

<style lang="scss" scoped>
.bloodRoutine {
  width: 100%;
  height: 100%;
  ::v-deep .el-input-group__append {
    padding: 0 !important;
    width: 60px;
    text-align: center;
  }
  .content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 16px;
    .form-container {
      width: 40%;
    }
    .ocr-recognition {
      flex: 1;
    }
  }
}
</style>
