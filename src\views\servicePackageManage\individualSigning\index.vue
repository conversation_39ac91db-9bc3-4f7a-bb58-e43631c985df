<!-- 个人签约 -->
<template>
  <div class="individual-signing">
    <el-card class="individual-signing-search">
      <el-form :model="queryParams" label-width="100px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="医疗机构：" prop="depart">
              <TreeSelect
                v-model="queryParams.departCode"
                :data="departTree"
                :props="{
                  children: 'children',
                  label: 'departName',
                  value: 'departCode'
                }"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item label="时间范围：" prop="timeRange">
              <el-date-picker
                v-model="queryParams.timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                format="yyyy-MM-dd"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="签约状态：" prop="signStatus">
              <el-select v-model="queryParams.signStatus" placeholder="请选择" style="width: 100%">
                <el-option label="待生效" value="1" />
                <el-option label="履约中" value="3" />
                <el-option label="即将到期" value="5" />
                <el-option label="已到期" value="7" />
                <el-option label="已解约" value="9" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="签约人：" prop="patientName">
              <el-input v-model="queryParams.keyword" placeholder="请输入签约人" />
            </el-form-item>
          </el-col>

          <el-col :span="4" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card class="individual-signing-table">
      <el-button type="primary" style="margin-bottom: 10px" @click="handleAdd">个人签约</el-button>
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 90px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #signStatus="{ row }">
          <el-tag v-if="row.signStatus === 1" type="success">待生效</el-tag>
          <el-tag v-if="row.signStatus === 3" type="warning">履约中</el-tag>
          <el-tag v-if="row.signStatus === 5" type="info">即将到期</el-tag>
          <el-tag v-if="row.signStatus === 7" type="danger">已到期</el-tag>
          <el-tag v-if="row.signStatus === 9" type="info">已解约</el-tag>
        </template>

        <template #operate="{ row }">
          <el-button type="text" @click="handleDetail(row)">查看</el-button>
          <el-button v-if="row.signStatus === 1" type="text" @click="handleCancel(row)">解约</el-button>
          <el-button type="text" @click="handleProtocol(row)">协议</el-button>
          <el-button
            v-if="row.signStatus === 3 || row.signStatus === 5"
            type="text"
            @click="handleRenewal(row)"
          >履约</el-button>
        </template>
      </base-table>
    </el-card>

    <!-- 个人签约 -->
    <ProDialog :visible.sync="addDialogVisible" title="个人签约" width="800px" top="10vh">
      <div>
        <el-form ref="formRef" :model="form" label-width="100px" :rules="rules">
          <el-form-item label="身份证号：" prop="idCard">
            <el-input v-model="form.idCard" placeholder="请输入身份证号" />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button type="primary" @click="handleNext">下一步</el-button>
      </template>
    </ProDialog>

    <ProDialog :visible.sync="addFormVisible" title="个人签约" width="1200px" top="10vh">
      <AddForm ref="addForm" :depart-tree="departTree" @saveSuccess="handleSaveSuccess" />
      <template #footer>
        <el-button @click="addFormVisible = false">关闭</el-button>
        <el-button v-if="!disabledSubmit" type="primary" @click="handlePrev">上一步</el-button>
        <el-button v-if="!disabledSubmit" type="primary" @click="handleConfirm">确认</el-button>
      </template>
    </ProDialog>

    <!-- 解约 -->
    <ProDialog :visible.sync="cancelDialogVisible" title="解约" width="600px" top="10vh">
      <el-form ref="cancelFormRef" :model="cancelForm" label-width="100px" :rules="cancelRules">
        <el-form-item label="解约原因：" prop="cancelReason">
          <el-input v-model="cancelForm.cancelReason" type="textarea" rows="3" placeholder="请输入解约原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="cancelDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleCancelConfirm">确认</el-button>
      </template>
    </ProDialog>

    <ContractSigningProtocol ref="contractSigningProtocol" />
    <Performance ref="performance" />
  </div>
</template>

<script>
import { getOrgTreeByIdApi } from '@/api/system'
import {
  getIndividualSigningList,
  cancelIndividualSigning,
  getIndividualSigningTemplateDetail
} from '@/api/individualSigning'
import { getUserId } from '@/utils/auth'
import { localCache } from '@/utils/cache'
import { idCardValidator } from '@/utils/cspUtils'
import BaseTable from '@/components/BaseTable/index.vue'
import TreeSelect from '@/components/TreeSelect/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ProDialog from '@/components/ProDialog/index.vue'
import AddForm from './component/addForm.vue'
import ContractSigningProtocol from './component/contractSigningProtocol.vue'
import Performance from './component/performance.vue'

export default {
  name: 'FamilyMedicalTeam',
  components: {
    BaseTable,
    TreeSelect,
    ProDialog,
    AddForm,
    ContractSigningProtocol,
    Performance
  },
  mixins: [tableMixin],
  data() {
    return {
      addFormVisible: false,
      cancelDialogVisible: false,
      form: {
        idCard: ''
      },
      rules: {
        idCard: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { validator: idCardValidator, trigger: 'blur' }
        ]
      },
      cancelForm: {
        id: '',
        cancelReason: ''
      },
      cancelRules: {
        cancelReason: [{ required: true, message: '请输入解约原因', trigger: 'blur' }]
      },

      queryParams: {
        departCode: localCache.getCache('userInfo').departCode || '',
        timeRange: [],
        signStatus: '',
        keyword: ''
      },
      departTree: [],
      disabledSubmit: false,
      columns: [
        { prop: 'departName', label: '签约机构', width: 150, showOverflowTooltip: true },
        { prop: 'patientName', label: '签约人' },
        { prop: 'signStatus', label: '签约状态', slot: 'signStatus' },
        { prop: 'spNameStr', label: '服务包名称', width: 200, showOverflowTooltip: true },
        { prop: 'cycleStartDate', label: '签约开始日期' },
        { prop: 'cycleEndDate', label: '签约结束日期' },
        { prop: 'doctorName', label: '家庭医生名称' },
        { prop: 'cancelReason', label: '解约原因', width: 200, showOverflowTooltip: true },
        { prop: 'operate', label: '操作', width: 150, slot: 'operate' }
      ]
    }
  },
  created() {
    this.getDepartTree()
  },
  methods: {
    async getTableList(params) {
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }

      return await getIndividualSigningList(queryParams)
    },
    // 机构树
    async getDepartTree() {
      const res = await getOrgTreeByIdApi({
        patientId: getUserId()
      })
      this.departTree = res.data
    },

    handleAdd() {
      this.addDialogVisible = true
      this.disabledSubmit = false
      this.form.idCard = ''
    },

    // 上一步
    handlePrev() {
      this.addFormVisible = false
      this.addDialogVisible = true
    },

    // 下一步
    handleNext() {
      this.$refs.formRef.validate(async(valid) => {
        if (valid) {
          this.addFormVisible = true
          this.addDialogVisible = false
          this.$nextTick(() => {
            this.$refs.addForm.getPatientInfoByIdCard(this.form.idCard)
          })
        }
      })
    },

    // 查看
    handleDetail(row) {
      this.disabledSubmit = true
      this.addFormVisible = true
      this.$nextTick(() => {
        this.$refs.addForm.getIndividualSigningDetail(row.id)
      })
    },

    // 解约
    handleCancel(row) {
      this.cancelForm.cancelReason = ''
      this.cancelDialogVisible = true
      this.cancelForm.id = row.id
    },

    // 解约确认
    async handleCancelConfirm() {
      const valid = await this.$refs.cancelFormRef.validate()
      if (!valid) return
      const res = await cancelIndividualSigning(this.cancelForm)
      if (res.code === 200) {
        this.$message.success('解约成功')
        this.cancelDialogVisible = false
        this.handleSearch()
      }
    },

    // 协议
    handleProtocol(row) {
      getIndividualSigningTemplateDetail({ id: row.id }).then((res) => {
        if (res.code === 200) {
          const content = res.data.moduleContent
          this.$nextTick(() => {
            this.$refs.contractSigningProtocol.previewTemplate(content)
          })
        }
      })
    },

    // 履约
    handleRenewal(row) {
      this.$refs.performance.getIndividualSigningDetail(row.id)
    },

    // 签约确认
    handleConfirm() {
      this.$refs.addForm.handleSave()
    },

    // 保存成功后刷新列表
    handleSaveSuccess() {
      this.addFormVisible = false
      this.addDialogVisible = false
      this.handleSearch()
    },

    handleReset() {
      this.queryParams = {
        departCode: localCache.getCache('userInfo').departCode || '',
        timeRange: [],
        signStatus: '',
        pageNo: 1,
        pageSize: 20
      }
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.individual-signing {
  display: flex;
  flex-direction: column;
  height: 100%;
  .individual-signing-search {
    margin: 16px;
    height: 77px;
  }
  .individual-signing-table {
    margin: 0 16px;
    flex: 1;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
}
</style>
