<template>
  <div class="ultrasonic-quality-control-detail">
    <el-card>
      <patient-info :patient-info="detail" />
    </el-card>

    <el-card>
      <el-form ref="formRef" :model="form" :rules="rules">
        <el-form-item :label="`${detail.patientName}的“${detail.itemName}”质控结果是：`" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="5">通过</el-radio>
            <el-radio :label="9">未通过</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="质控结论：" prop="auditResult">
          <el-input v-model="form.auditResult" type="textarea" :rows="3" style="width: 80%" />
        </el-form-item>
      </el-form>

      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="图片影像" name="image">
          <PictureImage ref="pictureImage" :detail="detail" />
        </el-tab-pane>
        <el-tab-pane label="数据信息" name="data">
          <DataInfo ref="dataInfo" :detail="detail" />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 影像质控 -->
    <image-quality-control ref="imageQualityControl" :detail="detail" />

    <div
      v-if="detail.status === 1"
      style="
        position: fixed;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        z-index: 888;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        background-color: #fff;
        padding: 10px;
        border-radius: 4px;
        box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
      "
    >
      <el-button @click="handleCancel"> 取消 </el-button>
      <el-button v-if="detail.data && detail.data.attachmentDicomUrl" type="primary" @click="handleImageAudit">
        影像质控
      </el-button>
      <el-button type="success" style="background-color: #0bae01; border-color: #0bae01" @click="handleSave">
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import { getQualityControlDetail, auditQualityControl } from '@/api/qualityControl'
import PictureImage from './component/pictureImage.vue'
import DataInfo from './component/dataInfo.vue'
import PatientInfo from '@/components/patientInfo/index.vue'
import ImageQualityControl from '@/components/imageQualityControl/index.vue'

export default {
  name: 'UltrasonicQualityControlDetail',
  components: {
    PictureImage,
    DataInfo,
    PatientInfo,
    ImageQualityControl
  },
  data() {
    return {
      form: {
        status: '',
        auditResult: ''
      },
      id: this.$route.query.id,
      detail: {},
      itemCode: this.$route.query.itemCode,
      rules: {
        status: [{ required: true, message: '请选择质控结果', trigger: 'change' }],
        auditResult: [{ required: true, message: '请输入质控结论', trigger: 'blur' }]
      },
      activeName: 'image'
    }
  },
  created() {
    this.getQualityControlDetailFn()
  },
  methods: {
    getQualityControlDetailFn() {
      getQualityControlDetail({ id: this.id, itemCode: this.itemCode }).then((res) => {
        this.detail = res.data
        this.$nextTick(() => {
          this.$refs.pictureImage.initData(this.detail.data)
          this.$refs.dataInfo && this.$refs.dataInfo.initData(this.detail.data)
          this.form = {
            status: this.detail.status !== 5 && this.detail.status !== 9 ? null : this.detail.status,
            auditResult: this.detail.auditResult
          }
          this.$nextTick(() => {
            this.$refs.formRef.clearValidate()
          })
        })
      })
    },

    handleSave() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.handleSaveFn()
        }
      })
    },
    handleSaveFn() {
      auditQualityControl({
        id: this.id,
        itemCode: this.itemCode,
        status: this.form.status,
        auditResult: this.form.auditResult
      }).then((res) => {
        this.$message.success('审核成功')
        this.$router.back()
      })
    },

    handleCancel() {
      this.$router.back()
    },

    async handleImageAudit() {
      const params = {
        recordId: this.detail.data.recordId,
        module: 'cs',
        item: this.detail.itemCode,
        patientId: this.detail.data.patientId
      }
      const res = await this.$refs.imageQualityControl.getImageFileListFn(params)
      if (res && res.code === 200) {
        this.$refs.imageQualityControl.visible = true
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.ultrasonic-quality-control-detail {
  .el-card {
    margin: 16px;
  }
}
</style>
