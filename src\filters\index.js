/* eslint-disable no-bitwise */
// import parseTime, formatTime and set to filter
import { parseTime, isValue } from '@/utils'

/**
 * Show plural label if time is plural number
 * @param {number} time
 * @param {string} label
 * @return {string}
 */
function pluralize(time, label) {
  if (time === 1) {
    return time + label
  }
  return `${time + label}s`
}

/**
 * @param {number} time
 */
export function timeAgo(time) {
  const between = Date.now() / 1000 - Number(time)
  if (between < 3600) {
    return pluralize(~~(between / 60), ' minute')
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), ' hour')
  } else {
    return pluralize(~~(between / 86400), ' day')
  }
}

/**
 * Number formatting
 * like 10000 => 10k
 * @param {number} num
 * @param {number} digits
 */
export function numberFormatter(num, digits) {
  const si = [
    { value: 1e18, symbol: 'E' },
    { value: 1e15, symbol: 'P' },
    { value: 1e12, symbol: 'T' },
    { value: 1e9, symbol: 'G' },
    { value: 1e6, symbol: 'M' },
    { value: 1e3, symbol: 'k' }
  ]
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (num / si[i].value).toFixed(digits).replace(/\.0+$|(\.[0-9]*[1-9])0+$/, '$1') + si[i].symbol
    }
  }
  return num.toString()
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/g, (m) => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}

/**
 * Upper case first char
 * @param {String} string
 */
export function uppercaseFirst(string) {
  return string.charAt(0).toUpperCase() + string.slice(1)
}

/**
 * 加密手机号
 */
export function encryptPhone(phoneNumber) {
  let result = ''
  if (phoneNumber) {
    result = `${phoneNumber.slice(0, 3)}****${phoneNumber.slice(7, phoneNumber.length)}`
  }
  return result
}

/**
 * 加密姓名
 */
export function encryptName(name) {
  let result = ''
  if (name) {
    result = name[0]
    for (let nameIndex = 0; nameIndex < name.length; nameIndex++) {
      if (nameIndex !== 0) {
        result += '*'
      }
    }
  }
  return result
}

/**
 * 格式化时间 - {y}/{m}/{d}
 */
export function date2YMD(date) {
  let result = date
  if (isValue(date)) {
    try {
      const currDate = new Date(date)
      result = parseTime(currDate, '{y}/{m}/{d}')
      if (result === 'NaN/NaN/NaN') {
        result = date
      }
    } catch (e) {
      // TODO
    }
  }
  return result
}

/**
 * 格式化时间 - {y}/{m}/{d} {h}:{i}
 */
export function date2YMDsHI(date, format) {
  let result = date
  if (isValue(date)) {
    try {
      const currDate = new Date(date)
      result = parseTime(currDate, format || '{y}/{m}/{d} {h}:{i}')
      if (result === 'NaN/NaN/NaN NaN:NaN') {
        result = date
      }
    } catch (e) {
      // TODO
    }
  }
  return result
}

/**
 * 格式化时间 - {h}:{i}
 */
export function date2HI(date) {
  let result = date
  if (isValue(date)) {
    try {
      const currDate = new Date(date)
      result = parseTime(currDate, '{h}:{i}')
      if (result === 'NaN/NaN/NaN') {
        result = date
      }
    } catch (e) {
      // TODO
    }
  }
  return result
}

/**
 * 为空转化 ‘-/-’
 */
export function transEmptyToHGH(v) {
  let result = v
  if (!isValue(result)) {
    result = '- / -'
  }
  return result
}

/**
 * 为空转化 ‘-’
 */
export function transEmptyToH(v) {
  let result = v
  if (!isValue(result)) {
    result = '-'
  }
  return result
}

/**
 * 为空转化 ‘/’
 */
export function transEmptyToG(v) {
  let result = v
  if (!isValue(result)) {
    result = '/'
  }
  return result
}

/**
 * 为空转化 ‘/’
 */
export function transLast4(v) {
  let result = v
  console.log(`result`, result)
  if (!isValue(result)) {
    result = ''
  } else if (result.length > 4) {
    result = result.slice(-4)
  }
  console.log(`result`, result)
  return result
}
