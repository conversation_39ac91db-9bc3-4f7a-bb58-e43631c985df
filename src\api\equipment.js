/* eslint-disable max-len */
import axios from 'axios'
import request from '@/utils/request'
// 身份证读取 URL
export const READ_ID_CARD_URL = 'http://127.0.0.1:8808/ReadCard'
// 读取身份证
export const readIdCard = () => {
  return new Promise((resolve, reject) => {
    axios.get(READ_ID_CARD_URL).then(
      (response) => {
        resolve(response.data)
      },
      (error) => {
        reject(error)
      }
    )
  })
}
// socket方式读取身份证
export const READ_ID_CARD_WSURL_PT = 'ws:127.0.0.1:7415'

// 新增设备
export function addDeviceApi(data) {
  return request({
    url: '/cspapi/backend/wlw/device',
    method: 'post',
    data
  })
}
// 编辑设备
export function editDeviceApi(data) {
  return request({
    url: '/cspapi/backend/wlw/device',
    method: 'put',
    data
  })
}
// 删除设备
export function deleteDeviceApi(deviceId) {
  return request({
    url: `/cspapi/backend/wlw/device/${deviceId}`,
    method: 'delete'
  })
}
// 获取设备列表
export function getDeviceListApi(params, token) {
  return request({
    url: '/cspapi/backend/wlw/device/page',
    method: 'get',
    params,
    headers: {
      Authorization: token
    }
  })
}
// 某个村各种设备的数量
export function statisticEveryDeviceCountByDept(params, token) {
  return request({
    url: '/cspapi/backend/wlw/device/statisticEveryDeviceCountByDept',
    method: 'get',
    headers: {
      Authorization: token
    },
    params
  })
}
// 查看领用详情(通过领用ID查询)
export function leaseRecordByLeaseId(params) {
  return request({
    url: '/cspapi/backend/wlw/bandLeaseRecord/{leaseId}',
    method: 'get',
    params
  })
}
// 查询某个设备被谁领用(通过deviceSn查询)
export function leaseRecordByDeviceSn(params) {
  return request({
    url: '/cspapi/backend/wlw/bandLeaseRecord/getByDeviceSn',
    method: 'get',
    params
  })
}
// 获取领用历史列表
export function bandLeaseRecordPageApi(params) {
  return request({
    url: '/cspapi/backend/wlw/bandLeaseRecord/page',
    method: 'get',
    params
  })
}
// 领用设备
export function addLeaseRecord(data) {
  return request({
    url: '/cspapi/backend/wlw/bandLeaseRecord',
    method: 'post',
    data
  })
}
// 归还设备
export function editLeaseRecord(data) {
  return request({
    url: '/cspapi/backend/wlw/bandLeaseRecord',
    method: 'put',
    data
  })
}
// 地图模式 - 每个区的设备分布情况
export function listGroupByAreaId(params) {
  return request({
    url: '/cspapi/backend/wlw/device/listGroupByAreaId',
    method: 'get',
    params
  })
}
// 设备使用率 柱状图数据
export function deviceUserRateApi(params) {
  return request({
    url: '/cspapi/backend/wlw/device/chatByDeviceIdGroupByYear',
    method: 'get',
    params
  })
}

// 查询某个设备 经纬度
export function getDeviceBD(params) {
  return request({
    url: '/cspapi/backend/device/band/listByDeviceNo',
    method: 'get',
    params
  })
}
// 设备开机次数 折线图数据
export function deviceOpenTimesApi(params) {
  return request({
    url: '/cspapi/backend/wlw/device/chatByDeviceIdGroupByMonth',
    method: 'get',
    params
  })
}

// 设备使用情况 - 日
export function deviceOpenTimesDayApi(params) {
  // return request({
  //   url: '/cspapi/backend/wlw/device/chatByDeviceIdGroupByDay',
  //   method: 'get',
  //   params
  // })

  return new Promise((resolve) => {
    const dataListTmp = []
    for (let dayIndex = 0; dayIndex < 30; dayIndex++) {
      dataListTmp.push({
        day: dayIndex + 1,
        value: parseInt(Math.random() * 100)
      })
    }
    resolve({
      code: 200,
      msg: 'ok',
      data: dataListTmp,
      result: true
    })
  })
}
//  每个区的设备分布情况
export function getListGroupByAreaIdApi(data) {
  return request({
    url: '/cspapi/backend/wlw/device/listGroupByAreaId',
    method: 'get',
    params: data
  })
}
//  每个区的使用频率
export function getAreaFrequencyApi(data) {
  return request({
    url: '/cspapi/backend/wlw/device/staticUsageGroupByAreaCode',
    method: 'get',
    params: data
  })
}
//  每种设备的使用频率
export function getFacilityFrequencyApi(data) {
  return request({
    url: '/cspapi/backend/wlw/device/staticUsageGroupByDevice',
    method: 'get',
    params: data
  })
}
//  每个设备在不同地区的分布情况(设备区域分布)
export function getFacilityAreaApi(data) {
  return request({
    url: '/cspapi/backend/wlw/device/listGroupByDevice',
    method: 'get',
    params: data
  })
}

// 大屏 - 地区使用频次接口
export function areaUsageFrequencyDataApi(data) {
  // return request({
  //   url: '/cspapi/backend/wlw/device/listGroupByDevice',
  //   method: 'get',
  //   params: data
  // })
  return new Promise((resolve) => {
    resolve({
      code: '200',
      data: [
        { name: '海陵区', count: 250 },
        { name: '高港区', count: 200 },
        { name: '姜堰区', count: 20 },
        { name: '兴化市', count: 300 },
        { name: '靖江市', count: 100 },
        { name: '泰兴市', count: 210 }
      ]
    })
  })
}
// 大屏 - 设备使用情况接口
export function equipmentUsageSituationDataApi(data) {
  // return request({
  //   url: '/cspapi/backend/wlw/device/listGroupByDevice',
  //   method: 'get',
  //   params: data
  // })
  return new Promise((resolve) => {
    resolve({
      code: '200',
      data: [
        { name: '手环', count: 20 },
        { name: '手表', count: 100 },
        { name: '血压计', count: 20 },
        { name: '血糖仪', count: 160 },
        { name: '干式生化分析仪', count: 400 },
        { name: '血压表', count: 100 },
        { name: '体温枪', count: 130 },
        { name: '荧光免疫分析仪', count: 380 },
        { name: '听诊器', count: 160 },
        { name: '动脉硬化仪', count: 200 },
        { name: '心电图机', count: 500 }
      ]
    })
  })
}
// 大屏 - 设备统计 横向bar图
export function equipmentStatisticsDataApi(data) {
  // return request({
  //   url: '/cspapi/backend/wlw/device/listGroupByDevice',
  //   method: 'get',
  //   params: data
  // })
  return new Promise((resolve) => {
    resolve({
      code: 200,
      msg: 'ok',
      data: [
        {
          deviceId: null,
          deviceCode: 'sugar',
          deviceName: '血糖仪',
          deviceAreaList: [
            { count: 1, departCode: 553, departName: '兴化市', areaCode: null, areaName: null },
            { count: 1, departCode: 547, departName: '姜堰区', areaCode: null, areaName: null },
            { count: 1, departCode: 545, departName: '海陵区', areaCode: null, areaName: null },
            { count: 1, departCode: 556, departName: '靖江市', areaCode: null, areaName: null },
            { count: 1, departCode: 546, departName: '高港区', areaCode: null, areaName: null }
          ]
        },
        {
          deviceId: null,
          deviceCode: 'pressure',
          deviceName: '血压计',
          deviceAreaList: [
            { count: 2, departCode: 553, departName: '兴化市', areaCode: null, areaName: null },
            { count: 2, departCode: 547, departName: '姜堰区', areaCode: null, areaName: null },
            { count: 2, departCode: 545, departName: '海陵区', areaCode: null, areaName: null },
            { count: 2, departCode: 556, departName: '靖江市', areaCode: null, areaName: null },
            { count: 2, departCode: 546, departName: '高港区', areaCode: null, areaName: null }
          ]
        },
        {
          deviceId: null,
          deviceCode: 'watch_pressure',
          deviceName: '血压手表',
          deviceAreaList: [
            { count: null, departCode: 553, departName: '兴化市', areaCode: null, areaName: null },
            { count: null, departCode: 547, departName: '姜堰区', areaCode: null, areaName: null },
            { count: null, departCode: 545, departName: '海陵区', areaCode: null, areaName: null },
            { count: null, departCode: 556, departName: '靖江市', areaCode: null, areaName: null },
            { count: null, departCode: 546, departName: '高港区', areaCode: null, areaName: null }
          ]
        },
        {
          deviceId: null,
          deviceCode: 'sound',
          deviceName: '电子听诊器',
          deviceAreaList: [
            { count: 1, departCode: 553, departName: '兴化市', areaCode: null, areaName: null },
            { count: 1, departCode: 547, departName: '姜堰区', areaCode: null, areaName: null },
            { count: 1, departCode: 545, departName: '海陵区', areaCode: null, areaName: null },
            { count: 1, departCode: 556, departName: '靖江市', areaCode: null, areaName: null },
            { count: 1, departCode: 546, departName: '高港区', areaCode: null, areaName: null }
          ]
        },
        {
          deviceId: null,
          deviceCode: 'band',
          deviceName: '手环',
          deviceAreaList: [
            { count: null, departCode: 553, departName: '兴化市', areaCode: null, areaName: null },
            { count: null, departCode: 547, departName: '姜堰区', areaCode: null, areaName: null },
            { count: null, departCode: 545, departName: '海陵区', areaCode: null, areaName: null },
            { count: null, departCode: 556, departName: '靖江市', areaCode: null, areaName: null },
            { count: 5, departCode: 546, departName: '高港区', areaCode: null, areaName: null }
          ]
        },
        {
          deviceId: null,
          deviceCode: 'ecg',
          deviceName: '心电图机',
          deviceAreaList: [
            { count: null, departCode: 553, departName: '兴化市', areaCode: null, areaName: null },
            { count: null, departCode: 547, departName: '姜堰区', areaCode: null, areaName: null },
            { count: null, departCode: 545, departName: '海陵区', areaCode: null, areaName: null },
            { count: null, departCode: 556, departName: '靖江市', areaCode: null, areaName: null },
            { count: 1, departCode: 546, departName: '高港区', areaCode: null, areaName: null }
          ]
        },
        {
          deviceId: null,
          deviceCode: 'watch_ecg',
          deviceName: '心电手表',
          deviceAreaList: [
            { count: null, departCode: 553, departName: '兴化市', areaCode: null, areaName: null },
            { count: null, departCode: 547, departName: '姜堰区', areaCode: null, areaName: null },
            { count: null, departCode: 545, departName: '海陵区', areaCode: null, areaName: null },
            { count: null, departCode: 556, departName: '靖江市', areaCode: null, areaName: null },
            { count: 1, departCode: 546, departName: '高港区', areaCode: null, areaName: null }
          ]
        },
        {
          deviceId: null,
          deviceCode: 'temperature',
          deviceName: '体温枪',
          deviceAreaList: [
            { count: null, departCode: 553, departName: '兴化市', areaCode: null, areaName: null },
            { count: null, departCode: 547, departName: '姜堰区', areaCode: null, areaName: null },
            { count: null, departCode: 545, departName: '海陵区', areaCode: null, areaName: null },
            { count: null, departCode: 556, departName: '靖江市', areaCode: null, areaName: null },
            { count: 1, departCode: 546, departName: '高港区', areaCode: null, areaName: null }
          ]
        }
      ],
      result: true
    })
  })
}

// 大屏 - 设备数量统计
export function equipmentCountDataApi(data) {
  // return request({
  //   url: '/cspapi/backend/wlw/device/listGroupByDevice',
  //   method: 'get',
  //   params: data
  // })
  return new Promise((resolve) => {
    resolve({ code: '200', data: { total: 2300, using: 230, standby: 120, outOfDistance: 20, todayUp: 180 } })
  })
}

// 大屏 - 告警信息
export function alarmInformationDataApi(data) {
  // return request({
  //   url: '/cspapi/backend/wlw/device/listGroupByDevice',
  //   method: 'get',
  //   params: data
  // })
  return new Promise((resolve) => {
    const alarmList = []
    const alarmMsgList = [
      '血压计超出范围',
      '手环闲置',
      '手表闲置',
      '血糖仪超出距离',
      '听诊器超出距离',
      '体温枪超出距离'
    ]
    const total = 30
    for (let idx = 0; idx < total; idx++) {
      alarmList.push({
        departName: '阿如村',
        createTime: '2023-05-12',
        alarmMsg: alarmMsgList[idx % 6]
      })
    }
    resolve({ code: '200', data: { total: 30, list: alarmList } })
  })
}

// 大屏 - 设备状态地区分布
export function equipmentRegionalDistributionDataApi(data) {
  // return request({
  //   url: '/cspapi/backend/wlw/device/listGroupByDevice',
  //   method: 'get',
  //   params: data
  // })
  return new Promise((resolve) => {
    const statusList = [
      { departCode: 1, departName: '泰州市', total: 99999, using: 0, standby: 6, outOfDistance: 0, nouse: 6 },
      { departCode: 546, departName: '高港区', total: 220, using: 0, standby: 6, outOfDistance: 0, nouse: 6 },
      { departCode: 559, departName: '白马镇', total: 220, using: 0, standby: 6, outOfDistance: 0, nouse: 6 },
      { departCode: 572, departName: '白马社区', total: 220, using: 0, standby: 6, outOfDistance: 0, nouse: 6 }
    ]
    const total = 30
    for (let idx = 0; idx < total; idx++) {
      statusList.push({
        departCode: idx + 4,
        departName: '百合村',
        total: 220,
        using: 0,
        standby: 6,
        outOfDistance: 0,
        nouse: 6
      })
    }
    resolve({ code: '200', data: { total: 30, list: statusList } })
  })
}

// 大屏 - 村详细信息
export function getVillageInfoApi(data) {
  // return request({
  //   url: '/cspapi/backend/wlw/device/listGroupByDevice',
  //   method: 'get',
  //   params: data
  // })
  return new Promise((resolve) => {
    const villageInfo = {
      name: '白马社区',
      // 网关是否在线
      isGatewayOnline: '1',
      area: '白马镇白马社区',
      connectTime: '2023/02/01',
      chargePerson: '王负责',
      chargePhone: '13888881318'
    }
    resolve({ code: '200', data: villageInfo })
  })
}

// 30.18.2.1 网关与设备绑定或解绑
export function bindDeviceToGateway(data) {
  return request({
    url: '/cspapi/backend/wlw/device/bindDeviceToGateway',
    method: 'post',
    data
  })
}
//  30.36-1 设备的使用次数
export function getStatisticEveryDeviceCountByDeptApi(params) {
  return request({
    url: '/cspapi/backend/device/usage/statisticEveryDeviceCountByDept',
    method: 'get',
    params
  })
}
