/* eslint-disable no-shadow */
import {
  getStandardizedManageDetail,
  getStandardizedManageTargetDetail,
  getTherapeuticActionDetail
} from '@/api/standardizedManage'
import { cloneDeep } from 'lodash'

const state = {
  loading: false,
  saveBtnLoading: false,
  managePatientData: {}, // 规范管理详情
  managePatientTargetData: {}, // 目标管理详情
  therapeuticActionDetail: {} // 治疗动作详情
}

const mutations = {
  SET_LOADING(state, flag) {
    state.loading = flag
  },
  SET_SAVE_BTN_LOADING(state, flag) {
    state.saveBtnLoading = flag
  },
  SET_MANAGE_PATIENT_DATA(state, data) {
    state.managePatientData = data
  },
  SET_MANAGE_PATIENT_TARGET_DATA(state, data) {
    state.managePatientTargetData = data
  },
  SET_THERAPEUTIC_ACTION_DETAIL(state, data) {
    state.therapeuticActionDetail = data
  }
}

const actions = {
  // 规范管理详情
  async getStandardizedManageDetail({ commit }, params) {
    let res = null
    try {
      res = await getStandardizedManageDetail(params)
      // 慢病按照这个排序['糖尿病', '高血压', '房颤']
      const diseaseListTemp = cloneDeep(res.data.diseaseList)
      const diseaseList = [
        {
          name: '糖尿病',
          id: 'tnb'
        },
        {
          name: '高血压',
          id: 'gxy'
        },
        {
          name: '房颤',
          id: 'fc'
        }
      ]
      const diseaseListIndex = diseaseList.filter((item) => diseaseListTemp && diseaseListTemp.includes(item.name))
      res.data.diseaseList = diseaseListIndex
      commit('SET_MANAGE_PATIENT_DATA', res.data)
    } catch (error) {
      console.error(error)
    }
    return res ? res.data : null
  },
  // 目标管理详情
  async getStandardizedManageTargetDetailFn({ commit }, params) {
    commit('SET_LOADING', true)
    let res = null
    try {
      res = await getStandardizedManageTargetDetail(params)
      commit('SET_MANAGE_PATIENT_TARGET_DATA', res.data)
    } catch (error) {
      console.error(error)
    } finally {
      commit('SET_LOADING', false)
    }
    return res ? res.data : null
  },
  // 治疗动作详情
  async getTherapeuticActionDetailFn({ commit }, params) {
    commit('SET_LOADING', true)
    let res = null
    try {
      res = await getTherapeuticActionDetail(params)
      commit('SET_THERAPEUTIC_ACTION_DETAIL', res.data)
    } catch (error) {
      console.error(error)
    } finally {
      commit('SET_LOADING', false)
    }
    return res ? res.data : null
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
