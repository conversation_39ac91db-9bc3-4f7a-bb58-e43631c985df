<template>
  <div class="audit-status-container">
    <!-- 质控通过状态 -->
    <div v-if="status === 'pass'" class="audit-status-item audit-pass">
      <i class="el-icon-success" />
      <span>{{ passText || '质控已通过' }}</span>
    </div>

    <!-- 质控未通过状态 -->
    <div v-else-if="status === 'reject'" class="audit-status-item audit-reject">
      <i class="el-icon-error" />
      <span>{{ rejectText || '质控：未通过' }} {{ reason ? `未通过原因：${reason}` : '' }}</span>
    </div>

    <!-- 质控中状态 -->
    <div v-else-if="status === 'pending'" class="audit-status-item audit-pending">
      <i class="el-icon-warning" />
      <span>{{ pendingText || '已提交质控，请耐心等待。' }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AuditStatus',
  props: {
    /**
     * 质控状态，可选值：pass(通过)、reject(拒绝)、pending(质控中)
     */
    status: {
      type: String,
      required: true,
      validator: (value) => ['pass', 'reject', 'pending'].includes(value)
    },
    /**
     * 质控未通过原因（仅在status为reject时有效）
     */
    reason: {
      type: String,
      default: ''
    },
    /**
     * 自定义通过文本
     */
    passText: {
      type: String,
      default: ''
    },
    /**
     * 自定义拒绝文本
     */
    rejectText: {
      type: String,
      default: ''
    },
    /**
     * 自定义等待文本
     */
    pendingText: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.audit-status-container {
  width: 100%;

  .audit-status-item {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    border-radius: 4px;

    i {
      margin-right: 8px;
      font-size: 16px;
    }

    span {
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .audit-pass {
    background-color: #f0f9eb;
    border: 1px solid #e1f3d8;
    color: #67c23a;
  }

  .audit-reject {
    background-color: #fef0f0;
    border: 1px solid #fde2e2;
    color: #f56c6c;
  }

  .audit-pending {
    background-color: #fdf6ec;
    border: 1px solid #faecd8;
    color: #e6a23c;
  }
}
</style>
