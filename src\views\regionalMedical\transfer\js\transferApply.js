import { getUserId, getToken } from '@/utils/auth'
import { getPatientList } from '@/api/receptionWorkbench'
import { getOrgTreeByIdApi, getmanageDoctorApi, getuserInfoApi } from '@/api/system'
import { getDictionaryPage } from '@/api/dict'
import { addReferralRecord, getReferralRecordDetail } from '@/api/regionalMedical'

import { REGEX_MOBILE } from '@/utils/enum'
import { localCache } from '@/utils/cache'
import CustomUpload from '@/components/customUpload/index.vue'

export default {
  name: 'TransferApply',
  components: { CustomUpload },
  props: {
    type: {
      type: String,
      default: 'referral'
    }
  },

  data() {
    const validateTime = (rule, value, callback) => {
      const valueGetTime = new Date(value).getTime()
      const nowGetTime = Date.now()
      const nowhours = new Date().getHours()

      // 12点之前可以选择今天的日期，12点之后不可以选择今天的日期
      if (valueGetTime < nowGetTime && nowhours >= 12) {
        callback(new Error('已过12点，无法选择当日'))
      } else {
        callback()
      }
    }

    const validatePhone = (rule, value, callback) => {
      console.log(REGEX_MOBILE.test(value))
      if (!value) {
        callback()
      } else if (value.indexOf('*') === -1 && REGEX_MOBILE.test(value) === false) {
        callback(new Error('手机号码格式有误！'))
      } else {
        callback()
      }
    }

    return {
      token: getToken(),
      tenantId: localCache.getCache('tenantId') || '',
      disabledSubmit: !!this.$route.query.id,
      isShowRefuseDialog: false, // 拒绝弹出是否显示 true:是 false：否
      // 设置开始时间
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      refuseParams: {
        // 拒绝参数
        content: '' // 拒绝原因
      },
      refuseCommonPhrases: ['收入患者已达上限', '无法诊断或治疗'], // 拒绝常用语

      formData: {
        patientId: null, // 患者ID
        originalIdCard: '',
        originalPhone: '',
        sex: '',
        age: '',
        address: '',
        referralType: '',
        referralAdvice: '',
        inDepartCode: '',
        inOfficeCode: '',
        treatDoctorId: '',
        planDate: '',
        outReason: '',
        diagnosticResult: '',
        outDoctorId: '',
        outDoctorName: '',
        outDepartCode: '',
        outDepartName: '',
        mainCheckResult: '',
        nextTreatPlan: '',
        firstImpression: '',
        mainJws: '',
        treatProcess: '',
        attachmentPhotoUrl: ''
      },
      serviceStationTree: [], // 获取医疗体系树
      patientList: [], // 获取病人列表
      sexList: [
        {
          value: 1,
          txt: '男'
        },
        {
          value: 0,
          txt: '女'
        },
        {
          value: 9,
          txt: '未说明'
        },
        {
          value: 2,
          txt: '未知'
        }
      ], // 性别数据
      referralTypeList: [
        {
          value: 1,
          txt: '上转'
        },
        {
          value: 2,
          txt: '下转'
        }
      ], // 转诊申请类型
      referralAdviceList: [
        {
          value: 1,
          txt: '门诊'
        },
        {
          value: 2,
          txt: '住院'
        }
      ], // 转诊建议
      medicalDepartmentList: [], // 科室列表
      medicalDoctorList: [], // 医生列表
      outReasonList: [
        {
          value: 1,
          txt: '病情康复'
        },
        {
          value: 2,
          txt: '病情稳定'
        },
        {
          value: 3,
          txt: '患者意愿'
        }
      ], // 转出原因
      userInfo: {}, // 当前登录的用户信息
      rules: {
        patientId: { required: true, message: '请输入患者姓名或首字母搜索', trigger: ['blur', 'change'] },
        referralType: { required: true, message: '请选择申请类型', trigger: ['blur', 'change'] },
        treatSuggest: { required: true, message: '请选择转诊建议', trigger: ['blur', 'change'] },
        inDepartCode: [{ required: true, message: '请选择转入单位', trigger: ['blur', 'change'] }],
        planDate: [
          { required: true, message: '请选择转入时间', trigger: ['blur', 'change'] },
          { validator: validateTime, trigger: ['blur', 'change'] }
        ],

        firstImpression: {
          required: true,
          message: '请输入对患者病情作出的初步判断',
          trigger: 'blur'
        },
        mainHpi: {
          required: true,
          message: '请输入患者转诊时存在的主要临床问题',
          trigger: 'blur'
        },

        mainCheckResult: {
          required: true,
          message: '请输入主要检查结果',
          trigger: 'blur'
        },
        nextTreatPlan: {
          required: true,
          message: '请输入治疗经过、下一步治疗方案及康复建议',
          trigger: 'blur'
        },
        diagnosticResult: {
          required: true,
          message: '请输入诊断结果',
          trigger: 'blur'
        },
        patientPhoneReplace: [{ validator: validatePhone, trigger: 'blur' }]
      },
      imageUrl: '',
      treatDoctorLoading: false, // 接诊医生Loading
      remoteLoading: false // 远程搜索Loading
    }
  },
  computed: {
    width1280() {
      return document.documentElement.clientWidth <= 1280
    }
  },

  created() {
    this.initData() // 初始化数据
  },

  methods: {
    /**
     * @description: 初始化数据
     * @author: LiSuwan
     * @Date: 2024-09-27 15:38:10
     */
    initData() {
      if (!this.$route.query.id) {
        this.getuserInfo() // 获取用户信息
      } else {
        this.getReferralRecordDetailFn()
      }
      this.getDepartmentTree() // 获取医疗体系树
      // this.getDictionaryVal() // 获取科室数据
    },

    async getReferralRecordDetailFn() {
      const res = await getReferralRecordDetail({ id: this.$route.query.id })
      if (res.code === 200) {
        this.formData = res.data
        this.getmanageDoctorFun()
      }
    },

    /**
     * @description: 获取科室数据
     * @author: LiSuwan
     * @Date: 2024-08-28 11:05:35
     */
    async getDictionaryVal() {
      const res = await getDictionaryPage({ moduleCode: 'department', pageNo: 1, pageSize: 9999 })
      if (res.code === 200) {
        this.medicalDepartmentList = res.data.list // 科室数据
      }
    },

    /**
     * @description: 获取组织树
     * @author: LiSuwan
     * @Date: 2024-08-31 11:48:30
     */
    async getDepartmentTree() {
      const res = await getOrgTreeByIdApi({
        patientId: getUserId()
      })
      if (res.code === 200) {
        if (res.data) {
          this.serviceStationTree = res.data
        }
      }
    },
    /**
     * @description: 获取医生列表
     * @author: LiSuwan
     * @Date: 2024-10-10 14:29:28
     */
    async getmanageDoctorFun() {
      this.treatDoctorLoading = true
      const res = await getmanageDoctorApi({
        pageNo: 1,
        pageSize: 1000,
        departCode: this.formData.inDepartCode
      })

      this.treatDoctorLoading = false
      if (res.code === 200) {
        this.medicalDoctorList = res.data.list
      }
    },

    // 获取病人列表
    async getPatientListFn(val) {
      this.remoteLoading = true
      const reqData = {
        pageNo: 1,
        pageSize: 100,
        keyword: val === undefined ? '' : val
      }
      const res = await getPatientList(reqData)
      if (res.code === 200) {
        this.patientList = res.data.list
      }
      this.remoteLoading = false
    },

    /**
     * @description: 关闭拒绝弹窗
     * @author: LiSuwan
     * @Date: 2024-09-29 11:23:53
     */
    closeRefuseDialog() {
      this.isShowRefuseDialog = false
      this.refuseParams = Object.assign({}, this.$options.data().refuseParams)
    },
    /**
     * @description: 确认按钮
     * @author: LiSuwan
     * @Date: 2024-09-29 11:27:01
     */
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          console.log('确认操作')
          this.closeRefuseDialog()
        } else {
          return false
        }
      })
    },

    goBack() {
      this.$router.go(-1)
    },

    async getuserInfo() {
      const res = await getuserInfoApi(getUserId())
      if (res.code === 200) {
        const { data } = res
        this.formData = {
          ...this.formData,
          outDoctorId: data.id,
          outDoctorName: data.name,
          outDepartCode: data.departCode,
          outDepartName: data.departName,
          outDoctorPhone: data.originalPhone
        }
      }
    },

    changePatient() {
      if (this.formData.patientId) {
        const findObj = this.patientList.find((val) => val.id === this.formData.patientId)
        this.formData.originalIdCard = findObj.originalIdCard
        this.formData.originalPhone = findObj.originalPhone
        this.formData.sex = findObj.sex
        this.formData.age = findObj.age
        this.formData.address = findObj.address
      } else {
        this.formData.originalIdCard = ''
        this.formData.originalPhone = ''
        this.formData.sex = ''
        this.formData.age = ''
        this.formData.address = ''
      }
    },

    // changeInOffice() {
    //   if (this.formData.inOfficeCode) {
    //     const findObj = this.medicalDepartmentList.find((val) => val.id === this.formData.inOfficeCode)
    //     this.formData.inOfficeName = findObj.value
    //   } else {
    //     this.formData.inOfficeName = ''
    //   }
    // },

    changeTreatDoctor() {
      if (this.formData.treatDoctorId) {
        const findObj = this.medicalDoctorList.find((val) => val.id === this.formData.treatDoctorId)
        this.formData.treatDoctorName = findObj.name
      } else {
        this.formData.treatDoctorName = ''
      }
    },

    changeReferral() {
      this.formData = {
        ...this.formData,
        inDepartCode: '',
        treatDoctorId: '',
        treatDoctorName: ''
      }
    },

    async submitReferralRecord() {
      const valid = await this.$refs.ruleForm.validate()
      if (valid) {
        const res = await addReferralRecord(this.formData)
        if (res.code === 200) {
          this.$message.success('提交成功')
          this.goBack()
        }
      }
    }
  }
}
