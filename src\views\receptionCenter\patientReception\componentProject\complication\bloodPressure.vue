<!-- 24小时动态血压 -->
<template>
  <div class="blood-pressure">
    <el-form ref="hypertension24FormRef" :model="hypertension24Form" :rules="rules" label-width="140px">
      <el-row>
        <el-col :span="8">
          <el-form-item label="24小时平均血压">
            <div class="item">
              <custom-input-number v-model="hypertension24Form.allAvgSp">
                <template #append>/</template>
              </custom-input-number>
              <custom-input-number v-model="hypertension24Form.allAvgDp">
                <template #append>mmHg</template>
              </custom-input-number>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="日间平均血压">
            <div class="item">
              <custom-input-number v-model="hypertension24Form.dayAvgSp">
                <template #append>/</template>
              </custom-input-number>
              <custom-input-number v-model="hypertension24Form.dayAvgDp">
                <template #append>mmHg</template>
              </custom-input-number>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="夜间平均血压">
            <div class="item">
              <custom-input-number v-model="hypertension24Form.nightAvgSp">
                <template #append>/</template>
              </custom-input-number>
              <custom-input-number v-model="hypertension24Form.nightAvgDp">
                <template #append>mmHg</template>
              </custom-input-number>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="夜间血压下降率">
            <div class="item">
              <custom-input-number v-model="hypertension24Form.nightSpDeclineRate">
                <template #append>/</template>
              </custom-input-number>
              <custom-input-number v-model="hypertension24Form.nightDpDeclineRate">
                <template #append>%</template>
              </custom-input-number>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="晨峰血压">
            <div class="item">
              <custom-input-number v-model="hypertension24Form.morningSp">
                <template #append>/</template>
              </custom-input-number>
              <custom-input-number v-model="hypertension24Form.morningDp">
                <template #append>mmHg</template>
              </custom-input-number>
            </div>
          </el-form-item>
        </el-col>

        <el-col :span="8">
          <el-form-item label="动态血压系数">
            <custom-input-number v-model="hypertension24Form.dynamicCoefficient" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-col :span="24">
        <el-form-item label="检查结论">
          <el-input v-model="hypertension24Form.bpDescription" type="textarea" />
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="上传报告图片">
          <custom-upload v-model="hypertension24Form.attachmentPhotoUrl" />
        </el-form-item>
      </el-col>
    </el-form>
  </div>
</template>

<script>
import CustomUpload from '@/components/customUpload/index.vue'

export default {
  name: 'BloodPressure',
  components: {
    CustomUpload
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      hypertension24Form: {
        allAvgSp: '',
        allAvgDp: '',
        dayAvgSp: '',
        dayAvgDp: '',
        nightAvgSp: '',
        nightAvgDp: '',
        nightSpDeclineRate: '',
        nightDpDeclineRate: '',
        morningSp: '',
        morningDp: '',
        dynamicCoefficient: '',
        bpDescription: '',
        attachmentPhotoUrl: ''
      },
      rules: {
        allAvgSp: [{ required: true, message: '请输入24小时平均血压' }],
        allAvgDp: [{ required: true, message: '请输入24小时平均血压' }],
        dayAvgSp: [{ required: true, message: '请输入日间平均血压' }],
        dayAvgDp: [{ required: true, message: '请输入日间平均血压' }],
        nightAvgSp: [{ required: true, message: '请输入夜间平均血压' }],
        nightAvgDp: [{ required: true, message: '请输入夜间平均血压' }],
        nightSpDeclineRate: [{ required: true, message: '请输入夜间血压下降率' }],
        nightDpDeclineRate: [{ required: true, message: '请输入夜间血压下降率' }],
        morningSp: [{ required: true, message: '请输入晨峰血压' }],
        morningDp: [{ required: true, message: '请输入晨峰血压' }],
        dynamicCoefficient: [{ required: true, message: '请输入动态血压系数' }]
      }
    }
  },

  methods: {
    initData(data) {
      Object.keys(this.hypertension24Form).forEach((key) => {
        this.hypertension24Form[key] = data[key]
      })
      this.hypertension24Form.id = data.id
    },

    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: false,
        data: {
          ...this.hypertension24Form,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.hypertension24Form.id
        }
      }

      try {
        const valid = await this.$refs.hypertension24FormRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }

      return result
    }
  }
}
</script>

<style lang="scss" scoped>
.blood-pressure {
  padding: 16px;
  .item {
    display: flex;
    align-items: center;
    ::v-deep .el-input-group__append {
      width: 50px !important;
      text-align: center;
      padding: 0 10px;
    }
  }
}
</style>
