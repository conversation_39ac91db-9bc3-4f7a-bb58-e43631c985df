<template>
  <div>
    <div id="rich-text" />
  </div>
</template>

<script>
import E from 'wangeditor'
import Axios from 'axios'
import { getToken } from '@/utils/auth'
import envModule from '@/utils/env'

export default {
  name: 'RichText',
  data() {
    return {
      editor: null
    }
  },
  beforeDestroy() {
    this.destroyEditor()
  },
  methods: {
    // 初始化富文本编辑器
    initEditor() {
      // 如果已存在编辑器实例，先销毁
      if (this.editor != null) {
        this.destroyEditor()
      }

      const editor = new E('#rich-text')
      this.editor = editor

      editor.config.excludeMenus = ['code', 'todo']

      editor.config.onchange = html => {
        if (html.length > 12000) {
          const str = html.substring(0, 11900)
          editor.txt.html(str)
          this.$message('字数超出已自动截断！')
        }
      }

      editor.config.height = 500

      editor.config.customUploadImg = (resultFiles, insertImgFn) => {
        // resultFiles 是选择的文件列表
        const formData = new FormData()
        formData.append('file', resultFiles[0]) // 字段名需与后端一致（如 'file'）

        // 手动发起上传请求
        Axios.post('/cspapi/backend/cos/uploadFile/private2', formData, {
          headers: {
            Authorization: getToken()
          }
        })
          .then(res => {
            try {
              if (res.data && res.data.code === 200 && res.data.data[0]) {
                const env = envModule.getEnv()
                const domain = env === 'test' ? 'http://192.168.11.231' : window.location.origin
                const originalUrl = res.data.data[0].fullFileUrl.replace(/^https?:\/\/[^\/]+/, '')
                const imgHTML = `<img src="${domain}${originalUrl}" style="display:inline-block; height:50px; width:100px; vertical-align:middle;" />` // eslint-disable-line max-len
                editor.cmd.do('insertHTML', imgHTML)
                // insertImgFn(originalUrl)
                // const Imgs = document.querySelectorAll('#rich-text img')
                // if (Imgs.length > 0) {
                //   Imgs.forEach(img => {
                //     img.style.width = '100px'
                //     img.style.height = '50px'
                //     img.style.verticalAlign = 'middle'
                //   })
                // }
              } else {
                this.$message.error('上传失败')
              }
            } catch (error) {
              console.log(error)
            }
          })
          .catch(err => {
            console.log(err)
            this.$message.error('上传异常')
          })
      }

      editor.create()
    },

    // 销毁编辑器实例
    destroyEditor() {
      if (this.editor != null) {
        this.editor.destroy()
        this.editor = null
      }
    }
  }
}
</script>
