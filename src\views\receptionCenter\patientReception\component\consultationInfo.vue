<!-- 问诊信息 -->
<template>
  <div v-loading="$store.state.receptionWorkbench.loading" class="consultation-info">
    <questionnaire-tags :questionnaire-list="questionnaireList" @click="handleQuestionnaireNameClick" />
    <div class="questionnaire-content">
      <el-form
        v-show="questionnaireId === 'tnb' || questionnaireId === 'gxy'"
        ref="tnbAndgxyQuestionRef"
        :model="formData"
        :rules="rules"
        label-width="180px"
      >
        <component
          :is="getComponent(item.type)"
          v-for="item in tnbGxyQuestionnaireList"
          :key="item.prop"
          v-model="formData[`${item.prop}`]"
          :form-data="formData"
          :item="item"
          :rules="rules[`${item.prop}`]"
        />
      </el-form>

      <el-form
        v-show="questionnaireId === 'COPD'"
        ref="copdQuestionRef"
        :model="formData"
        :rules="rules"
        label-width="180px"
      >
        <component
          :is="getComponent(item.type)"
          v-for="item in copdQuestionnaireList"
          :key="item.prop"
          v-model="formData[`${item.prop}`]"
          :form-data="formData"
          :item="item"
          :rules="rules[`${item.prop}`]"
        />
      </el-form>

      <el-form
        v-show="questionnaireId === 'fangchan'"
        ref="fcQuestionRef"
        :model="formData"
        :rules="rules"
        label-width="180px"
      >
        <component
          :is="getComponent(item.type)"
          v-for="item in fcQuestionnaireList"
          :key="item.id"
          v-model="formData[`${item.prop}`]"
          :form-data="formData"
          :item="item"
          :rules="rules[`${item.prop}`]"
        />
      </el-form>
    </div>
  </div>
</template>

<script>
import { tnbGxyCopdFcQuestion } from './consultationInfo'
import { saveConsultationInfo, saveHighRiskScreening } from '@/api/receptionWorkbench'
import { cloneDeep, isEmpty, isNil, every } from 'lodash'
import QuestionnaireTags from '@/components/questionnaireTags/index.vue'
import TextField from '@/components/questionnaireElementUi/TextField.vue'
import RadioGroupField from '@/components/questionnaireElementUi/RadioGroupField.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'
import SelectField from '@/components/questionnaireElementUi/SelectField.vue'
import DateField from '@/components/questionnaireElementUi/DateField.vue'
import MedicationTable from '@/views/receptionCenter/patientReception/componentDetail/medicationTable.vue'
import UploadField from '@/components/questionnaireElementUi/uploadField.vue'

export default {
  components: {
    QuestionnaireTags,
    TextField,
    RadioGroupField,
    CheckboxGroupField,
    SelectField,
    DateField,
    MedicationTable,
    UploadField
  },
  props: {
    historyId: {
      type: String,
      default: ''
    }
  },
  data() {
    const questionnaireListTemp = cloneDeep(this.$store.state.receptionWorkbench.receptionWorkbenchData.diseaseList)
    let questionnaireList = []
    const idx1 = questionnaireListTemp.findIndex((item) => item.id === 'tnb')
    const idx2 = questionnaireListTemp.findIndex((item) => item.id === 'gxy')
    if (idx1 !== -1 && idx2 !== -1) {
      questionnaireListTemp[0].name = '糖尿病和高血压'
      questionnaireList = questionnaireListTemp.filter((item) => item.id !== 'gxy')
    } else {
      questionnaireList = questionnaireListTemp
    }

    const formData = {}
    const rules = {}
    tnbGxyCopdFcQuestion.forEach((q) => {
      formData[`${q.prop}`] = q.type === 'checkbox' || q.type === 'MedicationTable' ? [] : ''
      if (q.remark) {
        q.options.forEach((opt) => {
          formData[`${q.prop}Remark${opt.value}`] = ''
        })
      }
      if (q.required) {
        rules[`${q.prop}`] = [{ required: true, message: `${q.label}不能为空` }]
      }
    })

    return {
      formData,
      rules,
      tnbGxyCopdFcQuestion,
      questionnaireList,
      questionnaireId: questionnaireList[0].id
    }
  },
  computed: {
    tnbGxyQuestionnaireList() {
      return this.tnbGxyCopdFcQuestion.filter((q) => q.belong === 'tnb,gxy' || q.belong === 'all')
    },
    copdQuestionnaireList() {
      return this.tnbGxyCopdFcQuestion.filter((q) => q.belong === 'copd' || q.belong === 'all')
    },
    fcQuestionnaireList() {
      return this.tnbGxyCopdFcQuestion.filter((q) => q.belong === 'fc' || q.belong === 'all')
    }
  },
  async created() {
    // 获取药品列表
    await this.$store.dispatch('drugManagement/getDrugList')

    const data = await this.$store.dispatch('receptionWorkbench/getConsultationInfoData', {
      rrId: this.historyId || this.$route.query.id
    })

    // checkbox 数据需要转换

    // 人群普筛的数据带过来是没有id的，不能这样直接用id判断 ==> data.receptionConsultation.id
    if (data && !this.isAllEmpty(data.receptionConsultation)) {
      Object.entries(data.receptionConsultation).forEach(([key, value]) => {
        const type =
          this.tnbGxyCopdFcQuestion.find((q) => q.prop === key) &&
          this.tnbGxyCopdFcQuestion.find((q) => q.prop === key).type
        if (type === 'checkbox') {
          data.receptionConsultation[key] = value ? value.split(',') : []
        }
      })
      this.formData = data.receptionConsultation
      this.formData.medicationList = data.medicationList
      this.$nextTick(() => {
        this.$refs.tnbAndgxyQuestionRef && this.$refs.tnbAndgxyQuestionRef.clearValidate()
        this.$refs.copdQuestionRef && this.$refs.copdQuestionRef.clearValidate()
        this.$refs.fcQuestionRef && this.$refs.fcQuestionRef.clearValidate()
      })
    }
  },
  methods: {
    // 判断对象是否为空,通过value判断
    isAllEmpty(obj) {
      return every(obj, (v) => isNil(v) || isEmpty(v.toString()))
    },
    getComponent(type) {
      switch (type) {
        case 'radio':
          return 'RadioGroupField'
        case 'checkbox':
          return 'CheckboxGroupField'
        case 'select':
          return 'SelectField'
        case 'date':
          return 'DateField'
        case 'MedicationTable':
          return 'MedicationTable'
        case 'upload':
          return 'UploadField'
        default:
          return 'TextField'
      }
    },
    handleQuestionnaireNameClick(item) {
      this.questionnaireId = item.id
    },

    async handletnbAndgxySave() {
      const result = {
        name: '糖尿病和高血压',
        success: false,
        data: this.formData
      }
      try {
        const valid = await this.$refs.tnbAndgxyQuestionRef.validate()
        result.success = valid
      } catch (err) {
        console.warn('糖尿病和高血压校验异常', err)
        result.success = false
      }
      return result
    },

    async handleCopdSave() {
      const result = {
        name: '慢阻肺',
        success: false,
        data: this.formData
      }
      try {
        const valid = await this.$refs.copdQuestionRef.validate()
        result.success = valid
      } catch (err) {
        console.warn('慢阻肺校验异常', err)
        result.success = false
      }
      return result
    },

    async handleFcSave() {
      const result = {
        name: '房颤',
        success: false,
        data: this.formData
      }
      try {
        const valid = await this.$refs.fcQuestionRef.validate()
        result.success = valid
      } catch (err) {
        console.warn('房颤校验异常', err)
        result.success = false
      }
      return result
    },

    async handleConsultationInfoSave(type) {
      const rrId = this.$route.query.id
      const consultationId =
        (this.$store.getters.consultationInfoData &&
          this.$store.getters.consultationInfoData.receptionConsultation.id) ||
        ''
      let status =
        (this.$store.getters.consultationInfoData &&
          this.$store.getters.consultationInfoData.receptionConsultation.status) ||
        1

      // 如果是跳过，直接保存状态为9
      if (type === 'skip') {
        status = 9
        await saveConsultationInfo({ rrId, id: consultationId, status })
        await saveHighRiskScreening({ rrId })
        this.$store.dispatch('receptionWorkbench/getConsultationInfoData', { rrId })
        return status
      }

      // 如果是重新编辑，直接保存状态为1
      if (type === 'edit') {
        status = 1
        await saveConsultationInfo({ rrId, id: consultationId, status })
        this.$store.dispatch('receptionWorkbench/getConsultationInfoData', { rrId })
        return status
      }

      if (status === 9) {
        return status
      }

      // 保存慢病信息
      const resultMap = {
        tnb_gxy: await this.handletnbAndgxySave(),
        copd: await this.handleCopdSave(),
        fc: await this.handleFcSave()
      }

      const excludedDiseases = new Set(
        this.questionnaireList.map((it) => {
          if (it.id === 'COPD') {
            return 'copd'
          }
          if (it.id === 'fangchan') {
            return 'fc'
          }
          return it.id
        })
      )

      // 补充缺失病种的 success 标志
      if (!excludedDiseases.has('tnb') && !excludedDiseases.has('gxy')) {
        resultMap.tnb_gxy.success = true
      }
      if (!excludedDiseases.has('copd')) {
        resultMap.copd.success = true
      }
      if (!excludedDiseases.has('fc')) {
        resultMap.fc.success = true
      }

      // 收集未填写提示
      const warnings = Object.values(resultMap)
        .filter((r) => !r.success)
        .map((r) => `${r.name}存在必填未填项！`)

      // 处理checkbox类型的字段
      const formData = cloneDeep(resultMap.tnb_gxy.data || {})
      Object.entries(formData).forEach(([key, value]) => {
        if (Array.isArray(value) && key !== 'medicationList') {
          formData[key] = value.join(',')
        }
      })

      if (warnings.length > 0) {
        if (type === 'next') {
          for (const msg of warnings) {
            this.$message.warning(msg)
            // eslint-disable-next-line no-await-in-loop
            await new Promise((resolve) => setTimeout(resolve, 500))
          }
        }
        status = 1
      } else {
        status = 5
      }

      // 保存
      const saveParams = {
        ...formData,
        status,
        rrId,
        id: consultationId
      }

      const res = await saveConsultationInfo(saveParams)
      if (res.code === 200) {
        if (type === 'save') {
          this.$message.success('保存成功')
        }
        // 生成高危筛查
        await saveHighRiskScreening({ rrId })
        this.$store.dispatch('receptionWorkbench/getConsultationInfoData', { rrId })
        return status
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.questionnaire-content {
  margin-top: 16px;
  ::v-deep .long-label .el-form-item__label {
    width: auto !important;
    margin-left: 60px;
  }
}
</style>
