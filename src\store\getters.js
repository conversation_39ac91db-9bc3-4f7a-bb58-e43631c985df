const getters = {
  sidebar: (state) => state.app.sidebar,
  size: (state) => state.app.size,
  fz: (state) => state.app.fz,
  willOverdueStatus: (state) => state.app.willOverdueStatus,
  replaceMenus: (state) => state.app.replaceMenus,
  device: (state) => state.app.device,
  healthUserNumber: (state) => state.app.healthUserNumber,
  bigFont: (state) => state.app.bigFont,
  visitedViews: (state) => state.tagsView.visitedViews,
  cachedViews: (state) => state.tagsView.cachedViews,
  token: (state) => state.user.token,
  avatar: (state) => state.user.avatar,
  name: (state) => state.user.name,
  userInfo: (state) => state.user.userInfo,
  userId: (state) => state.user.userId,
  introduction: (state) => state.user.introduction,
  roles: (state) => state.user.roles,
  role: (state) => state.user.role,
  homeRole: (state) => state.user.homeRole,
  permission_routes: (state) => state.permission.routes,
  errorLogs: (state) => state.errorLog.logs,
  menus: (state) => state.user.menus,
  permission: (state) => state.user.permission,
  telPhone: (state) => state.user.telPhone,
  hospitalId: (state) => state.user.hospitalId,
  sex: (state) => state.user.sex,
  type: (state) => state.user.type,
  patientInfo: (state) => state.user.patientInfo,
  hospital_code: (state) => state.user.hospital_code,
  serviceStation: (state) => state.user.serviceStation,
  stationId: (state) => state.user.stationId,
  departCode: (state) => state.user.departCode,
  departName: (state) => state.user.departName,
  open: (state) => state.telemedicine.open,
  ring: (state) => state.telemedicine.ring,
  deviceConnect: (state) => state.app.deviceConnect,
  highRiskScreeningData: (state) => state.receptionWorkbench.highRiskScreeningData,
  diagnosisOfIllnessData: (state) => state.receptionWorkbench.diagnosisOfIllnessData,
  consultationInfoData: (state) => state.receptionWorkbench.consultationInfoData,
  complicationsScreeningData: (state) => state.receptionWorkbench.complicationsScreeningData,
  managePatientTargetData: (state) => state.managePatient.managePatientTargetData,
  therapeuticActionDetail: (state) => state.managePatient.therapeuticActionDetail
}
export default getters
