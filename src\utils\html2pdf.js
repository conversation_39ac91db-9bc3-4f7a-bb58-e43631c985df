// 导出页面为PDF格式
import html2canvas from 'html2canvas'
import JSP<PERSON> from 'jspdf'

import { getToken } from '@/utils/auth'

export default {
  install(Vue, options) {
    Vue.prototype.ExportSavePdf = function (htmlTitle, currentTime, id, uploadObj) {
      const element = document.getElementById(id)
      html2canvas(element, {
        logging: false
      }).then(canvas => {
        const pdf = new JSPDF('p', 'mm', 'a4') // A4纸，纵向
        const ctx = canvas.getContext('2d')
        const a4w = 210
        const a4h = 297 // A4大小，210mm x 297mm，四边各保留20mm的边距，显示区域170x257
        // const imgHeight = Math.floor((a4h * canvas.width) / a4w) // 按A4显示比例换算一页图像的像素高度
        const imgHeight = canvas.width * 1.422
        let renderedHeight = 0
        console.log('canvas.height====>', canvas.height)

        while (renderedHeight < canvas.height) {
          const page = document.createElement('canvas')
          page.width = canvas.width
          page.height = Math.min(imgHeight, canvas.height - renderedHeight) // 可能内容不足一页

          // 用getImageData剪裁指定区域，并画到前面创建的canvas对象中
          page
            .getContext('2d')
            .putImageData(
              ctx.getImageData(0, renderedHeight, canvas.width, Math.min(imgHeight, canvas.height - renderedHeight)),
              0,
              0
            )
          pdf.addImage(
            page.toDataURL('image/jpeg', 1.0),
            'JPEG',
            0,
            0,
            a4w,
            Math.min(a4h, (a4w * page.height) / page.width)
          ) // 添加图像到页面，保留10mm边距

          renderedHeight += imgHeight
          console.log('imgHeight=>', imgHeight, 'renderedHeight=>', renderedHeight)
          if (renderedHeight < canvas.height) {
            pdf.addPage()
          } // 如果后面还有内容，添加一个空页
          // delete page;
        }
        if ((uploadObj && !uploadObj.type) || !uploadObj) pdf.save(htmlTitle + currentTime)
        if (uploadObj && uploadObj.type) {
          const a = pdf.output('blob')
          const formData = new FormData()
          formData.append(uploadObj.key || 'file', a, `${htmlTitle}.pdf`) // 'file'是后端期望的字段名，'example.pdf'是文件名
          formData.append('folder', 'bodycheck/star/report/')
          formData.append('platform', 'minio')
          // 使用fetch API上传文件
          fetch(uploadObj.url || '/cspapi/backend/cos/uploadFile/private3', {
            method: uploadObj.method || 'POST',
            body: formData,
            headers: {
              Authorization: getToken()
            }
          })
            .then(response => response.json())
            .then(data => {
              if (uploadObj.callBack) uploadObj.callBack(data)
            })
            .catch(error => {
              if (uploadObj.callBack) uploadObj.callBack(error)
            })
        }
      })
    }
  }
}
