//+----------------------------------------------------------------+
const on_web_start = function() {
  alert('on_web_start')
	_sdk_version_pkg_()
}
//+----------------------------------------------------------------+
const __io_lnk = function() {
	return 'localhost:8009'
}
const __io_url = function() {
	return 'http://' + __io_lnk() + '/thermal.io?data='
}
const __ws_url = function() {
	return 'ws://' + __io_lnk() + '/thermal.ws'
}
//+----------------------------------------------------------------+
const output_control = function(out) {
	let item = document.getElementById('out_window')
	if (item != null) {
		if (out === 'window clear') {
			item.value = ''
		} else {
			item.value = out + '\r\n' + item.value
		}
	}
}
const on_link_device = function(pack, onback) {
	let radio = document.getElementsByName('req_model')
	if (radio != null) {
		if (radio.length > 0) {
			if (radio[0].checked) { jqpost(__io_url(), pack, onback) }
		}
		if (radio.length > 1) {
			if (radio[1].checked) { jqlink(__ws_url(), pack, onback) }
		}
	}
}
const require_type = function() {
	let type = ''
	let radio = document.getElementsByName('use_model')
	if (radio != null) {
		if (radio.length > 0) {
			if (radio[0].checked) { type = 'usb' }
		}
		if (radio.length > 1) {
			if (radio[1].checked) { type = 'tcp' }
		}
	}
	return type
}
//+----------------------------------------------------------------+
const _is_label_machine_ = function() {
	return 'false'
}
//+----------------------------------------------------------------+
function sleep(d){
	for(var t = Date.now();Date.now() - t <= d;);
}


const _string2hex_ = function(src) {
	let ret = ''
	for (let i = 0; i < src.length; i++) {
		ret += src.charCodeAt(i).toString(16)
	}
	return ret
}
//+----------------------------------------------------------------+
//SDK信息
const _sdk_version_pkg_ = function() {
	let sdk_message = document.getElementById("sdk_message")
	if (sdk_message) {
		sdk_message.innerHTML = '正在连接服务器...'
		let onback = function(isok, res) {
			if (!isok) {
				sdk_message.innerHTML = '连接服务端失败...'; return
			}
			if (res.code !== 0) {
				sdk_message.innerHTML = res.msg; return
			}
			let text = ''
			for (let i = 0; i < res.message.length; i++) {
				text += res.message[i]
				if (i < (res.message.length - 1)) { text += '\n' }
			}
			sdk_message.innerHTML = text
		}
		let pack = {
			command: '_sdk_version_pkg_',
			source : 'thermal',
		}
		on_link_device(pack, onback)
	}
}
//+----------------------------------------------------------------+
//枚举设备
const _thermal_enum_printer_ = function() {
	let onback = function(isok, res) {
    console.log(isok);
    console.log(res);
		if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
		if (res.code !== 0) { output_control(res.msg); return }
		let enum_list = document.getElementById('enum_list')
		if (enum_list.length > 0) { enum_list.clear() }
		
		if (res.device.length > 0) {
			for (let i = 0; i < res.device.length; i++) {
				let opt = document.createElement('option')
				opt.text= res.device[i]
				enum_list.options.add(opt)
			}
		} else {
			output_control(res.message)
		}
	}
	let pack = {
		command: '_thermal_enum_printer_',
		require: require_type(),
	}
	on_link_device(pack, onback)
}

//+----------------------------------------------------------------+
//获取设备状态(4字节解析)
const _thermal_get_printer_status_ = function() {
	// let device = document.getElementById('enum_list').value
	// if (device != '') {
	// 	let onback = function(isok, res) {
	// 		if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
	// 		if (res.code !== 0) { output_control(res.msg); return }
	// 		//output_control('设备状态:' + JSON.stringify(res.state))
	// 		output_control(JSON.stringify(res))
	// 	}
	// 	let pack = {
	// 		command: '_thermal_get_printer_status_',
	// 		device : device,
	// 	}
	// 	on_link_device(pack, onback)
	// } else {
	// 	output_control('请先枚举设备后再操作！')
	// }
	
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			//output_control('设备状态:' + JSON.stringify(res.state))
			output_control(JSON.stringify(res))
			
			//十六进制字符串转二进制数据
			let data = _hex2bin(res.recv_data);
			let dataleng = data.length;
			let byte1 = data[dataleng-4], byte2 = data[dataleng-3], byte3 = data[dataleng-2], byte4 = data[dataleng-1];
			
			//二进制数据转十六进制字符串
			output_control(_bin2hex(data))
			
			if ((byte1 & 0x02) == 0x02) {
				output_control('卡纸')
			}
			if ((byte1 & 0x10) != 0x10) {
				output_control('非联机')
			}
			if ((byte1 & 0x20) == 0x20) {
				output_control('缺纸')
			}
			if ((byte1 & 0x40) == 0x40) {
				output_control('切刀出错')
			}
			if ((byte1 & 0x80) == 0x80) {
				output_control('设备忙')
			}
			if ((byte2 & 0x08) == 0x08) {
				output_control('缓冲非空')
			}
			if ((byte3 & 0x01) == 0x01) {
				output_control('碳带出错')
			}
			if ((byte3 & 0x02) == 0x02) {
				output_control('打印头抬起')
			}
						
			if ((byte3 & 0x04) == 0x04) {
				output_control('标签纸')
			}
			else {
				output_control('连续纸')
			}
						
			if ((byte3 & 0x08) == 0x08) {
				output_control('热敏')
			}
			else {
				output_control('热转印')
			}
						
			if ((byte3 & 0x10) == 0x10) {
				output_control('切刀开')
			}
			else {
				output_control('切刀关')
			}
						
			if ((byte3 & 0x20) == 0x20) {
				output_control('电源复位')
			}
			if ((byte3 & 0x40) == 0x40) {
				output_control('打印头过热')
			}
			/*			
			if ((byte5 & 0x02) == 0x02) {
				output_control('打印完成')
			}
			if ((byte5 & 0x08) == 0x08) {
				output_control('打印机忙')
			}
			if ((byte5 & 0x20) == 0x20) {
				output_control('印头抬起出错')
			}
			if ((byte5 & 0x80) == 0x80) {
				output_control('切纸完成')
			}
						
			if ((byte6 & 0x01) == 0x01) {
				output_control('RFID报错')
			}
			if ((byte6 & 0x02) == 0x02) {
				output_control('打印机暂停')
			}
			if ((byte6 & 0x04) == 0x04) {
				output_control('RFID操作完成')
			}
			if ((byte6 & 0x10) == 0x10) {
				output_control('重试作废流程进行中')
			}
			if ((byte6 & 0x40) == 0x40) {
				output_control('rfid自动校验进行中')
			}
			if ((byte6 & 0x80) == 0x80) {
				output_control('rfid自定义指令进行中')
			}
			*/
		}
		//接口详情使用，可参考thermal_web_api.txt文件
		let pack = {
			command: "_thermal_cmd_engine_controler_",
			device : device,
			func   : "thermal_get_printer_status"
		}
		on_link_device(pack, onback)
	} else {
		output_control('请先枚举设备后再操作！')
	}
}

//+----------------------------------------------------------------+
//获取状态(64字节解析DL-950)
const _thermal_get_printer_status64_ = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			//output_control('设备状态:' + JSON.stringify(res.state))
			output_control(JSON.stringify(res))
			
			//十六进制字符串转二进制数据
			let data = _hex2bin(res.recv_data)
			let byte1 = data[0], byte2 = data[1], byte3 = data[2], byte4 = data[3], byte5 = data[24], byte6 = data[25];
			
			//二进制数据转十六进制字符串
			output_control(_bin2hex(data))
			
			if ((byte1 & 0x02) == 0x02) {
				output_control('卡纸')
			}
			if ((byte1 & 0x10) != 0x10) {
				output_control('非联机')
			}
			if ((byte1 & 0x20) == 0x20) {
				output_control('缺纸')
			}
			if ((byte1 & 0x40) == 0x40) {
				output_control('切刀出错')
			}
			if ((byte1 & 0x80) == 0x80) {
				output_control('设备忙')
			}
			if ((byte2 & 0x08) == 0x08) {
				output_control('缓冲非空')
			}
			if ((byte3 & 0x01) == 0x01) {
				output_control('碳带出错')
			}
			if ((byte3 & 0x02) == 0x02) {
				output_control('打印头抬起')
			}
						
			if ((byte3 & 0x04) == 0x04) {
				output_control('标签纸')
			}
			else {
				output_control('连续纸')
			}
						
			if ((byte3 & 0x08) == 0x08) {
				output_control('热敏')
			}
			else {
				output_control('热转印')
			}
						
			if ((byte3 & 0x10) == 0x10) {
				output_control('切刀开')
			}
			else {
				output_control('切刀关')
			}
						
			if ((byte3 & 0x20) == 0x20) {
				output_control('电源复位')
			}
			if ((byte3 & 0x40) == 0x40) {
				output_control('打印头过热')
			}
						
			if ((byte5 & 0x02) == 0x02) {
				output_control('打印完成')
			}
			if ((byte5 & 0x08) == 0x08) {
				output_control('打印机忙')
			}
			if ((byte5 & 0x20) == 0x20) {
				output_control('印头抬起出错')
			}
			if ((byte5 & 0x80) == 0x80) {
				output_control('切纸完成')
			}
						
			if ((byte6 & 0x01) == 0x01) {
				output_control('RFID报错')
			}
			if ((byte6 & 0x02) == 0x02) {
				output_control('打印机暂停')
			}
			if ((byte6 & 0x04) == 0x04) {
				output_control('RFID操作完成')
			}
			if ((byte6 & 0x10) == 0x10) {
				output_control('重试作废流程进行中')
			}
			if ((byte6 & 0x40) == 0x40) {
				output_control('rfid自动校验进行中')
			}
			if ((byte6 & 0x80) == 0x80) {
				output_control('rfid自定义指令进行中')
			}
		}
		//接口详情使用，可参考thermal_web_api.txt文件
		let pack = {
			command: "_thermal_cmd_engine_controler_",
			device : device,
			func   : "thermal_get_printer_status"
		}
		on_link_device(pack, onback)
	} else {
		output_control('请先枚举设备后再操作！')
	}
}

//+----------------------------------------------------------------+
//获取RFID状态
const _thermal_rfid_get_status_ = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			//output_control('设备状态:' + JSON.stringify(res.state))
			output_control(JSON.stringify(res))
			
			//十六进制字符串转二进制数据
			let data = _hex2bin(res.recv_data)
			let byte1 = data[0], byte2 = data[1], byte3 = data[2], byte4 = data[3], byte5 = data[24], byte6 = data[25];
			
			//二进制数据转十六进制字符串
			output_control(_bin2hex(data))
			
			if ((byte1 & 0x02) == 0x02) {
				output_control('卡纸')
			}
			if ((byte1 & 0x10) != 0x10) {
				output_control('非联机')
			}
			if ((byte1 & 0x20) == 0x20) {
				output_control('缺纸')
			}
			if ((byte1 & 0x40) == 0x40) {
				output_control('切刀出错')
			}
			if ((byte1 & 0x80) == 0x80) {
				output_control('设备忙')
			}
			if ((byte2 & 0x08) == 0x08) {
				output_control('缓冲非空')
			}
			if ((byte3 & 0x01) == 0x01) {
				output_control('碳带出错')
			}
			if ((byte3 & 0x02) == 0x02) {
				output_control('打印头抬起')
			}
						
			if ((byte3 & 0x04) == 0x04) {
				output_control('标签纸')
			}
			else {
				output_control('连续纸')
			}
						
			if ((byte3 & 0x08) == 0x08) {
				output_control('热敏')
			}
			else {
				output_control('热转印')
			}
						
			if ((byte3 & 0x10) == 0x10) {
				output_control('切刀开')
			}
			else {
				output_control('切刀关')
			}
						
			if ((byte3 & 0x20) == 0x20) {
				output_control('电源复位')
			}
			if ((byte3 & 0x40) == 0x40) {
				output_control('打印头过热')
			}
						
			if ((byte5 & 0x02) == 0x02) {
				output_control('打印完成')
			}
			if ((byte5 & 0x08) == 0x08) {
				output_control('打印机忙')
			}
			if ((byte5 & 0x20) == 0x20) {
				output_control('印头抬起出错')
			}
			if ((byte5 & 0x80) == 0x80) {
				output_control('切纸完成')
			}
						
			if ((byte6 & 0x01) == 0x01) {
				output_control('RFID报错')
			}
			if ((byte6 & 0x02) == 0x02) {
				output_control('打印机暂停')
			}
			if ((byte6 & 0x04) == 0x04) {
				output_control('RFID操作完成')
			}
			if ((byte6 & 0x10) == 0x10) {
				output_control('重试作废流程进行中')
			}
			if ((byte6 & 0x40) == 0x40) {
				output_control('rfid自动校验进行中')
			}
			if ((byte6 & 0x80) == 0x80) {
				output_control('rfid自定义指令进行中')
			}
		}
		//接口详情使用，可参考thermal_web_api.txt文件
		let pack = {
			command: "_thermal_cmd_engine_controler_",
			device : device,
			func   : "thermal_rfid_get_status"
		}
		on_link_device(pack, onback)
	} else {
		output_control('请先枚举设备后再操作！')
	}
}

//切换成标准流程模式
const stdProcess = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			console.log("isok=[" +isok + "],res=[" + res + "]");
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			//res.code为0表示执行成功。 res.msg是失败信息。
			
			output_control('打印机切换成「标准流程模式」成功')
		}
		let pack = {
				command: "_thermal_customized_api_",
				device : device,
				func   : "custom_test_usb_rfid_static_base_read_write_process"
	       }
		   on_link_device(pack, onback)
			
		}//if device
		else {
		output_control('请先枚举设备后再操作！')
	}
}

//切换成客户化流程模式
const customProcess = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			console.log("isok=[" +isok + "],res=[" + res + "]");
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			//res.code为0表示执行成功。 res.msg是失败信息。
			
			output_control('打印机切换成「客户化流程模式」成功')
		}
		let pack = {
				command: "_thermal_customized_api_",
				device : device,
				func   : "custom_test_usb_rfid_static_base_demand_print"
	       }
		   on_link_device(pack, onback)
			
		}//if device
		else {
		output_control('请先枚举设备后再操作！')
	}
}

const fetchEPC = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			console.log("isok=[" +isok + "],res=[" + res + "]");
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			//res.code为0表示执行成功。 res.msg是失败信息。
			
			output_control('获取EPC成功:' + res.hex )
		}
		let pack = {
				command: "_thermal_customized_api_",
				device : device,
				func   : "custom_test_usb_rfid_static_get_epc"
	       }
		   on_link_device(pack, onback)
			
		}//if device
		else {
		output_control('请先枚举设备后再操作！')
	}
}


const fetchTID = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			console.log("isok=[" +isok + "],res=[" + res + "]");
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			//res.code为0表示执行成功。 res.msg是失败信息。
			
			output_control('获取TID成功:' + res.hex)
		}
		let pack = {
				command: "_thermal_customized_api_",
				device : device,
				func   : "custom_test_usb_rfid_static_get_tid"
	       }
		   on_link_device(pack, onback)
			
		}//if device
		else {
		output_control('请先枚举设备后再操作！')
	}
}



//+----------------------------------------------------------------+
//前后移动标签
const _thermal_label_move_ = function(direction) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let mm = document.getElementById('move_mm').value.toString()
		if(mm == ""|| mm == null){
			output_control('设置的数值不能为空！')
			return;
		}
		if(isNaN(mm) || mm<0){
			output_control('设置的数值不能有其他字符！')
			return;
		}
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (direction == 'font') {
				output_control('向前移动: ' + mm + ' mm')
			}
			
			if (direction == 'back') {
				output_control('向后移动: ' + mm + ' mm')
			}
		}
		
		if (direction == 'font') {
				let pack = {
						command: "_thermal_cmd_engine_controler_",
						device : device,
						func   : "rfid_move_forward",
						mm  : parseInt(mm),
		       }
			   on_link_device(pack, onback)
		}
		
		if (direction == 'back') {
				let pack = {
						command: "_thermal_cmd_engine_controler_",
						device : device,
						func   : "rfid_move_backward",
						mm  : parseInt(mm),
		       }
			   on_link_device(pack, onback)
			}
			
		}else {
		output_control('请先枚举设备后再操作！')
	}
}

//+----------------------------------------------------------------+
//获取标签个数
const _thermal_get_label_count_ = function(static) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (static == 'true') {
				output_control('静态检索标签')
			}
			if (static == 'false') {
				output_control('动态标签个数:' + res.recv_data)
			}
		}
		if (static == 'true') {
				let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func   : "rfid_static_get_label_count",
			}	
			on_link_device(pack, onback)
		}
		if (static == 'false') {
				let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func   : "rfid_dynmic_get_label_count",
			}	
			on_link_device(pack, onback)
		}
	} else {
		output_control('请先枚举设备后再操作！')
	}
}

//+----------------------------------------------------------------+
//获取TID
const _thermal_rfid_read_tid_ = function(static) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (static == 'true') {
				output_control('读取静态TID:' + res.recv_data)
			}
			if (static == 'false') {
				output_control('读取动态TID:' + res.recv_data)
			}
		}
		if (static == 'true') {
				let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func   : "rfid_static_read_tid",
			}	
			on_link_device(pack, onback)
		}
		if (static == 'false') {
				let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func   : "rfid_dynmic_read_tid",
			}	
			on_link_device(pack, onback)
		}
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+
//+----------------------------------------------------------------+
//黑标定位
const _thermal_label_auto_locate_ = function(mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (mode == 'black_label') {
				output_control('黑标定位成功。')
			}
		}
		let pack = {
			command: '_thermal_cmd_engine_controler_',
			device : device,
			func   :  'thermal_blackmarking_auto_locate',
		}
		on_link_device(pack, onback)
	} else {
		output_control('请先枚举设备后再操作！')
	}
}

//+----------------------------------------------------------------+
//设置天线读写功率
const _thermal_rfid_set_power_ = function(mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let power = document.getElementById('antenna_power').value.toString()
		
		if(power == ""|| power == null){
			output_control('设置的数值不能为空！')
			return;
		}
		if(isNaN(power) || power<0){
			output_control('设置的数值不能有其他字符！')
			return;
		}
		
		if(power<0){
			output_control('设置功率不能小于0！')
			return;
		}
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (mode == 'read') {
				output_control('设置读取功率成功！')
			}
			if (mode == 'write') {
				output_control('设置写入功率成功！')
			}
		}
		
		if (mode == 'read') {
			let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func: 'rfid_set_read_power',
				power : parseInt(power.toString()),
			}
			on_link_device(pack, onback)
		}

		if (mode == 'write') {
			let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func: 'rfid_set_write_power',
				power : parseInt(power.toString()),
			}
			on_link_device(pack, onback)
		}
		
	} else {
		output_control('请先枚举设备后再操作！')
	}
}

//+----------------------------------------------------------------+
//获取天线读写功率
const _thermal_rfid_get_power_ = function(mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			output_control(JSON.stringify(res))
			
			let byte1 = res.recv_data[0]+res.recv_data[1];
			let byte2 = res.recv_data[2]+res.recv_data[3];
			let _data = byte1+byte2;
			//十六进制数值转十进制整数
			let data_ = _hex2int(_data);
			if (mode == 'read') {
				output_control('当前读取功率:' + data_)
			}
			if (mode == 'write') {
				output_control('当前写入功率:' + data_)
			}
		}
		
		if (mode == 'read') {
				let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func: 'rfid_get_read_power',
			}
			on_link_device(pack, onback)
		}
		if (mode == 'write') {
				let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func: 'rfid_get_write_power',
			}
			on_link_device(pack, onback)
		}
		
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+
//设置读取TID
const _thermal_rfid_set_read_tid_ = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
		}
		let pack = {
			command: '_thermal_cmd_engine_controler_',
			device : device,
			func   :  'rfid_read_tid',
		}
		on_link_device(pack, onback)
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+

//+----------------------------------------------------------------+
//设置读取旧的EPC
const _thermal_rfid_read_old_epc_ = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
		}
		let pack = {
			command: '_thermal_cmd_engine_controler_',
			device : device,
			func   :  'rfid_read_old_epc',
		}
		on_link_device(pack, onback)
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+

//+----------------------------------------------------------------+
//写入EPC数据
const _thermal_rfid_write_epc_ = function(static, mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let src = document.getElementById('epc_data').value.toString()
		if(src == ""|| src == null){
			output_control('设置的数值不能为空！')
			return;
		}
		let radio = document.getElementsByName('epc_model')
			if (radio != null) {
				if (radio.length > 0) {
					if (radio[0].checked) { mode = 'string' }
				}
				if (radio.length > 1) {
					if (radio[1].checked) { mode = 'hex' }
					
				}
		}
		
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			
			if (static == 'true' && mode == 'hex') {
				let staticepc = res.recv_data[6]+res.recv_data[7]+res.recv_data[8]+res.recv_data[9]
				if(staticepc == "0000"){
					output_control('静态EPC写Hex成功：' + src)
					output_control('注意静态检索标签后再读取静态epc')
				}else{
					output_control('静态EPC写Hex失败返回：'+res.recv_data)
				}
				
			}
			if (static == 'false' && mode == 'hex') {
				output_control('动态EPC写Hex成功：' + src)
			}
			if (static == 'true' && mode == 'string') {
				let staticepc = res.recv_data[6]+res.recv_data[7]+res.recv_data[8]+res.recv_data[9]
				if(staticepc == "0000"){
					output_control('静态EPC写String成功：' + src)
					output_control('注意静态检索标签后再读取静态epc')
				}else{
					output_control('静态EPC写String失败返回：'+res.recv_data)
				}
			}
			if (static == 'false' && mode == 'string') {
				output_control('动态EPC写String成功：' + src)
			}
		}
		
		
		if (static == 'true' && mode == 'string') {
				let pack = {
					command: '_thermal_cmd_engine_controler_',
					device : device,
					func: 'rfid_static_write_epc',
					data     : src,
				}
				on_link_device(pack, onback)
			}
		if (static == 'false' && mode == 'string') {
			let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func: 'rfid_dynmic_write_epc',
				data     : src,
			}
			on_link_device(pack, onback)
		}
			
		if (static == 'true' && mode == 'hex') {
				let src_hex  = _str2Hex(src)
				let pack = {
					command: '_thermal_cmd_engine_controler_',
					device : device,
					func: 'rfid_static_write_epc',
					data     : src_hex,
				}
				on_link_device(pack, onback)
		}
			
		if (static == 'false' && mode == 'hex') {
			let src_hex  = _str2Hex(src)
			let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func: 'rfid_dynmic_write_epc',
				data     : src_hex,
			}
			on_link_device(pack, onback)
		}
		
		
		
	} else {
		output_control('请先枚举设备后再操作！')
	}
}

//+----------------------------------------------------------------+
//读取EPC数据
const _thermal_rfid_read_epc_ = function(static, mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			let radio = document.getElementsByName('epc_model')
			if (radio != null) {
				if (radio.length > 0) {
					if (radio[0].checked) { mode = 'string' }
				}
				if (radio.length > 1) {
					if (radio[1].checked) { mode = 'hex' }
				}
			}
			if (static == 'true' && mode == 'hex') {
				if(res.recv_data == "4572726F7221"){
					output_control('静态EPC读Hex失败返回：'+ res.recv_data)
				}else{
					output_control('静态EPC读Hex：' + res.recv_data)
				}
			}
			if (static == 'false' && mode == 'hex') {
				if(res.recv_data == "4572726F7221"){
					output_control('动态EPC读Hex失败返回：'+ res.recv_data)
				}else{
					output_control('动态EPC读Hex：' + res.recv_data)
				}
				
			}
			if (static == 'true' && mode == 'string') {
				if(res.recv_data == "4572726F7221"){
					output_control('静态EPC读String失败返回：'+ res.recv_data)
				}else{
					output_control('静态EPC读String：' + res.recv_data)
				}
			}
			if (static == 'false' && mode == 'string') {
				if(res.recv_data == "4572726F7221"){
					output_control('动态EPC读String失败返回：'+ res.recv_data)
				}else{
					output_control('动态EPC读String：' + res.recv_data)
				}
			}
		}
		
		if (static == 'true') {
				let pack = {
					command: '_thermal_cmd_engine_controler_',
					device : device,
					func: 'rfid_static_read_epc',
				}
			on_link_device(pack, onback)
		}
		
		if (static == 'false') {
				let pack = {
					command: '_thermal_cmd_engine_controler_',
					device : device,
					func: 'rfid_dynmic_read_epc',
				}
			on_link_device(pack, onback)
		}
		
	} else {
		output_control('请先枚举设备后再操作！')
	}
}

//+----------------------------------------------------------------+///////////////////////////////////////////
//写入USER数据
const _thermal_rfid_write_user_ = function(static,mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let src = document.getElementById('user_data').value.toString()
		if(src == ""|| src == null){
			output_control('设置的数值不能为空！')
			return;
		}
		let radio = document.getElementsByName('user_model')
			if (radio != null) {
				if (radio.length > 0) {
					if (radio[0].checked) { mode = 'string' }
				}
				if (radio.length > 1) {
					if (radio[1].checked) { mode = 'hex' }
				}
		}
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (static == 'false' && mode == 'hex') {
				output_control('动态USER写Hex成功：' + src)
			}
			if (static == 'false' && mode == 'string') {
				output_control('动态USER写String成功：' + src)
			}
			if (static == 'true' && mode == 'hex') {
				output_control('静态USER写Hex成功：' + src)
			}
			if (static == 'true' && mode == 'string') {
				output_control('静态USER写String成功：' + src)
			}
		}
		if (mode == 'hex')
		{
			src = _str2Hex(src);
		}
		
		if(static == 'false'){
			let pack = {
			command: '_thermal_cmd_engine_controler_',
			device : device,
			func: 'rfid_dynmic_write_user',
			data   : src,
		    }
		    on_link_device(pack, onback)
		}
		
		if(static == 'true'){
			let pack = {
			command: '_thermal_cmd_engine_controler_',
			device : device,
			func: 'rfid_static_write_user',
			data   : src,
		    }
		    on_link_device(pack, onback)
		}
		
		
	} else {
		output_control('请先枚举设备后再操作！')
	}
}

//+----------------------------------------------------------------+
//读取USER数据
const _thermal_rfid_read_user_ = function(static,mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			
			let radio = document.getElementsByName('user_model')
			if (radio != null) {
				if (radio.length > 0) {
					if (radio[0].checked) { mode = 'string' }
				}
				if (radio.length > 1) {
					if (radio[1].checked) { mode = 'hex' }
				}
			}
			
			if (static == 'false' && mode == 'hex') {
				output_control('动态USER读Hex成功：' + res.recv_data)
			}
			if (static == 'false' && mode == 'string') {
				output_control('动态USER读String成功：' + res.recv_data)
			}
			if (static == 'true' && mode == 'hex') {
				output_control('静态USER读Hex成功：' + res.recv_data)
			}
			if (static == 'true' && mode == 'string') {
				output_control('静态USER读String成功：' + res.recv_data)
			}
		}
		
		if(static == 'false'){
			let pack = {
			command: '_thermal_cmd_engine_controler_',
			device : device,
			func : 'rfid_dynmic_read_user',
			size :  4
		    }
		    on_link_device(pack, onback)
		}
		if(static == 'true'){
			let pack = {
			command: '_thermal_cmd_engine_controler_',
			device : device,
			func : 'rfid_static_read_user',
			size :  4
		    }
		    on_link_device(pack, onback)
		}
		
		
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+

//+----------------------------------------------------------------+
//开启/关闭扫描
const _thermal_scan_open_ = function(mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (mode == 'true') {
				output_control('开启扫描')
			}
			if (mode == 'false') {
				output_control('关闭扫描')
			}
		}
		if (mode == 'true') {
			let pack = {
			command: '_thermal_cmd_engine_controler_',
			device : device,
			func   :  'scan_open',
		    }
		    on_link_device(pack, onback)
		}
		
		if (mode == 'false') {
			let pack = {
			command: '_thermal_cmd_engine_controler_',
			device : device,
			func   :  'scan_close',
		    }
		    on_link_device(pack, onback)
		}
		
	} else {
		output_control('请先枚举设备后再操作！')
	}
}

//+----------------------------------------------------------------+
//+----------------------------------------------------------------+
//设置扫描走纸长度
const _thermal_scan_set_backwardlength_ = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let dot = document.getElementById('backward_length').value.toString()
		
		if(dot == ""|| dot == null){
			output_control('设置的数值不能为空！')
			return;
		}
		if(isNaN(dot) || dot<0){
			output_control('设置的数值不能有其他字符！')
			return;
		}
		
		if(dot<0){
			output_control('设置扫描走纸长度不能小于0！')
			return;
		}
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			output_control('设置扫描走纸长度！')
		}
		
		let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func: 'scan_set_backwardlength',
				dot : parseInt(dot.toString()),
			}
			on_link_device(pack, onback)
		
	} else {
		output_control('请先枚举设备后再操作！')
	}
}

//+----------------------------------------------------------------+

//+----------------------------------------------------------------+
//获取扫描走纸长度
const _thermal_scan_get_backwardlength_ = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			output_control(JSON.stringify(res))
			let byte1 = res.recv_data[2]+res.recv_data[3]+res.recv_data[0]+res.recv_data[1];
			//十六进制数值转十进制整数
			let data_ = _hex2int(byte1);
			output_control('当前扫描走纸长度为:' + data_)
		}
		
		let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func: 'scan_get_backwardlength',
			}
			on_link_device(pack, onback)
		
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+
//+----------------------------------------------------------------+
//扫描
const _thermal_scan_get_data_ = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			output_control(JSON.stringify(res))
			output_control('获取扫描内容为:' + res.recv_data)
		}
		
		let pack = {
				command: '_thermal_cmd_engine_controler_',
				device : device,
				func: 'scan_get_data',
			}
			on_link_device(pack, onback)
		
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+
//+----------------------------------------------------------------+

//绘图打印文本
const _thermal_text_print_ = function(mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		//直接打印（ 打印:'true' / 预览:'false' ）
		let printdirectly  = 'true'
		
		//获取预览图为base64数据（ base64:'true' / 路径:'false' ）
		let get_base64_img = 'false'
		
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (printdirectly == 'true') {
				output_control('绘图打印成功')
			} else {
				if (get_base64_img == 'true') {
					output_control(res.image_base64)
				} else {
					output_control(res.image_path)
				}
			}
		}
		//张纸宽度
		let draw_w = parseInt(document.getElementById('draw_w').value.toString())
		if(draw_w == ""|| draw_w == null){
			output_control('张纸宽度不能为空！')
			return;
		}
		if(isNaN(draw_w) || draw_w<0){
			output_control('张纸宽度不能有其他字符！')
			return;
		}
		
		//页长
		let draw_h = parseInt(document.getElementById('draw_h').value.toString())
		if(draw_h == ""|| draw_h == null){
			output_control('页长不能为空！')
			return;
		}
		if(isNaN(draw_h) || draw_h<0){
			output_control('页长不能有其他字符！')
			return;
		}
		
		//横向位置
		let text_x = parseInt(document.getElementById('text_x').value.toString())
		if(isNaN(text_x) || text_x<0){
			output_control('横向位置不能有其他字符！')
			return;
		}
		//纵向位置
		let text_y = parseInt(document.getElementById('text_y').value.toString())
		if(isNaN(text_y) || text_y<0){
			output_control('纵向位置不能有其他字符！')
			return;
		}
		//文本宽度
		let text_w = parseInt(document.getElementById('text_w').value.toString())
		if(text_w == ""|| text_w == null){
			output_control('文本宽度不能为空！')
			return;
		}
		if(isNaN(text_w) || text_w<0){
			output_control('文本宽度不能有其他字符！')
			return;
		}
		//文本高度
		let text_h = parseInt(document.getElementById('text_h').value.toString())
		if(text_h == ""|| text_h == null){
			output_control('文本高度不能为空！')
			return;
		}
		if(isNaN(text_h) || text_h<0){
			output_control('文本高度不能有其他字符！')
			return;
		}
		
		
		
		//字号风格
		let text_styleA = document.getElementById('text_style')
		let text_style = text_styleA.options[text_styleA.selectedIndex].text;
        let text_style_iIsStrong= null;
		let text_style_iIsItalic = null;
        switch(text_style){
            case "正常":
                text_style_iIsStrong = 0;
				text_style_iIsItalic = 0;
				break;
            case "粗体":
                text_style_iIsStrong = 1;
				text_style_iIsItalic = 0;
				break;
			case "斜体":
                text_style_iIsStrong = 0;
				text_style_iIsItalic = 1;
				break;
			case "粗体&斜体":
                text_style_iIsStrong = 1;
				text_style_iIsItalic = 1;
				break;
        }
		
		//字体类型
		let text_typeA = document.getElementById('text_type')
		let text_type = text_typeA.options[text_typeA.selectedIndex].text;
		
		//旋转角度
		let text_rotionA = document.getElementById('text_rotion')
		let text_rotion = text_rotionA.options[text_rotionA.selectedIndex].text;
        let text_rotion_let = null;
        switch(text_rotion){
            case "不旋转":
                text_rotion_let = 0;break;
            case "旋转90度":
                text_rotion_let = 90;break;
			case "旋转180度":
                text_rotion_let = 180;break;
			case "旋转270度":
                text_rotion_let = 270;break;
        }
		
		//文本内容
		let text_data = document.getElementById('text_data').value.toString()
		
		
		let radio = document.getElementsByName('emulator_command_model')
		if (radio != null) {
			if (radio.length > 0) {
				if (radio[0].checked) 
				{ 
					//ZPL打印
					let pack = {
						command: '_thermal_zpl_draw_print_',
						device : device,
						printdirectly : 'true',
						measurement_mode: 'mm',
						canvas_size : {"width":draw_w, "height":draw_h},
						get_base64_img : 'false',
						is_label: 'false',
						print: [
							{"func":"PDSetFont","szFontName":text_type,"fSize":10},
							{"func":"PDSetTextRotation","iRotation":text_rotion_let},
							{"func":"PDSetTextIsStrong","iIsStrong":text_style_iIsStrong},
							{"func":"PDSetTextIsItalic","iIsItalic":text_style_iIsItalic},
							{"func":"PDSetTextDecorate","iIsLandScape":1,"iIsReverseSequence":0,"iIsAutoLineFeed":0,"iIsLayDown":0},
							{"func":"PDDrawTextW","iX":text_x,"iY":text_y,"iWidth":text_w, "iHeight":text_h,"wszText":text_data},
						]
					}
					
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					}
				}
			}
			if (radio.length > 1) {
				if (radio[1].checked) 
				{ 
					//TSPL打印
					let pack = {
						command: '_thermal_tspl_draw_print_',
						device : device,
						printdirectly : 'true',
						measurement_mode: 'mm',
						canvas_size : {"width":draw_w, "height":draw_h},
						get_base64_img : 'false',
						is_label: 'false',
						print: [
							{"func":"PDSetFont","szFontName":text_type,"fSize":10},
							{"func":"PDSetTextRotation","iRotation":text_rotion_let},
							{"func":"PDSetTextIsStrong","iIsStrong":text_style_iIsStrong},
							{"func":"PDSetTextIsItalic","iIsItalic":text_style_iIsItalic},
							{"func":"PDSetTextDecorate","iIsLandScape":1,"iIsReverseSequence":0,"iIsAutoLineFeed":0,"iIsLayDown":0},
							{"func":"PDDrawTextW","iX":text_x,"iY":text_y,"iWidth":text_w, "iHeight":text_h,"wszText":text_data},
						]
					}
					
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					}
				}
			}
			if (radio.length > 2) {
				if (radio[2].checked) 
				{ 
					//esc pos打印
					let pack = {
						command: '_thermal_escpos_draw_print_',
						device : device,
						printdirectly : 'true',
						measurement_mode: 'mm',
						canvas_size : {"width":draw_w, "height":draw_h},
						get_base64_img : 'false',
						is_label: 'false',
						select_mode: '0',
						print: [
							{"func":"PDSetFont","szFontName":text_type,"fSize":10},
							{"func":"PDSetTextRotation","iRotation":text_rotion_let},
							{"func":"PDSetTextIsStrong","iIsStrong":text_style_iIsStrong},
							{"func":"PDSetTextIsItalic","iIsItalic":text_style_iIsItalic},
							{"func":"PDSetTextDecorate","iIsLandScape":1,"iIsReverseSequence":0,"iIsAutoLineFeed":0,"iIsLayDown":0},
							{"func":"PDDrawTextW","iX":text_x,"iY":text_y,"iWidth":text_w, "iHeight":text_h,"wszText":text_data},
						]
					}
					
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					}
				}
			}
			
		}
	}else {
			output_control('请先枚举设备后再操作！')
		}
}

//绘图打印条码
const _thermal_barcode_print_ = function(mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		//直接打印（ 打印:'true' / 预览:'false' ）
		let printdirectly  = 'true'
		
		//获取预览图为base64数据（ base64:'true' / 路径:'false' ）
		let get_base64_img = 'false'
		
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (printdirectly == 'true') {
				output_control('绘图打印成功')
			} else {
				if (get_base64_img == 'true') {
					output_control(res.image_base64)
				} else {
					output_control(res.image_path)
				}
			}
		}
		
		//张纸宽度
		let draw_w = parseInt(document.getElementById('draw_w').value.toString())
		if(draw_w == ""|| draw_w == null){
			output_control('张纸宽度不能为空！')
			return;
		}
		if(isNaN(draw_w) || draw_w<0){
			output_control('张纸宽度不能有其他字符！')
			return;
		}
		//页长
		let draw_h = parseInt(document.getElementById('draw_h').value.toString())
		if(draw_h == ""|| draw_h == null){
			output_control('页长不能为空！')
			return;
		}
		if(isNaN(draw_h) || draw_h<0){
			output_control('页长不能有其他字符！')
			return;
		}
		//横向位置
		let barcode_x = parseInt(document.getElementById('barcode_x').value.toString())
		if(isNaN(barcode_x) || barcode_x<0){
			output_control('横向位置不能有其他字符！')
			return;
		}
		//纵向位置
		let barcode_y = parseInt(document.getElementById('barcode_y').value.toString())
		if(isNaN(barcode_y) || barcode_y<0){
			output_control('纵向位置不能有其他字符！')
			return;
		}
		//条码宽度
		let barcode_w = parseInt(document.getElementById('barcode_w').value.toString())
		if(barcode_w == ""|| barcode_w == null){
			output_control('条码宽度不能为空！')
			return;
		}
		if(isNaN(barcode_w) || barcode_w<0){
			output_control('条码宽度不能有其他字符！')
			return;
		}
		//条码高度
		let barcode_h = parseInt(document.getElementById('barcode_h').value.toString())
		if(barcode_h == ""|| barcode_h == null){
			output_control('条码高度不能为空！')
			return;
		}
		if(isNaN(barcode_h) || barcode_h<0){
			output_control('条码高度不能有其他字符！')
			return;
		}
		//条码类型
		let barcode_styleA = document.getElementById('barcode_style')
		let barcode_style = barcode_styleA.options[barcode_styleA.selectedIndex].text;
		let barcode_style_let = null;
        switch(barcode_style){
            case "code39":
                barcode_style_let = 8;
				break;
			case "code128":
                barcode_style_let = 20;
				break;
			case "code93":
                barcode_style_let = 25;
				break;
        }
		
		//是否打印注释
		let barcode_annotationA = document.getElementById('barcode_annotation')
		let barcode_annotation = barcode_annotationA.options[barcode_annotationA.selectedIndex].text;
		let barcode_annotation_let = null;
        switch(barcode_annotation){
            case "是":
                barcode_annotation_let = 1;
				break;
            case "否":
                barcode_annotation_let = 0;
				break;
        }
		
		//旋转角度
		let barcode_rotionA = document.getElementById('barcode_rotion')
		let barcode_rotion = barcode_rotionA.options[barcode_rotionA.selectedIndex].text;
        let barcode_rotion_let = null;
        switch(barcode_rotion){
            case "不旋转":
                barcode_rotion_let = 0;break;
            case "旋转90度":
                barcode_rotion_let = 90;break;
			case "旋转180度":
                barcode_rotion_let = 180;break;
			case "旋转270度":
                barcode_rotion_let = 270;break;
        }
		
		
		//条码内容
		let barcode_data = document.getElementById('barcode_data').value.toString()
		
		
		let radio = document.getElementsByName('emulator_command_model')
		if (radio != null) {
			if (radio.length > 0) {
				if (radio[0].checked) {
					//ZPL打印
					let pack = {
						command: '_thermal_zpl_draw_print_',
						device : device,
						printdirectly : 'true',
						measurement_mode: 'mm',
						canvas_size : {"width": draw_w, "height": draw_h},
						get_base64_img : 'false',
						is_label: 'false',
						print: [
							{"func":"PDSetBarCodeParameter","iSymbology":barcode_style_let,"bHumanRead":barcode_annotation_let,"iRotation":barcode_rotion_let},
							{"func":"PDDrawBarCode","iX":barcode_x,"iY":barcode_y,"iWidth":barcode_w, "iHeight":barcode_h, "szData":barcode_data},
						]
					}
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					}
				}
			}
			if (radio.length > 1) {
				if (radio[1].checked) {
					//TSPL打印
					let pack = {
						command: '_thermal_tspl_draw_print_',
						device : device,
						printdirectly : 'true',
						measurement_mode: 'mm',
						canvas_size : {"width": draw_w, "height": draw_h},
						get_base64_img : 'false',
						is_label: 'false',
						print: [
							{"func":"PDSetBarCodeParameter","iSymbology":barcode_style_let,"bHumanRead":barcode_annotation_let,"iRotation":barcode_rotion_let},
							{"func":"PDDrawBarCode","iX":barcode_x,"iY":barcode_y,"iWidth":barcode_w, "iHeight":barcode_h,"szData":barcode_data},
						]
					}
					
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					}
				}
			}
			if (radio.length > 2) {
				if (radio[2].checked) {
					//escpos打印
					let pack = {
						command: '_thermal_escpos_draw_print_',
						device : device,
						printdirectly : 'true',
						measurement_mode: 'mm',
						canvas_size : {"width": draw_w, "height": draw_h},
						get_base64_img : 'false',
						is_label: 'false',
						select_mode: '0',
						print: [
							{"func":"PDSetBarCodeParameter","iSymbology":barcode_style_let,"bHumanRead":barcode_annotation_let,"iRotation":barcode_rotion_let},
							{"func":"PDDrawBarCode","iX":barcode_x,"iY":barcode_y,"iWidth":barcode_w, "iHeight":barcode_h,"szData":barcode_data},
						]
					}
					
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					}
				}
			}
			
		}
	}else {
			output_control('请先枚举设备后再操作！')
		}
}

//绘图打印二维码
const _thermal_qrcode_print_ = function(mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		//直接打印（ 打印:'true' / 预览:'false' ）
		let printdirectly  = 'true'
		
		//获取预览图为base64数据（ base64:'true' / 路径:'false' ）
		let get_base64_img = 'false'
		
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (printdirectly == 'true') {
				output_control('绘图打印成功')
			} else {
				if (get_base64_img == 'true') {
					output_control(res.image_base64)
				} else {
					output_control(res.image_path)
				}
			}
		}
		//张纸宽度
		let draw_w = parseInt(document.getElementById('draw_w').value.toString())
		if(draw_w == ""|| draw_w == null){
			output_control('张纸宽度不能为空！')
			return;
		}
		if(isNaN(draw_w) || draw_w<0){
			output_control('张纸宽度不能有其他字符！')
			return;
		}
		//页长
		let draw_h = parseInt(document.getElementById('draw_h').value.toString())
		if(draw_h == ""|| draw_h == null){
			output_control('页长不能为空！')
			return;
		}
		if(isNaN(draw_h) || draw_h<0){
			output_control('页长不能有其他字符！')
			return;
		}
		//横向位置
		let qrcode_x = parseInt(document.getElementById('qrcode_x').value.toString())
		if(isNaN(qrcode_x) || qrcode_x<0){
			output_control('横向位置不能有其他字符！')
			return;
		}
		//纵向位置
		let qrcode_y = parseInt(document.getElementById('qrcode_y').value.toString())
		if(isNaN(qrcode_y) || qrcode_y<0){
			output_control('纵向位置不能有其他字符！')
			return;
		}
		//二维码宽度
		let qrcode_w = parseInt(document.getElementById('qrcode_w').value.toString())
		if(qrcode_w == ""|| qrcode_w == null){
			output_control('二维码宽度不能为空！')
			return;
		}
		if(isNaN(qrcode_w) || qrcode_w<0){
			output_control('二维码宽度不能有其他字符！')
			return;
		}
		//二维码高度
		let qrcode_h = parseInt(document.getElementById('qrcode_h').value.toString())
		if(qrcode_h == ""|| qrcode_h == null){
			output_control('二维码高度不能为空！')
			return;
		}
		if(isNaN(qrcode_h) || qrcode_h<0){
			output_control('二维码高度不能有其他字符！')
			return;
		}
		//二维码内容
		let qrcode_data = document.getElementById('qrcode_data').value.toString()
		
		let radio = document.getElementsByName('emulator_command_model')
		if (radio != null) {
			if (radio.length > 0) {
				if (radio[0].checked) {
					//ZPL打印
					let pack = {
						command: '_thermal_zpl_draw_print_',
						device : device,
						printdirectly : 'true',
						measurement_mode: 'mm',
						canvas_size : {"width": draw_w, "height": draw_h},
						get_base64_img : 'false',
						is_label: 'false',
						print: [
						
							{"func":"PDDrawQRCode","iX":qrcode_x,"iY":qrcode_y,"iWidth":qrcode_w, "iHeight":qrcode_h,"szData":qrcode_data},
						]
					}
					
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					}
				}
			}
			if (radio.length > 1) {
				if (radio[1].checked) {
					//TSPL打印
					let pack = {
						command: '_thermal_tspl_draw_print_',
						device : device,
						printdirectly : 'true',
						measurement_mode: 'mm',
						canvas_size : {"width": draw_w, "height": draw_h},
						get_base64_img : 'false',
						is_label: 'false',
						print: [
						
							{"func":"PDDrawQRCode","iX":qrcode_x,"iY":qrcode_y,"iWidth":qrcode_w, "iHeight":qrcode_h,"szData":qrcode_data},
						]
					}
					
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					} 
				}
			}
			if (radio.length > 2) {
				if (radio[2].checked) {
					//escpos打印
					let pack = {
						command: '_thermal_escpos_draw_print_',
						device : device,
						printdirectly : 'true',
						measurement_mode: 'mm',
						canvas_size : {"width": draw_w, "height": draw_h},
						get_base64_img : 'false',
						is_label: 'false',
						select_mode: '0',
						print: [
						
							{"func":"PDDrawQRCode","iX":qrcode_x,"iY":qrcode_y,"iWidth":qrcode_w, "iHeight":qrcode_h,"szData":qrcode_data},
						]
					}
					
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					} 
				}
			}
		}
	}else {
			output_control('请先枚举设备后再操作！')
		}
}

//绘图打印图片
const _thermal_Image_print_ = function(mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		//直接打印（ 打印:'true' / 预览:'false' ）
		let printdirectly  = 'true'
		
		//获取预览图为base64数据（ base64:'true' / 路径:'false' ）
		let get_base64_img = 'false'
		
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (printdirectly == 'true') {
				output_control('绘图打印成功')
			} else {
				if (get_base64_img == 'true') {
					output_control(res.image_base64)
				} else {
					output_control(res.image_path)
				}
			}
		}
		
		//张纸宽度
		let draw_w = parseInt(document.getElementById('draw_w').value.toString())
		if(draw_w == ""|| draw_w == null){
			output_control('张纸宽度不能为空！')
			return;
		}
		if(isNaN(draw_w) || draw_w<0){
			output_control('张纸宽度不能有其他字符！')
			return;
		}
		//页长
		let draw_h = parseInt(document.getElementById('draw_h').value.toString())
		if(draw_h == ""|| draw_h == null){
			output_control('页长不能为空！')
			return;
		}
		if(isNaN(draw_h) || draw_h<0){
			output_control('页长不能有其他字符！')
			return;
		}
		
		//横向位置
		let Image_x = parseInt(document.getElementById('Image_x').value.toString())
		if(isNaN(Image_x) || Image_x<0){
			output_control('横向位置不能有其他字符！')
			return;
		}
		//纵向位置
		let Image_y = parseInt(document.getElementById('Image_y').value.toString())
		if(isNaN(Image_y) || Image_y<0){
			output_control('纵向位置不能有其他字符！')
			return;
		}
		//图片宽度
		let Image_w = parseInt(document.getElementById('Image_w').value.toString())
		if(Image_w == ""|| Image_w == null){
			output_control('图片宽度不能为空！')
			return;
		}
		if(isNaN(Image_w) || Image_w<0){
			output_control('图片宽度不能有其他字符！')
			return;
		}
		//图片高度
		let Image_h = parseInt(document.getElementById('Image_h').value.toString())
		if(Image_h == ""|| Image_h == null){
			output_control('图片高度不能为空！')
			return;
		}
		if(isNaN(Image_h) || Image_h<0){
			output_control('图片高度不能有其他字符！')
			return;
		}
		
		//图片打印模式
		let Image_styleA = document.getElementById('Image_style')
		let Image_style = Image_styleA.options[Image_styleA.selectedIndex].text;
        let Image_style_let = null;
        switch(Image_style){
            case "误差扩撒":
                Image_style_let = 1;break;
			case "有序抖动":
                Image_style_let = 2;break;
			case "阈值":
                Image_style_let = 3;break;
        }

		//图片阈值
		let Image_Threshold = parseInt(document.getElementById('Image_Threshold').value.toString())
		if(Image_Threshold == ""|| Image_Threshold == null){
			output_control('图片阈值不能为空！')
			return;
		}
		if(isNaN(Image_Threshold) || Image_Threshold<0){
			output_control('图片阈值不能有其他字符！')
			return;
		}
		//图片内容
		let Image_data = document.getElementById('Image_data').value.toString()
		let radio = document.getElementsByName('emulator_command_model')
		if (radio != null) {
			if (radio.length > 0) {
				//ZPL打印
				if (radio[0].checked) { 
					let pack = {
						command: '_thermal_zpl_draw_print_',
						device : device,
						printdirectly : 'true',
						measurement_mode: 'mm',
						canvas_size : {"width": draw_w, "height": draw_h},
						get_base64_img : 'false',
						is_label: 'false',
						print: [
							{"func":"PDSetAddImageMode","iMode":Image_style_let,"iValueThreshold":Image_Threshold},
							{"func":"PDDrawImage","iX":Image_x,"iY":Image_y,"iWidth":Image_w, "iHeight":Image_h,"szImageFile":Image_data},
						]
					}
					
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					}
				}
			}
			if (radio.length > 1) {
				//TSPL打印
				if (radio[1].checked) { 
					let pack = {
						command: '_thermal_tspl_draw_print_',
						device : device,
						printdirectly : 'true',
						measurement_mode: 'mm',
						canvas_size : {"width": draw_w, "height": draw_h},
						get_base64_img : 'false',
						is_label: 'false',
						print: [
							{"func":"PDSetAddImageMode","iMode":Image_style_let,"iValueThreshold":Image_Threshold},
							{"func":"PDDrawImage","iX":Image_x,"iY":Image_y,"iWidth":Image_w, "iHeight":Image_h, "szImageFile":Image_data},
						]
					}
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					}  
				}
			}
			if (radio.length > 2) {
				//escpos打印
				if (radio[2].checked) { 
					let pack = {
						command: '_thermal_escpos_draw_print_',
						device : device,
						printdirectly : 'true',
						measurement_mode: 'mm',
						canvas_size : {"width": draw_w, "height": draw_h},
						get_base64_img : 'false',
						is_label: 'false',
						select_mode: '0',
						print: [
							{"func":"PDSetAddImageMode","iMode":Image_style_let,"iValueThreshold":Image_Threshold},
							{"func":"PDDrawImage","iX":Image_x,"iY":Image_y,"iWidth":Image_w, "iHeight":Image_h, "szImageFile":Image_data},
						]
					}
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					}  
				}
			}
		}
	}else {
			output_control('请先枚举设备后再操作！')
		}
}

//+----------------------------------------------------------------+
//指令打印
const _thermal_zpl_print_ = function(mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		//直接打印（ 打印:'true' / 预览:'false' ）
		let printdirectly = 'true'
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (printdirectly == 'true') {
				output_control('打印数据发送成功')
			} else {
				output_control(res.zpl)
			}
		}
		
		//张纸宽度
		let draw_w = parseInt(document.getElementById('draw_w').value.toString())
		if(draw_w == ""|| draw_w == null){
			output_control('张纸宽度不能为空！')
			return;
		}
		if(isNaN(draw_w) || draw_w<0){
			output_control('张纸宽度不能有其他字符！')
			return;
		}
		//页长
		let draw_h = parseInt(document.getElementById('draw_h').value.toString())
		if(draw_h == ""|| draw_h == null){
			output_control('页长不能为空！')
			return;
		}
		if(isNaN(draw_h) || draw_h<0){
			output_control('页长不能有其他字符！')
			return;
		}
		
		let radio = document.getElementsByName('emulator_command_model')
		if (radio != null) {
			if (radio.length > 0) {
				if (radio[0].checked) { 
					let font_name = 'L'
					let pack = {
						command: '_thermal_zpl_print_',
						device : device,
						printdirectly: printdirectly,
						measurement_mode: 'mm',
						//重定义ZPL数据头(否则默认为: '^SEE:GB18030.DAT^CI26^CWL,E:simsun.fnt^CFL' )
						print_head: '^SEE:GB18030.DAT^CI26^CW' + font_name + ',E:simsun.fnt^CF' + font_name,
						print  : []
					}
					
					//设置起始位置
					pack.print.push({ func: 'zpl_set_label_size', w: draw_w, l: draw_h })
					pack.print.push({ func: 'zpl_set_label_coordinate_orgin', x: 2, y: 2 })
					
					
					//打印文字
					pack.print.push({ func: 'zpl_set_field_pos', x: 2, y: 0 })
					pack.print.push({ func: 'zpl_text_settings', mode: 1, w: 3, h: 3, name: font_name })
					pack.print.push({ func: 'zpl_add_field_data', text: 'W你好XI', auto_fex: 1 })
					pack.print.push({ func: 'zpl_add_field_separator' })
					
					//打印pdf417
					pack.print.push({ func: 'zpl_set_field_pos', x: 2, y: 5 })
					pack.print.push({ func: 'zpl_add_pdf417', h: 3, s: 4, row: 4, col:8 })
					pack.print.push({ func: 'zpl_add_field_separator' })
					
					//打印code128
					pack.print.push({ func: 'zpl_set_field_pos', x: 2, y: 20 })
					pack.print.push({ func: 'zpl_add_codeX', mode: 'code128', h: 50, e: 1, f: 1, g: 0, m: 'N' })
					pack.print.push({ func: 'zpl_add_field_data', text: 'www.baidu.com', auto_fex: 1 })
					pack.print.push({ func: 'zpl_add_field_separator' })
					
					//打印qr code
					pack.print.push({ func: 'zpl_set_field_pos', x: 2, y: 25 })
					pack.print.push({ func: 'zpl_add_qr_code', c: 2, d: 'H', m: 'M', z: 'N', data: '二维码测试' })
					pack.print.push({ func: 'zpl_add_field_separator' })
					
					
					//打印base64图片
					let imageBase64 = 'data:image/png;base64,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'
					pack.print.push({ func: 'zpl_add_image', x: 2, y: 30, w: 10, h: 10, src_data: imageBase64 })
					
					//打印服务端上的图片
					//let imagePath = 'C:/Users/<USER>/Desktop/aaa/头像.bmp'
					//pack.print.push({ func: 'zpl_add_image', x: 10, y: 300, w: 100, h: 100, src_data: imagePath })
					 if (pack.print.length > 0) {
							on_link_device(pack, onback)
					}
				}
			}
			
			//tspl打印
			if (radio.length > 1) {
				if (radio[1].checked) { 
					let font_name = 'L'
					let pack = {
						command: '_thermal_tspl_print_',
						device : device,
						printdirectly: printdirectly,
						print  : []
					}
					
					
					//打印文字
					pack.print.push({ tspl: 'TEXT 50,0,\"TSS24.BF2\",0,2,2,\"横向简体中文打印演示\"' })
					
					//打印code128
					pack.print.push({ tspl: 'BARCODE 400,30,\"128\",100,1,0,2,2,\"DASCOM\"' })
					
					//打印qr code
					pack.print.push({ tspl: 'QRCODE 600,100,H,3,M,0,\"Babcdefghjk\"' })
					
					
					//打印base64图片
					pack.print.push({ tspl: 'BITMAP 50,100,240,110,0,data:image/png;base64,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' })
					
					//打印服务端上的图片
					//let imagePath = 'C:/Users/<USER>/Desktop/aaa/头像.bmp'
					//pack.print.push({ func: 'zpl_add_image', x: 10, y: 300, w: 100, h: 100, src_data: imagePath })
					 if (pack.print.length > 0) {
							on_link_device(pack, onback)
					}
				}
			}
			
			
			if (radio.length > 2) {
				if (radio[2].checked) { 
					let font_name = 'L'
					let pack = {
						command: '_thermal_escpos_print_',
						device : device,
						printdirectly: printdirectly,
						print  : []
					}
					
					//打印文字
					pack.print.push({ func: 'initialize_printer'})
					pack.print.push({ func: 'set_left_margin', n: 30})
					pack.print.push({ func: 'select_character_size', n: 16})
					pack.print.push({ func: 'insert_text_data', data: 'W你好XI'})
					pack.print.push({ func: 'print_and_return_to_standard_mode_in_page_mode' })
					pack.print.push({ func: 'print_and_feed_n_lines', n: 2})
					
					
					//打印barcode
					pack.print.push({ func: 'initialize_printer'})
					pack.print.push({ func: 'set_left_margin', n: 30})
					pack.print.push({ func: 'set_bar_code_width', n: 2})
					pack.print.push({ func: 'select_bar_code_height', n: 101})
					pack.print.push({ func: 'select_printing_position_for_HRI_characters', n: 2}) 
					pack.print.push({ func: 'print_bar_code_A', m: 73, n: 12,data: '7B41313233343536373839'})
					pack.print.push({ func: 'print_and_feed_n_lines', n: 2})

					
					//打印QRcode
					
					pack.print.push({ func: 'initialize_printer'})
					pack.print.push({ func: 'set_left_margin', n: 30})
					pack.print.push({ func: 'set_qr_code_mode', n: 50})
					pack.print.push({ func: 'set_qr_code_size', n: 5})
					pack.print.push({ func: 'set_qr_code_err_level', n: 48})
					pack.print.push({ func: 'write_qr_data', n: 9,data: '123456'})
					pack.print.push({ func: 'print_qr_code', n: 48})
					pack.print.push({ func: 'print_and_feed_n_lines', n: 2})

					pack.print.push({ func: 'initialize_printer'})
					pack.print.push({ func: 'set_left_margin', n: 10})
					//打印base64图片
					let imageBase64 = 'data:image/png;base64,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'
					pack.print.push({ func: 'print_raster_bit_image', m: '0', w: '200', h: '200', base64: imageBase64 })
					pack.print.push({ func: 'print_and_feed_n_lines', n: 2})
					
					//打印服务端上的图片
					//let imagePath = 'C:/Users/<USER>/Desktop/aaa/头像.bmp'
					//pack.print.push({ func: 'zpl_add_image', x: 10, y: 300, w: 100, h: 100, src_data: imagePath })
					 if (pack.print.length > 0) {
							on_link_device(pack, onback)
					}
				}
			}
			
			
			
		}	
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+
//绘图打印
const _thermal_zpl_draw_print_ = function(mode) {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		//直接打印（ 打印:'true' / 预览:'false' ）
		let printdirectly  = 'true'
		
		//获取预览图为base64数据（ base64:'true' / 路径:'false' ）
		let get_base64_img = 'false'
		
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (printdirectly == 'true') {
				output_control('绘图打印成功')
			} else {
				if (get_base64_img == 'true') {
					output_control(res.image_base64)
				} else {
					output_control(res.image_path)
				}
			}
		}
		
		//张纸宽度
		let draw_w = parseInt(document.getElementById('draw_w').value.toString())
		if(draw_w == ""|| draw_w == null){
			output_control('张纸宽度不能为空！')
			return;
		}
		if(isNaN(draw_w) || draw_w<0){
			output_control('张纸宽度不能有其他字符！')
			return;
		}
		//页长
		let draw_h = parseInt(document.getElementById('draw_h').value.toString())
		if(draw_h == ""|| draw_h == null){
			output_control('页长不能为空！')
			return;
		}
		if(isNaN(draw_h) || draw_h<0){
			output_control('页长不能有其他字符！')
			return;
		}
		let radio = document.getElementsByName('emulator_command_model')
		if (radio != null) {
			if (radio.length > 0) {
				if (radio[0].checked) { 
					let pack = {
						command: '_thermal_zpl_draw_print_',
						device : device,
						printdirectly: printdirectly,
						measurement_mode: 'mm',
						canvas_size: { "width": draw_w, "height": draw_h },
						is_label: _is_label_machine_(),
						print  : []
					}
					if (printdirectly == 'false') {
						pack.get_base64_img = get_base64_img
					}
					
					//设置字体（需要系统存在的字体）
					pack.print.push({ func: 'PDSetFont', szFontName: '宋体', fSize: 5 })
					
					//设置文本信息
					pack.print.push({ func: 'PDSetTextDecorate', iIsLandScape: 1, iIsReverseSequence: 0, iIsAutoLineFeed: 0, iIsLayDown: 0 })
					
					//绘制文字
					pack.print.push({ func: 'PDDrawTextW', iX: 34, iY: 15, iWidth: 50, iHeight: 30, wszText: '姓 名: 王𪚥军' })
					
					//绘制条形码
					pack.print.push({ func: 'PDDrawBarCode', iX: 20, iY: 22.5, iWidth: 29, iHeight: 4, szData: '350521199007228535' })
					
					//绘制二维码
					pack.print.push({ func: 'PDDrawQRCode', iX: 50, iY: 30, iWidth: 10, iHeight: 10, szData: '电子居民健康卡' })
					
					//设置图片阈值
					pack.print.push({ func: 'PDSetAddImageMode', iMode: 3, iValueThreshold: 150 })
					
					//绘制base64图片
					let imageBase64 = 'data:image/png;base64,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'
					pack.print.push({ func: 'PDDrawImage', iX: 4, iY: 34, iWidth: 10, iHeight: 10, szImageFile: imageBase64 })
					
					//绘制服务端上的图片
					//let imagePath = 'C:/Users/<USER>/Desktop/aaa/头像.bmp'
					//pack.print.push({ func: 'PDDrawImage', iX: 0, iY: 0, iWidth: 0, iHeight: 0, iIsSetNoAbsoluteBlack: 1, szImageFile: imagePath })
					
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					} 
				}
			}
			
			if (radio.length > 1) {
				if (radio[1].checked) { 
				let pack = {
						command: '_thermal_tspl_draw_print_',
						device : device,
						printdirectly: printdirectly,
						measurement_mode: 'mm',
						canvas_size: { "width": draw_w, "height": draw_h },
						is_label: _is_label_machine_(),
						print  : []
					}
					if (printdirectly == 'false') {
						pack.get_base64_img = get_base64_img
					}
					
					//设置字体（需要系统存在的字体）
					pack.print.push({ func: 'PDSetFont', szFontName: '宋体', fSize: 5 })
					
					//设置文本信息
					pack.print.push({ func: 'PDSetTextDecorate', iIsLandScape: 1, iIsReverseSequence: 0, iIsAutoLineFeed: 0, iIsLayDown: 0 })
					
					//绘制文字
					pack.print.push({ func: 'PDDrawTextW', iX: 34, iY: 15, iWidth: 50, iHeight: 30, wszText: '姓 名: 王𪚥军' })
					
					//绘制条形码
					pack.print.push({ func: 'PDDrawBarCode', iX: 20, iY: 22.5, iWidth: 29, iHeight: 4, szData: '350521199007228535' })
					
					//绘制二维码
					pack.print.push({ func: 'PDDrawQRCode', iX: 50, iY: 30, iWidth: 10, iHeight: 10, szData: '电子居民健康卡' })
					
					//设置图片阈值
					pack.print.push({ func: 'PDSetAddImageMode', iMode: 3, iValueThreshold: 150 })
					
					//绘制base64图片
					let imageBase64 = 'data:image/png;base64,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'
					pack.print.push({ func: 'PDDrawImage', iX: 4, iY: 34, iWidth: 10, iHeight: 10, szImageFile: imageBase64 })
					
					//绘制服务端上的图片
					//let imagePath = 'C:/Users/<USER>/Desktop/aaa/头像.bmp'
					//pack.print.push({ func: 'PDDrawImage', iX: 0, iY: 0, iWidth: 0, iHeight: 0, iIsSetNoAbsoluteBlack: 1, szImageFile: imagePath })
					
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					} 
				}
			}
			
			if (radio.length > 2) {
				if (radio[2].checked) { 
				let pack = {
						command: '_thermal_escpos_draw_print_',
						device : device,
						printdirectly: printdirectly,
						measurement_mode: 'mm',
						canvas_size: { "width": draw_w, "height": draw_h },
						is_label: _is_label_machine_(),
						select_mode: '0',
						print  : []
					}
					if (printdirectly == 'false') {
						pack.get_base64_img = get_base64_img
					}
					
					//设置字体（需要系统存在的字体）
					pack.print.push({ func: 'PDSetFont', szFontName: '宋体', fSize: 5 })
					
					//设置文本信息
					pack.print.push({ func: 'PDSetTextDecorate', iIsLandScape: 1, iIsReverseSequence: 0, iIsAutoLineFeed: 0, iIsLayDown: 0 })
					
					//绘制文字
					pack.print.push({ func: 'PDDrawTextW', iX: 10, iY: 15, iWidth: 50, iHeight: 30, wszText: '姓 名: 王𪚥军' })
					
					//绘制条形码
					pack.print.push({ func: 'PDDrawBarCode', iX: 10, iY: 22.5, iWidth: 29, iHeight: 4, szData: '350521199007228535' })
					
					//绘制二维码
					pack.print.push({ func: 'PDDrawQRCode', iX: 20, iY: 30, iWidth: 10, iHeight: 10, szData: '电子居民健康卡' })
					
					//设置图片阈值
					pack.print.push({ func: 'PDSetAddImageMode', iMode: 3, iValueThreshold: 150 })
					
					//绘制base64图片
					let imageBase64 = 'data:image/png;base64,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'
					pack.print.push({ func: 'PDDrawImage', iX: 4, iY: 34, iWidth: 10, iHeight: 10, szImageFile: imageBase64 })
					
					//绘制服务端上的图片
					//let imagePath = 'C:/Users/<USER>/Desktop/aaa/头像.bmp'
					//pack.print.push({ func: 'PDDrawImage', iX: 0, iY: 0, iWidth: 0, iHeight: 0, iIsSetNoAbsoluteBlack: 1, szImageFile: imagePath })
					
					if (pack.print.length > 0) {
						on_link_device(pack, onback)
					} 
				}
			}
			
			
			
		}
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+
//PDF打印
const _thermal_pdf_print_ = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (res.lose_font != null) {
				output_control('缺少字体配置:' + JSON.stringify(res.lose_font));
			}
			if (res.success_page_ids != null) {
				output_control('已打印完成的序号:' + JSON.stringify(res.success_page_ids));
			}
		}
		
		let pdf_file = document.getElementById('pdf_file').value.toString()
		let first_page = parseInt(document.getElementById('first_page').value.toString())
		if(first_page == ""|| first_page == null){
			output_control('数值不能为空！')
			return;
		}
		if(isNaN(first_page) || first_page<0){
			output_control('数值不能有其他字符！')
			return;
		}
		let last_page = parseInt(document.getElementById('last_page').value.toString())
		if(last_page == ""|| last_page == null){
			output_control('数值不能为空！')
			return;
		}
		if(isNaN(last_page) || last_page<0){
			output_control('数值不能有其他字符！')
			return;
		}
		let pack = {
			command: '_thermal_pdf_print_',
			device : device,
			measurement_mode: 'pixel',
			is_label: _is_label_machine_(),
			data: {
				src_data: pdf_file,
				mode: 3,
				threshold: 150,
				first_page: first_page,
				last_page: last_page,
				width: 0,
				height: 0,
				rotate: 0,
				multiple: 1.2
			}
		}
		on_link_device(pack, onback)
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+

//+----------------------------------------------------------------+
//完整EPC打印测试流程
const _do_technological_process_ = function() {
	//先问状态，写EPC，发打印内容，查询状态（OK继续，唔OK报错跳出），直到打印完成，检索标签，Ok就读取EPC（完结），唔OK报错
	let epc_sn = 10000000
	
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let test_times = document.getElementById('test_times').value
		let ii = 0
		
		let _thermal_rfid_get_status_ = { command: "_thermal_cmd_engine_controler_", device : device, func   : "thermal_rfid_get_status" }
		
		let _thermal_rfid_write_epc_ = {
			command: "_thermal_cmd_engine_controler_", device : device, func: 'rfid_dynmic_write_epc',
			data   : 'wait change...' }

		let _thermal_zpl_print_ = {
			command: '_thermal_zpl_print_', device : device, printdirectly: 'true',
			print  : []
		}
		
		let _thermal_get_label_count_ = {
			command: "_thermal_cmd_engine_controler_", device : device, func   : "rfid_dynmic_get_label_count",
		}
		
		
		let _thermal_rfid_read_epc_ = {
			command: "_thermal_cmd_engine_controler_", device : device, func: 'rfid_dynmic_read_epc',
		}
		
		let now_step = ''
		let getstatus_number = 1;
		let getstatus_numberA = 1;
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (now_step == '_thermal_rfid_get_status_' + 'start') {
				let isok = true
				let data = _hex2bin(res.recv_data)
				let byte1 = data[0], byte2 = data[1], byte3 = data[2], byte4 = data[3], byte5 = data[24], byte6 = data[25];
				if(((byte1 & 0x02) == 0x02) || ((byte1 & 0x20) == 0x20) || ((byte1 & 0x40) == 0x40)){ isok = false}
				if(((byte3 & 0x40) == 0x40)|| ((byte3 & 0x01) == 0x01) || ((byte3 & 0x02) == 0x02)){ isok = false}
				if(((byte6 & 0x01) == 0x01) || ((byte6 & 0x02) == 0x02) || ((byte6 & 0x10) == 0x10)){ isok = false}
				if (isok) {
					if (((byte1 & 0x80) == 0x80) || ((byte2 & 0x08) == 0x08)) { isok = false }
					if (isok) {
						output_control('设备正常，流程继续执行。。。')
					    now_step = '_thermal_rfid_write_epc_'
					    epc_sn++; _thermal_rfid_write_epc_.data = epc_sn.toString()
					    on_link_device(_thermal_rfid_write_epc_, onback)
					}else{
						getstatus_number++
						setTimeout(function (){
							on_link_device(_thermal_rfid_get_status_, onback)
						}, 1000)
						if(getstatus_number == 20){
							output_control('设备获取状态次数超过最大值，请检查打印机状态是否正常')
						}
					}
				} else {
					output_control('设备状态异常，无法继续流程:' + JSON.stringify(res.state))
				}
				sleep(200);
				return
			}
			if (now_step == '_thermal_rfid_get_status_' + 'step') {
				let isok = true
				let data = _hex2bin(res.recv_data)
				let byte1 = data[0], byte2 = data[1], byte3 = data[2], byte4 = data[3], byte5 = data[24], byte6 = data[25];
				if(((byte1 & 0x02) == 0x02) ||((byte1 & 0x40) == 0x40) || ((byte3 & 0x01) == 0x01) || ((byte3 & 0x02) == 0x02)){ isok = false}
				if(((byte3 & 0x40) == 0x40)){ isok = false}
				if(((byte6 & 0x01) == 0x01) || ((byte6 & 0x02) == 0x02) || ((byte6 & 0x10) == 0x10)){ isok = false}
				
				if (isok) {
					if (((byte1 & 0x80) == 0x80) || ((byte2 & 0x08) == 0x08) || ((byte5 & 0x08) == 0x08)) { isok = false }
					if (isok) {
						output_control('设备正常，流程继续执行。。。')
						now_step = '_thermal_get_label_count_'
						on_link_device(_thermal_get_label_count_,  onback)
					} else {
						getstatus_numberA++
						setTimeout(function (){
							on_link_device(_thermal_rfid_get_status_, onback)
						}, 1000)
						if(getstatus_numberA == 20){
							output_control('设备获取状态次数超过最大值，请检查打印机状态是否正常')
						}
					}
				} else {
					output_control('设备状态异常，无法继续流程:' + JSON.stringify(res.state))
				}
				sleep(200);
				return
			}
			if (now_step == '_thermal_rfid_write_epc_') {
				output_control('写入EPC数据成功：' + epc_sn.toString())
				_thermal_zpl_print_.print = []
				_thermal_zpl_print_.print.push({ func: 'zpl_set_label_coordinate_orgin', x: 10, y: 10 })
				_thermal_zpl_print_.print.push({ func: 'zpl_set_field_pos', x: 100, y: 0 })
				_thermal_zpl_print_.print.push({ func: 'zpl_text_settings', mode: 1, w: 30, h: 30, name: 'L' })
				_thermal_zpl_print_.print.push({ func: 'zpl_add_field_data', text: 'EPC数据内容：' + epc_sn.toString(), auto_fex: 1 })
				_thermal_zpl_print_.print.push({ func: 'zpl_add_field_separator' })
				now_step = '_thermal_zpl_print_'
				on_link_device(_thermal_zpl_print_, onback)
				sleep(200);
				return
			}
			if (now_step == '_thermal_zpl_print_') {
				output_control('打印数据成功！')
				now_step = '_thermal_rfid_get_status_' + 'step'
				on_link_device(_thermal_rfid_get_status_, onback)
				sleep(200);
				return
			}
			if (now_step == '_thermal_get_label_count_') {
				output_control('检索到打印完成的标签个数：' + res.recv_data)
				if (res.recv_data == 01) {
					now_step = '_thermal_rfid_read_epc_'
					on_link_device(_thermal_rfid_read_epc_, onback)
				}
				sleep(200);
				return
			}
			if (now_step == '_thermal_rfid_read_epc_') {
				output_control('读取到的EPC内容：' + res.recv_data)
				output_control('本次测试流程结束！'); output_control('')
				
				if ((++ii) < test_times) {
					now_step = '_thermal_rfid_get_status_' + 'start'
					on_link_device(_thermal_rfid_get_status_, onback)
				}
				sleep(200);
				return
			}
		}
		now_step = '_thermal_rfid_get_status_' + 'start'
		on_link_device(_thermal_rfid_get_status_, onback)
		
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+

//+----------------------------------------------------------------+
//完整扫描打印测试流程
const _do_scan_technological_process_ = function() {
	//先问状态，写EPC，发打印内容，查询状态（OK继续，唔OK报错跳出），直到打印完成，检索标签，Ok就读取EPC（完结），唔OK报错
	let epc_sn = 10000000
	
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let test_times = document.getElementById('test_times').value
		let ii = 0
		
		let _thermal_get_printer_status_ = { command: "_thermal_cmd_engine_controler_", device : device, func   : "thermal_get_printer_status" }
		
		let _scan_get_data_ = {
			command: "_thermal_cmd_engine_controler_", device : device, func   : "scan_get_data",
		}

		let _thermal_zpl_print_ = {
			command: '_thermal_zpl_print_', device : device, printdirectly: 'true',
			print  : []
		}
		
		
		let now_step = ''
		let scannumber = 0;
		let getstatus_number = 1;
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			if (now_step == '_thermal_get_printer_status_' + 'start') {
				let isok = true
				let data = _hex2bin(res.recv_data)
				let byte1 = data[0], byte2 = data[1], byte3 = data[2], byte4 = data[3];
				if(((byte1 & 0x02) == 0x02) || ((byte1 & 0x20) == 0x20) || ((byte1 & 0x40) == 0x40)){ isok = false}
				if(((byte3 & 0x40) == 0x40)|| ((byte3 & 0x01) == 0x01) || ((byte3 & 0x02) == 0x02)){ isok = false}
				if (isok) 
				{
					if (((byte1 & 0x80) == 0x80) || ((byte2 & 0x08) == 0x08)) { isok = false }
					if (isok) 
					{
						output_control('设备正常，流程继续执行。。。')
					    now_step = '_scan_get_data_'
						on_link_device(_scan_get_data_,  onback)
						sleep(200);
				        return
					}
					else
					{
						getstatus_number++
						setTimeout(function (){
							on_link_device(_thermal_get_printer_status_, onback)
						}, 1000)
						if(getstatus_number == 20){
							output_control('设备获取状态次数超过最大值，请检查打印机状态是否正常')
						}
					}
				} 
				else 
				{
					output_control('设备状态异常，无法继续流程:' + JSON.stringify(res.state))
					sleep(200);
				    return
				}
				
			}
			if (now_step == '_scan_get_data_') {
				if (((res.recv_data).length) > 12) 
				{
					epc_sn = res.recv_data;
					output_control('获取扫描内容：' + res.recv_data)
					now_step = '_thermal_zpl_print_'
					on_link_device(_thermal_zpl_print_, onback)
					sleep(200);
				    return
				}
				else
				{
					++scannumber;
					if(3 ==  scannumber)
					{
						now_step = ''
					    output_control('获取扫描内容失败')
						sleep(200);
						return
					}
					else
					{
						setTimeout(function (){
						on_link_device(_scan_get_data_, onback)}, 1000)
					}
				}
			}
			if (now_step == '_thermal_zpl_print_') {
				_thermal_zpl_print_.print = []
				_thermal_zpl_print_.print.push({ func: 'zpl_set_label_coordinate_orgin', x: 10, y: 10 })
				_thermal_zpl_print_.print.push({ func: 'zpl_set_field_pos', x: 100, y: 0 })
				_thermal_zpl_print_.print.push({ func: 'zpl_text_settings', mode: 1, w: 30, h: 30, name: 'L' })
				_thermal_zpl_print_.print.push({ func: 'zpl_add_field_data', text: 'EPC数据内容：' + epc_sn.toString(), auto_fex: 1 })
				_thermal_zpl_print_.print.push({ func: 'zpl_add_field_separator' })
				on_link_device(_thermal_zpl_print_, onback)
				output_control('打印数据成功！')
				output_control('本次测试流程结束！'); output_control('')
				
				if ((++ii) < test_times) {
					now_step = '_thermal_get_printer_status_' + 'start'
					on_link_device(_thermal_get_printer_status_, onback)
				}
				else{
					now_step = ''
					sleep(200);
				    return
				}
			}
		}
		now_step = '_thermal_get_printer_status_' + 'start'
		on_link_device(_thermal_get_printer_status_, onback)
		
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+

//+----------------------------------------------------------------+
//(泛用型JSON指令接口)
const _thermal_controler_api_ = function() {
	let device = document.getElementById('enum_list').value
	if (device != '') {
		let onback = function(isok, res) {
			if (!isok) { output_control('通讯失败：' + JSON.stringify(res)); return }
			if (res.code !== 0) { output_control(res.msg); return }
			output_control(JSON.stringify(res))
			
			//十六进制字符串转二进制数据
			let data = _hex2bin(res.recv_data)
			let dataleng = data.length;
			let byte1 = data[dataleng-4], byte2 = data[dataleng-3], byte3 = data[dataleng-2], byte4 = data[dataleng-1];
			
			//二进制数据转十六进制字符串
			output_control(_bin2hex(data))
			
			if ((byte1 & 0x02) == 0x02) {
				output_control('卡纸')
			}
			if ((byte1 & 0x10) != 0x10) {
				output_control('非联机')
			}
			if ((byte1 & 0x20) == 0x20) {
				output_control('缺纸')
			}
			if ((byte1 & 0x40) == 0x40) {
				output_control('切刀出错')
			}
			if ((byte1 & 0x80) == 0x80) {
				output_control('设备忙')
			}
			if ((byte2 & 0x08) == 0x08) {
				output_control('缓冲非空')
			}
			if ((byte3 & 0x01) == 0x01) {
				output_control('碳带出错')
			}
			if ((byte3 & 0x02) == 0x02) {
				output_control('打印头抬起')
			}
						
			if ((byte3 & 0x04) == 0x04) {
				output_control('标签纸')
			}
			else {
				output_control('连续纸')
			}
						
			if ((byte3 & 0x08) == 0x08) {
				output_control('热敏')
			}
			else {
				output_control('热转印')
			}
						
			if ((byte3 & 0x10) == 0x10) {
				output_control('切刀开')
			}
			else {
				output_control('切刀关')
			}
						
			if ((byte3 & 0x20) == 0x20) {
				output_control('电源复位')
			}
			if ((byte3 & 0x40) == 0x40) {
				output_control('打印头过热')
			}
			/*			
			if ((byte5 & 0x02) == 0x02) {
				output_control('打印完成')
			}
			if ((byte5 & 0x08) == 0x08) {
				output_control('打印机忙')
			}
			if ((byte5 & 0x20) == 0x20) {
				output_control('印头抬起出错')
			}
			if ((byte5 & 0x80) == 0x80) {
				output_control('切纸完成')
			}
						
			if ((byte6 & 0x01) == 0x01) {
				output_control('RFID报错')
			}
			if ((byte6 & 0x02) == 0x02) {
				output_control('打印机暂停')
			}
			if ((byte6 & 0x04) == 0x04) {
				output_control('RFID操作完成')
			}
			if ((byte6 & 0x10) == 0x10) {
				output_control('重试作废流程进行中')
			}
			if ((byte6 & 0x40) == 0x40) {
				output_control('rfid自动校验进行中')
			}
			if ((byte6 & 0x80) == 0x80) {
				output_control('rfid自定义指令进行中')
			}
			*/
		}
		//接口详情使用，可参考thermal_web_api.txt文件
		let pack = {
			command: "_thermal_cmd_engine_controler_",
			device : device,
			func   : "thermal_get_printer_status"
		}
		
		on_link_device(pack, onback)
	} else {
		output_control('请先枚举设备后再操作！')
	}
}
//+----------------------------------------------------------------+
