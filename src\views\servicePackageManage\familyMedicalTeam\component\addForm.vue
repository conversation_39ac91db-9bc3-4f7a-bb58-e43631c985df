<template>
  <div class="add-form">
    <div style="display: flex; justify-content: space-between; align-items: center">
      <flag-component title="团队信息" style="margin-bottom: 10px" />
      <el-button v-if="type !== 'view'" type="text" style="font-size: 16px" @click="handleConfirm">保存</el-button>
    </div>
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="团队名称：" prop="teamName">
            <el-input v-model="form.teamName" placeholder="请输入团队名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属机构：" prop="departCode">
            <TreeSelect
              v-model="form.departCode"
              :data="departTree"
              :props="{
                children: 'children',
                label: 'departName',
                value: 'departCode'
              }"
              placeholder="请选择"
              @change="handleDepartChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="团队简介：" prop="teamDesc">
            <el-input v-model="form.teamDesc" type="textarea" placeholder="请输入团队简介" />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <flag-component title="团队医生" style="margin-bottom: 10px" />
    <el-button
      v-if="type !== 'view'"
      type="primary"
      :disabled="!id"
      style="margin-bottom: 10px"
      @click="handleAddDoctor"
    >
      新增医生
    </el-button>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column align="center" prop="teamRole" label="担任角色">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.teamRole === 0" type="success">队长</el-tag>
          <el-tag v-else-if="scope.row.teamRole === 1" type="warning">副队长</el-tag>
          <el-tag v-else-if="scope.row.teamRole === 2" type="primary">专家</el-tag>
          <el-tag v-else-if="scope.row.teamRole === 3" type="info">医生</el-tag>
          <el-tag v-else-if="scope.row.teamRole === 4" type="danger">护士</el-tag>
          <el-tag v-else-if="scope.row.teamRole === 5" type="success">健康管家</el-tag>
          <el-tag v-else-if="scope.row.teamRole === 6" type="warning">其他</el-tag>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="doctorName" label="医生姓名" />
      <el-table-column align="center" prop="originalPhone" label="联系电话" />
      <el-table-column align="center" prop="departName" label="所属机构" />
      <el-table-column align="center" prop="operation" label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click="handleAppointRole(scope.row)">任命角色</el-button>
          <el-button type="text" @click="handleAppointCaptain(scope.row)">任命队长</el-button>
          <el-button type="text" style="color: red" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增医生弹窗 -->
    <ProDialog :visible.sync="addDoctorDialogVisible" title="新增医生" width="900px" :append-to-body="true">
      <AddDoctorForm ref="addDoctorForm" @selection-change="handleSelectionChange" />
      <template #footer>
        <el-button @click="handleCancel">关闭</el-button>
        <el-button type="primary" @click="handleAddDoctorConfirm">确认</el-button>
      </template>
    </ProDialog>

    <!-- 任命角色弹窗 -->
    <ProDialog :visible.sync="appointRoleDialogVisible" title="任命角色" width="500px" :append-to-body="true">
      <el-form ref="roleFormRef" :model="roleForm" :rules="roleRules" label-width="100px">
        <el-form-item label="担任角色：" prop="role">
          <el-select v-model="roleForm.role" placeholder="请选择">
            <el-option label="副队长" :value="1" />
            <el-option label="专家" :value="2" />
            <el-option label="医生" :value="3" />
            <el-option label="护士" :value="4" />
            <el-option label="健康管家" :value="5" />
            <el-option label="其他" :value="6" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="appointRoleDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleAppointRoleConfirm">确认</el-button>
      </template>
    </ProDialog>
  </div>
</template>
<script>
import {
  saveFamilyMedicalTeam,
  addFamilyMedicalTeamDoctor,
  getFamilyMedicalTeamDetail,
  appointFamilyMedicalTeamLeader,
  appointFamilyMedicalTeamRole,
  deleteFamilyMedicalTeamDoctor
} from '@/api/familyMedicalTeam'
import { localCache } from '@/utils/cache'
import FlagComponent from '@/components/flagComponent/index.vue'
import TreeSelect from '@/components/TreeSelect/index.vue'
import ProDialog from '@/components/ProDialog/index.vue'
import AddDoctorForm from './addDoctorForm.vue'

export default {
  name: 'AddForm',
  components: {
    FlagComponent,
    TreeSelect,
    ProDialog,
    AddDoctorForm
  },
  props: {
    departTree: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      addDoctorDialogVisible: false,
      tableData: [],
      id: '',
      selectedRows: [],
      form: {
        teamName: '',
        departCode: localCache.getCache('userInfo').departCode || '',
        teamDesc: ''
      },
      rules: {
        teamName: [{ required: true, message: '请输入团队名称', trigger: 'blur' }],
        departCode: [{ required: true, message: '请选择所属机构', trigger: 'change' }]
      },
      roleForm: {
        id: '',
        role: ''
      },
      roleRules: {
        role: [{ required: true, message: '请选择担任角色', trigger: 'change' }]
      },
      appointRoleDialogVisible: false,
      type: ''
    }
  },
  methods: {
    // 查看
    handleDetail(row, type) {
      this.type = type
      if (type === 'add') {
        return
      }
      getFamilyMedicalTeamDetail({ id: row.id }).then((res) => {
        if (res.code === 200) {
          this.form = {
            teamName: res.data.teamName,
            departCode: res.data.departCode,
            teamDesc: res.data.teamDesc
          }
          this.id = res.data.id
          this.tableData = res.data.doctorList || []
        }
      })
    },

    // 新增医生
    handleAddDoctor() {
      this.addDoctorDialogVisible = true
    },

    // 任命角色
    handleAppointRole(row) {
      this.roleForm.id = row.id
      this.appointRoleDialogVisible = true
    },

    // 任命角色确认
    handleAppointRoleConfirm() {
      const params = {
        id: this.roleForm.id,
        role: this.roleForm.role
      }
      appointFamilyMedicalTeamRole(params).then((res) => {
        if (res.code === 200) {
          this.$message.success('任命角色成功')
          this.appointRoleDialogVisible = false
          this.handleDetail({ id: this.id })
        }
      })
    },

    // 任命队长
    async handleAppointCaptain(row) {
      this.$confirm('确定任命该医生为队长吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        appointFamilyMedicalTeamLeader({ id: row.id })
          .then((res) => {
            if (res.code === 200) {
              this.$message.success('任命队长成功')
              this.handleDetail({ id: this.id })
            }
          })
          .catch(() => {
            this.$message.info('取消任命')
          })
      })
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确定删除该医生吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteFamilyMedicalTeamDoctor({ id: row.id })
          .then((res) => {
            if (res.code === 200) {
              this.$message.success('删除医生成功')
              this.handleDetail({ id: this.id })
            }
          })
          .catch(() => {
            this.$message.info('取消删除')
          })
      })
    },

    // 新增医生关闭
    handleCancel() {
      this.addDoctorDialogVisible = false
    },

    // 新增医生选择
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },

    // 团队信息保存
    async handleConfirm() {
      const valid = await this.$refs.formRef.validate()
      if (valid) {
        const params = {
          ...this.form,
          id: this.id
        }
        saveFamilyMedicalTeam(params).then((res) => {
          if (res.code === 200) {
            this.$message.success('团队信息保存成功')
            this.id = res.data
            this.$emit('refresh')
          }
        })
      }
    },

    // 新增医生保存
    async handleAddDoctorConfirm() {
      const params = {
        id: this.id,
        userIdList: this.selectedRows.map((item) => item.id)
      }
      addFamilyMedicalTeamDoctor(params).then((res) => {
        if (res.code === 200) {
          this.$message.success('新增医生成功')
          this.addDoctorDialogVisible = false
          this.handleDetail({ id: this.id })
        }
      })
    },

    // 机构选择
    handleDepartChange(value) {
      if (this.$refs.addDoctorForm) {
        this.$refs.addDoctorForm.queryParams.departCode = value
        this.$nextTick(() => {
          this.$refs.addDoctorForm.fetchData()
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped></style>
