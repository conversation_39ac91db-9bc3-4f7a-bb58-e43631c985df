<template>
  <el-select v-model="selectedValue" style="width: 100%; margin: 16px 0" placeholder="请选择" @change="handleChange">
    <el-option v-for="option in options" :key="option.value" :label="option.label" :value="option.value" />
  </el-select>
</template>

<script>
import dayjs from 'dayjs'

export default {
  name: 'YearSelect',
  data() {
    return {
      selectedValue: this.getDefaultValue() // 组件内部管理默认值
    }
  },
  computed: {
    options() {
      const currentYear = dayjs().year()
      const years = [currentYear - 1, currentYear] // 仅包含当前年和上一年

      return years.flatMap((year) => [
        { label: `${year}上半年`, value: `${year}-01-01,${year}-06-30` },
        { label: `${year}下半年`, value: `${year}-07-01,${year}-12-31` },
        { label: `${year}全年`, value: `${year}-01-01,${year}-12-31` }
      ])
    }
  },
  methods: {
    getDefaultValue() {
      const year = dayjs().year()
      return `${year}-01-01,${year}-12-31` // 默认选中当前年全年
    },
    handleChange(value) {
      this.selectedValue = value
      this.$emit('update', value)
    }
  }
}
</script>
