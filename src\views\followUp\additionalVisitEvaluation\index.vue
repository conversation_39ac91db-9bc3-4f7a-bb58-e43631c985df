<template>
  <div class="additional-visit-evaluation">
    <el-card class="search-card">
      <el-form :model="queryParams" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="医疗机构：" prop="depart">
              <TreeSelect
                v-model="queryParams.departCode"
                :data="departTree"
                :props="{
                  children: 'children',
                  label: 'departName',
                  value: 'departCode'
                }"
                placeholder="请选择"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="姓名/身份证：">
              <el-input v-model="queryParams.keyword" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="处理状态：">
              <el-select v-model="queryParams.auditStatus" placeholder="请选择" clearable style="width: 100%">
                <el-option label="未处理" :value="2" />
                <el-option label="已处理" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="table-card">
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 50px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #disease="{ row }">
          <ChronicDiseaseType :record="row" />
        </template>

        <template #sex="{ row }">
          <span>{{ genderTransform(row.sex) }}</span>
        </template>

        <template #idCard="{ row }">
          <EncryptionStr :cipher-text="row.idCard" :replace="row.idCardReplace" />
        </template>

        <template #phone="{ row }">
          <EncryptionStr :cipher-text="row.phone" :replace="row.phoneReplace" />
        </template>

        <template #type="{ row }">
          <span>{{
            row.type === 1 ? '电话随访' : row.type === 2 ? '上门随访' : row.type === 3 ? '诊室随访' : ''
          }}</span>
        </template>

        <template #auditResult="{ row }">
          <el-tag v-if="row.auditResult === 1" type="success">稳定</el-tag>
          <el-tag v-else-if="row.auditResult === 2" type="warning">异常</el-tag>
        </template>

        <template #operation="{ row }">
          <el-button v-if="row.auditResult" type="text" size="small" @click="handleOperation(row, 'view')">
            查看
          </el-button>
          <el-button v-else type="text" size="small" @click="handleOperation(row, 'evaluation')">评估</el-button>
        </template>
      </base-table>
    </el-card>
  </div>
</template>

<script>
import { getFollowUpList } from '@/api/followUp'
import { getOrgTreeByIdApi } from '@/api/system'
import { getUserId } from '@/utils/auth'
import { genderTransform } from '@/utils/cspUtils'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import TreeSelect from '@/components/TreeSelect/index.vue'
import ChronicDiseaseType from '@/components/chronicDiseaseType/index.vue'
import EncryptionStr from '@/components/encryptionStr/index.vue'

export default {
  name: 'AdditionalVisitEvaluation',
  components: {
    BaseTable,
    TreeSelect,
    ChronicDiseaseType,
    EncryptionStr
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        departCode: '',
        auditStatus: 2,
        keyword: ''
      },
      departTree: [],
      columns: [
        { label: '姓名', prop: 'patientName', width: 120 },
        { label: '慢病病种', prop: 'disease', slot: 'disease', width: 160 },
        { label: '年龄', prop: 'age' },
        { label: '性别', prop: 'sex', slot: 'sex' },
        { label: '身份证号', prop: 'idCard', slot: 'idCard', width: 180 },
        { label: '手机号码', prop: 'phone', slot: 'phone', width: 140 },
        { label: '机构名称', prop: 'departName', width: 150 },
        { label: '随访方式', prop: 'type', slot: 'type' },
        { label: '计划下次随访日期', prop: 'planDate', width: 150 },
        { label: '随访日期', prop: 'realDate', width: 130 },
        { label: '随访医生', prop: 'doctorName' },
        { label: '责任医生', prop: 'manageDoctorName' },
        { label: '评估状态', prop: 'auditResult', slot: 'auditResult' },
        { label: '操作', prop: 'operation', slot: 'operation', fixed: 'right' }
      ]
    }
  },
  created() {
    this.getDepartTree()
  },
  methods: {
    genderTransform,
    // 机构树
    async getDepartTree() {
      const res = await getOrgTreeByIdApi({
        patientId: getUserId()
      })
      this.departTree = res.data
    },

    async getTableList(params) {
      return await getFollowUpList({
        ...params,
        status: 5
      })
    },

    handleOperation(row, type) {
      this.$router.push({
        path: '/followUp/additionalVisitEvaluation/detail',
        query: { id: row.id, type }
      })
    },

    handleReset() {
      this.queryParams = {
        departCode: '',
        auditStatus: 2,
        keyword: '',
        pageNo: 1,
        pageSize: 20
      }
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.additional-visit-evaluation {
  height: 100%;
  display: flex;
  flex-direction: column;

  .search-card {
    margin: 16px;
    height: 77px;
  }
  .table-card {
    flex: 1;
    margin: 0 16px;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
}
</style>
