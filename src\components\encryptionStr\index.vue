<template>
  <span class="encryptionStr">
    {{ show ? newReplace : replace }}
    <template v-if="replace && showEye">
      <svg-icon v-if="show" class="icon_eye_comment" icon-class="eye-open" @click="onCloseReplace" />
      <svg-icon v-else class="icon_eye_comment" icon-class="eye-close" @click="onShowReplace" />
    </template>
    <template v-if="!replace"> - / - </template>
  </span>
</template>

<script>
export default {
  name: 'EncryptionStr',
  props: {
    // 密文
    replace: {
      type: String,
      default: ''
    },
    // 密文参数
    cipherText: {
      type: String,
      default: ''
    },
    // 是否显示眼睛
    showEye: {
      type: <PERSON>olean,
      default: true
    }
  },
  data() {
    return {
      show: false,
      newReplace: '' // 原文
    }
  },
  methods: {
    // 显示原文
    onShowReplace() {
      const self = this
      self
        .$http({
          url: '/cspapi/backend/param/decrypt',
          method: 'get',
          params: { cipherText: self.cipherText }
        })
        .then((result) => {
          // console.log('result--result', result);
          if (result.code === 200) {
            self.show = true
            self.newReplace = result.data.originalNumber
          }
        })
    },
    // 显示密文
    onCloseReplace() {
      const self = this
      setTimeout(() => {
        self.show = false
        self.newReplace = ''
      }, 100)
    }
  }
}
</script>
<style lang="scss" scoped>
.encryptionStr {
  padding-right: 1.3021vw;
  position: relative;
}

.svg-icon.icon_eye_comment {
  width: 0.8rem;
  height: 0.8rem;
  cursor: pointer;
  position: absolute;
  top: 50%;
  right: -0.5rem;
  transform: translate3d(0, -50%, 0);
  -webkit-transform: translate3d(0, -50%, 0);
  -ms-transform: translate3d(0, -50%, 0);
}
</style>
