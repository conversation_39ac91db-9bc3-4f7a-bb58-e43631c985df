<!-- 病情诊断-去转诊 -->
<template>
  <div class="referral">
    <el-form :model="form" label-width="120px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="患者姓名：">
            <el-input v-model="form.patientName" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="手机号：">
            <el-input v-model="form.patientPhoneReplace" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="身份证：">
            <el-input v-model="form.patientIdcard" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="性别：">
            <el-input v-model="form.patientSexStr" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="年龄：">
            <el-input v-model="form.patientAge" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="住址：">
            <el-input v-model="form.patientAddress" />
          </el-form-item>
        </el-col>
      </el-row>
      <flag-component title="转诊信息" />
      <el-row>
        <el-col :span="6">
          <el-form-item label="申请类型：">
            <el-select v-model="form.referralType" style="width: 100%">
              <el-option label="上转" value="1" />
              <el-option label="下转" value="2" />
              <el-option label="同转" value="3" />
              <el-option label="回转" value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="转入单位：">
            <el-input />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="接诊医生：">
            <el-input />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="转入时间：">
            <el-date-picker
              v-model="form.referralTime"
              type="datetime"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              placeholder="请选择转入时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="转出原因：">
            <el-select v-model="form.outReason" style="width: 100%">
              <el-option label="病情康复" value="1" />
              <el-option label="病情稳定" value="2" />
              <el-option label="患者意愿" value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="住院病案号：">
            <el-input v-model="form.hosPin" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="诊断结果：">
            <el-input v-model="form.diagnosisResult" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="转诊医生：">
            <el-input v-model="form.outDoctorName" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="医生电话：">
            <el-input v-model="form.outDoctorPhone" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="转出单位：">
            <el-input v-model="form.outUnit" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="主要检查结果：">
            <el-input v-model="form.mainCheck" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="治疗经过，下一步治疗方案及康复建议：">
            <el-input v-model="form.treatmentProcess" type="textarea" :rows="2" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <!-- <ProUpload v-model="form.fileList" /> -->
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import FlagComponent from '@/components/flagComponent/index.vue'

export default {
  name: 'Referral',
  components: { FlagComponent },
  data() {
    return {
      form: {}
    }
  }
}
</script>

<style lang="scss" scoped></style>
