<!-- 治疗动作 -->
<template>
  <div v-loading="$store.state.managePatient.loading" class="therapeutic-action">
    <el-tabs
      v-model="activeTab"
      type="card"
      style="padding: 16px"
      addable
      :closable="showTabList.length > 1"
      @edit="handleTabsEdit"
    >
      <el-tab-pane v-for="item in showTabList" :key="item.value" :label="item.label" :name="item.value">
        <component :is="item.component" :ref="`${item.component}Ref`" :item-temp="item" />
      </el-tab-pane>
    </el-tabs>

    <ProDialog ref="proDialog" title="添加项目" :visible.sync="dialogVisible" width="500px" top="30vh">
      <div class="pro-dialog-content">
        <el-checkbox-group v-model="checkList">
          <el-col v-for="item in tabList" :key="item.value" :span="12" style="margin-bottom: 8px">
            <el-checkbox :label="item.value" :disabled="item.status === 2">
              {{ item.label }}
            </el-checkbox>
          </el-col>
        </el-checkbox-group>
      </div>
      <template #footer>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
        <el-button @click="dialogVisible = false">关闭</el-button>
      </template>
    </ProDialog>
  </div>
</template>

<script>
import {
  getTherapeuticActionItemList,
  addTherapeuticActionItem,
  deleteTherapeuticActionItem,
  saveTherapeuticAction
} from '@/api/standardizedManage'
import BloodGlucoseMonitoring from '../componentProject/bloodGlucoseMonitoring.vue'
import BloodPressureMonitoring from '../componentProject/bloodPressureMonitoring.vue'
import DrugMonitoring from '../componentProject/drugMonitoring.vue'
import ExercisePrescription from '../componentProject/exercisePrescription.vue'
import MedicalNutrition from '../componentProject/medicalNutrition.vue'
import ProDialog from '@/components/ProDialog/index.vue'
import SurgicalTreatment from './surgicalTreatment.vue'

export default {
  components: {
    BloodGlucoseMonitoring,
    BloodPressureMonitoring,
    DrugMonitoring,
    ExercisePrescription,
    MedicalNutrition,
    ProDialog,
    SurgicalTreatment
  },
  props: {
    historyId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeTab: null,
      tabList: [
        {
          label: '医学营养',
          value: 'MEDICAL_NUTRITION',
          component: 'medicalNutrition'
        },
        {
          label: '运动处方',
          value: 'MOTION_PRESCRIPTION',
          component: 'exercisePrescription'
        },
        {
          label: '血糖监测',
          value: 'SUGAR_MONITOR',
          component: 'bloodGlucoseMonitoring'
        },
        {
          label: '血压监测',
          value: 'PRESSURE_MONITOR',
          component: 'bloodPressureMonitoring'
        },
        {
          label: '药物监测',
          value: 'MEDICAL_MONITOR',
          component: 'drugMonitoring'
        },
        {
          label: '手术治疗',
          value: 'SURGICAL_TREATMENT',
          component: 'surgicalTreatment'
        }
      ],
      showTabList: [],
      dialogVisible: false,
      checkList: []
    }
  },
  watch: {
    activeTab: {
      handler(newVal) {
        if (newVal === 'SUGAR_MONITOR') {
          this.$refs.bloodGlucoseMonitoringRef && this.$refs.bloodGlucoseMonitoringRef[0].initEcharts()
        }
        if (newVal === 'PRESSURE_MONITOR') {
          this.$refs.bloodPressureMonitoringRef && this.$refs.bloodPressureMonitoringRef[0].initEcharts()
        }
      }
    }
  },
  created() {
    this.getTherapeuticActionItemListFn()
    this.getTherapeuticActionItemDetailFn()
  },
  methods: {
    // 获取治疗动作项目列表
    async getTherapeuticActionItemListFn() {
      const res = await getTherapeuticActionItemList({
        smrId: this.historyId || this.$route.query.id
      })

      if (res.code === 200) {
        this.tabList = res.data.map((item) => {
          const project = this.tabList.find((tab) => tab.value === item.code)
          return {
            ...project,
            status: item.status
          }
        })
        this.showTabList = this.tabList.filter((item) => item.status === 2)
        this.checkList = this.tabList.filter((item) => item.status === 2).map((item) => item.value)
        this.activeTab = this.activeTab === '0' ? this.showTabList[0].value : this.activeTab // 不知道这里怎么有0的
      }
    },

    // 治疗动作项目详情查询
    async getTherapeuticActionItemDetailFn() {
      const res = await this.$store.dispatch('managePatient/getTherapeuticActionDetailFn', {
        smrId: this.historyId || this.$route.query.id,
        disease: this.$store.state.managePatient.managePatientData.disease
      })
      if (res) {
        this.handleFormInit(res.itemList)
      }
    },

    // 表单赋值
    async handleFormInit(res) {
      for (const item of this.showTabList) {
        await this.$refs[`${item.component}Ref`][0].initData(res.find((it) => it.itemCode === item.value).data)
      }
    },

    async handleTabsEdit(value, action) {
      const projectItem = this.tabList.find((it) => it.value === value)

      if (action === 'remove') {
        try {
          await this.$confirm(`确定移除 ${projectItem.label} 项目吗？`, '操作确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'error',
            iconClass: 'el-icon-delete-solid',
            customClass: 'remove-confirm',
            closeOnClickModal: false,
            confirmButtonClass: 'bgred'
          })
          const res = await deleteTherapeuticActionItem({
            taId: this.$store.state.managePatient.therapeuticActionDetail.id,
            itemCodeList: [value]
          })
          if (res.code === 200) {
            this.$message.success('移除成功')
            this.getTherapeuticActionItemListFn()
          }
        } catch (error) {
          this.$message.info('已取消移除')
        }
      }

      if (action === 'add') {
        this.dialogVisible = true
      }
    },

    async handleConfirm() {
      const res = await addTherapeuticActionItem({
        taId: this.$store.state.managePatient.therapeuticActionDetail.id,
        itemCodeList: this.checkList
      })
      if (res.code === 200) {
        this.$message.success('添加成功')
        this.dialogVisible = false
        this.getTherapeuticActionItemListFn()
        this.getTherapeuticActionItemDetailFn()
      }
    },

    async handleSave(type) {
      const allResults = []
      // 并发保存所有 tab 对应组件
      for (const item of this.showTabList) {
        const result = await this.$refs[`${item.component}Ref`][0].handleSave()
        allResults.push(result)
      }

      const itemList = allResults.map((item) => item.data)

      // 收集未通过项的提示
      const warnings = allResults.filter((item) => !item.success).map((item) => `${item.name}存在必填未填项！`)

      // 弹出警告信息
      if (type === 'next') {
        for (const warning of warnings) {
          this.$message.warning(warning)
          // eslint-disable-next-line no-await-in-loop
          await new Promise((resolve) => setTimeout(resolve, 500))
        }
      }

      const status = warnings.length > 0 ? 1 : 5

      const params = {
        id: this.$store.getters.therapeuticActionDetail.id,
        disease: this.$store.state.managePatient.managePatientData.disease,
        smrId: this.$store.getters.therapeuticActionDetail.smrId,
        status,
        itemList
      }

      const res = await saveTherapeuticAction(params)
      if (res.code === 200) {
        if (type === 'save') {
          this.$message.success('保存成功')
        }
        this.getTherapeuticActionItemListFn()
        this.getTherapeuticActionItemDetailFn()
        return status
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.therapeutic-action {
  ::v-deep .el-tabs__new-tab {
    width: 80px;
    height: 30px;
    line-height: 30px;
  }
  ::v-deep .el-tabs__new-tab:after {
    content: '添加项目';
  }
  ::v-deep .el-tabs__new-tab:hover {
    color: #fff;
  }
}
</style>
