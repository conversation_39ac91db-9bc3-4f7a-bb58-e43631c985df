<template>
  <div style="position: relative; border-top: 1px solid #ccc">
    <div class="biaoqingContent" :style="{ width: width }">
      <img class="expression" src="@/assets/common_images/smile.png" @click.stop="show = !show" />
      <el-upload
        ref="upload"
        action="/cspapi/backend/cos/uploadFile/private"
        :headers="{ Authorization: token }"
        :limit="1"
        accept=".jpg,.jepg,.png,.bmp,.webp"
        :show-file-list="false"
        :on-success="handleSuccess"
        :data="{ folder: 'chat' }"
        style="display: inline-block"
      >
        <button style="border: 0; background-color: transparent">
          <img class="expression" src="@/assets/common_images/img.png" />
        </button>
      </el-upload>
      <!--      文本框内容-->
      <el-scrollbar :style="`height: ${height};border: 1px solid transparent;`">
        <div
          :id="id"
          ref="msgcont"
          :style="{ height: height }"
          contenteditable="true"
          class="contentBox"
          placeholder="请输入消息。"
          @click="contentClick"
          @blur="onBlur"
          @change="onBlur"
          @paste="pasting"
          @keydown="eventListen"
        />
      </el-scrollbar>
      <div style="text-align: right; padding: 0 10px">
        <el-button type="primary" @click="sendBtn">发送</el-button>
      </div>
      <!--      表情选择框-->
      <div v-if="show" class="box" :style="{ width: '500px' }" @click.stop>
        <div class="left">
          <ul>
            <li
              v-for="(item, index) in biaoqingList"
              :key="index"
              :class="['cursor', biaoqingActive === index ? 'active' : '']"
            >
              <div @click="bqNameChange(index, item.iconArr)">{{ item.name }}</div>
            </li>
          </ul>
        </div>
        <div class="right">
          <button
            v-for="(i, index) in rightList"
            :key="index"
            class="emoji"
            @click.stop="insertHtmlAtCaret(i.className, i.icon)"
          >
            <img
              src="http://tkeasyemoji.oss-cn-shanghai.aliyuncs.com/images/placeholder.png"
              :class="i.className"
              :alt="i.icon"
            />
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
// 定义最后光标对象
import { biaoqingArr } from './biaoqing.js'
import { uploadPasteApi } from '@/api/msglist'

export default {
  components: {},
  props: {
    // 表情框宽度
    width: {
      type: String,
      default: '50%'
    },
    // 表情框高度
    height: {
      type: String,
      default: '200px'
    },
    // 表情框ID
    id: {
      type: String,
      default: 'text'
    },
    // 内容
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      token: '',
      lastEditRange: '',
      content: '',
      show: false,
      biaoqingList: biaoqingArr,
      biaoqingActive: 0,
      rightList: [],
      allList: [],
      photoUrl: '',
      btnclick: false,
      loading: false
    }
  },
  watch: {
    value() {
      this.valueChange()
    },
    content() {
      // 内容变更重新获取光标位置
      this.contentClick()
    }
  },

  created() {
    this.token = getToken()
    this.rightList = biaoqingArr[0].iconArr
    biaoqingArr.forEach(e => {
      this.allList = this.allList.concat(e.iconArr)
    })
  },
  mounted() {
    this.valueChange()
    const that = this
    // eslint-disable-next-line space-before-function-paren
    document.body.onclick = function () {
      that.show = false
    }
  },
  methods: {
    async sendBtn() {
      this.onBlur()
      this.$nextTick(() => {
        setTimeout(() => {
          this.$nextTick(() => {
            this.btnClick()
          })
        }, 300)
      })
    },
    getText(str) {
      return str.replace(/<[^<>]+>/g, '').replace(/&nbsp;/gi, '')
    },
    isNull(str) {
      if (str === '') return true
      const regu = '^[ ]+$'
      const re = new RegExp(regu)
      return re.test(str)
    },
    async btnClick() {
      if (!this.loading) {
        if (this.content.includes('<img')) {
          this.content = this.content.replaceAll('<br>', '')
          const msg = this.content.slice(0, this.content.indexOf('<img'))
          // if (msg.includes('<br>')) {
          //   msg = msg.replaceAll('<br>', '')
          // }
          this.$emit('contentChange', { id: this.id, value: msg })
          const imgstr = this.content.slice(msg.length, this.content.replace(msg, '').indexOf('>') + 1 + msg.length)
          this.content = this.content.slice(imgstr.length + msg.length)
          this.$emit('contentChange', { id: this.id, value: imgstr })
          // this.content = restOf
          this.btnClick()
          // document.getElementById(this.id).innerHTML = ''
          // this.content = ''
          // this.btnclick = false
        } else {
          const text = this.getText(this.content)
          if (this.isNull(text)) {
            document.getElementById(this.id).innerHTML = ''
            this.content = ''
            this.btnclick = false
          } else {
            this.$emit('contentChange', { id: this.id, value: this.content })
            document.getElementById(this.id).innerHTML = ''
            this.content = ''
            this.btnclick = false
          }
        }
      }
    },
    eventListen(event) {
      // const allowKey = [8, 13, 37, 38, 39, 40] // 上下左右 回车 删除
      if (event.keyCode === 13 && !event.shiftKey) {
        event.preventDefault()
        this.onBlur()
        this.$nextTick(() => {
          this.btnclick = true
          if (this.content) {
            this.btnClick()
          }
        })
        return false
      }
      // shift+回车-->换行
      if (event.shiftKey && `${event.keyCode}` === '13') {
        this.contenteditableDivRange()
        return false
      }
      // ctrl+v-->  粘贴
      if (event.ctrlKey && `${event.keyCode}` === '17') {
        this.$nextTick(() => {
          this.onBlur()
        })
        return false
      }
    },

    contenteditableDivRange() {
      const docFragment = document.createDocumentFragment()
      // add the br, or p, or something else
      const newEle = document.createElement('br')
      docFragment.appendChild(newEle)

      // make the br replace selection
      let range = window.getSelection().getRangeAt(0)
      range.deleteContents() // 从文档中移除 Range 包含的内容
      range.insertNode(docFragment) // 在 Range 的起点处插入一个节点

      // create a new range
      range = document.createRange()
      range.setStartAfter(newEle) // 以其它节点为基准，设置 Range 的起点
      range.collapse(true) // 将 Range 折叠至其端点之一

      // make the cursor there
      const sel = window.getSelection()
      sel.removeAllRanges() // 将所有的区域都从选区中移除
      sel.addRange(range) // 一个区域（Range）对象将被加入选区
    },
    // 监听粘贴，去掉复制文本的样式，放过图片
    pasting(event) {
      event.preventDefault()
      let newText
      // let newHtml
      const clp = (event.originalEvent || event).clipboardData
      // 兼容针对opera ie等浏览器
      if (clp === undefined || clp === null) {
        newText = window.clipboardData.getData('text') || ''
        if (newText !== '') {
          if (window.getSelection) {
            // 针对IE11 10 9 safari
            const newNode = document.createElement('span')
            newNode.innerHTML = newText
            window.getSelection().getRangeAt(0).insertNode(newNode)
          } else {
            // 兼容ie 10 9 8 7 6 5
            // eslint-disable-next-line no-unused-expressions
            document.selection.createRange().past
          }
        }
      } else {
        // 兼容chrome或hotfire
        newText = clp.getData('text/plain') || ''
        const file = clp.items && clp.items[0]
        // newHtml=clp.getData('text/html')||""
        if (newText !== '') {
          document.execCommand('insertText', false, newText)
        } else if (file && /image\/\w+/i.test(file.type)) {
          // 类型为图片, 并且文件大小不为 0
          this.imgReader(file)
        }
      }
    },
    imgReader(item) {
      const blob = item.getAsFile()
      const reader = new FileReader()
      const that = this
      // eslint-disable-next-line space-before-function-paren
      reader.onload = function (e) {
        const img = new Image()

        img.src = e.target.result
        img.style.maxWidth = '300px'
        img.style.maxheight = '300px'
        img.style.cursor = 'pointer'
        document.getElementById(that.id).appendChild(img)
      }
      reader.readAsDataURL(blob)
    },
    valueChange() {
      if (!this.value) return
      const str = this.value
      const newStr = this.forArr(this.forArr(str), 'className')
      document.getElementById(this.id).innerHTML = newStr
    },
    // 替换emoji 表情图片
    forArr(str, _type) {
      for (let j = 0; j < this.allList.length; j++) {
        const val = this.allList[j]
        const reg = _type === 'className' ? `alt${val.className}` : val.icon
        if (_type === 'className') {
          str = str.replace(new RegExp(reg, 'g'), val.icon)
        } else {
          str = str.replace(
            new RegExp(reg, 'g'),
            `<img style="vertical-align: sub;" src="http://tkeasyemoji.oss-cn-shanghai.aliyuncs.com/images/placeholder.png" class="${val.className}" alt="alt${val.className}">`
          )
        }
      }
      return str
    },
    // 失去焦点 内容返回
    onBlur() {
      const text = document.getElementById(this.id).innerHTML
      const img = document.getElementById(this.id).querySelectorAll('img')
      if (text.includes('src="http://tkeasyemoji.oss-cn-shanghai.aliyuncs.com/images/placeholder.png"')) {
        this.content = text
          .replace(
            // eslint-disable-next-line max-len
            /<img style="vertical-align: sub;" src="http:\/\/tkeasyemoji\.oss-cn-shanghai\.aliyuncs\.com\/images\/placeholder\.png" class="/g,
            ''
          )
          .replace(/alt="/g, '')
          .replace(/">/g, '')
          .replace(/bg-.{7}/g, '')
      } else if (text.includes('base64')) {
        for (let i = 0; i < img.length; i++) {
          const e = img[i]
          if (e.src.includes('data:image/png;base64,')) {
            this.uploadPhoto(this.base64toFile(e.src), e)
          }
        }
      } else {
        this.content = text
      }
      // this.$emit('contentChange', {
      //   id: this.id,
      //   value: this.content
      // })
    },
    // 表情title点击事件
    bqNameChange(index, arr) {
      this.biaoqingActive = index
      this.rightList = arr
    },
    async uploadPhoto(file, img) {
      this.loading = true
      const formData = new FormData()
      formData.append('file', file)
      formData.append('folder', 'chat')
      const res = await uploadPasteApi(formData)
      // this.photoUrl = res.data[0].fullFileUrl
      img.src = res.data[0].fullFileUrl
      this.content = document.getElementById(this.id).innerHTML
      this.loading = false

      if (this.btnclick) {
        this.btnClick()
      }
    },
    contentClick() {
      // 获取选定对象
      const selection = getSelection()
      // 设置最后光标对象
      this.lastEditRange = selection.getRangeAt(0)
    },
    // 表情插入文本框
    insertHtmlAtCaret(className, icon) {
      const edit = document.getElementById(this.id)
      // 编辑框设置焦点
      edit.focus()
      let sel = getSelection()
      // 判断是否有最后光标对象存在
      if (this.lastEditRange) {
        // 存在最后光标对象，选定对象清除所有光标并添加最后光标还原之前的状态
        sel.removeAllRanges()
        sel.addRange(this.lastEditRange)
      }
      const html = `<img style="vertical-align: sub;" src="http://tkeasyemoji.oss-cn-shanghai.aliyuncs.com/images/placeholder.png" class="${className}" alt="${icon}">`
      let range
      if (window.getSelection) {
        // IE9 and non-IE
        sel = window.getSelection()
        if (sel.getRangeAt && sel.rangeCount) {
          range = sel.getRangeAt(0)
          range.deleteContents()
          const el = document.createElement('div')
          el.innerHTML = html
          const frag = document.createDocumentFragment()
          let node
          let lastNode
          // eslint-disable-next-line no-cond-assign
          while ((node = el.firstChild)) {
            lastNode = frag.appendChild(node)
          }
          range.insertNode(frag)
          if (lastNode) {
            range = range.cloneRange()
            range.setStartAfter(lastNode)
            range.collapse(true)
            sel.removeAllRanges()
            sel.addRange(range)
          }
        }
      } else if (document.selection && document.selection.type !== 'Control') {
        // IE < 9
        document.selection.createRange().pasteHTML(html)
      }
      // 无论如何都要记录最后光标对象
      this.lastEditRange = sel.getRangeAt(0)
    },
    handleSuccess(res, file) {
      this.$refs.upload.clearFiles()
      const url = res.data[0].fullFileUrl
      const edit = document.getElementById(this.id)
      // 编辑框设置焦点
      edit.focus()
      const sel = getSelection()
      // 判断是否有最后光标对象存在
      if (this.lastEditRange) {
        // 存在最后光标对象，选定对象清除所有光标并添加最后光标还原之前的状态
        sel.removeAllRanges()
        sel.addRange(this.lastEditRange)
      }
      // eslint-disable-next-line max-len
      const html = `<img style="vertical-align: sub;max-width:300px;max-height:300px;cursor: pointer;" src="${url}" class="image" />`
      this.$emit('contentChange', { id: this.id, value: html })
      // var range;
      // if (window.getSelection) {
      //   // IE9 and non-IE
      //   sel = window.getSelection();
      //   if (sel.getRangeAt && sel.rangeCount) {
      //     range = sel.getRangeAt(0);
      //     range.deleteContents();
      //     var el = document.createElement("div");
      //     el.innerHTML = html;
      //     var frag = document.createDocumentFragment(), node, lastNode;
      //     while ((node = el.firstChild)) {
      //       lastNode = frag.appendChild(node);
      //     }
      //     range.insertNode(frag);
      //     if (lastNode) {
      //       range = range.cloneRange();
      //       range.setStartAfter(lastNode);
      //       range.collapse(true);
      //       sel.removeAllRanges();
      //       sel.addRange(range);
      //     }
      //   }
      // } else if (document.selection && document.selection.type != "Control") {
      //   // IE < 9
      //   document.selection.createRange().pasteHTML(html);
      // }
      // // 无论如何都要记录最后光标对象
      // this.lastEditRange = sel.getRangeAt(0)
    },
    base64toFile(dataurl, filename = 'file') {
      const arr = dataurl.split(',')
      const mime = arr[0].match(/:(.*?);/)[1]
      const suffix = mime.split('/')[1]
      const bstr = atob(arr[1])
      let n = bstr.length
      const u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      return new File([u8arr], `${filename}.${suffix}`, {
        type: mime
      })
    }
  }
}
</script>
<style>
@import './emoji_sorites.css';
</style>
<style lang="scss" scoped>
.contentBox {
  width: 100%;
  border-radius: 5px;
  padding: 10px;
  font-size: 0.8rem;
  position: relative;

  img {
    max-width: 300px !important;
    max-height: 300px !important;
  }
}

.image {
  max-width: 300px !important;
  max-height: 300px !important;
}

.biaoqingContent {
  padding: 5px;
  margin: 5px auto;

  .expression {
    width: 30px;
    margin: 5px 13px 0;

    cursor: pointer;
  }

  .box {
    border: 1px solid #73a8f9;
    height: 400px;
    box-shadow: 0px 0px 16px rgba(0, 0, 0, 0.2);
    background: #fff;
    z-index: 10;
    position: absolute;
    top: -400px;
    width: 100%;
    display: flex;
  }

  .left {
    width: 90px;
    height: 100%;
    display: table-cell;
    border-right: 1px solid #ebebeb;

    padding: 4px;

    ul {
      padding: 0;
      margin: 0;
      text-align: center;
      list-style-type: none;

      li {
        height: 30px;
        line-height: 30px;
        cursor: pointer;
        font-size: 0.7rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        button {
          border: none;
          background: none;
          width: 100%;
          height: 100%;
        }
      }
    }

    .active {
      background-color: #3171d1;
      color: #ffffff;
      border-radius: 4px;

      button {
        color: #ffffff;
      }
    }
  }

  .right {
    flex: 1;
    padding-left: 10px;
    padding-top: 15px;
    overflow: auto;

    button {
      border: none;
      padding: 0;
      cursor: pointer;
    }

    .emoji {
      display: inline-block;
      padding: 3px;
      border: 1px solid transparent;
      cursor: pointer;
      background: #fff;

      &:hover {
        height: 32px;
        background-color: #ddded8;
        border: 1px solid #b3c1fd;
        border-radius: 4px;
      }
    }
  }
}

div[contenteditable]:empty:before {
  content: attr(placeholder);
  color: #cccccc;
}

div[contenteditable]:focus {
  content: none;
}
</style>
