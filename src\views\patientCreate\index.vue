<template>
  <div class="put-on-record">
    <div class="put-on-record-content">
      <el-card v-loading="loading">
        <h1 style="text-align: center; margin-bottom: 10px">居民健康档案</h1>
        <div style="margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center">
          <read-card-module v-if="type === 'add'" @onSuccess="handleReadCardSuccess" />
          <div v-else />
          <el-button :loading="loading" type="primary" @click="handleSave"> 保存 </el-button>
        </div>
        <el-form ref="form" :model="formModel" :rules="rules" :label-width="type === 'add' ? '110px' : '100px'">
          <el-row>
            <el-col
              v-for="item in filteredPutOnRecordJson"
              :key="item.prop"
              :span="typeof item.span === 'function' ? item.span(formModel) : item.span"
            >
              <component
                :is="getComponent(item.type)"
                v-if="item.type !== 'custom'"
                v-model="formModel[item.prop]"
                :item="item"
              />
              <el-form-item v-else :label="item.label" :prop="item.prop">
                <el-col v-for="child in item.customs" :key="child.prop" :span="child.span" :class="item.class">
                  <el-col v-for="childChild in child" :key="childChild.prop" :span="childChild.span">
                    <component
                      :is="getComponent(childChild.type)"
                      v-model="formModel[childChild.prop]"
                      :style="childChild.type === 'select' ? 'width: 100%' : ''"
                      :item="childChild"
                    />
                  </el-col>
                </el-col>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getOrgTreeByIdApi, getDictApi, getmanageDoctorApi } from '@/api/system'
import { savePutOnRecord, getPutOnRecord } from '@/api/patientCreate'
import { getUserId } from '@/utils/auth'
import { localCache } from '@/utils/cache'
import { phoneValidator, idCardValidator } from '@/utils/cspUtils'
import { cloneDeep } from 'lodash'
import putOnRecordJson from './patientCreateJson'
import TextField from '@/components/questionnaireElementUi/TextField.vue'
import RadioGroupField from '@/components/questionnaireElementUi/RadioGroupField.vue'
import CheckboxGroupField from '@/components/questionnaireElementUi/CheckboxGroupField.vue'
import SelectField from '@/components/questionnaireElementUi/SelectField.vue'
import DateField from '@/components/questionnaireElementUi/DateField.vue'
import CascaderField from '@/components/questionnaireElementUi/CascaderField.vue'
import dayjs from 'dayjs'
import ReadCardModule from '@/components/ReadCardModule/ReadCardModule.vue'

export default {
  name: 'PatientCreate',
  components: {
    TextField,
    RadioGroupField,
    CheckboxGroupField,
    SelectField,
    DateField,
    CascaderField,
    ReadCardModule
  },
  props: {
    type: {
      type: String,
      default: 'add'
    }
  },
  data() {
    const initializeForm = (configArray) => {
      const formModel = {}
      const rules = {}

      const defaultValueByType = {
        checkbox: [],
        default: ''
      }

      const selectionTypes = ['select', 'radio', 'checkbox', 'cascader', 'date', 'time']

      const createRequiredRule = (node, cleanLabel) => {
        const trigger = selectionTypes.includes(node.type) ? 'change' : 'blur'
        const message = `${selectionTypes.includes(node.type) ? '请选择' : '请输入'}${cleanLabel}`
        const rule = { required: true, message, trigger }

        if (node.type === 'checkbox') {
          rule.type = 'array'
        }

        if (node.type === 'date') {
          delete rule.type
          rule.validator = (_, value, callback) => {
            if (!value) {
              callback(new Error(`请选择${cleanLabel}`))
            } else if (!/^\d{4}-\d{2}-\d{2}$/.test(value)) {
              callback(new Error('日期格式应为yyyy-MM-dd'))
            } else {
              callback()
            }
          }
        }

        return rule
      }

      const addFieldRules = (prop, rule) => {
        if (!rules[prop]) rules[prop] = []
        rules[prop].push(rule)
      }

      const traverse = (nodes) => {
        if (!Array.isArray(nodes)) return

        nodes.forEach((node) => {
          if (node.prop) {
            const { prop, type = 'default', required, label = '' } = node
            const cleanLabel = label.replace(/^[0-9]+、\s*/, '')

            formModel[prop] = defaultValueByType[type] ? defaultValueByType[type] : defaultValueByType.default

            if (required) {
              const rule = createRequiredRule(node, cleanLabel)
              addFieldRules(prop, rule)
            }

            if (prop === 'phone') {
              addFieldRules(prop, { validator: phoneValidator, trigger: 'blur' })
            }

            if (prop === 'idCard') {
              addFieldRules(prop, { validator: idCardValidator, trigger: 'blur' })
            }
          }

          if (node.type === 'custom' && node.customs) {
            Object.values(node.customs).forEach(traverse)
          }
        })
      }

      traverse(configArray)

      return { formModel, rules }
    }

    const { formModel, rules } = initializeForm(putOnRecordJson)

    return {
      loading: false,
      putOnRecordJson,
      formModel,
      rules,
      departTree: [],
      // 传参是array
      propsArray: ['disease', 'payType', 'staffAttribute']
    }
  },
  computed: {
    filteredPutOnRecordJson() {
      return this.putOnRecordJson.filter((item) => !item.visibleOn || item.visibleOn(this.formModel))
    }
  },
  watch: {
    'formModel.idCard': {
      handler(newVal) {
        if (newVal) {
          const { sex, birthday, age } = this.parseGender(newVal)
          this.formModel.sex = sex
          this.formModel.birthday = birthday
          this.formModel.age = age
        }
      }
    }
  },
  async created() {
    this.getDepartName()
    this.getDictFn()
    this.initFormModel()
    this.getDoctorList()
  },
  methods: {
    getComponent(type) {
      switch (type) {
        case 'radio':
          return 'RadioGroupField'
        case 'checkbox':
          return 'CheckboxGroupField'
        case 'select':
          return 'SelectField'
        case 'date':
          return 'DateField'
        case 'cascader':
          return 'CascaderField'
        default:
          return 'TextField'
      }
    },

    // 建档查询
    async getPutOnRecordFn() {
      if (this.type === 'add') {
        return
      }
      const userInfo = localCache.getCache('userInfo')
      const res = await getPutOnRecord({ patientId: this.$route.query.id })
      if (res.code === 200) {
        const model = res.data || {}
        const formModelClone = cloneDeep(this.formModel)
        // 参数解析
        this.propsArray.forEach((item) => {
          model[item] = model[item] ? model[item].split(',') : []
        })
        Object.keys(formModelClone).forEach((key) => {
          formModelClone[key] = model[key]
        })
        this.formModel = {
          ...formModelClone,
          idCard: model.originalIdCard,
          phone: model.originalPhone,
          doctorName: userInfo.name,
          filingTime: model.registerDate,
          departPhone: userInfo.originalPhone,
          id: model.id
        }
      }
    },

    initFormModel() {
      if (this.type === 'edit') {
        return
      }
      const userInfo = localCache.getCache('userInfo')
      this.formModel.departCode = userInfo.departCode
      this.formModel.departName = userInfo.departName
      this.formModel.doctorName = userInfo.name
      this.formModel.departPhone = userInfo.originalPhone
      this.formModel.manageDoctorId = userInfo.id
      this.formModel.filingTime = dayjs().format('YYYY-MM-DD')
      this.formModel.countryCode = 'CN'
      this.formModel.idType = 'ID_CARD'
      this.formModel.nativeAddress = 'jiangsu'
      this.formModel.nation = 'hz'
      this.formModel.isDie = 0
    },

    // 字典查询：国家COUNTRY/民族NATION/籍贯NATIVE_PROVINCE/联系人关系RELATION/证件类型ID_TYPE
    async getDictFn() {
      const res = await Promise.all([
        getDictApi('COUNTRY'),
        getDictApi('NATION'),
        getDictApi('NATIVE_PROVINCE'),
        getDictApi('RELATION'),
        getDictApi('ID_TYPE')
      ])
      this.putOnRecordJson.find((item) => item.prop === 'countryCode').options = res[0].data.map((item) => ({
        label: item.value,
        value: item.code
      }))
      this.putOnRecordJson.find((item) => item.prop === 'nation').options = res[1].data.map((item) => ({
        label: item.value,
        value: item.code
      }))
      this.putOnRecordJson.find((item) => item.prop === 'nativeAddress').options = res[2].data.map((item) => ({
        label: item.value,
        value: item.code
      }))
      this.putOnRecordJson
        .find((item) => item.type === 'custom')
        .customs[1].find((item) => item.prop === 'contactRelation1').options = res[3].data.map((item) => ({
          label: item.value,
          value: item.code
        }))
      this.putOnRecordJson
        .find((item) => item.type === 'custom')
        .customs[2].find((item) => item.prop === 'contactRelation2').options = res[3].data.map((item) => ({
          label: item.value,
          value: item.code
        }))
      this.putOnRecordJson.find((item) => item.prop === 'idType').options = res[4].data.map((item) => ({
        label: item.value,
        value: item.code
      }))
    },

    // 获取机构名称
    getDepartName() {
      getOrgTreeByIdApi({ patientId: getUserId() }).then((res) => {
        const treeData = this.transformTreeData(res.data)
        this.putOnRecordJson.find((item) => item.prop === 'departCode').options = treeData
        this.departTree = treeData
        // 获取到机构名称后，再获取建档信息
        this.$nextTick(() => {
          this.getPutOnRecordFn()
        })
      })
    },

    transformTreeData(sourceData) {
      if (!Array.isArray(sourceData) || sourceData.length === 0) {
        return []
      }

      const transformNode = (node) => {
        const newNode = {
          label: node.departName,
          value: node.departCode
        }

        if (node.children && node.children.length > 0) {
          newNode.children = node.children.map(transformNode)
        }

        return newNode
      }

      return sourceData.map(transformNode)
    },

    // 责任医生
    async getDoctorList() {
      const params = {
        pageNo: 1,
        pageSize: 2000,
        departCode: localCache.getCache('userInfo').departCode
      }
      const res = await getmanageDoctorApi(params)
      if (res.code === 200) {
        this.putOnRecordJson.find((item) => item.prop === 'manageDoctorId').options = res.data.list.map((item) => ({
          label: item.name,
          value: item.id
        }))
      }
    },

    // 根据身份证解析性别, 生日, 年龄
    // 生日格式yyyy-MM-dd
    // 年龄根据生日计算
    parseGender(idCard) {
      if (!idCard || idCard.length !== 18) {
        return { sex: '', birthday: '', age: '' }
      }

      // 性别：身份证第17位，奇数为男(1)，偶数为女(0)
      const genderCode = parseInt(idCard.slice(16, 17))
      const sex = genderCode % 2 === 1 ? 1 : 0

      // 生日：身份证第7-14位，格式化为yyyy-MM-dd
      const year = idCard.slice(6, 10)
      const month = idCard.slice(10, 12)
      const day = idCard.slice(12, 14)
      const birthday = `${year}-${month}-${day}`

      // 计算精确年龄
      const birthDate = new Date(`${year}-${month}-${day}`)
      const today = new Date()

      let age = today.getFullYear() - birthDate.getFullYear()
      // 如果当前月份小于出生月份，或当前月份等于出生月份但当前日期小于出生日期，则年龄减1
      if (
        today.getMonth() < birthDate.getMonth() ||
        (today.getMonth() === birthDate.getMonth() && today.getDate() < birthDate.getDate())
      ) {
        age--
      }

      return { sex, birthday, age }
    },

    // 保存
    async handleSave() {
      try {
        this.loading = true
        const valid = await this.$refs.form.validate()
        if (valid) {
          const params = cloneDeep(this.formModel)
          // 参数解析
          this.propsArray.forEach((item) => {
            params[item] = params[item].join(',')
          })
          params.departCode =
            typeof params.departCode === 'string' ? params.departCode : params.departCode[params.departCode.length - 1]
          // 身份证号转大写
          params.idCard = params.idCard.toUpperCase()

          savePutOnRecord(params).then((res) => {
            if (res.code === 200) {
              this.$message.success('保存成功')
              if (this.type === 'add') {
                this.formModel.idCard = ''
              }
            }
          })
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },

    // 读卡器 读卡成功
    handleReadCardSuccess(res) {
      this.formModel.idCard = res.certNumber
      this.formModel.name = res.name
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/form-overrides.scss';
.put-on-record {
  height: 100%;
  width: 100%;
  .put-on-record-content {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .el-card {
      margin: 16px;
      height: 100%;
      width: 70%;
      overflow: auto;
      @media screen and (max-width: 1600px) {
        width: 100%;
      }

      ::v-deep .general {
        padding: 8px;
      }

      ::v-deep .el-form {
        border-right: 1px solid;
        border-bottom: 1px solid;
      }

      ::v-deep .el-form-item {
        border-top: 1px solid;
        border-left: 1px solid;
        margin-bottom: 0;
        line-height: 16px !important;
        position: relative;
      }

      ::v-deep .el-form-item__label {
        background-color: #e7ebf0;
        border-right: 1px solid;
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        height: 100% !important;
        line-height: 16px !important;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 0 6px;
      }

      ::v-deep .el-form-item__content {
        line-height: 33px !important;
        height: auto !important;
        min-height: 55px !important;
      }

      ::v-deep .el-date-editor.el-input {
        width: 100%;
      }

      ::v-deep .el-form-item__error {
        position: absolute;
        top: 80%;
        left: 0;
        color: #f56c6c;
        font-size: 12px;
        z-index: 5;
      }

      ::v-deep .el-radio-group {
        line-height: 15px !important;
        min-height: auto !important;
      }

      .jjlxr {
        ::v-deep .el-form-item {
          border: none;
        }
        ::v-deep .el-form-item__label {
          border: none;
          background-color: #fff;
        }
        ::v-deep .el-form-item__content {
          border: none;
        }
      }
    }
  }
}
</style>
