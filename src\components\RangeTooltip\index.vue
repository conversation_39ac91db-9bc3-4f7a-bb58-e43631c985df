<template>
  <el-tooltip effect="dark" placement="top" :content="''" popper-class="custom-tooltip">
    <i class="el-icon-question trigger-icon" />
    <div slot="content" class="tooltip-content">
      <div class="item">{{ range }}</div>
      <div class="item">{{ description }}</div>
    </div>
  </el-tooltip>
</template>

<script>
export default {
  name: 'RangeTooltip',
  props: {
    range: {
      type: String,
      default: ''
    },
    description: {
      type: String,
      default: ''
    }
  }
}
</script>

<style>
.trigger-icon {
  cursor: pointer;
  color: #909399;
  font-size: 16px;
  vertical-align: middle;
}

.custom-tooltip {
  max-width: 300px !important;
  font-size: 13px;
  line-height: 1.6;
  background-color: rgba(50, 50, 50, 0.7) !important;
}

.custom-tooltip .popper__arrow::after {
  border-top-color: rgba(50, 50, 50, 0.7) !important;
}

.custom-tooltip .popper__arrow {
  border-top-color: rgba(50, 50, 50, 0.7) !important;
}

.tooltip-content .item {
  margin-bottom: 6px;
}
</style>
