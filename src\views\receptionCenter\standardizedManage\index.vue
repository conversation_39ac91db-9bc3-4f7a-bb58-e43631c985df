<template>
  <div class="standardized-manage">
    <el-card class="manage-expired-patient">
      <el-button type="text" @click="handleManageExpiredPatient">
        <span style="border-bottom: 1px solid #409eff">管理即将过期的患者</span>
      </el-button>
      <el-button type="text" @click="handleBloodSugarModal">
        <span style="border-bottom: 1px solid #409eff">血糖管理异常患者</span>
      </el-button>
      <el-button type="text" @click="handleBloodPressureModal">
        <span style="border-bottom: 1px solid #409eff">血压管理异常患者</span>
      </el-button>
      <DiseaseCategory ref="diseaseCategory" :query-params="queryParams" @change="handleDiseaseChange" />
    </el-card>

    <el-card class="standardized-manage-search">
      <el-form :model="queryParams" label-width="110px">
        <el-row>
          <el-col :span="5">
            <el-form-item label="管理时间：">
              <el-date-picker
                v-model="queryParams.timeRange"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="管理状态：">
              <el-select v-model="queryParams.status" placeholder="请选择" style="width: 100%">
                <el-option label="管理中" :value="1" />
                <el-option label="已完成" :value="5" />
                <el-option label="已取消" :value="9" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="管理医生：">
              <el-input v-model="queryParams.doctorName" placeholder="请输入" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col :span="5">
            <el-form-item label="姓名/身份证：">
              <el-input v-model="queryParams.keyword" placeholder="请输入" style="width: 100%" />
            </el-form-item>
          </el-col>

          <el-col :span="4" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button
              icon="el-icon-search"
              type="primary"
              @click="
                () => {
                  handleSearch()
                  $refs.diseaseCategory.getDiseaseList()
                }
              "
            >
              查询
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="standardized-manage-table">
      <div class="operation-container">
        <el-button icon="el-icon-plus" type="primary" @click="handleAdd">新增规范管理</el-button>
      </div>
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 100px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <template #disease="{ row }">
          <ChronicDiseaseType :record="row" />
        </template>

        <template #sex="{ row }">
          <span>{{ genderTransform(row.sex) }}</span>
        </template>

        <template #idCard="{ row }">
          <EncryptionStr :cipher-text="row.idCard" :replace="row.idCardReplace" />
        </template>

        <template #phone="{ row }">
          <EncryptionStr :cipher-text="row.phone" :replace="row.phoneReplace" />
        </template>

        <template #status="{ row }">
          <el-tag v-if="row.status === 1" type="primary">管理中</el-tag>
          <el-tag v-else-if="row.status === 5" type="success">已完成</el-tag>
          <el-tag v-else-if="row.status === 9" type="danger">已取消</el-tag>
        </template>

        <template #operation="{ row }">
          <OperateMenu :row="row">
            <template v-if="row.status !== 1" #view>
              <el-button type="text" size="small" @click="handleView(row)">详情</el-button>
            </template>
            <template v-if="row.status === 1" #continue>
              <el-button type="text" size="small" @click="handleContinueManage(row)">继续管理</el-button>
            </template>
            <template v-if="row.status === 1" #cancel>
              <el-button type="text" size="small" @click="handleCancelManage(row)">取消管理</el-button>
            </template>
            <template v-if="row.status === 1" #finish>
              <el-button type="text" size="small" @click="handleCompleteManage(row)">完成管理</el-button>
            </template>
          </OperateMenu>
        </template>
      </base-table>
    </el-card>

    <!-- 即将过期的患者 -->
    <ManageExpiredPatientModal ref="manageExpiredPatientModal" />
    <!-- 血糖管理异常患者 -->
    <BloodSugarModal ref="bloodSugarModal" />
    <!-- 血压管理异常患者 -->
    <BloodPressureModal ref="bloodPressureModal" />

    <ProDialog ref="addDialog" title="新增规范管理" :visible.sync="addDialogVisible" width="960px">
      <PatientList ref="patientList" />
      <span slot="footer">
        <el-button @click="addDialogVisible = false">关闭</el-button>
        <el-button type="primary" :loading="buttonLoading" @click="handleAddConfirm">确定</el-button>
      </span>
    </ProDialog>

    <CancelModal ref="cancelDialog" @confirm="handleCancelConfirm" />
  </div>
</template>

<script>
import DiseaseCategory from '@/components/diseaseCategory/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ProDialog from '@/components/ProDialog/index.vue'
import PatientList from '../receptionWorkbench/component/patientList.vue'
import ChronicDiseaseType from '@/components/chronicDiseaseType/index.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import OperateMenu from '@/components/operateMenu/index.vue'
import CancelModal from '@/views/receptionCenter/receptionWorkbench/component/cancelModal.vue'
import EncryptionStr from '@/components/encryptionStr/index.vue'
import ManageExpiredPatientModal from './component/manageExpiredPatientModal.vue'
import BloodSugarModal from './component/bloodSugarModal.vue'
import BloodPressureModal from './component/bloodPressureModal.vue'
import {
  addStandardizedManage,
  cancelStandardizedManage,
  completeStandardizedManage,
  getStandardizedManageList
} from '@/api/standardizedManage'
import { genderTransform } from '@/utils/cspUtils'

export default {
  name: 'StandardizedManage',
  components: {
    DiseaseCategory,
    ProDialog,
    PatientList,
    ChronicDiseaseType,
    BaseTable,
    OperateMenu,
    CancelModal,
    EncryptionStr,
    ManageExpiredPatientModal,
    BloodSugarModal,
    BloodPressureModal
  },
  mixins: [tableMixin],
  data() {
    return {
      queryParams: {
        disease: '',
        status: '',
        doctorName: '',
        keyword: '',
        timeRange: []
      },
      buttonLoading: false,
      columns: [
        // { type: 'index', label: '序号' },
        { prop: 'patientName', label: '姓名', width: 120 },
        { prop: 'disease', label: '慢病病种', width: 150, slot: 'disease' },
        { prop: 'sex', label: '性别', slot: 'sex' },
        { prop: 'age', label: '年龄' },
        { prop: 'idCardReplace', label: '身份证号', width: 190, slot: 'idCard' },
        { prop: 'phoneReplace', label: '手机号码', width: 140, slot: 'phone' },
        { prop: 'address', label: '地址', width: 180, showOverflowTooltip: true },
        { prop: 'doctorName', label: '管理医生' },
        { prop: 'manageDate', label: '管理日期', width: 120 },
        { prop: 'manageResult', label: '管理结果' },
        { prop: 'status', label: '状态', slot: 'status' },
        { prop: 'cancelReason', label: '取消管理员原因', width: 160, showOverflowTooltip: true },
        {
          prop: 'operation',
          label: '操作',
          slot: 'operation',
          width: 160,
          fixed: window.innerWidth < 1600 ? 'right' : false
        }
      ]
    }
  },
  methods: {
    genderTransform,
    handleBloodSugarModal() {
      this.$refs.bloodSugarModal.visible = true
    },

    handleBloodPressureModal() {
      this.$refs.bloodPressureModal.visible = true
    },

    handleManageExpiredPatient() {
      this.$refs.manageExpiredPatientModal.visible = true
    },

    handleDiseaseChange(value) {
      this.queryParams.disease = value.diseaseCode === 'all' ? '' : value.diseaseCode
      this.handleSearch()
    },

    handleAdd() {
      this.addDialogVisible = true
    },

    async handleAddConfirm() {
      try {
        this.buttonLoading = true
        const id = this.$refs.patientList.selectedRow
        if (!id) {
          this.$message.warning('请选择患者')
          return
        }
        const res = await addStandardizedManage({
          patientId: id
        })
        if (res.code === 200) {
          this.$message.success('添加成功')
          this.handleReset()
          this.addDialogVisible = false
        }
      } catch (error) {
        console.log(error)
      } finally {
        this.buttonLoading = false
      }
    },

    handleContinueManage(row) {
      this.$store.commit('managePatient/SET_MANAGE_PATIENT_DATA', {})
      this.$router.push({
        path: '/receptionCenter/managePatient',
        query: {
          id: row.id
        }
      })
    },

    handleCancelManage(row) {
      this.record = row
      this.$refs.cancelDialog.form.cancelReason = ''
      this.$refs.cancelDialog.visible = true
    },

    async handleCompleteManage(row) {
      const res = await completeStandardizedManage({
        id: row.id
      })
      if (res.code === 200) {
        this.$message.success('规范管理完成')
        this.handleReset()
      }
    },

    handleView(row) {
      this.$store.commit('managePatient/SET_MANAGE_PATIENT_DATA', {})
      this.$router.push({
        path: '/receptionCenter/managePatient',
        query: {
          id: row.id
        }
      })
    },

    async getTableList(params) {
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }

      return await getStandardizedManageList(queryParams)
    },

    async handleCancelConfirm(reason) {
      const res = await cancelStandardizedManage({
        id: this.record.id,
        cancelReason: reason
      })
      if (res.code === 200) {
        this.$message.success('取消成功')
        this.handleReset()
        this.$refs.cancelDialog.visible = false
      }
    },

    handleReset() {
      this.queryParams = {
        pageNo: 1,
        pageSize: 20
      }
      this.$refs.diseaseCategory.activeCode = 'all'
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.standardized-manage {
  display: flex;
  flex-direction: column;
  height: 100%;
  .manage-expired-patient {
    margin: 16px;
  }
  .standardized-manage-search {
    margin: 0 16px;
    height: 77px;
  }
  .standardized-manage-table {
    flex: 1;
    margin: 16px 16px 0 16px;
    ::v-deep .el-card__body {
      height: 100%;
    }
    .operation-container {
      margin-bottom: 10px;
    }
  }
}
</style>
