<!-- 缺血 -->
<template>
  <div class="iron-deficiency">
    <el-table :data="tableData" border stripe>
      <el-table-column prop="project" label="项目" width="100" align="center" />
      <el-table-column prop="riskFactor" label="危险因素" width="200" align="center" />
      <el-table-column prop="description" label="说明" min-width="400" align="center" />
      <el-table-column label="分值" width="200" align="left">
        <template slot-scope="scope">
          <el-radio-group v-model="scope.row.score">
            <el-radio v-for="option in scope.row.scoreOptions" :key="option.value" :label="option.value">
              {{ option.label }}
            </el-radio>
          </el-radio-group>
        </template>
      </el-table-column>
    </el-table>
    <div class="total-score">
      <span>总分：{{ totalScore }}</span>
    </div>
    <div class="note">
      <p>
        注:心衰为心力衰竭,HFrEF为射血分数降低的心衰,HFmrEF为射血分数轻度降低的心衰,HFpEF为射血分数保留的心衰,LVEF为左心室射血分数;1mmHg=0.133kPa
      </p>
    </div>
  </div>
</template>

<script>
import { mapDataToTable } from '@/utils/cspUtils'
import { cloneDeep } from 'lodash'

export default {
  name: 'IronDeficiency',
  data() {
    return {
      tableData: [
        {
          project: 'C',
          riskFactor: '充血性心衰',
          description: '包括HFrEF、HFmrEF、HFpEF及左心室收缩功能障碍(LVEF小于40%)',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 }
          ],
          qxChfRisk: 'score'
        },
        {
          project: 'H',
          riskFactor: '高血压',
          description: '高血压病史,或目前血压≥140/90 mmHg',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 }
          ],
          qxGxyRisk: 'score'
        },
        {
          project: 'A2',
          riskFactor: '年龄≥65岁',
          description: '亚洲房颤患者≥65岁',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 },
            { label: '2', value: 2 }
          ],
          qxOldRisk: 'score'
        },
        {
          project: 'D',
          riskFactor: '糖尿病',
          description: '包括I型和II型糖尿病,病程越长,卒中风险越高',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 }
          ],
          qxTnbRisk: 'score'
        },
        {
          project: 'S2',
          riskFactor: '卒中',
          description: '既往卒中、短暂性脑缺血发作或体循环栓塞:包括缺血性和出血性卒中',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 },
            { label: '2', value: 2 }
          ],
          qxNzzRisk: 'score'
        },
        {
          project: 'V',
          riskFactor: '血管疾病',
          description: '包括影像证实的冠心病或心肌梗死病史、外周动脉疾病(外周动脉狭窄≥50%或行血运重建)主动脉斑块',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 }
          ],
          qxBloodRisk: 'score'
        },
        {
          project: 'A',
          riskFactor: '年龄 60-64岁',
          description: '亚洲房颤患者60-64岁',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 }
          ],
          qxAgeRisk: 'score'
        },
        {
          project: 'Sc',
          riskFactor: '性别(女性)',
          description: '卒中风险的修正因素,但不是独立危险因素',
          score: 0,
          scoreOptions: [
            { label: '0', value: 0 },
            { label: '1', value: 1 }
          ],
          qxSexRisk: 'score'
        }
      ]
    }
  },
  computed: {
    totalScore() {
      return this.tableData.reduce((sum, item) => {
        return sum + (typeof item.score === 'number' ? item.score : 0)
      }, 0)
    }
  },
  methods: {
    initData(data) {
      this.tableData = mapDataToTable(data, cloneDeep(this.tableData))
      this.tableData.forEach((row) => {
        row.score = row.score || 0
      })
    },
    async handleSave() {
      const resultData = this.extractTableData(this.tableData)
      const result = {
        name: '缺血风险评估',
        success: true,
        data: {
          ...resultData,
          qxScore: this.totalScore
        }
      }
      return result
    },
    extractTableData(tableData) {
      const result = {}

      tableData.forEach((row) => {
        for (const key in row) {
          if (row[key] === 'score') {
            result[key] = row[row[key]]
          }
        }
      })

      return result
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.iron-deficiency {
  padding: 20px;

  .total-score {
    margin-top: 20px;
    font-size: 16px;
    font-weight: bold;
    text-align: right;
  }

  .note {
    margin-top: 15px;
    font-size: 16px;
    line-height: 1.5;
    color: red;
  }
}
</style>
