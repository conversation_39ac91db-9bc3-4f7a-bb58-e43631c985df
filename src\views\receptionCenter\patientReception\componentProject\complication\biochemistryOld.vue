<!-- 生化 -->
<template>
  <div class="biochemistry">
    <device-icon :socket-connect="deviceConnect" style="display: flex; justify-content: flex-end" />

    <div class="content">
      <div class="form-container">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="190px">
          <el-row :gutter="20">
            <flag-component title="血脂" />
            <el-col v-for="item in biochemistry.bloodFat" :key="item.label" :span="24">
              <el-form-item :label="item.label" :prop="item.prop">
                <custom-input-number v-model="form[item.prop]" style="width: 85%">
                  <template #append>{{ item.append }}</template>
                </custom-input-number>
                <range-tooltip style="margin-left: 8px" :range="item.range" :description="item.description" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <flag-component title="肝功能" />
            <el-col v-for="item in biochemistry.liverFunction" :key="item.label" :span="24">
              <el-form-item :label="item.label" :prop="item.prop">
                <custom-input-number v-model="form[item.prop]" style="width: 85%">
                  <template #append>{{ item.append }}</template>
                </custom-input-number>
                <range-tooltip style="margin-left: 8px" :range="item.range" :description="item.description" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <flag-component title="肾功能" />
            <el-col v-for="item in biochemistry.kidneyFunction" :key="item.label" :span="24">
              <el-form-item :label="item.label" :prop="item.prop">
                <custom-input-number v-model="form[item.prop]" style="width: 85%">
                  <template #append>{{ item.append }}</template>
                </custom-input-number>
                <range-tooltip style="margin-left: 8px" :range="item.range" :description="item.description" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <flag-component title="其它" />
            <el-col v-for="item in biochemistry.others" :key="item.label" :span="24">
              <el-form-item :label="item.label" :prop="item.prop">
                <custom-input-number v-model="form[item.prop]" style="width: 85%">
                  <template #append>{{ item.append }}</template>
                </custom-input-number>
                <range-tooltip style="margin-left: 8px" :range="item.range" :description="item.description" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <div class="ocr-recognition">
        <ocr-recognition v-if="activeTab === 'BIOCHEMISTRY'" type="bt" />
      </div>
    </div>
  </div>
</template>

<script>
import { biochemistry } from '@/views/receptionCenter/patientReception/component/complicationsScreening.js'
import { mapGetters } from 'vuex'
import FlagComponent from '@/components/flagComponent/index.vue'
import DeviceIcon from '@/components/deviceIcon/index.vue'
import RangeTooltip from '@/components/RangeTooltip/index.vue'
import OcrRecognition from '@/components/ocrRecognition/index.vue'

export default {
  name: 'Biochemistry',
  components: {
    FlagComponent,
    DeviceIcon,
    RangeTooltip,
    OcrRecognition
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    },
    activeTab: {
      type: String,
      default: ''
    }
  },
  data() {
    const form = {}
    Object.entries(biochemistry).forEach(([key, value]) => {
      value.forEach((item) => {
        form[item.prop] = ''
      })
    })
    return {
      biochemistry,
      form,
      rules: {}
    }
  },
  computed: {
    ...mapGetters(['deviceConnect'])
  },
  methods: {
    initData(data) {
      Object.keys(this.form).forEach((key) => {
        this.form[key] = data[key]
      })
      this.form.id = data.id
    },
    async handleSave() {
      const result = {
        name: `${this.itemTemp.label}`,
        success: false,
        data: {
          ...this.form,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    }
  }
}
</script>

<style lang="scss" scoped>
.biochemistry {
  width: 100%;
  .content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 16px;

    .form-container {
      width: 40%;
    }

    .ocr-recognition {
      flex: 1;
    }
  }

  ::v-deep .el-input-group__append {
    width: 50px !important;
    text-align: center;
    padding: 0 10px;
  }
}
</style>
