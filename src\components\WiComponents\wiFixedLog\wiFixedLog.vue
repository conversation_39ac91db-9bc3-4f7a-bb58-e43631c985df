<template>
  <div>
    <div v-if="logDrawerVisible" class="masking" @click="$emit('toggleVisible')" />
    <div class="applicationLogFixedDrawer" :class="{ 'logVisible': logDrawerVisible }">
      <div v-if="!hideBtn" class="toggleVisibleBox" @click="$emit('toggleVisible')">
        <i v-if="logDrawerVisible" class="el-icon-arrow-right" />
        <img v-if="!logDrawerVisible" :src="logToggleIcon" alt="">
        <span v-if="!logDrawerVisible" class="logToggleTxt">日志</span>
      </div>
      <div class="titleBox">
        <img :src="logToggleIcon" alt=""> 日志 <span class="nameBox">（{{ userInfo.username }}）</span>
      </div>
      <div class="searchBox">
        <span class="searchLabel">操作模块：</span>
        <el-select
          v-model="logSearchForm.keyword"
          clearable
          placeholder="请选择"
          class="searchInput"
          size="small"
          @change="$emit('refresh')"
        >
          <el-option label="全部" value="" />
          <el-option v-for="item in exemItems" :key="'enum-' + item.code" :label="item.value" :value="item.code" />
        </el-select>
        <el-button class="searchBtn" type="primary" plain @click="$emit('refresh')">搜索</el-button>
      </div>
      <div class="timelineBox">
        <div
          v-for="(itemValue, itemKey) of logDataMap"
          :key="itemKey"
          class="timelineItemBox"
          :class="{ 'hideItem': itemValue.hide }"
        >
          <div class="dateLine">
            <div class="dateTxt">{{ itemKey }}</div>
            <div class="toggleBox" @click="handleToggle(itemKey, 0)">
              <span v-if="itemValue.hide">展开</span>
              <span v-else>收起</span>
              <i class="el-icon-arrow-up" :class="{ rotate180: itemValue.hide }" />
            </div>
          </div>
          <div
            v-for="(timelineItem, timelineIndex) in itemValue.list"
            :key="itemKey + '-' + timelineIndex"
            class="timeTextItemBox"
          >
            <span class="timeTextBox">{{ timelineItem.createTime | date2HI }}</span>
            <span class="timelineContentBox">
              医生-{{ timelineItem.createUsername }}在
              {{ OPERATE_FROM_TYPE_ENUM[timelineItem.platform || JSON.parse(timelineItem.requestParam).from] }}
              <span :class="'operateBox ' + 'type-' + timelineItem.operation">
                {{ OPERATE_TYPE_ENUM[timelineItem.operation] }}了
              </span>
              <!-- {{ timelineItem.createTime | date2YMDsHI }}的 -->
              {{ PATIENT_EXAM_FOLLOWUP_MODULE_CODE_ENUM[timelineItem.module] }}
              {{ timelineItem.itemName }}数据
              <!-- {{ JSON.parse(timelineItem.requestParamData) }} -->
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  PATIENT_EXAM_FOLLOWUP_MODULE_CODE_ENUM,
  OPERATE_FROM_TYPE_ENUM,
  OPERATE_TYPE_ENUM,
  NO_LOG_COMMON_HIDE_LIST,
  LOG_ITEMS_WEIGHT
} from '@/utils/enum'
/**
 体温 身高体重 血压 面部视诊 听诊 语音 肺功能
 血糖 血脂 糖化血红蛋白 BNP 肌钙蛋白
 心电图 肢体动脉硬化 超声心动图 颈动脉超声 冠脉CTA 头颅MRI 心电贴
 */
import { getDictionaryValApi } from '@/api/dict'

export default {
  name: 'WiFixedLog',
  props: {
    logDrawerVisible: Boolean,
    logSearchForm: {
      type: Object,
      default: () => {
        return {}
      }
    },
    logDataMap: {
      type: Object,
      default: () => {
        return {}
      }
    },
    userInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    hideBtn: {
      type: Boolean,
      default: false
    },
    module: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      PATIENT_EXAM_FOLLOWUP_MODULE_CODE_ENUM,
      OPERATE_FROM_TYPE_ENUM,
      OPERATE_TYPE_ENUM,
      logToggleIcon: require('@/assets/common_images/logToggleIcon.png'),
      exemItems: []
    }
  },
  mounted() {
    this.getExamItems()
  },
  methods: {
    // 收起 / 展开
    handleToggle(itemKey, timelineIndex) {
      if (timelineIndex === 0) {
        this.logDataMap[itemKey].hide = !this.logDataMap[itemKey].hide
      }
    },

    // 获取 体检项目
    async getExamItems() {
      const res = await getDictionaryValApi('examinationItem')
      const REG_NO_MODULE_LIST = [...NO_LOG_COMMON_HIDE_LIST]
      if (this.module === 'REG') {
        this.exemItems = res.data.filter((i) => !REG_NO_MODULE_LIST.includes(i.value))
      }
      if (this.module === 'BODY-CHECK') {
        // console.log(`res.data`, res.data)
        this.exemItems = res.data.filter((i) => !REG_NO_MODULE_LIST.includes(i.value))
      }
      if (this.module === 'VISIT') {
        // console.log(`res.data`, res.data)
        this.exemItems = res.data.filter((i) => !REG_NO_MODULE_LIST.includes(i.value))
      }
      this.exemItems = this.exemItems.sort((a, b) => {
        return LOG_ITEMS_WEIGHT[a.value] - LOG_ITEMS_WEIGHT[b.value]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.masking {
  position: fixed;
  left: 0;
  top: 0;
  z-index: 80;
  width: 100vw;
  height: 100vh;
}
// 自定义 右侧弹出log drawer
.applicationLogFixedDrawer {
  padding: 20px 30px;
  background-color: rgba(255, 255, 255, 0.77);
  backdrop-filter: blur(7px);
  position: fixed;
  width: 500px;
  height: 100vh;
  right: -500px;
  top: 0;
  z-index: 99;
  transition-property: transform;
  transition-duration: 0.3s;
  box-shadow: 0px 0px 5px 8px rgba(0, 0, 0, 0.1);

  .toggleVisibleBox {
    position: absolute;
    left: -30px;
    top: calc(50% + 20px);
    transform: translateY(-50%);
    width: 30px;
    height: 100px;
    background: #ffffff;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.1);
    border-radius: 15px 0px 0px 15px;
    cursor: pointer;
    user-select: none;
    transition-duration: 0.3s;
    transition-property: all;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    img {
      width: 18px;
      margin-bottom: 4px;
    }
    .logToggleTxt {
      width: 12px;
      font-size: 0.6rem;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 400;
      color: #0a86c8;
      line-height: 16px;
      transition-duration: 0s;
      transition-property: all;
    }
    &:hover {
      box-shadow: 0px 0px 5px 1px rgba(0, 0, 0, 0.2);
      .logToggleTxt {
        font-size: 0.65rem;
      }
    }
  }

  &.logVisible {
    transform: translateX(-500px);

    .toggleVisibleBox {
      position: absolute;
      left: 0;
      top: calc(50% + 20px);
      transform: translateY(-50%);
      width: 20px;
      height: 80px;
      line-height: 80px;
      background: #ebf7f5;
      border-radius: 0px 100px 100px 0px;
      cursor: pointer;
      user-select: none;
      i {
        font-size: 1rem;
        font-weight: bold;
        color: #0a86c8;
      }
    }
  }

  .titleBox {
    font-size: 0.8rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #222222;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    img {
      width: 18px;
      height: 17px;
      margin-right: 6px;
    }

    .nameBox {
      font-size: 0.7rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #666666;
    }
  }

  .searchBox {
    margin-top: 20px;
    display: flex;
    justify-content: flex-start;

    .searchLabel {
      width: 76px;
      flex-shrink: 0;
      font-size: 0.6rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #999999;
      line-height: 36px;
    }
    .searchInput {
      width: 200px;
      background: #ffffff;
    }
    .searchBtn {
      padding: 0;
      width: 70px;
      height: 24px;
      background: #ebf7f5;
      border-radius: 15px;
      border: 1px solid #0a86c8;
      line-height: 24px;
      margin-top: 4px;
      margin-left: 100px;
    }
  }

  .timelineBox {
    margin-top: 15px;
    margin-right: -16px;
    padding-right: 10px;
    height: calc(100vh - 133px);
    overflow-y: auto;
    overflow-x: hidden;

    .timelineItemBox {
      margin-top: 15px;
      & + .timelineItemBox {
        margin-top: 20px;
      }
      &.hideItem {
        max-height: 16px;
        overflow: hidden;
      }

      .dateLine {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        .dateTxt {
          font-size: 0.7rem;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 600;
          color: #666666;
        }
        .toggleBox {
          cursor: pointer;
          user-select: none;
          font-size: 0.6rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #999999;
          i {
            font-size: 0.7rem;
            transition: transform 0.3s ease-in-out;
            &.rotate180 {
              transform: rotateX(180deg);
            }
          }
        }
      }
      .timeTextItemBox {
        padding: 10px 15px;
        background: #f3f3f3;
        border-radius: 8px;
        position: relative;
        line-height: 17px;
        & + .timeTextItemBox {
          margin-top: 15px;

          &::before {
            content: ' ';
            position: absolute;
            left: 15px;
            top: -16px;
            display: block;
            height: 15px;
            width: 1px;
            background: linear-gradient(to bottom, #dedede 0%, #dedede 50%, transparent 50%, transparent 100%);
            background-size: 8px 6px;
            background-repeat: repeat-y;
          }
        }

        .timeTextBox {
          font-size: 0.7rem;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 600;
          color: #666666;
        }
        .timelineContentBox {
          margin-left: 13px;
          font-size: 0.6rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;

          .operateBox {
            font-weight: 600;
            &.type-INSERT {
              color: #0a86c8;
            }
            &.type-UPDATE {
              color: #37b4d8;
            }
            &.type-DELETE {
              color: #e75454;
            }
          }
        }
      }
    }
  }
}
</style>
