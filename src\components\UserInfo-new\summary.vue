<template>
  <div class="summary">
    <div class="section-info">
      <div class="recently-measured">
        <div class="title-label">
          <div class="label blue-before-line">最近测量</div>
        </div>
        <recentlyMeasured :last-measure-data="lastMeasureData" />
      </div>
      <div class="schematic-diagram">
        <div class="title-label">
          <div class="label blue-before-line">示意图</div>
        </div>
        <bmiInfo :last-measure-data="lastMeasureData" :user-info="userInfo" />
      </div>
    </div>
    <div class="records">
      <!-- 接诊记录 -->
      <div class="records-item">
        <div class="title-label">
          <div class="label blue-before-line">接诊记录</div>
        </div>
        <el-table :data="[]" style="width: 100%" stripe class="public_table">
          <el-table-column prop="date" label="时间" width="180">
            <template slot-scope="scope">
              {{ scope.row.date | date2YMD }}
            </template>
          </el-table-column>
          <el-table-column prop="doctorName" label="医生姓名" width="200" />
          <el-table-column prop="doctorAdvice" label="诊断结果">
            <template slot-scope="scope">
              <span v-if="scope.row.doctorAdvice">{{ scope.row.doctorAdvice }}</span>
              <span v-else>暂无诊断</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="records-item">
        <div class="title-label">
          <div class="label blue-before-line">随访记录</div>
        </div>
        <el-table :data="[]" style="width: 100%" stripe class="public_table">
          <el-table-column prop="visitDate" label="计划随访时间">
            <template slot-scope="scope">
              {{ scope.row.visitDate | date2YMD }}
            </template>
          </el-table-column>
          <el-table-column prop="sortNo" label="随访进度">
            <template slot-scope="scope">
              <span>{{ scope.row.sortNo }}/{{ scope.row.frequency }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="realVisitDate" label="实际随访时间">
            <template slot-scope="scope">
              {{ scope.row.realVisitDate | date2YMD }}
            </template>
          </el-table-column>
          <el-table-column prop="visitDiseaseStr" label="类型" />
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { getUserDbMeasureInfo } from '@/api/archives'
import recentlyMeasured from './components/recently-measured.vue'
import bmiInfo from './components/bmi-info.vue'

export default {
  name: 'SummaryNew',
  components: {
    recentlyMeasured,
    bmiInfo
  },
  props: {
    userInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // 最近一次测量数据
      lastMeasureData: {}
    }
  },
  created() {
    this.getUserDbMeasureInfoFn()
  },
  methods: {
    async getUserDbMeasureInfoFn() {
      const res = await getUserDbMeasureInfo({
        patientId: this.$route.query.id
      })
      if (res.code === 200) {
        this.lastMeasureData = res.data || {}
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.summary {
  background-color: #fff;
  .section-info {
    display: flex;
    justify-content: space-between;
    .recently-measured {
      width: 45%;
      margin-right: 1rem;
    }
    .schematic-diagram {
      flex: 1;
    }
  }
  .records {
    margin-top: 1.5rem;
    display: flex;
    justify-content: space-between;
    .records-item {
      width: 49%;
    }
  }
}
.title-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.8rem;
  &.mt20 {
    margin-top: 1rem;
  }
  .label {
    font-weight: 600;
    font-size: 0.9rem;
    color: #222222;
    line-height: 1.25rem;
  }
}
.blue-before-line::before {
  content: '';
  display: inline-block;
  width: 0.1rem;
  height: 0.6rem;
  background: #1ebfa7;
  border-radius: 0.05rem;
  margin-right: 0.4rem;
}
::v-deep .appTableBgBorder tr .el-table__cell:first-child {
  padding: 0.41666vw 0 !important;
  .cell {
    padding-left: 10px;
    padding-right: 10px;
  }
}
::v-deep .thNoPadding {
  &.thCenter {
    .cell {
      padding: 0;
      text-align: center;
    }
  }
  &.el-table th.el-table__cell > .cell {
    padding: 0;
    text-align: center;
  }
}
</style>
