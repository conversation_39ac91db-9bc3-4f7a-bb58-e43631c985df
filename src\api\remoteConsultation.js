import request from '@/utils/request'

// 查询在线的远程专家
export function getOnlineDoctorsApi(data) {
  return request({
    url: '/cspapi/backend/base/user/remote/doctor/list',
    method: 'post',
    data
  })
}

// 创建房间发起连线
export function createRoomApi(data) {
  return request({
    url: '/cspapi/backend/remote/room/create',
    method: 'post',
    data
  })
}

// 解散/离开房间
export function leaveRoomApi(data) {
  return request({
    url: '/cspapi/backend/remote/room/disband',
    method: 'post',
    data
  })
}

// 新增与会人
export function addParticipantApi(data) {
  return request({
    url: '/cspapi/backend/remote/room/user/create',
    method: 'post',
    data
  })
}

// 重新呼叫与会人
export function reCallParticipantApi(data) {
  return request({
    url: '/cspapi/backend/remote/room/user/recall',
    method: 'post',
    data
  })
}

// 更新与会人状态
export function updateParticipantApi(data) {
  return request({
    url: '/cspapi/backend/remote/room/user/save',
    method: 'post',
    data
  })
}

// 根据患者ID查询远程会诊记录
export function getConsultationRecordApi(data) {
  return request({
    url: '/cspapi/backend/base/user/remote/list/history',
    method: 'post',
    data
  })
}

// 保存处方，会诊结论
export function saveConsultationRecordApi(data) {
  return request({
    url: '/cspapi/backend/remote/room/save',
    method: 'post',
    data
  })
}

// 查询会诊记录详情(只查处方和结论)
export function getConsultationRecordDetailApi(data) {
  return request({
    url: '/cspapi/backend/remote/room/detail',
    method: 'post',
    data
  })
}

// 会诊视频
export function getConsultationVideoApi(data) {
  return request({
    url: '/cspapi/backend/tencent/vod/detail',
    method: 'post',
    data
  })
}

// 分页查询远程会诊记录
export function getConsultationRecordsApi(data) {
  return request({
    url: '/cspapi/backend/remote/room/page',
    method: 'post',
    data
  })
}
