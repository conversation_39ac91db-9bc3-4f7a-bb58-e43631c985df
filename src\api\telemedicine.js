import request from '@/utils/request'

export function getTelemedicineInfoApi(data) {
  return request({
    url: '/cspapi/backend/remoteConsultation/pageRemoteRoomByDoctorId',
    method: 'get',
    params: data
  })
}
// 某医生入会/离会给其他人发通知/
export function putRemoteUsersApi(data) {
  return request({
    url: '/cspapi/backend/bot/remoteUsers',
    method: 'put',
    data
  })
}
// 机器人工作台列表
export function getUserListApi(params) {
  return request({
    url: '/cspapi/backend/bot/registeredrecord/pageByModel',
    method: 'get',
    params
  })
}

// 机器人设置列表参数获取
export function getTelemedicineSettingParApi(params) {
  return request({
    url: '/cspapi/backend/',
    method: 'get',
    params
  })
}

// 机器人工作台列表
export function setTelemedicineSettingParApi(params) {
  return request({
    url: '/cspapi/backend/',
    method: 'get',
    params
  })
}

// 某次就诊-所有检查项的状态
export function getRobotListApi(regId) {
  return request({
    url: '/cspapi/backend/bot/reg/checkStatus',
    method: 'get',
    params: { regId }
  })
}
// 查看当前患者的体征数据
export function getBotCheckActiveApi(batchId, patientId) {
  return request({
    url: '/cspapi/backend/visitCheck/active2/getByBatchIdAndPatientIdAndType',
    method: 'get',
    params: { recordId: batchId, patientId, module: 'BOT' }
  })
}
// 单个就诊的cdss详情
export function getRobotQuestionnaireApi(regId) {
  return request({
    url: '/cspapi/backend/bot/reg/cdss/getByRegId',
    method: 'get',
    params: { regId }
  })
}
//  单个机器状态
export function getRobotStatusApi() {
  return request({
    url: '/cspapi/backend/bot/device/getBySn',
    method: 'get',
    params: { sn: 'robot' }
  })
}
//  某次就诊-所有检查项信息
export function getRobotActiveInfoApi(regId) {
  return request({
    url: '/cspapi/backend/bot/reg/active/list',
    method: 'get',
    params: { regId }
  })
}
//  30.1.3 结束就诊 + 放弃就诊  写死 2:就诊结束 5:放弃就诊
export function getFinishRobotApi(data) {
  return request({
    url: '/cspapi/backend/bot/registeredrecord',
    method: 'put',
    data
  })
}
//  机器人-常用药 列表
export function getBotDrugListApi(botSn) {
  return request({
    url: '/cspapi/backend/bot/medicine/manage/list',
    method: 'get',
    params: { botSn }
  })
}
//  药品开箱
export function getDrugOpenBoxApi(data) {
  return request({
    url: '/cspapi/backend/bot/prescription/door/open',
    method: 'post',
    data
  })
}
