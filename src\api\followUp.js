import request from '@/utils/request'

// 添加随访记录
export const addFollowUp = (data) => {
  return request({
    url: '/cspapi/backend/interview/create',
    method: 'post',
    data
  })
}

// 分页查询随访记录
export const getFollowUpList = (data) => {
  return request({
    url: '/cspapi/backend/interview/page',
    method: 'post',
    data
  })
}

// 查询随访记录详情
export const getFollowUpDetail = (data) => {
  return request({
    url: '/cspapi/backend/interview/detail',
    method: 'post',
    data
  })
}

// 完成随访
export const completeFollowUp = (data) => {
  return request({
    url: '/cspapi/backend/interview/complete',
    method: 'post',
    data
  })
}

// 查询随访状态数量
export const getFollowUpStatusCount = (data) => {
  return request({
    url: '/cspapi/backend/interview/status/count',
    method: 'post',
    data
  })
}

// 评估随访
export const evaluationFollowUp = (data) => {
  return request({
    url: '/cspapi/backend/interview/audit',
    method: 'post',
    data
  })
}

// 随访统计
export const getFollowUpStatistics = (data) => {
  return request({
    url: '/cspapi/backend/interview/count',
    method: 'post',
    data
  })
}
