<template>
  <div v-if="pageType === 'list'" class="popularScience">
    <div class="search-box">
      <div class="hr" />
      <div class="left">
        <div class="search-item">
          <div class="label">文章标题：</div>
          <el-input
            v-model="searchData.title"
            placeholder="请输入"
            clearable
            @change=";(searchData.pageNo = 1), getTableData()"
          />
        </div>
        <div class="search-item">
          <div class="label">创建时间：</div>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            align="right"
            unlink-panels
            format="yyyy/MM/dd"
            value-format="yyyy-MM-dd"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
          />
        </div>
        <div class="search-item">
          <div class="label">创建人：</div>
          <el-input
            v-model="searchData.author"
            placeholder="请输入"
            clearable
            @change=";(searchData.pageNo = 1), getTableData()"
          />
        </div>
        <div class="search-item">
          <div class="label">文章类型：</div>
          <el-select
            v-model="searchData.articleType"
            clearable
            placeholder="请选择"
            @change=";(searchData.pageNo = 1), getTableData()"
          >
            <el-option label="全部" value="" />
            <el-option label="活动预告" :value="1" />
            <el-option label="健康知识" :value="0" />
          </el-select>
        </div>
      </div>
      <div class="right">
        <el-button round @click="handleReset">{{ '\u3000重置\u3000' }}</el-button>
        <el-button round plain @click="getTableData">{{ '\u3000搜索\u3000' }}</el-button>
        <el-button type="primary" icon="el-icon-circle-plus-outline" @click="addArticleBtn">新增文章</el-button>
      </div>
    </div>
    <div class="list-box">
      <el-table
        v-loading="tableLoad"
        :data="tableData"
        height="calc(100vh - 350px)"
        empty-text="暂无数据"
        tooltip-effect="light"
        class="applicationCustomPageTable applicationCustomPageTableVw appTableBgBorder"
      >
        <el-table-column prop="articleTitle" label="文章标题" width="450" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间">
          <template slot-scope="scope">
            <span style="white-space: nowrap">
              {{ scope.row.createTime | date2YMDsHI('{y}/{m}/{d} {h}:{i}') }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="authorName" label="创建人" />
        <el-table-column prop="articleType" label="文章类型">
          <template slot-scope="scope">{{ articleType[scope.row.articleType] }}</template>
        </el-table-column>
        <!-- <el-table-column prop="count" label="访问量" sortable>
          <template slot-scope="scope">{{ scope.row.viewCount ? scope.row.viewCount : 0 }}</template>
        </el-table-column> -->
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="handleClick(scope.row)">查看</el-button>
            <el-button type="text" style="color: #ee5757" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          :current-page="searchData.pageNo"
          :page-sizes="tablePageSizes"
          :page-size="searchData.pageSize"
          :layout="tablePaginationLayout"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
  <div v-else-if="pageType === 'article'" class="popularScience article">
    <div class="top">
      <div class="left" @click="goList">
        <img src="@/assets/personage_images/<EMAIL>" alt="">
        文章详情
      </div>
      <div class="right">
        <el-button
          v-if="articlePageType === 'look'"
          size="small"
          type="primary"
          plain
          icon="el-icon-edit-outline"
          @click="handleEdit"
        >编辑</el-button>
        <el-button
          v-if="articlePageType === 'add' || articlePageType === 'edit'"
          v-loading="loading"
          size="small"
          type="primary"
          @click="saveArticle"
        >{{ '\u3000保存\u3000' }}</el-button>
      </div>
    </div>
    <div class="main">
      <div class="left">
        <div class="from-item">
          <div class="label">
            <div class="red">*</div>
            文章标题：
          </div>
          <el-input
            v-model="articleData.title"
            type="textarea"
            placeholder="请输入文章标题"
            :autosize="{ minRows: 5, maxRows: 5 }"
            resize="none"
            maxlength="30"
            show-word-limit
            :disabled="articlePageType === 'look'"
          />
        </div>
        <div class="from-item">
          <div class="label">
            <div class="red">*</div>
            类型：
          </div>
          <el-select
            v-model="articleData.type"
            placeholder="请选择"
            style="width: 100%"
            :disabled="articlePageType === 'look'"
          >
            <el-option v-for="(i, idx) in articleType" :key="idx" :label="i" :value="idx" />
          </el-select>
        </div>
        <div class="from-item">
          <div class="label">
            <div class="red">*</div>
            文章封面：
          </div>
          <el-upload
            class="avatar-uploader"
            action="/cspapi/backend/cos/uploadFile/private"
            :headers="{ Authorization: token }"
            :data="{ folder: 'health-article' }"
            accept=".jpg,.jepg,.png,.bmp,.webp"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
            :disabled="articlePageType === 'look'"
          >
            <img v-if="imageUrl" :src="imageUrl" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon" />
          </el-upload>
          <p style="font-size: 0.6rem; color: #606266">只能上传JPG、JPEG、PNG、BMP、WEBP文件，且不超过2MB</p>
        </div>
      </div>
      <div class="right">
        <div class="label">
          <div class="red">*</div>
          文章内容：
        </div>
        <div id="rich-text" style="height: 700px" />
      </div>
    </div>
  </div>
</template>
<script>
import { getToken } from '@/utils/auth'
import E from 'wangeditor'
import {
  getHealthQueryArticle,
  getHealthArticleSet,
  getHealthArticleAdd,
  getHealthArticle,
  getHealthArticledel
} from '@/api/system'

export default {
  name: 'PopularScience',
  data() {
    return {
      articleType: ['健康知识', '活动预告'],
      pageType: 'list',
      // pageType: 'article',
      // add  新增  look 查看  edit  编辑
      articlePageType: 'add',
      setarticleId: '',
      token: '',
      searchData: {
        title: '',
        startDate: '',
        endDate: '',
        author: '',
        articleType: '',
        pageNo: 1,
        pageSize: 10
      },
      total: 0,
      tableLoad: false,
      tableData: [],
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      dateRange: null,
      articleData: {
        title: '',
        type: 0,
        url: ''
      },
      editor: null,
      imageUrl: '',
      loading: false
    }
  },
  watch: {
    dateRange(newval) {
      if (newval) {
        this.searchData.startDate = newval[0]
        this.searchData.endDate = newval[1]
      } else {
        this.searchData.startDate = ''
        this.searchData.endDate = ''
      }
      this.searchData.pageNo = 1
      this.getTableData()
    }
  },
  created() {
    this.token = getToken()

    this.getTableData()
  },
  mounted() {},
  methods: {
    initEditor() {
      this.editor = new E('#rich-text')
      this.editor.config.excludeMenus = ['code', 'todo']
      this.editor.customConfig = this.editor.customConfig ? this.editor.customConfig : this.editor.config
      this.editor.customConfig.onchange = (html) => {
        // console.log('html',html,html.length);
        if (html.length > 12000) {
          const str = html.substring(0, 11900)
          this.editor.txt.html(str)
          this.$message('字数超出已自动删除！')
        }
      }
      this.editor.create()
    },
    handleDelete(row) {
      this.$confirm('此操作将永久删除该文章, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async() => {
          // console.log(row.articleId.toString());
          const res = await getHealthArticledel(row.articleId.toString())
          // console.log(res);
          this.handleReset()
          this.stateArr = []
          if (res.code === 200) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    async handleClick(row) {
      this.setarticleId = row.articleId
      const res = await getHealthArticle(row.articleId)
      if (res.code === 200) {
        this.pageType = 'article'
        this.articlePageType = 'look'
        this.articleData.title = res.data.articleTitle
        this.articleData.type = res.data.articleType
        this.articleData.url = res.data.cover
        this.imageUrl = res.data.cover
        this.$nextTick(() => {
          this.initEditor()
          this.editor.txt.html(res.data.articleContent)
          this.editor.disable()
        })
      }
    },
    handleEdit() {
      this.articlePageType = 'edit'
      this.editor.enable()
    },
    addArticleBtn() {
      this.articlePageType = 'add'
      this.pageType = 'article'
      this.$nextTick(() => {
        this.initEditor()
      })
    },
    handleReset() {
      this.searchData = {
        title: '',
        startDate: '',
        endDate: '',
        author: '',
        articleType: '',
        pageNo: 1,
        pageSize: 10
      }
      this.dateRange = null
      this.getTableData()
    },
    handleAvatarSuccess(res, file) {
      this.imageUrl = URL.createObjectURL(file.raw)
      // console.log(res);
      this.articleData.url = res.data[0].fullFileUrl
    },
    beforeAvatarUpload(file) {
      // console.log('file.size',file);
      const isLt2M = file.size / 1024 / 1024 < 2
      const isImage = file.type.indexOf('image/') === -1
      // console.log('isImage',isImage);
      if (isImage) {
        this.$message.error('请上传正确的图片格式!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传封面大小不能超过 2MB!')
        return false
      }
      return isLt2M && !isImage
    },
    goList() {
      this.setarticleId = ''
      this.articlePageType = ''
      this.pageType = 'list'
      this.articleData = {
        title: '',
        type: 0,
        url: ''
      }
      this.imageUrl = ''
      this.handleReset()
    },
    async saveArticle() {
      if (!this.articleData.title) {
        this.$message({
          message: '请填写文章标题',
          type: 'warning'
        })
      } else if (!this.articleData.url) {
        this.$message({
          message: '请上传文章封面',
          type: 'warning'
        })
      } else if (!this.editor.txt.html()) {
        this.$message({
          message: '请填写文章内容',
          type: 'warning'
        })
      } else if (!(this.articleData.type === 0 || this.articleData.type === 1)) {
        this.$message({
          message: '请选择文章类型',
          type: 'warning'
        })
      } else {
        // eslint-disable-next-line no-lonely-if
        if (this.setarticleId) {
          this.loading = true
          const res = await getHealthArticleSet({
            articleId: this.setarticleId,
            articleTitle: this.articleData.title,
            articleType: this.articleData.type,
            articleContent: this.editor.txt.html(),
            articleState: 0,
            cover: this.articleData.url,
            isTop: 0,
            isPushReminder: 0
          })
          // console.log(res);
          if (res.code === 200) {
            this.$message.success('修改文章成功')
            this.loading = false
            this.goList()
          }
        } else {
          this.loading = true
          const res = await getHealthArticleAdd({
            articleTitle: this.articleData.title,
            articleType: this.articleData.type,
            articleContent: this.editor.txt.html(),
            articleState: 0,
            cover: this.articleData.url,
            isTop: 0,
            isPushReminder: 0
          })
          // console.log(res);
          if (res.code === 200) {
            this.$message.success('新增文章成功')
            this.loading = false
            this.goList()
          }
        }
      }
    },
    async getTableData() {
      this.tableLoad = true
      try {
        const res = await getHealthQueryArticle(this.searchData)
        if (res.code === 200) {
          this.tableData = res.data.list
          this.total = res.data.total
        }
        this.tableLoad = false
      } catch (error) {
        this.tableLoad = false
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
      this.searchData.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`)
      this.searchData.pageNo = val
      this.getTableData()
    }
  }
}
</script>
<style lang="scss" scoped>
.popularScience {
  margin: 30px 30px 0;
  .search-box {
    position: relative;
    padding: 35px;
    background: linear-gradient(180deg, #eefffc 0%, #ffffff 100%);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    overflow: hidden;
    .hr {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 5px;
      border-radius: 20px;
      background: #0a86c8;
    }
    .left {
      display: flex;
    }
    .search-item {
      margin-right: 10px;
      .label {
        font-size: 0.8rem;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #666666;
        line-height: 22px;
        margin-bottom: 7px;
      }
    }
    .right {
      height: 70px;
      display: flex;
      align-items: flex-end;
    }
  }
  .list-box {
    margin-top: 30px;
    background: #ffffff;
    border-radius: 15px;
    .pagination {
      padding-right: 20px;
      display: flex;
      align-items: center;
      justify-content: right;
      height: 64px;
      backdrop-filter: blur(7px);
    }
  }
}
.article {
  border-radius: 15px;
  background-color: #fff;
  padding: 20px 30px;
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    .left {
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 0.85rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #222222;
      img {
        width: 24px;
        height: 24px;
        margin-right: 10px;
      }
    }
  }
  .main {
    display: flex;
    justify-content: space-between;
    .left {
      width: 35%;
      min-width: 400px;
      .from-item {
        margin-bottom: 30px;
      }
    }
    .label {
      font-size: 0.7rem;
      font-family: PingFangSC, PingFang SC;
      font-weight: 500;
      color: #666;
      line-height: 20px;
      margin-bottom: 10px;
      .red {
        color: #ee5757;
        display: inline-block;
      }
    }
    .right {
      width: 63%;
    }
  }
}
</style>
<style>
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 1.4rem;
  color: #8c939d;
  width: 400px;
  height: 400px;
  line-height: 400px;
  text-align: center;
}

.avatar {
  width: 400px;
  height: 400px;
  display: block;
}
.w-e-text-container {
  height: 650px !important;
}
</style>
