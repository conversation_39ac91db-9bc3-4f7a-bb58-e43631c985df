<template>
  <div class="data-info">
    <PulmonaryCT v-if="detail.itemCode === 'COPD_CT'" ref="pulmonaryCT" />
    <CarotidUltrasound v-if="detail.itemCode === 'CAROTID_ULTRASOUND'" ref="carotidUltrasound" />
    <Echocardiogram v-if="detail.itemCode === 'ECHOCARDIOGRAM'" ref="echocardiogram" />
    <Echocardiography v-if="detail.itemCode === 'ECHOCARDIOGRAPHY'" ref="echocardiography" />
  </div>
</template>

<script>
import PulmonaryCT from '@/views/receptionCenter/patientReception/componentProject/pulmonaryCT.vue'
import CarotidUltrasound from '@/views/receptionCenter/patientReception/componentProject/complication/carotidUltrasound.vue'
import Echocardiogram from '@/views/receptionCenter/patientReception/componentProject/complication/echocardiogram.vue'
import Echocardiography from '@/views/receptionCenter/patientReception/componentProject/complication/echocardiography.vue'

export default {
  name: 'DataInfo',
  components: {
    PulmonaryCT, // 肺部ct
    CarotidUltrasound, // 颈动脉超声
    Echocardiogram, // 超声心动图
    Echocardiography // 心脏彩超
  },
  props: {
    detail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {}
  },
  methods: {
    initData(data) {
      this.$refs.pulmonaryCT && this.$refs.pulmonaryCT.initData({ data })
      this.$refs.carotidUltrasound && this.$refs.carotidUltrasound.initData(data)
      this.$refs.echocardiogram && this.$refs.echocardiogram.initData(data)
      this.$refs.echocardiography && this.$refs.echocardiography.initData(data)
    }
  }
}
</script>
