<!-- 运动处方 -->
<template>
  <div class="exercise-prescription">
    <el-form :model="formAerobic" label-width="130px">
      <el-row>
        <div class="title">心肺康复运动-有氧运动</div>
        <el-col :span="8">
          <el-form-item label="处方名称：">
            <el-input v-model="formAerobic.prescriptionName" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="使用病症：">
            <el-select v-model="formAerobic.useDisease" clearable multiple style="width: 100%">
              <el-option label="高血压" value="1" />
              <el-option label="糖尿病" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="风险等级：">
            <el-select v-model="formAerobic.riskLevel" clearable style="width: 100%">
              <el-option label="高" :value="1" />
              <el-option label="中" :value="2" />
              <el-option label="低" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="训练类型：">
            <el-select v-model="formAerobic.trainType" clearable style="width: 100%">
              <el-option label="有氧运动" :value="1" />
              <el-option label="抗阻运动" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="训练部位：">
            <el-select v-model="formAerobic.trainPart" clearable style="width: 100%">
              <el-option label="心肺" :value="1" />
              <el-option label="胸部" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="训练设备：">
            <el-select v-model="formAerobic.trainDevice" clearable style="width: 100%">
              <el-option label="立式功率车" :value="1" />
              <el-option label="离心功率车" :value="2" />
              <el-option label="有氧训练机" :value="3" />
              <el-option label="坐立卧推机" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处方疗程：">
            <el-input v-model="formAerobic.prescriptionCourse">
              <template #append>周</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="训练频次：">
            <el-select v-model="formAerobic.trainCount" clearable style="width: 100%">
              <el-option label="1" :value="1" />
              <el-option label="2" :value="2" />
              <el-option label="3" :value="3" />
              <el-option label="4" :value="4" />
              <el-option label="5" :value="5" />
              <el-option label="6" :value="6" />
              <el-option label="7" :value="7" />
              <el-option label="8" :value="8" />
              <el-option label="9" :value="9" />
              <el-option label="10" :value="10" />
              <el-option label="11" :value="11" />
              <el-option label="12" :value="12" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="运动时段：">
            <el-select v-model="formAerobic.sportPeriod" clearable style="width: 100%">
              <el-option label="餐前半小时" :value="1" />
              <el-option label="餐后一小时" :value="2" />
              <el-option label="任意时段" :value="9" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 有氧运动部分 -->
      <el-row
        v-for="(item, index) in aerobicList"
        :key="'aerobic' + index"
        style="border: 1px solid #41a1d4; padding-top: 16px; margin-bottom: 10px"
      >
        <div class="operate-button">
          <span><i class="el-icon-plus" style="color: #41a1d4; cursor: pointer" @click="addAerobic" /></span>
          <span><i
            class="el-icon-delete"
            style="color: red; margin-right: 16px; cursor: pointer"
            @click="deleteAerobic(index)"
          /></span>
        </div>
        <el-col :span="8">
          <el-form-item label="目标心率：">
            <el-input v-model="item.baseHeartRate" style="width: 50%">
              <template #append>~</template>
            </el-input>
            <el-input v-model="item.targetHeartRate" style="width: 50%">
              <template #append>次/分</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="警戒心率：">
            <el-input v-model="item.alertHeartRate">
              <template #append>次/分</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="运动强度：">
            <el-select v-model="item.sportLevel" style="width: 100%">
              <el-option label="1" :value="1" />
              <el-option label="2" :value="2" />
              <el-option label="3" :value="3" />
              <el-option label="4" :value="4" />
              <el-option label="5" :value="5" />
              <el-option label="6" :value="6" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="运动感觉：">
            <el-input v-model="item.baseSportFeel" style="width: 50%">
              <template #append>~</template>
            </el-input>
            <el-input v-model="item.targetSportFeel" style="width: 50%">
              <template #append>次/分</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="训练时长：">
            <el-input v-model="item.trainMinute" style="width: 50%">
              <template #append>分</template>
            </el-input>
            <el-input v-model="item.trainSecond" style="width: 50%">
              <template #append>秒</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="组间休息：">
            <el-input v-model="item.relaxMinute" style="width: 50%">
              <template #append>分</template>
            </el-input>
            <el-input v-model="item.relaxSecond" style="width: 50%">
              <template #append>秒</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-form :model="formResistance" label-width="130px" style="margin-top: 16px">
      <el-row>
        <div class="title">心肺康复运动-抗阻运动</div>
        <el-col :span="8">
          <el-form-item label="处方名称：">
            <el-input v-model="formResistance.prescriptionName" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="使用病症：">
            <el-select v-model="formResistance.useDisease" clearable multiple style="width: 100%">
              <el-option label="高血压" value="1" />
              <el-option label="糖尿病" value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="风险等级：">
            <el-select v-model="formResistance.riskLevel" clearable style="width: 100%">
              <el-option label="高" :value="1" />
              <el-option label="中" :value="2" />
              <el-option label="低" :value="3" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="训练类型：">
            <el-select v-model="formResistance.trainType" clearable style="width: 100%">
              <el-option label="有氧运动" :value="1" />
              <el-option label="抗阻运动" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="训练部位：">
            <el-select v-model="formResistance.trainPart" clearable style="width: 100%">
              <el-option label="心肺" :value="1" />
              <el-option label="胸部" :value="2" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="训练设备：">
            <el-select v-model="formResistance.trainDevice" clearable style="width: 100%">
              <el-option label="立式功率车" :value="1" />
              <el-option label="离心功率车" :value="2" />
              <el-option label="有氧训练机" :value="3" />
              <el-option label="坐立卧推机" :value="4" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="处方疗程：">
            <el-input v-model="formResistance.prescriptionCourse">
              <template #append>周</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="训练频次：">
            <el-select v-model="formResistance.trainCount" clearable style="width: 100%">
              <el-option label="1" :value="1" />
              <el-option label="2" :value="2" />
              <el-option label="3" :value="3" />
              <el-option label="4" :value="4" />
              <el-option label="5" :value="5" />
              <el-option label="6" :value="6" />
              <el-option label="7" :value="7" />
              <el-option label="8" :value="8" />
              <el-option label="9" :value="9" />
              <el-option label="10" :value="10" />
              <el-option label="11" :value="11" />
              <el-option label="12" :value="12" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="运动时段：">
            <el-select v-model="formResistance.sportPeriod" clearable style="width: 100%">
              <el-option label="餐前半小时" :value="1" />
              <el-option label="餐后一小时" :value="2" />
              <el-option label="任意时段" :value="9" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 抗阻运动部分 -->
      <el-row
        v-for="(item, index) in resistanceList"
        :key="'resistance' + index"
        style="border: 1px solid #41a1d4; padding-top: 16px; margin-bottom: 10px"
      >
        <div class="operate-button">
          <span><i class="el-icon-plus" style="color: #41a1d4; cursor: pointer" @click="addResistance" /></span>
          <span><i
            class="el-icon-delete"
            style="color: red; margin-right: 16px; cursor: pointer"
            @click="deleteResistance(index)"
          /></span>
        </div>
        <el-col :span="8">
          <el-form-item label="目标心率：">
            <el-input v-model="item.baseHeartRate" style="width: 50%">
              <template #append>~</template>
            </el-input>
            <el-input v-model="item.targetHeartRate" style="width: 50%">
              <template #append>次/分</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="警戒心率：">
            <el-input v-model="item.alertHeartRate">
              <template #append>次/分</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="使用重量：">
            <el-select v-model="item.useWeight" style="width: 100%">
              <el-option label="10" :value="10" />
              <el-option label="15" :value="15" />
              <el-option label="20" :value="20" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="运动感觉：">
            <el-input v-model="item.baseSportFeel" style="width: 50%">
              <template #append>~</template>
            </el-input>
            <el-input v-model="item.targetSportFeel" style="width: 50%">
              <template #append>次/分</template>
            </el-input>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="训练次数：">
            <el-select v-model="item.trainTimes" style="width: 100%">
              <el-option label="1" :value="1" />
              <el-option label="2" :value="2" />
              <el-option label="3" :value="3" />
              <el-option label="4" :value="4" />
              <el-option label="5" :value="5" />
              <el-option label="6" :value="6" />
              <el-option label="7" :value="7" />
              <el-option label="8" :value="8" />
              <el-option label="9" :value="9" />
              <el-option label="10" :value="10" />
              <el-option label="11" :value="11" />
              <el-option label="12" :value="12" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="组间休息：">
            <el-input v-model="item.relaxMinute" style="width: 50%">
              <template #append>分</template>
            </el-input>
            <el-input v-model="item.relaxSecond" style="width: 50%">
              <template #append>秒</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'ExercisePrescription',
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formAerobic: {
        prescriptionName: '',
        useDisease: '',
        riskLevel: '',
        trainType: '',
        trainPart: '',
        trainDevice: '',
        prescriptionCourse: '',
        trainCount: '',
        sportPeriod: ''
      },
      formResistance: {
        prescriptionName: '',
        useDisease: '',
        riskLevel: '',
        trainType: '',
        trainPart: '',
        trainDevice: '',
        prescriptionCourse: '',
        trainCount: '',
        sportPeriod: ''
      },
      // 有氧运动列表
      aerobicList: [
        {
          baseHeartRate: '',
          targetHeartRate: '',
          alertHeartRate: '',
          sportLevel: '',
          baseSportFeel: '',
          targetSportFeel: '',
          trainMinute: '',
          trainSecond: '',
          relaxMinute: '',
          relaxSecond: ''
        }
      ],
      // 抗阻运动列表
      resistanceList: [
        {
          alertHeartRate: '',
          sportLevel: '',
          useWeight: '',
          baseSportFeel: '',
          targetSportFeel: '',
          trainTimes: '',
          relaxMinute: '',
          relaxSecond: ''
        }
      ]
    }
  },
  methods: {
    initData(data) {
      if (data) {
        const [aerobic, resistance] = data.motionPrescriptionVOList
        this.formAerobic = {
          ...aerobic.motionPrescription,
          useDisease: aerobic.motionPrescription.useDisease ? aerobic.motionPrescription.useDisease.split(',') : []
        }
        this.formResistance = {
          ...resistance.motionPrescription,
          useDisease: resistance.motionPrescription.useDisease
            ? resistance.motionPrescription.useDisease.split(',')
            : []
        }
        this.aerobicList = aerobic.itemList
        this.resistanceList = resistance.itemList
      }
    },
    // 添加有氧运动项
    addAerobic() {
      this.aerobicList.push({
        baseHeartRate: '',
        targetHeartRate: '',
        alertHeartRate: '',
        sportLevel: '',
        baseSportFeel: '',
        targetSportFeel: '',
        trainMinute: '',
        trainSecond: '',
        relaxMinute: '',
        relaxSecond: ''
      })
    },
    // 删除有氧运动项
    deleteAerobic(index) {
      if (this.aerobicList.length > 1) {
        this.aerobicList.splice(index, 1)
      }
    },
    // 添加抗阻运动项
    addResistance() {
      this.resistanceList.push({
        alertHeartRate: '',
        sportLevel: '',
        useWeight: '',
        baseSportFeel: '',
        targetSportFeel: '',
        trainTimes: '',
        relaxMinute: '',
        relaxSecond: ''
      })
    },
    // 删除抗阻运动项
    deleteResistance(index) {
      if (this.resistanceList.length > 1) {
        this.resistanceList.splice(index, 1)
      }
    },

    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: true,
        data: {
          itemCode: this.itemTemp.value,
          taItemId: this.$store.getters.therapeuticActionDetail.itemList.find((it) => it.itemCode === this.itemTemp.value)
            .taItemId,
          data: {
            motionPrescriptionVOList: [
              {
                motionPrescription: {
                  ...this.formAerobic,
                  useDisease: this.formAerobic.useDisease.length > 0 ? this.formAerobic.useDisease.join(',') : ''
                },
                itemList: this.aerobicList
              },
              {
                motionPrescription: {
                  ...this.formResistance,
                  useDisease: this.formResistance.useDisease.length > 0 ? this.formResistance.useDisease.join(',') : ''
                },
                itemList: this.resistanceList
              }
            ]
          }
        }
      }

      return result
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.exercise-prescription {
  padding: 16px;
  .title {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
  }
  .operate-button {
    width: 100%;
    display: flex;
    justify-content: end;
    align-items: center;
    gap: 20px;
    margin-bottom: 16px;
    font-size: 16px;
  }
  ::v-deep .el-input__inner {
    height: 1.8rem !important;
  }
}
</style>
