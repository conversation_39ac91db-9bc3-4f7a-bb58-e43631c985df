<template>
  <el-form-item :label="item.label" :prop="item.prop">
    <div :class="item.class">
      <el-input v-model="localValue" :disabled="item.disabled" :style="{ width: item.width || '100%' }">
        <template v-if="item.append" #append>
          {{ item.append }}
        </template>
      </el-input>
    </div>
  </el-form-item>
</template>

<script>
export default {
  name: 'TextField',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: ['item', 'value'],
  computed: {
    localValue: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  }
}
</script>
