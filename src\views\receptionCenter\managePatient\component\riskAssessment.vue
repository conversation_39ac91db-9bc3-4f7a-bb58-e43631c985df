<!-- 风险评估 -->
<template>
  <div class="risk-assessment">
    <el-tabs v-model="activeTab" type="card" style="padding: 16px">
      <el-tab-pane label="缺血风险评估" name="ironDeficiency">
        <IronDeficiency ref="ironDeficiency" />
      </el-tab-pane>
      <el-tab-pane label="出血风险评估" name="bleeding">
        <Bleeding ref="bleeding" />
      </el-tab-pane>
      <!-- <el-tab-pane label="历史评估记录" name="history">
        <History />
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>
import { saveRiskAssessment, getRiskAssessment } from '@/api/standardizedManage'
import IronDeficiency from '../componentProject/IronDeficiency.vue'
import Bleeding from '../componentProject/bleeding.vue'
// import History from '../componentProject/history.vue'

export default {
  name: 'RiskAssessment',
  components: {
    IronDeficiency,
    Bleeding
    // History
  },
  props: {
    historyId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      activeTab: 'ironDeficiency',
      id: null
    }
  },
  created() {
    if (this.$route.path !== '/receptionCenter/patientReception') {
      this.getRiskAssessmentFn()
    }
  },
  methods: {
    async getRiskAssessmentFn() {
      const res = await getRiskAssessment({ smrId: this.historyId || this.$route.query.id })
      if (res.code === 200 && res.data) {
        this.id = res.data.id
        this.$nextTick(() => {
          this.$refs.ironDeficiency && this.$refs.ironDeficiency.initData(res.data)
          this.$refs.bleeding && this.$refs.bleeding.initData(res.data)
        })
      }
    },

    // 并发症筛查里面使用
    initData(data) {
      this.$refs.ironDeficiency && this.$refs.ironDeficiency.initData(data)
      this.$refs.bleeding && this.$refs.bleeding.initData(data)
    },

    async handleSave(type) {
      if (this.$route.path === '/receptionCenter/patientReception') {
        const ironDeficiencyResult = await this.$refs.ironDeficiency.handleSave()
        const bleedingResult = await this.$refs.bleeding.handleSave()
        return {
          ...ironDeficiencyResult.data,
          ...bleedingResult.data
        }
      }
      const status = 5
      const ironDeficiencyResult = await this.$refs.ironDeficiency.handleSave()
      const bleedingResult = await this.$refs.bleeding.handleSave()
      const result = {
        ...ironDeficiencyResult.data,
        ...bleedingResult.data,
        status,
        id: this.id,
        smrId: this.$route.query.id
      }
      const res = await saveRiskAssessment(result)
      if (res.code === 200) {
        if (type === 'save') {
          this.$message.success('保存成功')
          this.getRiskAssessmentFn()
        }
        return status
      }
    }
  }
}
</script>
