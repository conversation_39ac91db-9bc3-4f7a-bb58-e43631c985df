<!-- 肌电图检查 -->
<template>
  <div class="emg">
    <div class="emg-data">
      <el-table :data="tableDataMvc" border>
        <el-table-column label="神经传导-运动传导速度（MCV）" align="center">
          <el-table-column prop="nerve" label="神经" width="150" align="center" />
          <el-table-column prop="segment" label="节段" width="180" align="center" />
          <el-table-column label="波幅 (mV)" align="center">
            <template slot-scope="scope">
              <custom-input-number v-model="scope.row.amplitude" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="速度 (m/s)" align="center">
            <template slot-scope="scope">
              <custom-input-number v-model="scope.row.velocity" size="small" />
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>

      <el-table :data="tableDataScv" border style="margin-top: 30px">
        <el-table-column label="神经传导-感觉传导速度（SCV）" align="center">
          <el-table-column prop="nerve" label="神经" width="160" align="center" />
          <el-table-column prop="stimulate" label="刺激部位" width="120" align="center" />
          <el-table-column prop="record" label="记录部位" width="120" align="center" />
          <el-table-column label="波幅 (uV)" align="center">
            <template slot-scope="scope">
              <custom-input-number v-model="scope.row.amplitude" size="small" />
            </template>
          </el-table-column>
          <el-table-column label="速度 (m/s)" align="center">
            <template slot-scope="scope">
              <custom-input-number v-model="scope.row.velocity" size="small" />
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>

      <el-form ref="formRef" :model="form" :rules="rules" label-width="130px" style="margin-top: 30px">
        <el-form-item label="结论意见：">
          <el-input v-model="form.emgSuggest" type="textarea" :rows="3" />
        </el-form-item>

        <el-form-item label="报告：">
          <custom-upload v-model="form.emgAttachmentUrl" />
        </el-form-item>

        <el-form-item label="神经检查结果：" prop="emgResult">
          <el-radio-group v-model="form.emgResult">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="2">异常</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </div>
    <div class="emg-img">
      <img src="@/assets/cspImg/emg.png" alt="emg">
    </div>
  </div>
</template>

<script>
import CustomUpload from '@/components/customUpload/index.vue'
import { cloneDeep } from 'lodash'
import { mapDataToTable } from '@/utils/cspUtils'

export default {
  name: 'EMG',
  components: {
    CustomUpload
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        emgSuggest: '',
        emgAttachmentUrl: '',
        emgResult: ''
      },
      rules: {
        emgResult: [{ required: true, message: '请选择神经检查结果' }]
      },
      tableDataMvc: [
        {
          nerve: '右正中神经',
          segment: '肘→腕',
          amplitude: '',
          velocity: '',
          mcvRightMiddle: 'amplitude',
          mcvRightMiddleSpeed: 'velocity'
        },
        {
          nerve: '左正中神经',
          segment: '肘→腕',
          amplitude: '',
          velocity: '',
          mcvLeftMiddle: 'amplitude',
          mcvLeftMiddleSpeed: 'velocity'
        },
        {
          nerve: '右尺神经',
          segment: '肘→腕',
          amplitude: '',
          velocity: '',
          mcvRightElbow: 'amplitude',
          mcvRightElbowSpeed: 'velocity'
        },
        {
          nerve: '左尺神经',
          segment: '肘→腕',
          amplitude: '',
          velocity: '',
          mcvLeftElbow: 'amplitude',
          mcvLeftElbowSpeed: 'velocity'
        },
        {
          nerve: '右桡神经',
          segment: '肘→前臂中点',
          amplitude: '',
          velocity: '',
          mcvRightArm: 'amplitude',
          mcvRightArmSpeed: 'velocity'
        },
        {
          nerve: '左桡神经',
          segment: '肘→前臂中点',
          amplitude: '',
          velocity: '',
          mcvLeftArm: 'amplitude',
          mcvLeftArmSpeed: 'velocity'
        },
        {
          nerve: '右腓总神经',
          segment: '腓骨小头→踝前',
          amplitude: '',
          velocity: '',
          mcvRightFoot: 'amplitude',
          mcvRightFootSpeed: 'velocity'
        },
        {
          nerve: '左腓总神经',
          segment: '腓骨小头→踝前',
          amplitude: '',
          velocity: '',
          mcvLeftFoot: 'amplitude',
          mcvLeftFootSpeed: 'velocity'
        },
        {
          nerve: '右胫神经',
          segment: '腘窝→踝',
          amplitude: '',
          velocity: '',
          mcvRightLeg: 'amplitude',
          mcvRightLegSpeed: 'velocity'
        },
        {
          nerve: '左胫神经',
          segment: '腘窝→踝',
          amplitude: '',
          velocity: '',
          mcvLeftLeg: 'amplitude',
          mcvLeftLegSpeed: 'velocity'
        }
      ],

      tableDataScv: [
        {
          nerve: '右正中神经',
          stimulate: '中指',
          record: '腕',
          amplitude: '',
          velocity: '',
          scvRightMiddle: 'amplitude',
          scvRightMiddleSpeed: 'velocity'
        },
        {
          nerve: '左正中神经',
          stimulate: '中指',
          record: '腕',
          amplitude: '',
          velocity: '',
          scvLeftMiddle: 'amplitude',
          scvLeftMiddleSpeed: 'velocity'
        },
        {
          nerve: '右尺神经',
          stimulate: '小指',
          record: '腕',
          amplitude: '',
          velocity: '',
          scvRightElbow: 'amplitude',
          scvRightElbowSpeed: 'velocity'
        },
        {
          nerve: '左尺神经',
          stimulate: '小指',
          record: '腕',
          amplitude: '',
          velocity: '',
          scvLeftElbow: 'amplitude',
          scvLeftElbowSpeed: 'velocity'
        },
        {
          nerve: '右桡神经',
          stimulate: '拇指',
          record: '腕',
          amplitude: '',
          velocity: '',
          scvRightArm: 'amplitude',
          scvRightArmSpeed: 'velocity'
        },
        {
          nerve: '左桡神经',
          stimulate: '拇指',
          record: '腕',
          amplitude: '',
          velocity: '',
          scvLeftArm: 'amplitude',
          scvLeftArmSpeed: 'velocity'
        },
        {
          nerve: '右胫神经',
          stimulate: '腘窝',
          record: '踝',
          amplitude: '',
          velocity: '',
          scvRightLeg: 'amplitude',
          scvRightLegSpeed: 'velocity'
        },
        {
          nerve: '左胫神经',
          stimulate: '腘窝',
          record: '踝',
          amplitude: '',
          velocity: '',
          scvLeftLeg: 'amplitude',
          scvLeftLegSpeed: 'velocity'
        },
        {
          nerve: '右腓总神经',
          stimulate: '',
          record: '',
          amplitude: '',
          velocity: '',
          scvRightFoot: 'amplitude',
          scvRightFootSpeed: 'velocity'
        },
        {
          nerve: '左腓总神经',
          stimulate: '',
          record: '',
          amplitude: '',
          velocity: '',
          scvLeftFoot: 'amplitude',
          scvLeftFootSpeed: 'velocity'
        },
        {
          nerve: '右腓肠神经',
          stimulate: '跟骨上14厘米',
          record: '外踝',
          amplitude: '',
          velocity: '',
          scvRightFootBowel: 'amplitude',
          scvRightFootBowelSpeed: 'velocity'
        },
        {
          nerve: '左腓肠神经',
          stimulate: '跟骨上14厘米',
          record: '外踝',
          amplitude: '',
          velocity: '',
          scvLeftBowelFoot: 'amplitude',
          scvLeftFootBowelSpeed: 'velocity'
        },
        {
          nerve: '右腓浅神经',
          stimulate: '浅骨上肌',
          record: '足背',
          amplitude: '',
          velocity: '',
          scvRightFootShallow: 'amplitude',
          scvRightFootShallowSpeed: 'velocity'
        },
        {
          nerve: '左腓浅神经',
          stimulate: '浅骨上肌',
          record: '足背',
          amplitude: '',
          velocity: '',
          scvLeftFootShallow: 'amplitude',
          scvLeftFootShallowSpeed: 'velocity'
        }
      ]
    }
  },
  methods: {
    initData(data) {
      this.tableDataMvc = mapDataToTable(data, cloneDeep(this.tableDataMvc))
      this.tableDataScv = mapDataToTable(data, cloneDeep(this.tableDataScv))
      this.form = {
        emgSuggest: data.emgSuggest,
        emgAttachmentUrl: data.emgAttachmentUrl,
        emgResult: data.emgResult,
        id: data.id
      }
    },

    async handleSave() {
      const mvcTemp = this.mapAmplitudeAndVelocityOnly(this.tableDataMvc).reduce((acc, item) => {
        acc = Object.assign(acc, item)
        return acc
      }, {})
      const scvTemp = this.mapAmplitudeAndVelocityOnly(this.tableDataScv).reduce((acc, item) => {
        acc = Object.assign(acc, item)
        return acc
      }, {})
      const result = {
        name: this.itemTemp.label,
        success: true,
        data: {
          ...this.form,
          ...mvcTemp,
          ...scvTemp,
          itemCode: this.itemTemp.value,
          itemId: this.itemTemp.id,
          itemDetailId: this.form.id
        }
      }
      try {
        const valid = await this.$refs.formRef.validate()
        result.success = valid
      } catch (err) {
        console.warn(`${this.itemTemp.label}校验异常`, err)
        result.success = false
      }
      return result
    },

    mapAmplitudeAndVelocityOnly(data) {
      return data.map((item) => {
        const result = {}
        for (const key in item) {
          if (key.startsWith('mcv') && item[key] === 'amplitude') {
            result[key] = item.amplitude
          } else if (key.startsWith('mcv') && item[key] === 'velocity') {
            result[key] = item.velocity
          } else if (key.startsWith('scv') && item[key] === 'amplitude') {
            result[key] = item.amplitude
          } else if (key.startsWith('scv') && item[key] === 'velocity') {
            result[key] = item.velocity
          }
        }
        return result
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.emg {
  display: flex;
  justify-content: space-between;
  gap: 50px;
  .emg-data {
    width: 70%;
  }
  .emg-img {
    margin-top: 50px;
    flex: 1;
  }
}
</style>
