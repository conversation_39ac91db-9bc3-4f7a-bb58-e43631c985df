<template>
  <ManageByObjectives ref="manageByObjectivesRef" :is-target-inspection="true" :history-id="historyId" />
</template>

<script>
import ManageByObjectives from './manageByObjectives.vue'
import { saveTargetInspection } from '@/api/standardizedManage'

export default {
  name: 'TargetInspection',
  components: {
    ManageByObjectives
  },
  props: {
    historyId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  methods: {
    async handleSave(type) {
      const status = 5
      const result = await this.$refs.manageByObjectivesRef.handleTargetInspectionSave()
      const params = {
        ...result.data,
        smrId: this.$route.query.id,
        status
      }
      const res = await saveTargetInspection(params)
      if (res.code === 200) {
        this.$refs.manageByObjectivesRef.getTargetInspectionFn()
        if (type === 'save') {
          this.$message.success('保存成功')
        }
      }
      return status
    }
  }
}
</script>
