<template>
  <div class="read-card-module" :class="{ 'is-reading': isReading }">
    <img v-if="!isConnected" :src="noConnectIcon || require('@/assets/cspImg/cardNoConnect.png')" alt="未连接">
    <img
      v-else-if="isReading"
      :src="readingIcon || require('@/assets/cspImg/cardConnect.png')"
      class="reading-animation"
      alt="读取中"
    >
    <img v-else :src="connectedIcon || require('@/assets/cspImg/cardConnect.png')" alt="已连接">
    <span>{{ buttonText }}</span>
    <el-tooltip v-if="errorMessage" effect="dark" :content="errorMessage" placement="top">
      <i class="el-icon-warning-outline error-icon" />
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'ReadCardModule',
  props: {
    // 读卡器WebSocket服务地址
    socketUrl: {
      type: String,
      default: 'ws://127.0.0.1:7415'
    },
    // 读卡间隔时间(毫秒)
    readInterval: {
      type: Number,
      default: 3000
    },
    // 重连间隔时间(毫秒)
    reconnectInterval: {
      type: Number,
      default: 5000
    },
    // 是否自动连接
    autoConnect: {
      type: Boolean,
      default: true
    },
    // 按钮文本
    buttonText: {
      type: String,
      default: '读取身份证'
    },
    // 自定义未连接图标
    noConnectIcon: {
      type: String,
      default: ''
    },
    // 自定义已连接图标
    connectedIcon: {
      type: String,
      default: ''
    },
    // 自定义读取中图标
    readingIcon: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      ws: null,
      isConnected: false,
      isReading: false,
      readIdCardTimer: null,
      reconnectTimer: null,
      reconnectCount: 0,
      errorMessage: '',
      maxReconnectAttempts: 5
    }
  },
  computed: {
    isSocketAvailable() {
      return 'WebSocket' in window
    }
  },
  watch: {
    socketUrl: {
      handler(newUrl) {
        if (this.isConnected && this.ws) {
          this.doSocketClose()
          this.$nextTick(() => {
            this.initSocket()
          })
        }
      }
    }
  },
  mounted() {
    if (this.autoConnect) {
      this.initSocket()
    }
  },
  beforeDestroy() {
    this.clearTimers()
    this.ws && this.ws.close()
  },
  methods: {
    /** 初始化 WebSocket 连接 */
    initSocket() {
      this.errorMessage = ''

      if (!this.isSocketAvailable) {
        this.errorMessage = '您的浏览器不支持WebSocket，无法使用身份证读卡器'
        this.$message.error(this.errorMessage)
        return
      }

      try {
        this.ws = new WebSocket(this.socketUrl)
      } catch (error) {
        this.errorMessage = `连接读卡器服务失败: ${error.message || '未知错误'}`
        this.$message.error(this.errorMessage)
        return
      }

      this.ws.onopen = this.handleSocketOpen
      this.ws.onmessage = this.handleSocketMessage
      this.ws.onerror = this.handleSocketError
      this.ws.onclose = this.handleSocketClose
    },

    /** 处理WebSocket打开事件 */
    handleSocketOpen() {
      this.isConnected = true
      this.reconnectCount = 0
      this.errorMessage = ''
      this.$emit('onChange', true)
      this.$emit('onConnected')
      this.readIdCard()
    },

    /** 处理WebSocket消息事件 */
    handleSocketMessage(res) {
      if (!res || !res.data) return

      this.isReading = false

      if (res.data === 'failed to obtain ID card information') {
        // 这是正常的"未读到卡"消息，不需要显示错误
        return
      }

      try {
        const allData = res.data.split('|')
        const resData =
          allData.length >= 17
            ? {
              code: 200,
              msg: '读卡成功',
              cardType: allData[0],
              name: allData[1],
              gender: allData[2],
              nation: allData[3],
              bornDay: allData[4],
              certAddress: allData[5],
              certNumber: allData[6],
              certOrg: allData[7],
              effDate: allData[8],
              expDate: allData[9],
              photoDisplay: `data:image/bmp;base64,${allData[17]}`
            }
            : {
              code: 100,
              msg: res.data
            }
        this.$emit('onMessage', resData)

        if (resData.code === 200) {
          this.$emit('onSuccess', resData)
        } else {
          this.$emit('onFail', resData)
        }
      } catch (error) {
        this.errorMessage = `解析身份证数据失败: ${error.message || '未知错误'}`
        this.$emit('onError', {
          code: 500,
          msg: this.errorMessage,
          error
        })
      }
    },

    /** 处理WebSocket错误事件 */
    handleSocketError(error) {
      this.errorMessage = `读卡器连接错误: ${error.message || '未知错误'}`
      this.$emit('onError', {
        code: 500,
        msg: this.errorMessage,
        error
      })
      this.handleDisconnect()
    },

    /** 处理WebSocket关闭事件 */
    handleSocketClose(event) {
      if (event && event.code !== 1000) {
        this.errorMessage = `读卡器连接已关闭: ${event.reason || '未知原因'}`
        this.$emit('onError', {
          code: 500,
          msg: this.errorMessage,
          event
        })
      }
      this.handleDisconnect()
    },

    /** 轮询读取身份证 */
    readIdCard() {
      if (!this.isConnected || !this.ws || this.ws.readyState !== WebSocket.OPEN) {
        this.errorMessage = '读卡器服务未连接'
        return
      }

      try {
        this.isReading = true
        const command = '02?timeout=1'
        this.ws.send(command)
      } catch (error) {
        this.errorMessage = `发送读卡命令失败: ${error.message || '未知错误'}`
        this.isReading = false
        this.$emit('onError', {
          code: 500,
          msg: this.errorMessage,
          error
        })
      }

      clearTimeout(this.readIdCardTimer)
      this.readIdCardTimer = setTimeout(() => {
        this.isReading = false
        this.readIdCard()
      }, this.readInterval)
    },

    /** 主动关闭 socket */
    doSocketClose() {
      this.clearTimers()
      if (this.ws) {
        try {
          this.ws.close(1000, '用户主动关闭连接')
        } catch (error) {
          console.error('关闭WebSocket连接失败:', error)
        } finally {
          this.ws = null
        }
      }
    },

    /** 处理断开连接 */
    handleDisconnect() {
      this.isConnected = false
      this.isReading = false
      this.$emit('onChange', false)
      this.$emit('onDisconnected')
      this.clearTimers()

      // 尝试重连，但限制重连次数
      if (this.reconnectCount < this.maxReconnectAttempts && !this.reconnectTimer) {
        this.reconnectCount++
        this.reconnectTimer = setTimeout(() => {
          this.initSocket()
          this.reconnectTimer = null
        }, this.reconnectInterval)
      } else if (this.reconnectCount >= this.maxReconnectAttempts) {
        this.errorMessage = `已尝试重连${this.maxReconnectAttempts}次，请检查读卡器服务是否正常运行`
        this.$emit('onMaxReconnectAttempts')
      }
    },

    /** 清理所有定时器 */
    clearTimers() {
      clearTimeout(this.readIdCardTimer)
      clearTimeout(this.reconnectTimer)
      this.readIdCardTimer = null
      this.reconnectTimer = null
    },

    /** 手动重新连接 */
    connect() {
      this.reconnectCount = 0
      this.clearTimers()
      this.ws && this.ws.close()
      this.ws = null
      this.$nextTick(() => {
        this.initSocket()
      })
    }
  }
}
</script>

<style scoped lang="scss">
.read-card-module {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 6px 10px;
  border-radius: 4px;
  transition: all 0.3s;
  cursor: pointer;

  &:hover {
    background-color: #f5f7fa;
  }

  span {
    font-size: 14px;
    color: #333;
    user-select: none;
  }

  img {
    width: 30px;
    height: 30px;
    object-fit: contain;
  }

  .error-icon {
    color: #f56c6c;
    margin-left: 5px;
    font-size: 16px;
  }
}
</style>
