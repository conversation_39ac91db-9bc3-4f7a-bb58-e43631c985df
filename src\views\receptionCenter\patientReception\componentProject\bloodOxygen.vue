<template>
  <div>
    <el-form :model="form" label-width="200px">
      <el-form-item label="血氧饱和度（SpO2）：">
        <custom-input-number v-model="form.spo2" style="width: 30%">
          <template #append>%</template>
        </custom-input-number>
      </el-form-item>
      <el-form-item label="脉率（PR）：">
        <custom-input-number v-model="form.pr" style="width: 30%">
          <template #append>次/分</template>
        </custom-input-number>
      </el-form-item>
      <el-form-item label="血流灌注指数（PI）：">
        <custom-input-number v-model="form.pi" style="width: 30%" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'BloodOxygen',
  data() {
    return {
      form: {
        spo2: '',
        pr: '',
        pi: ''
      }
    }
  },
  methods: {
    initData({ id, data }) {
      this.form = {
        spo2: data && data.spo2,
        pr: data && data.pr,
        pi: data && data.pi,
        itemId: id,
        itemDetailId: data && data.id
      }
    }
  }
}
</script>
