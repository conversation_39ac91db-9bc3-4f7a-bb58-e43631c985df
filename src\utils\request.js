import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'
import { addPendingRequest, removePendingRequest } from './requestManager'
import { localCache } from '@/utils/cache'

let currentContextKey = '' // 当前上下文（如路由路径）
let baseUrlAndroid = ''
if (window.android && window.android.getBaseURL) {
  baseUrlAndroid = window.android.getBaseURL()
}

const service = axios.create({
  baseURL: baseUrlAndroid || process.env.VUE_APP_BASE_API, // url = base url + request url
  timeout: 60 * 1000 // request timeout
})

// request interceptor
service.interceptors.request.use(
  (config) => {
    // do something before request is sent
    config.headers.clientId = 'pc'
    config.headers['X-Tenant-Id'] = localCache.getCache('tenantId')
    if (store.getters.token) {
      config.headers.Authorization = getToken()
    }
    addPendingRequest(config, currentContextKey) // 将当前请求添加到 pending 中
    return config
  },
  (error) => {
    // do something with request error
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  (response) => {
    removePendingRequest(response.config, currentContextKey)
    const res = response.data
    if (window.android && window.android.consoleLog) {
      window.android.consoleLog('每次接口调用结束回调：')
      window.android.consoleLog(`接口地址：${response.config.url}`)
      window.android.consoleLog(`接口返回的数据：${JSON.stringify(res)}`)
    }
    // removePending(response) // 在请求结束后，移除本次请求
    // /api/gateway/loginByPhone
    if (response.config.url === '/api/gateway/getSmsCode') {
      if (`${res.code}` !== '200') {
        if (res.msg) {
          Message.error(res.msg)
        }
        return Promise.reject(new Error(res.msg || '服务器异常！'))
      } else {
        return res
      }
    } else {
      if (res.code === 101) {
        Message.error(res.msg)
        return Promise.reject(new Error(res.msg || '请求错误'))
      }
      if (`${res.code}` === '401') {
        // token失效  跳转登录页
        window.location.href = '/login'
        Message.error('登录过期，请重新登录！')
        return Promise.reject(new Error('登录过期，请重新登录！'))
      }
      // 后端自定义的一个错误状态码
      if (res.code === 601) {
        Message.error(res.msg)
        return Promise.reject(new Error(res.msg || '请求错误'))
      }
      if (`${res.code}` === '500') {
        Message({
          message: res.msg || '服务器异常！',
          type: 'error',
          duration: 5 * 1000
        })
        return Promise.reject(new Error(res.msg || '服务器异常！'))
      } else {
        return res
      }
    }
  },
  (error) => {
    // if (axios.isCancel(error)) { // 处理手动cancel
    //   console.log('这是手动cancel的', error)
    // } else {
    // 检查是否有响应内容
    console.log(error.response)
    if (error.response) {
      const { status, data, request } = error.response
      if (status === 500) {
        // 如果服务器在 500 状态码时返回了错误信息
        const errorMsg = data ? data + request.responseURL : '服务器异常！'
        Message({
          message: errorMsg,
          type: 'error',
          duration: 5 * 1000
        })
        return Promise.reject(new Error(errorMsg))
      }
    }
    // 处理没有响应的网络错误 请求已取消 重复请求已取消 是路由跳转取消未完成的请求
    console.log(error.message, 'error.message')
    // if (error.message !== '请求已取消' && error.message !== '重复请求已取消') {
    if (error.message !== '请求已取消') {
      Message({
        message: error.message === 'Network Error' ? '网络异常' : error.message,
        type: 'error'
        // duration: 5 * 1000
      })
    }
    removePendingRequest(error.config, currentContextKey)
    return Promise.reject(error)
  }
)

export const setRequestContext = (contextKey) => {
  currentContextKey = contextKey
}
export default service
