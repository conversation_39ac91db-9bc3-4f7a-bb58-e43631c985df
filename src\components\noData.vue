<template>
  <div class="noData tac">
    <img :src="imgPath">
    <slot name="noDataTxt">
      <p>{{ txt }}</p>
    </slot>
  </div>
</template>

<script>
export default {
  name: 'IconSvg',
  props: {
    imgPath: {
      type: String,
      default: require('@/assets/img/noData.png')
    },
    txt: {
      type: String,
      default: '暂无数据'
    }
  }
}
</script>

<style lang="scss" scoped>
.noData {
  width: 100%;
  fill: currentColor;
  overflow: hidden;
  cursor: pointer;

  img {
    display: block;
    margin: 1rem auto;
    width: 50%;
  }

  div {
    text-align: center;
    color: #999;
    margin-top: 1rem;
    font-size: 1rem;
  }
}
</style>
