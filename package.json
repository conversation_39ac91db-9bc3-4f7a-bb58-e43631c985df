{"name": "csp_web", "version": "1.0.0", "description": "csp_web", "author": "<PERSON><PERSON> <http://wigroup.com.cn/#/>", "scripts": {"start": "vue-cli-service serve --skip-plugins @vue/cli-plugin-eslint", "dev": "vue-cli-service serve --skip-plugins @vue/cli-plugin-eslint", "lint": "eslint --ext .js,.vue src", "build:prod": "cross-env VUE_APP_ENV=prod vue-cli-service build", "build:test": "cross-env VUE_APP_ENV=test vue-cli-service build", "preview": "node build/index.js --preview", "new": "plop", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "p": "prettier --write .", "esf": "eslint --fix --ext .js,.vue src"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "@riophae/vue-treeselect": "^0.4.0", "@wangeditor/editor": "^5.1.11", "axios": "0.18.1", "clipboard": "2.0.4", "codemirror": "5.45.0", "core-js": "3.6.5", "cornerstone-core": "^2.6.1", "cornerstone-math": "^0.1.10", "cornerstone-tools": "^6.0.10", "cornerstone-wado-image-loader": "^4.13.2", "cornerstone-web-image-loader": "^2.1.1", "cross-env": "^7.0.3", "dayjs": "^1.11.13", "dicom-parser": "^1.8.21", "driver.js": "0.9.5", "dropzone": "5.5.1", "easy-ring": "^2.0.2", "echarts": "^5.4.2", "element-ui": "^2.15.7", "file-saver": "2.0.1", "fuse.js": "3.4.4", "hammerjs": "^2.0.8", "html2canvas": "^1.4.1", "js-audio-recorder": "^1.0.7", "js-cookie": "^3.0.1", "js-pinyin": "^0.2.4", "jsbarcode": "^3.11.5", "jsonlint": "1.6.3", "jspdf": "^2.5.1", "lodash": "^4.17.21", "normalize.css": "7.0.0", "nprogress": "0.2.0", "path-to-regexp": "2.4.0", "print-js": "^1.6.0", "qrcodejs2": "0.0.2", "recorder-core": "^1.2.23070100", "screenfull": "4.2.0", "script-loader": "0.7.2", "sortablejs": "1.8.4", "trtc-js-sdk": "^4.15.3", "v-viewer": "^1.6.4", "vue": "2.6.10", "vue-count-to": "1.0.13", "vue-pdf": "^4.3.0", "vue-print-nb": "^1.7.5", "vue-qr": "^4.0.9", "vue-router": "3.0.2", "vue-splitpane": "1.0.4", "vuedraggable": "2.20.0", "vuex": "3.1.0", "vuex-persistedstate": "^4.1.0", "wangeditor": "^4.7.15", "wavesurfer.js": "^7.8.4"}, "devDependencies": {"@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "4.4.4", "@vue/cli-service": "4.4.4", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "9.5.1", "babel-eslint": "^7.2.3", "babel-jest": "23.6.0", "babel-plugin-component": "^1.1.1", "babel-plugin-dynamic-import-node": "2.3.3", "chalk": "2.4.2", "chokidar": "2.1.5", "compression-webpack-plugin": "^v6.1.1", "connect": "3.6.6", "crypto-js": "^4.1.1", "eslint": "^7.2.0", "eslint-config-ali": "^14.0.2", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.19.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-vue": "^9.11.0", "html-webpack-plugin": "3.2.0", "husky": "^1.3.1", "lint-staged": "8.1.5", "mockjs": "1.0.1-beta3", "plop": "2.3.0", "prettier": "^2.8.7", "runjs": "4.3.2", "sass": "1.39.0", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "speed-measure-webpack-plugin": "^1.5.0", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "uglifyjs-webpack-plugin": "^2.2.0", "vue-eslint-parser": "^7.0.0", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "bugs": {"url": "https://github.com/PanJiaChen/vue-element-admin/issues"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "repository": {"type": "git", "url": "git+https://github.com/PanJiaChen/vue-element-admin.git"}}