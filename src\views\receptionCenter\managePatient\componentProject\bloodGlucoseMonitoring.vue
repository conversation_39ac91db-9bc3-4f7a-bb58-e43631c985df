<!-- 血糖监测 -->
<template>
  <div class="blood-glucose-monitoring">
    <div class="table-container">
      <flag-component :title="'血糖监测'" desc="时间和血糖值为必填项，任何一项为空均视为无效数据。" />
      <el-table :data="tableData" border style="margin-top: 16px">
        <el-table-column prop="measureTime" label="时间" align="center">
          <template slot-scope="scope">
            <el-date-picker
              v-model="scope.row.measureTime"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="选择时间"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sugarType" label="血糖类型" align="center">
          <template slot-scope="scope">
            <el-select v-model="scope.row.sugarType" placeholder="请选择血糖类型">
              <el-option label="空腹血糖" :value="1" />
              <!-- <el-option label="随机血糖" :value="2" /> -->
              <el-option label="餐后2h血糖" :value="3" />
              <el-option label="糖化血红蛋白" :value="4" />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column prop="sugarValue" label="血糖值" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.sugarValue" />
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center">
          <template slot-scope="scope">
            <el-input v-model="scope.row.remark" />
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template slot-scope="scope">
            <span v-if="scope.$index === tableData.length - 1">
              <i class="el-icon-plus" style="color: #41a1d4; cursor: pointer" @click="handleAdd" />
            </span>
            <span>
              <i
                class="el-icon-delete"
                style="color: red; margin-left: 16px; cursor: pointer"
                @click="handleDelete(scope.$index)"
              />
            </span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="table-echarts">
      <flag-component title="血糖折线图" />
      <blood-glucose-echart ref="bloodGlucoseEchart" :table-data="tableData" />
    </div>
    <div class="desc">
      <flag-component title="血糖监测说明" />
      <el-input v-model="description" type="textarea" :rows="3" style="margin-top: 16px" />
    </div>
  </div>
</template>

<script>
import flagComponent from '@/components/flagComponent/index.vue'
import bloodGlucoseEchart from '../echarts/bloodGlucoseEchart.vue'

export default {
  name: 'BloodGlucoseMonitoring',
  components: { flagComponent, bloodGlucoseEchart },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      description: '',
      descriptionId: null,
      tableData: [
        {
          measureTime: '',
          sugarType: '',
          sugarValue: '',
          remark: ''
        }
      ]
    }
  },
  methods: {
    initData(data) {
      if (data) {
        this.description = data.sugarMonitor.description
        this.descriptionId = data.sugarMonitor.id
        this.tableData = data.itemList
      }
    },

    initEcharts() {
      this.$nextTick(() => {
        this.$refs.bloodGlucoseEchart.initChart()
      })
    },

    handleAdd() {
      this.tableData.push({
        measureTime: '',
        sugarType: '',
        sugarValue: '',
        remark: ''
      })
    },

    handleDelete(index) {
      this.tableData.splice(index, 1)
    },

    async handleSave() {
      const result = {
        name: this.itemTemp.label,
        success: true,
        data: {
          data: {
            sugarMonitor: {
              description: this.description,
              id: this.descriptionId
            },
            itemList: this.tableData
          },
          itemCode: this.itemTemp.value,
          taItemId: this.$store.getters.therapeuticActionDetail.itemList.find((it) => it.itemCode === this.itemTemp.value)
            .taItemId
        }
      }

      return result
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/form-overrides.scss';
.blood-glucose-monitoring {
  padding: 16px;
  .table-echarts {
    margin-top: 16px;
    width: 100%;
  }
}
</style>
