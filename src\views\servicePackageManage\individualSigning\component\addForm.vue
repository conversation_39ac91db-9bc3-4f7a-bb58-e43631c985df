<template>
  <div class="add-form">
    <el-form ref="formRef" :model="form" label-width="130px" :rules="rules">
      <flag-component title="签约团队" style="margin-bottom: 10px" />
      <el-row>
        <el-col :span="12">
          <el-form-item label="所属机构：" prop="departCode">
            <TreeSelect
              v-model="form.departCode"
              disabled
              :data="departTree"
              :props="{
                children: 'children',
                label: 'departName',
                value: 'departCode'
              }"
              placeholder="请选择"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="家医团队：" prop="teamId">
            <el-select
              v-model="form.teamId"
              placeholder="请选择家医团队"
              style="width: 100%"
              @change="handleTeamChange"
            >
              <el-option v-for="item in teamList" :key="item.id" :label="item.teamName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="家庭医生：" prop="doctorId">
            <el-select v-model="form.doctorId" placeholder="请选择家庭医生" style="width: 100%">
              <el-option
                v-for="item in doctorList"
                :key="item.doctorId"
                :label="item.doctorName"
                :value="item.doctorId"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <flag-component title="居民信息" style="margin-bottom: 10px" />
        <el-col :span="12">
          <el-form-item label="姓名：" prop="name">
            <el-input v-model="form.name" disabled placeholder="请输入姓名" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="证件号码：" prop="originalIdCard">
            <el-input v-model="form.originalIdCard" disabled placeholder="请输入证件号码" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="联系电话：" prop="originalPhone">
            <el-input v-model="form.originalPhone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="户籍地址：" prop="nativeAddress">
            <el-input v-model="form.nativeAddress" disabled placeholder="请输入户籍地址" />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="现居地址：" prop="currentAddress">
            <el-input v-model="form.currentAddress" disabled placeholder="请输入现居地址" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <flag-component title="签约信息" style="margin-bottom: 10px" />

        <el-col :span="12">
          <el-form-item label="签约服务包：" prop="spIdList">
            <el-select
              v-model="form.spIdList"
              placeholder="请选择签约服务包"
              multiple
              style="width: 100%"
              @change="handleSpChange"
            >
              <el-option v-for="item in servicePackageList" :key="item.id" :label="item.spName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item label="签约金额：" prop="selfAmt">
            <el-input v-model="form.selfAmt" disabled placeholder="请输入签约金额" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="签约周期：" prop="signCycle">
            <el-date-picker
              v-model="form.signCycle"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="12">
          <el-form-item label="签约协议模板：" prop="moduleId">
            <el-select v-model="form.moduleId" placeholder="请选择签约协议模板" style="width: 100%">
              <el-option v-for="item in protocolList" :key="item.id" :label="item.moduleName" :value="item.id" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12" style="text-align: center">
          <span style="color: #409eff; cursor: pointer" @click="previewTemplate">预览模板</span>
        </el-col>
      </el-row>
    </el-form>

    <ContractSigningProtocol ref="contractSigningProtocol" />
  </div>
</template>

<script>
import { getPatientInfoByIdCardApi } from '@/api/system'
import {
  getTeamListByDepart,
  getDoctorListByTeam,
  getProtocolList,
  getServicePackageList,
  createIndividualSigning,
  getIndividualSigningDetail
} from '@/api/individualSigning'
import FlagComponent from '@/components/flagComponent/index.vue'
import TreeSelect from '@/components/TreeSelect/index.vue'
import ContractSigningProtocol from './contractSigningProtocol.vue'

export default {
  name: 'AddForm',
  components: {
    FlagComponent,
    TreeSelect,
    ContractSigningProtocol
  },
  props: {
    departTree: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      form: {
        id: '',
        departCode: '',
        teamId: '',
        doctorId: '',
        name: '',
        originalIdCard: '',
        originalPhone: '',
        nativeAddress: '',
        currentAddress: '',
        signCycle: [],
        spIdList: [],
        selfAmt: '',
        moduleId: ''
      },
      patientInfo: {},
      teamList: [],
      doctorList: [],
      protocolList: [],
      servicePackageList: [],
      rules: {
        departCode: [{ required: true, message: '请选择所属机构', trigger: 'change' }],
        teamId: [{ required: true, message: '请输入家医团队', trigger: 'change' }],
        doctorId: [{ required: true, message: '请输入家庭医生', trigger: 'change' }],
        name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
        originalIdCard: [{ required: true, message: '请输入证件号码', trigger: 'blur' }],
        originalPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
        signCycle: [{ required: true, message: '请选择签约周期', trigger: 'change' }],
        spIdList: [{ required: true, message: '请选择签约服务包', trigger: 'change' }],
        moduleId: [{ required: true, message: '请选择签约协议模板', trigger: 'change' }]
      }
    }
  },

  methods: {
    async getPatientInfoByIdCard(idCard) {
      const res = await getPatientInfoByIdCardApi({ idCard })
      if (res.code === 200) {
        this.patientInfo = res.data
        this.form.departCode = res.data.departCode
        this.form.name = res.data.name
        this.form.originalIdCard = res.data.originalIdCard
        this.form.originalPhone = res.data.originalPhone
        this.form.nativeAddress = res.data.nativeAddress
        this.form.currentAddress = res.data.currentAddress
        this.getTeamListByDepart(res.data.departCode)
        this.getProtocolListFn(res.data.departCode)
        this.getServicePackageListFn(res.data.departCode)
      } else {
        this.$message.error(res.message)
      }
    },

    // 获取签约记录详情
    async getIndividualSigningDetail(id) {
      const res = await getIndividualSigningDetail({ id })
      if (res.code === 200) {
        this.form = {
          ...res.data,
          signCycle: [res.data.cycleStartDate, res.data.cycleEndDate]
        }
        this.getPatientInfoByIdCard(res.data.originalIdCard)
        this.getDoctorListByTeamFn(res.data.teamId)
      }
    },

    // 获取协议模板列表
    async getProtocolListFn(departCode) {
      const res = await getProtocolList({ departCode })
      if (res.code === 200) {
        this.protocolList = res.data
      }
    },

    // 获取团队列表
    async getTeamListByDepart(departCode) {
      const res = await getTeamListByDepart({ departCode })
      if (res.code === 200) {
        this.teamList = res.data
      } else {
        this.$message.error(res.message)
      }
    },

    // 获取医生列表
    async getDoctorListByTeamFn(teamId) {
      const res = await getDoctorListByTeam({ id: teamId })
      if (res.code === 200) {
        this.doctorList = res.data
      } else {
        this.$message.error(res.message)
      }
    },

    // 获取服务包列表
    async getServicePackageListFn(departCode) {
      const res = await getServicePackageList({ departCode })
      if (res.code === 200) {
        this.servicePackageList = res.data
      }
    },

    // 团队选择
    handleTeamChange(value) {
      this.form.doctorId = ''
      this.getDoctorListByTeamFn(value)
    },

    // 服务包选择
    handleSpChange(value) {
      const itemList = this.servicePackageList.filter((item) => value.includes(item.id))
      this.form.selfAmt = itemList.reduce((acc, curr) => acc + curr.spAmt, 0)
    },

    // 预览模板
    previewTemplate() {
      if (!this.form.moduleId) {
        this.$message.error('请选择签约协议模板')
        return
      }
      const item = this.protocolList.find((it) => it.id === this.form.moduleId)
      this.$nextTick(() => {
        this.$refs.contractSigningProtocol.previewTemplate(item.moduleContent)
      })
    },

    // 保存
    async handleSave() {
      const valid = await this.$refs.formRef.validate()
      if (!valid) return
      const params = {
        ...this.form,
        patientId: this.patientInfo.id,
        cycleStartDate: this.form.signCycle[0],
        cycleEndDate: this.form.signCycle[1]
      }
      createIndividualSigning(params).then((res) => {
        if (res.code === 200) {
          this.$message.success('保存成功')
          this.$emit('saveSuccess')
        }
      })
    }
  }
}
</script>
