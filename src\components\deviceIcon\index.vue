<template>
  <div class="device-icon">
    <svg-icon :icon-class="getIcon" style="width: 60px; height: 60px" />
  </div>
</template>

<script>
export default {
  name: 'DeviceIcon',
  props: {
    deviceType: {
      type: String,
      default: ''
    },
    socketConnect: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    getIcon() {
      if (this.deviceType === 'bodyComposition') {
        if (this.socketConnect) {
          return 'bodyCompositionOpen'
        } else {
          return 'bodyCompositionClose'
        }
      } else if (this.deviceType === 'fundusExamination') {
        if (this.socketConnect) {
          return 'fundusExaminationOpen'
        } else {
          return 'fundusExaminationClose'
        }
      } else if (this.deviceType === 'arteriosclerosis') {
        if (this.socketConnect) {
          return 'arteriosclerosisOpen'
        } else {
          return 'arteriosclerosisClose'
        }
      } else if (this.deviceType === 'peripheralArtery') {
        if (this.socketConnect) {
          return 'peripheralArteryOpen'
        } else {
          return 'peripheralArteryClose'
        }
      } else if (this.socketConnect) {
        return 'ocrOpen'
      } else {
        return 'ocrClose'
      }
    }
  }
}
</script>
