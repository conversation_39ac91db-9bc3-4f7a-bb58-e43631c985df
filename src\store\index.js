/* eslint-disable no-shadow */
import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'
import createPersistedState from 'vuex-persistedstate'

Vue.use(Vuex)

// https://webpack.js.org/guides/dependency-management/#requirecontext
const modulesFiles = require.context('./modules', true, /\.js$/)

// you do not need `import app from './modules/app'`
// it will auto require all vuex module from modules file
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  // set './app.js' => 'app'
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
  const value = modulesFiles(modulePath)
  modules[moduleName] = value.default
  return modules
}, {})

const store = new Vuex.Store({
  modules,
  getters: {
    ...getters,
    parsedRouteParams: (state) => {
      return Object.keys(state.routeParams).reduce((acc, key) => {
        try {
          const value = state.routeParams[key]
          if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
            acc[key] = JSON.parse(value)
          } else {
            acc[key] = value
          }
        } catch (e) {
          acc[key] = state.routeParams[key]
        }
        return acc
      }, {})
    },
    getRouteParam: (state, getters) => (key) => {
      return getters.parsedRouteParams[key]
    }
  },
  state: {
    routeParams: {},
    userdbSearchData: {},
    publicHealthSearchData: {},
    followupSearchData: {},
    bodyCheckSearchData: {},
    manageSearchData: {}
  },
  mutations: {
    SET_ROUTE_PARAMS(state, params) {
      const processedParams = Object.keys(params).reduce((acc, key) => {
        try {
          if (typeof params[key] === 'object' && params[key] !== null) {
            acc[key] = JSON.stringify(params[key])
          } else {
            acc[key] = params[key]
          }
        } catch (e) {
          console.error(`参数处理错误: ${key}`, e)
          acc[key] = params[key]
        }
        return acc
      }, {})

      state.routeParams = processedParams
    },
    SET_USERDB_SEARCH_DATA(state, payload) {
      state.userdbSearchData = payload
    },
    SET_PUBLIC_HEALTH_SEARCH_DATA(state, payload) {
      state.publicHealthSearchData = payload
    },
    SET_FOLLOWUP_SEARCH_DATA(state, payload) {
      state.followupSearchData = payload
    },
    SET_BODY_CHECK_SEARCH_DATA(state, payload) {
      state.bodyCheckSearchData = payload
    },
    SET_MANAGE_SEARCH_DATA(state, payload) {
      state.manageSearchData = payload
    }
  },
  actions: {
    setRouteParams({ commit }, params) {
      commit('SET_ROUTE_PARAMS', params)
    }
  },

  plugins: [
    createPersistedState({
      storage: window.sessionStorage,
      paths: [
        'routeParams',
        'userdbSearchData',
        'publicHealthSearchData',
        'followupSearchData',
        'bodyCheckSearchData',
        'manageSearchData'
      ]
    })
  ]
})

export default store
