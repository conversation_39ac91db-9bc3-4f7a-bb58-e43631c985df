<!-- 效果评估 -->
<template>
  <div v-loading="$store.state.managePatient.loading" class="effect-evaluation">
    <div class="effect-evaluation-title">
      <flag-component title="管理效果评估：" />
      <el-tag v-if="detail.tnbResult === 1" type="danger">糖尿病未达标</el-tag>
      <el-tag v-if="detail.gxzResult === 1" type="danger">高血脂未达标</el-tag>
      <el-tag v-if="detail.gxyResult === 1" type="danger">高血压未达标</el-tag>
      <el-tag v-if="detail.fcResult === 1" type="danger">房颤未达标</el-tag>
    </div>

    <div class="effect-evaluation-content">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="name" label="检查项">
          <template slot-scope="scope">
            <span v-if="scope.row.result === 1" style="color: red">{{ scope.row.name }}</span>
            <span v-else>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="baseValue" label="基线值">
          <template slot-scope="scope">
            <span v-if="scope.row.result === 1" style="color: red">{{ scope.row.baseValue }}</span>
            <span v-else>{{ scope.row.baseValue }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="targetValue" label="目标值">
          <template slot-scope="scope">
            <span v-if="scope.row.result === 1" style="color: red">{{ scope.row.targetValue }}</span>
            <span v-else>{{ scope.row.targetValue }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="checkValue" label="检查值">
          <template slot-scope="scope">
            <span v-if="scope.row.result === 1" style="color: red">{{ scope.row.checkValue }}</span>
            <span v-else>{{ scope.row.checkValue }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getTargetInspection } from '@/api/standardizedManage'
import FlagComponent from '@/components/flagComponent/index.vue'

export default {
  name: 'EffectEvaluation',
  components: {
    FlagComponent
  },
  props: {
    historyId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      detail: {},
      tableData: []
    }
  },
  created() {
    this.getEffectEvaluationFn()
  },
  methods: {
    async getEffectEvaluationFn() {
      this.$store.commit('managePatient/SET_LOADING', true)

      const res = await getTargetInspection({
        smrId: this.historyId || this.$route.query.id
      })
      this.$store.commit('managePatient/SET_LOADING', false)

      if (res.code === 200) {
        this.detail = res.data
        const {
          baseKfSugar,
          baseChTwoSugar,
          baseSugarHemoglobin,
          baseSp,
          baseDp,
          baseBmi,
          baseTc,
          baseTg,
          baseLdlc,
          baseHdlc,
          minKfSugar,
          maxKfSugar,
          minChTwoSugar,
          maxChTwoSugar,
          minSugarHemoglobin,
          maxSugarHemoglobin,
          minHeartRate,
          maxHeartRate,
          baseHeartRate,
          minSp,
          maxSp,
          minDp,
          maxDp,
          minBmi,
          maxBmi,
          minTc,
          maxTc,
          minTg,
          maxTg,
          minLdlc,
          maxLdlc,
          minHdlc,
          maxHdlc
        } = this.$store.getters.managePatientTargetData
        const { disease } = this.$store.state.managePatient.managePatientData
        const tableDataTemp = [
          {
            name: '空腹血糖(mmol/L)',
            baseValue: baseKfSugar,
            targetValue: `${minKfSugar} ≤ X ≤ ${maxKfSugar}`,
            checkValue: this.detail.kfSugarValue,
            result: this.detail.kfSugarResult,
            isShow: true
          },
          {
            name: '餐后2h血糖(mmol/L)',
            baseValue: baseChTwoSugar,
            targetValue: `${minChTwoSugar} ≤ X ≤ ${maxChTwoSugar}`,
            checkValue: this.detail.chTwoSugar,
            result: this.detail.chTwoSugarResult,
            isShow: true
          },
          {
            name: '糖化血红蛋白(%)',
            baseValue: baseSugarHemoglobin,
            targetValue: `${minSugarHemoglobin} ≤ X ≤ ${maxSugarHemoglobin}`,
            checkValue: this.detail.sugarHemoglobin,
            result: this.detail.sugarHemoglobinResult,
            isShow: true
          },
          {
            name: '静息状态下心率次/分',
            baseValue: baseHeartRate,
            targetValue: `${minHeartRate} ≤ X ≤ ${maxHeartRate}`,
            checkValue: this.detail.heartRate,
            result: this.detail.heartRateResult,
            isShow: disease.includes('fangchan')
          },
          {
            name: '收缩压(mmHg)',
            baseValue: baseSp,
            targetValue: `${minSp} ≤ X ≤ ${maxSp}`,
            checkValue: this.detail.sp,
            result: this.detail.spResult,
            isShow: true
          },
          {
            name: '舒张压(mmHg)',
            baseValue: baseDp,
            targetValue: `${minDp} ≤ X ≤ ${maxDp}`,
            checkValue: this.detail.dp,
            result: this.detail.dpResult,
            isShow: true
          },
          {
            name: '体质指数(kg/㎡)',
            baseValue: baseBmi,
            targetValue: `${minBmi} ≤ X ≤ ${maxBmi}`,
            checkValue: this.detail.bmi,
            result: this.detail.bmiResult,
            isShow: disease !== 'fangchan'
          },
          {
            name: '总胆固醇(mmol/L)',
            baseValue: baseTc,
            targetValue: `${minTc} ≤ X ≤ ${maxTc}`,
            checkValue: this.detail.tc,
            result: this.detail.tcResult,
            isShow: disease !== 'fangchan'
          },
          {
            name: '甘油三酯(mmol/L)',
            baseValue: baseTg,
            targetValue: `${minTg} ≤ X ≤ ${maxTg}`,
            checkValue: this.detail.tg,
            result: this.detail.tgResult,
            isShow: disease !== 'fangchan'
          },
          {
            name: '低密度脂蛋白胆固醇(mmol/L)',
            baseValue: baseLdlc,
            targetValue: `${minLdlc} ≤ X ≤ ${maxLdlc}`,
            checkValue: this.detail.ldlc,
            result: this.detail.ldlcResult,
            isShow: disease !== 'fangchan'
          },
          {
            name: '高密度脂蛋白胆固醇(mmol/L)',
            baseValue: baseHdlc,
            targetValue: `${minHdlc} ≤ X ≤ ${maxHdlc}`,
            checkValue: this.detail.hdlc,
            result: this.detail.hdlcResult,
            isShow: disease !== 'fangchan'
          }
        ]
        this.tableData = tableDataTemp.filter((item) => item.isShow)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.effect-evaluation {
  padding: 16px;
}
.effect-evaluation-title {
  width: 500px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}
</style>
