<!-- 尿常规检查 -->
<template>
  <div class="urinalysis">
    <div class="content">
      <div class="form-container">
        <dynamic-template ref="dynamicTemplate" :type="'URINE_ROUTINE'" :is-save="isSave" />
        <custom-upload v-model="attachmentPhotoUrl" style="margin-top: 16px" />
      </div>
      <div class="ocr-recognition">
        <ocr-recognition v-if="activeTab === 'URINE_ROUTINE'" type="ut" @get-ocr-data="getOcrData" />
        <device-icon
          :socket-connect="deviceConnect"
          style="margin-bottom: 8px; display: flex; justify-content: flex-end"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import DeviceIcon from '@/components/deviceIcon/index.vue'
import OcrRecognition from '@/components/ocrRecognition/index.vue'
import DynamicTemplate from '@/views/system/settings/projectConfiguration/component/dynamicTemplate.vue'
import CustomUpload from '@/components/customUpload/index.vue'

export default {
  name: 'Urinalysis',
  components: {
    DeviceIcon,
    OcrRecognition,
    DynamicTemplate,
    CustomUpload
  },
  props: {
    itemTemp: {
      type: Object,
      default: () => {}
    },
    activeTab: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      attachmentPhotoUrl: '',
      id: '',
      isSave: false
    }
  },
  computed: {
    ...mapGetters(['deviceConnect'])
  },
  methods: {
    initData(data) {
      this.isSave = true
      this.id = data.measureUrineRoutine && data.measureUrineRoutine.id
      this.attachmentPhotoUrl = data.measureUrineRoutine && data.measureUrineRoutine.attachmentPhotoUrl
      this.$refs.dynamicTemplate.tempDetail = data.itemList
      this.$nextTick(() => {
        if (this.id) {
          this.$refs.dynamicTemplate.tempValue = ''
        }
      })
    },

    getOcrData(data, url) {
      this.$refs.dynamicTemplate.tempValue = ''
      this.$refs.dynamicTemplate.tempDetail = data
      this.attachmentPhotoUrl = url
    },

    async handleSave() {
      const result = {
        name: `${this.itemTemp.label}`,
        success: true,
        data: {
          itemId: this.itemTemp.id,
          itemCode: this.itemTemp.value,
          itemDetailId: this.id,
          unireList: this.$refs.dynamicTemplate.tempDetail,
          attachmentPhotoUrl: this.attachmentPhotoUrl
        }
      }
      return result
    }
  }
}
</script>
<style lang="scss" scoped>
.urinalysis {
  width: 100%;
  .content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    gap: 16px;
    .form-container {
      width: 40%;
    }
    .ocr-recognition {
      flex: 1;
    }
  }
}
</style>
