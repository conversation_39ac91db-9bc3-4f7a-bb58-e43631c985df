#app {
  .main-container {
    min-height: 100%;
    transition: margin-left 0.28s;
    margin-left: $sideBarWidth;
    position: relative;
  }

  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    background-color: $menuBg;
    // background-color: #DFF2EF;
    color: #fff;
    height: 100%;
    position: fixed;
    // font-size: 0.65rem;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 12px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }

    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }
    .el-menu > div {
      box-sizing: border-box;
    }

    // menu hover
    .submenu-title-noDropdown,
    .el-submenu__title {
      font-size: 0.7rem;
      display: flex;
      align-items: center;
      height: 40px;
      // line-height: 40px;
      &:hover {
        // background-color: $menuHover  !important;
        background-color: #edf2f9 !important;
        color: #232e3c !important;
      }
      .el-submenu__icon-arrow {
        color: currentColor;
      }
    }
    .is-active {
      // background-color: #48A6EB !important;
      color: #232e3c;
    }
    .is-active > .el-submenu__title {
      position: relative;
      color: $subMenuActiveText !important;
      // color: #fff !important;
      // background-color: #e9f5f3 !important;
    }

    & .nest-menu .el-submenu > .el-submenu__title,
    & .el-submenu .el-menu-item {
      min-width: $sideBarWidth !important;
      // background-color: $subMenuBg  !important;
      &:hover {
        // background-color: $subMenuHover  !important;
        background-color: #e4eeec !important;
        color: #2dae96 !important;
      }
    }
  }

  .hideSidebar {
    .sidebar-container {
      width: 80px !important;
    }

    .main-container {
      margin-left: 0px;
    }

    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;

      .el-tooltip {
        padding: 0 !important;
        padding-left: 10px !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }
      }
    }

    .el-submenu {
      overflow: hidden;

      & > .el-submenu__title {
        padding: 0 !important;
        padding-left: 10px !important;

        .svg-icon {
          margin-left: 20px;
        }

        .sub-el-icon {
          margin-left: 19px;
        }

        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        & > .el-submenu__title {
          & > span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .el-menu-item.is-active {
    background: #f3fffd !important;
    font-weight: 600;
    color: #0a86c8 !important;
    &::before {
      content: '';
      border-left: 3px solid #0a86c8;
      position: absolute;
      height: 100%;
      left: 1px;
      display: block;
      z-index: 1;
    }
  }
  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform 0.28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        pointer-events: none;
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }

    .sub-el-icon {
      margin-right: 12px;
      margin-left: -2px;
    }
  }

  .nest-menu .el-submenu > .el-submenu__title,
  .el-menu-item {
    &:hover {
      // you can use $subMenuHover
      // background-color: $menuHover  !important;
      background-color: #edf2f9 !important;
    }
  }

  // the scroll bar appears when the subMenu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
