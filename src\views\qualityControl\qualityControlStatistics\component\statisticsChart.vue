<template>
  <div ref="chart" class="statistics-chart" />
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'StatisticsChart',
  props: {
    chartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      handler() {
        this.initChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chart)
      }

      if (!this.chartData || this.chartData.length === 0) return

      // 提取X轴数据(日期)
      const xAxisData = this.chartData.map((item) => item.auditDate)

      // 提取Y轴数据
      const submitCountData = this.chartData.map((item) => item.submitCount)
      const waitCountData = this.chartData.map((item) => item.waitCount)
      const passCountData = this.chartData.map((item) => item.passCount)
      const failCountData = this.chartData.map((item) => item.failCount)

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['已提交', '待质控', '已通过', '未通过']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter(value) {
              return Math.round(value)
            }
          },
          minInterval: 1,
          min: 0
        },
        series: [
          {
            name: '已提交',
            type: 'bar',
            data: submitCountData,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '待质控',
            type: 'bar',
            data: waitCountData,
            itemStyle: {
              color: '#E6A23C'
            }
          },
          {
            name: '已通过',
            type: 'bar',
            data: passCountData,
            itemStyle: {
              color: '#67C23A'
            }
          },
          {
            name: '未通过',
            type: 'bar',
            data: failCountData,
            itemStyle: {
              color: '#F56C6C'
            }
          }
        ]
      }

      this.chart.setOption(option)
    }
  }
}
</script>

<style scoped>
.statistics-chart {
  width: 100%;
  height: 350px;
}
</style>
