<template>
  <div class="step-indicator">
    <div v-for="(step, index) in steps" :key="index" class="step-item">
      <div
        class="circle"
        :class="{
          // temporarilyStore: step.stepStatus === 1,
          completed: step.stepStatus === 5 || step.stepStatus === 9,
          // skip: step.stepStatus === 9,
          // disabled: step.stepStatus === 0,
          active: index === currentStep
        }"
        @click="handleStepClick(step, index)"
      >
        <span v-if="step.stepStatus === 5" class="check-icon">✓</span>
        <span v-else>{{ index + 1 }}</span>
      </div>
      <div class="label">{{ step.title || '' }}</div>
      <div v-if="index < steps.length - 1" class="line" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'StepIndicator',
  props: {
    steps: {
      type: Array,
      required: true
    },
    currentStep: {
      type: Number,
      required: true
    }
  },
  methods: {
    handleStepClick(step, index) {
      // const currentStatus = this.steps[this.currentStep].stepStatus
      // const currentTitle = this.steps[this.currentStep].title
      // if (currentStatus !== 5) {
      //   this.$message.warning(`${currentTitle}未完成，请先完成`)
      // } else {
      //   this.$emit('stepClick', step, index)
      // }
      let state = null
      if (this.$route.path === '/receptionCenter/patientReception') {
        state = this.$store.state.receptionWorkbench.receptionWorkbenchData.receptionStatus
      } else if (this.$route.path === '/receptionCenter/managePatient') {
        state = this.$store.state.managePatient.managePatientData.status
      }
      if (state !== 1 && step.stepStatus !== 0) {
        this.$emit('stepClick', step, index)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.step-indicator {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding: 20px;
  width: 600px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
}

.circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #dcdcdc;
  color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1;
  cursor: pointer;
}

// .circle.temporarilyStore {
//   background-color: #ffc833;
//   color: #fff;
// }

.circle.completed {
  background-color: #9cd5ed;
  color: #fff;
}

// .circle.skip {
//   background-color: #dcdcdc;
//   color: #000;
// }

// .circle.disabled {
//   background-color: #dcdcdc;
//   color: #000;
//   cursor: not-allowed;
// }

.circle.active {
  background-color: #41a1d4;
  color: #fff;
}

.check-icon {
  color: #fff;
  font-weight: bold;
  font-size: 16px;
}

.label {
  margin-top: 8px;
  font-size: 14px;
  color: #333;
}

.line {
  position: absolute;
  top: 16px;
  left: 50%;
  width: 100%;
  height: 1px;
  background-color: #ccc;
  z-index: 0;
}

.line.active {
  background-color: #409eff;
}
</style>
