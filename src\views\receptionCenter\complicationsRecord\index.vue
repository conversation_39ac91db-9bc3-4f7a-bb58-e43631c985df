<template>
  <div class="complications">
    <el-card class="complications-search">
      <el-form :model="queryParams" label-width="120px">
        <el-row>
          <el-col :span="6">
            <el-form-item label="姓名：">
              <el-input v-model="queryParams.keyword" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="筛查日期：">
              <el-date-picker
                v-model="queryParams.timeRange"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="检查项目：">
              <el-select v-model="queryParams.itemCodeList" multiple placeholder="请选择检查项目" style="width: 100%">
                <el-option v-for="item in checkProjectList" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6" style="text-align: center">
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>
            <el-button icon="el-icon-search" type="primary" @click="handleSearch">查询</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <el-card class="complications-table">
      <base-table
        ref="baseTable"
        :table-data="tableData"
        :loading="loading"
        :stripe="true"
        height="calc(100% - 50px)"
        row-key="id"
        :columns="columns"
        :total="total"
        :page-info="queryParams"
        @pagination-change="handlePaginationChange"
      >
        <!-- 慢病病种 -->
        <template #disease="{ row }">
          <ChronicDiseaseType :record="row" />
        </template>

        <template #sex="{ row }">
          <span>{{ genderTransform(row.sex) }}</span>
        </template>

        <!-- <template #idCard="{ row }">
          <EncryptionStr :cipher-text="row.idCard" :replace="row.idCardReplace" />
        </template>

        <template #phone="{ row }">
          <EncryptionStr :cipher-text="row.phone" :replace="row.phoneReplace" />
        </template> -->

        <template #operation="{ row }">
          <el-button type="text" @click="handleView(row)">详情</el-button>
        </template>
      </base-table>
    </el-card>
  </div>
</template>

<script>
import { genderTransform } from '@/utils/cspUtils'
import { getComplicationsScreeningRecordList } from '@/api/receptionWorkbench'
import { tabList } from '@/views/receptionCenter/patientReception/component/complicationsScreening.js'
import BaseTable from '@/components/BaseTable/index.vue'
import tableMixin from '@/mixins/tableMixin'
import ChronicDiseaseType from '@/components/chronicDiseaseType/index.vue'
// import EncryptionStr from '@/components/encryptionStr/index.vue'

export default {
  name: 'ComplicationsRecord',
  components: {
    BaseTable,
    ChronicDiseaseType
    // EncryptionStr
  },
  mixins: [tableMixin],
  data() {
    return {
      checkProjectList: tabList,
      columns: [
        // { type: 'index', width: 80 },
        { prop: 'patientName', label: '姓名' },
        { prop: 'disease', label: '慢病病种', width: 180, slot: 'disease' },
        { prop: 'sex', label: '性别', slot: 'sex' },
        { prop: 'age', label: '年龄' },
        // { prop: 'idCardReplace', label: '身份证号', width: 190, slot: 'idCard' },
        // { prop: 'phoneReplace', label: '手机号码', width: 140, slot: 'phone' },
        // { prop: 'address', label: '地址', showOverflowTooltip: true },
        { prop: 'doctorName', label: '医生' },
        { prop: 'itemNameStr', label: '检查项目', width: 300, showOverflowTooltip: true },
        { prop: 'csDate', label: '筛查时间', width: 120 },
        { prop: 'operation', label: '操作', slot: 'operation' }
      ],
      queryParams: {
        keyword: '',
        timeRange: [],
        itemCodeList: []
      }
    }
  },
  methods: {
    genderTransform,
    async getTableList(params) {
      const { timeRange, ...rest } = params || {}
      const [startDate, endDate] = Array.isArray(timeRange) ? timeRange : [null, null]

      const queryParams = {
        ...rest,
        startDate,
        endDate
      }

      return await getComplicationsScreeningRecordList(queryParams)
    },
    handleView(row) {
      this.$router.push({
        path: '/receptionCenter/patientReception',
        query: {
          id: row.rrId,
          active: 3
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/styles/form-overrides.scss';
.complications {
  display: flex;
  flex-direction: column;
  height: 100%;
  .complications-search {
    margin: 16px;
    height: 77px;
  }
  .complications-table {
    margin: 0 16px;
    flex: 1;
    ::v-deep .el-card__body {
      height: 100%;
    }
  }
}
</style>
