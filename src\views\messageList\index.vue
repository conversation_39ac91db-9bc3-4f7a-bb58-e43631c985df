<template>
  <!-- eslint-disable vue/no-use-v-if-with-v-for -->
  <div class="messageList">
    <div class="left">
      <div class="top">
        <p>
          消息列表<template v-if="msgCount">({{ msgCount }})</template>
        </p>
        <el-input
          v-model="search"
          placeholder="请输入患者姓名搜索"
          prefix-icon="el-icon-search"
          clearable
          style="width: 95%; height: 32px; margin: 0 2.5% 20px"
          @change="getUserList"
        />
      </div>
      <el-scrollbar style="height: calc(85vh - 110px)">
        <el-collapse v-model="activeNames">
          <el-collapse-item v-if="userList.filter(it => it.type == 2).length != 0" title="村医" name="2">
            <ul class="list">
              <li
                v-for="(item, index) in userList"
                v-if="item.type == 2"
                :key="index"
                class="item"
                :style="`background:${index == activeNum ? '#ECF2FF' : ''};`"
                @click="handleList(index)"
              >
                <div class="avatar">{{ item.name[0] }}</div>
                <div class="user">
                  <h3>
                    {{ item.name }} <span>{{ item.timeStr && item.timeStr.slice(0, 16) }}</span>
                  </h3>
                  <p v-html="lastmsgChange(item.content)" />
                  <div v-if="item.notReadCount" class="i">{{ item.notReadCount }}</div>
                </div>
              </li>
            </ul>
          </el-collapse-item>
          <el-collapse-item v-if="userList.filter(it => it.type == 0).length != 0 && type == 2" title="患者" name="0">
            <ul class="list">
              <li
                v-for="(item, index) in userList"
                v-if="item.type == 0"
                :key="index"
                class="item"
                :style="`background:${index == activeNum ? '#ECF2FF' : ''};`"
                @click="handleList(index)"
              >
                <div class="avatar">{{ item.name[0] }}</div>
                <div class="user">
                  <h3>
                    {{ item.name }} <span>{{ item.timeStr && item.timeStr.slice(0, 16) }}</span>
                  </h3>
                  <p>{{ lastmsgChange(item.content) }}</p>
                  <div v-if="item.notReadCount" class="i">{{ item.notReadCount }}</div>
                </div>
              </li>
            </ul>
          </el-collapse-item>
          <el-collapse-item v-if="userList.filter(it => it.type == 1).length != 0" title="医生" name="1">
            <ul class="list">
              <li
                v-for="(item, index) in userList"
                v-if="item.type == 1"
                :key="index"
                class="item"
                :style="`background:${index == activeNum ? '#ECF2FF' : ''};`"
                @click="handleList(index)"
              >
                <div class="avatar">{{ item.name[0] }}</div>
                <div class="user">
                  <h3>
                    {{ item.name }} <span>{{ item.timeStr && item.timeStr.slice(0, 16) }}</span>
                  </h3>
                  <p>{{ lastmsgChange(item.content) }}</p>
                  <div v-if="item.notReadCount" class="i">{{ item.notReadCount }}</div>
                </div>
              </li>
            </ul>
          </el-collapse-item>
        </el-collapse>
      </el-scrollbar>
    </div>
    <div v-if="data.name" class="center">
      <p>{{ data.name }}</p>
      <el-scrollbar id="msgContainer" ref="msgContainer" style="height: calc(100% - 310px)">
        <ul class="centent">
          <template v-for="(i, index) in msg[activeNum]">
            <el-divider
              v-if="index == 0 || i.sendTime - msg[activeNum][index - 1].sendTime > 1000 * 60 * 5"
              :key="i.time + '-' + i.toUserId + '-' + index + '-divider1'"
              style="padding: 0 30px"
            >{{ i.time && i.time.slice(0, 16) }}
            </el-divider>
            <li v-if="i.toUserId == data.id" :key="i.time + '-' + i.toUserId + '-' + index + '-li1'" class="mine">
              <div
                :class="[
                  'message',
                  i.content.indexOf('<img') == 0
                    ? i.content.split('<').length === 2
                      ? i.content.lastIndexOf('>') == i.content.length - 1
                        ? 'msgimg'
                        : ''
                      : ''
                    : ''
                ]"
                v-html="formatWord(i.content)"
              />
              <!-- <div class="message">{{i.content}}</div> -->
              <div class="avatar">
                <!-- <img :src="avatar+'?imageView2/1'" alt=""> -->
                <img :src="require(`@/assets/common_images/${avatar}.png`)" alt="">
              </div>
            </li>
            <li v-else :key="i.time + '-' + i.toUserId + '-' + index + '-li2'" class="user">
              <div class="avatar">
                <img :src="require(`@/assets/common_images/${userhead}.png`)" alt="">
              </div>
              <div
                class="message"
                :class="[
                  'message',
                  i.content.indexOf('<img') == 0
                    ? i.content.split('<').length === 2
                      ? i.content.lastIndexOf('>') == i.content.length - 1
                        ? 'msgimg'
                        : ''
                      : ''
                    : ''
                ]"
                v-html="formatWord(i.content)"
              />
              <!-- <div class="message">{{i.content}}</div> -->
            </li>
          </template>
        </ul>
      </el-scrollbar>
      <biaoqing id="biaoqing1" :type="0" width="100%" height="150px" :value="value" @contentChange="contentChange" />
    </div>
    <div v-else class="center">
      <el-empty style="height: 100%" :image="require('@/assets/common_images/nodisease.png')" description="" />
    </div>
    <div v-if="userData.name && userData.type == 0" class="right">
      <div class="box">
        <h3>基础资料</h3>
        <table class="userData">
          <tr>
            <td>姓名：{{ userData.name }}</td>
            <td>性别：{{ GENDER_ENUM[userData.sex] ? GENDER_ENUM[userData.sex] : '-/-' }}</td>
          </tr>
          <br>
          <tr>
            <td>
              手机：
              <!-- {{ userData.phone }} -->
              <encryptionStr :cipher-text="userData.phone" :replace="userData.phoneReplace" />
            </td>
            <td>年龄：{{ userData.age }}</td>
          </tr>
          <br>
          <tr>
            <td>
              服务包：{{
                userData.servicePackage && userData.servicePackage.packageName
                  ? userData.servicePackage.packageName
                  : '无'
              }}
            </td>
          </tr>
          <!-- <br>
          <tr>
            <td>绑定医生：吴海波</td>
          </tr> -->
        </table>
      </div>
      <div class="box">
        <h3>病症标签</h3>
        <el-tag
          v-for="i in userData.confirm_diseases ? userData.confirm_diseases.split(',') : []"
          :key="i"
          class="tag"
        >{{ i }}
        </el-tag>
      </div>
      <div class="box">
        <h3>高风险预警</h3>
        <el-empty
          v-if="userWarn.length === 0"
          :image="require('@/assets/common_images/nodata.png')"
          description="暂无预警"
        />
        <li v-for="(i, index) in userWarn" :key="index" :class="{ high: i.risk == 1 }">
          <img v-if="i.risk == 1" src="@/assets/personage_images/<EMAIL>" alt="">
          <img v-else src="@/assets/personage_images/<EMAIL>" alt="">
          <div class="text">
            <p>房颤{{ i.risk == 1 ? '高' : '中' }}风险预警</p>
            <span>{{ i.datetime }}</span>
          </div>
        </li>
      </div>
      <el-button
        type="primary"
        style="
          background: #ecf5ff;
          color: #409eff;
          width: 70%;
          position: absolute;
          bottom: 20px;
          left: 0;
          right: 0;
          margin: 0 auto;
        "
        @click="handleClick"
      >
        查看PHR
      </el-button>
    </div>
    <el-image-viewer
      v-if="showViewer"
      :on-close="
        () => {
          showViewer = false
        }
      "
      :url-list="[viewUrl]"
    />
  </div>
</template>
<script>
import ElImageViewer from 'element-ui/packages/image/src/image-viewer'
// import { biaoqing } from "./components/biaoqing.vue";
import { GENDER_ENUM } from '@/utils/enum'
import { userImApi, getQueryPhrLastAlarmsID, userPatientApi, chat2HistoryApi } from '@/api/msglist'
import { mapGetters, mapState } from 'vuex'
import { getUserId } from '@/utils/auth'
import { parseTime } from '@/utils/index'

const userId = getUserId()
export default {
  name: 'MessageList',
  components: {
    biaoqing: () => import('./components/biaoqing.vue'),
    ElImageViewer
  },
  data() {
    return {
      // enum data
      GENDER_ENUM,

      activeNames: '',
      search: '',
      userList: [],
      list: [],
      activeNum: null,
      data: {},
      socket: null,
      msg: [],
      value: '',
      interval_timer: null,
      timer_count: 0,
      userWarn: [],
      userData: {},
      srcList: [],
      showViewer: false,
      viewUrl: '',
      userhead: 'doctorhand'
    }
  },
  computed: {
    ...mapGetters(['avatar', 'name', 'hospitalId', 'sex', 'type']),
    ...mapState(['sessions', 'currentSession']),
    msgCount() {
      let num = 0
      for (let i = 0; i < this.userList.length; i++) {
        const e = this.userList[i]
        num += e.notReadCount * 1
      }
      return num
    }
  },
  watch: {},
  created() {
    this.getUserList().then(() => {
      for (let i = 0; i < this.userList.length; i++) {
        // this.msg[i] = []
        this.$set(this.msg, i, [])
      }
      // this.handleList(0)
    })
  },
  mounted() {
    this.createOrConnectWebSocket()
  },
  beforeDestroy() {
    this.socket.onclose = this.close
    clearInterval(this.interval_timer)
    this.timer_count = 0
  },
  methods: {
    //   getRandomColor() {
    //     return '#' + Math.floor(Math.random() * 16777215).toString(16);
    //   },
    formatWord(val) {
      return val.replace(/\n/g, '<br>')
    },
    openImg() {
      const imgs = document.querySelectorAll('.message img')
      for (let i = 0; i < imgs.length; i++) {
        imgs[i].onclick = () => {
          this.viewUrl = imgs[i].src
          this.showViewer = true
        }
      }
    },
    lastmsgChange(msg) {
      if (msg.includes('<img')) {
        return '[图片]'
      }
      const content = msg.replace(/<\/?((?!img).)*?\/?>/g, '') // 去除标签
      return content
    },
    handleClick() {
      sessionStorage.setItem('healthUserId', this.userData.userId)
      this.$router.push({ path: '/summary/' })
    },
    msglistbottom() {
      this.$nextTick(() => {
        this.$refs.msgContainer.wrap.scrollTop = this.$refs.msgContainer.wrap.scrollHeight
      })
    },
    async getUserHistory(id) {
      const res = await chat2HistoryApi(id)
      for (let i = 0; i < res.data.length; i++) {
        res.data[i].time = parseTime(new Date(res.data[i].sendTime))
      }
      this.$set(this.msg, this.activeNum, res.data)

      this.$nextTick(() => {
        this.msglistbottom()
        this.openImg()
      })
    },
    async getUserWarn(id) {
      const res = await getQueryPhrLastAlarmsID(id)
      this.userWarn = res.data
    },
    async getUserData(id) {
      const res = await userPatientApi(id)
      this.userData = res.data
    },
    async getUserList() {
      const res = await userImApi({ userName: this.search })
      this.userList = res.data
    },
    handleList(i) {
      this.activeNum = i
      this.data = this.userList[i]
      if (this.data.type) {
        if (`${this.data.sex}` === '0') {
          this.userhead = 'womandoctorhand'
        } else {
          this.userhead = 'doctorhand'
        }
      } else if (`${this.data.sex}` === '0') {
        this.userhead = 'womenhand'
      } else {
        this.userhead = 'manhand'
      }
      this.getUserWarn(this.data.id)
      this.getUserData(this.data.id)
      this.getUserHistory(this.data.id).then(() => {
        this.userList[i].notReadCount = 0
      })
    },
    createOrConnectWebSocket() {
      if (typeof WebSocket === 'undefined') {
        // eslint-disable-next-line no-alert
        alert('您的浏览器不支持socket')
      } else {
        if (!this.socket) {
          // TODO ws 不存在
          // 实例化socket
          this.socket = new WebSocket(`ws://192.168.11.242:8602/ws/ep/${userId}`)
          // this.socket = new WebSocket('wss://wearwell.com.cn/ws/ep/' + userId)
          this.initsocket()
        }
        // 开启定时器
        this.init_start_timer()
      }
    },
    init_start_timer() {
      // 重置计数器
      this.timer_count = 0
      if (this.interval_timer != null) {
        clearInterval(this.interval_timer)
        this.interval_timer = null
      }
      this.interval_timer = setInterval(this.myTimer, 30000)
    },
    myTimer() {
      // TODO 如果超过半小时没有交互，则关闭计时器
      if (this.timer_count >= 1800) {
        clearInterval(this.interval_timer)
      } else {
        this.timer_count += 30
        // var online = '{"type":"timer","from_id":"' + 'from_id' + '","to_id":"' + 'to_id' + '"}';
        this.socket.send(JSON.stringify({ cmd: 'keepAlive' }))
      }
    },
    // 判断当前用户是否 还在线
    isOnlineCurrUser() {
      if (this.socket) {
        if (`${this.socket.readyState}` === `${WebSocket.OPEN}`) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    },
    initsocket() {
      // 监听socket连接
      this.socket.onopen = this.open
      // 监听socket错误信息
      this.socket.onerror = this.error
      // 监听socket消息
      this.socket.onmessage = this.getMessage
    },
    open() {},
    error() {
      this.createOrConnectWebSocket()
    },
    getMessage(msg) {
      // msg.time = parseTime(msg.sendTime)
      const obj = JSON.parse(msg.data)
      obj.time = parseTime(new Date(obj.sendTime))
      let num
      for (let i = 0; i < this.userList.length; i++) {
        if (`${this.userList[i].id}` === `${obj.fromUserId}` || `${this.userList[i].id}` === `${obj.toUserId}`) {
          num = i
        }
      }
      if (`${userId}` === `${obj.toUserId}`) {
        if (this.userList[num].notReadCount) {
          this.userList[num].notReadCount = 1
        } else {
          this.userList[num].notReadCoun++
        }
      }
      this.userList[num].content = obj.content
      this.msg[num].push(obj)
      this.$nextTick(() => {
        this.msglistbottom()
        this.openImg()
      })
    },
    contentChange(data) {
      const name = this.name.substring(0, this.name.lastIndexOf('('))
      if (data.value === '') {
        return
      }
      this.socket.send(
        JSON.stringify({
          hospitalId: this.hospitalId,
          content: data.value,
          toUserId: this.userList[this.activeNum].id,
          toUserName: this.userList[this.activeNum].name,
          fromUserId: userId,
          fromUserName: name,
          toUserSex: this.userList[this.activeNum].sex,
          fromUserSex: this.sex
        })
      )
    },
    close() {}
  }
}
</script>
<style scoped lang="scss">
.messageList {
  margin: 30px;
  display: flex;
  width: 96%;
  height: 85vh;
  min-height: 700px;

  .left {
    background-color: #fff;
    width: 20.62%;
    height: auto;
    margin: 0 5px;
    border-radius: 15px;
    min-width: 280px;

    .top {
      p {
        text-align: center;
        color: #666;
        font-size: 0.65rem;
      }

      ::v-deep .el-input__inner {
        border-radius: 15px;
      }
    }

    .list {
      margin: 0;
      padding: 0;

      .item {
        list-style-type: none;
        display: flex;
        padding: 8px 15px;
        height: 56px;
        cursor: pointer;

        .avatar {
          width: 40px;
          height: 40px;
          color: #fff;
          text-align: center;
          line-height: 40px;
          border-radius: 5px;
          font-weight: bold;
          background-color: #5a8bed;
        }

        .user {
          width: calc(100% - 60px);
          margin-left: 12px;
          position: relative;

          h3 {
            color: #333;
            font-weight: 400;
            font-size: 0.7rem;
            margin: 0 0 3px 0;
            line-height: 20px;

            span {
              float: right;
              color: #666;
              font-size: 0.5rem;
            }
          }

          p {
            padding-right: 40px;
            color: #999;
            font-size: 0.5rem;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            margin: 0;
          }

          .i {
            font-size: 0.6rem;
            color: #fff;
            background-color: #ff3737;
            display: inline-block;
            width: 14px;
            height: 14px;
            line-height: 14px;
            text-align: center;
            border-radius: 50%;
            position: absolute;
            right: 0;
            bottom: 3px;
          }
        }
      }
    }
  }

  .center {
    background-color: #fff;
    width: 55.96%;
    flex-grow: 2;
    height: auto;
    margin: 0 5px;
    border-radius: 15px;

    p {
      display: block;
      height: 60px;
      text-align: center;
      color: #666666;
      font-size: 0.9rem;
      line-height: 60px;
      margin: 0;
      border-bottom: 1px solid #e7e7e7;
    }

    .centent {
      padding: 0px 25px;

      li {
        list-style-type: none;
        display: flex;
        margin: 20px 0;

        .avatar {
          width: 40px;
          height: 40px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .message {
          max-width: calc(100% - 100px);
          font-size: 0.8rem;
          // height: 46px;
          line-height: 26px;
          margin: 0 20px;
          padding: 10px 20px;
        }

        .msgimg {
          padding: 0 !important;
          border-radius: 0 !important;
          border: none !important;
          background-color: #fff !important;
        }

        &.user {
          .message {
            // background: linear-gradient(135deg, #5FA9F5 0%, #298BF2 100%);
            background-color: #298bf2;
            border-radius: 0px 10px 10px 10px;
            color: #fff;
          }
        }

        &.mine {
          display: flex;
          justify-content: right;

          .message {
            background-color: #c9ddff;
            border-radius: 10px 0px 10px 10px;
            // border: 1px solid #C7C7C7;
          }
        }
      }
    }
  }

  .right {
    position: relative;
    background-color: #fff;
    width: 22.17%;
    flex-grow: 1;
    height: auto;
    margin: 0 5px;
    border-radius: 15px;
    padding-left: 25px;
    min-width: 280px;

    .box {
      margin-bottom: 50px;
    }

    h3 {
      color: #666666;
      font-size: 0.9rem;
      font-weight: 600;
      margin: 2vh 0;
    }

    table {
      font-size: 0.7rem;
      color: #666;
    }

    .tag {
      margin-right: 10px;
    }

    li {
      display: flex;
      align-items: center;
      // width: 320px;
      height: 60px;
      background-color: #fff;
      border-radius: 15px;
      list-style-type: none;
      color: #999;

      img {
        width: 12%;
        height: auto;
        margin: 0 10px 0px 10px;
      }

      .text {
        font-size: 0.75rem;

        p {
          margin-bottom: 7px;
          margin-top: 0;
        }

        span {
          font-size: 0.6rem;
        }
      }
    }

    .high {
      .text {
        p {
          color: #ff3737;
        }
      }
    }
  }
}

::v-deep .el-scrollbar__wrap {
  overflow-x: hidden;
}

::v-deep .el-collapse-item__content {
  padding-bottom: 0;
}

::v-deep .el-collapse-item__header {
  padding-left: 15px;
}
</style>
<style lang="scss">
.message {
  img {
    max-width: 300px !important;
    max-height: 300px !important;
    cursor: pointer;
  }
}
</style>
