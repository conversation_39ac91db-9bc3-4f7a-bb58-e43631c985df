<template>
  <div class="navDiv">
    <button v-if="smallScreen" class="scroll-button left" @click="scrollMenu('left')">
      <img style="width: 30px" src="./L.png">
    </button>
    <ul ref="menuContainer" class="navOh">
      <template v-for="(item, index) in menus">
        <li
          v-if="!item.hidden"
          :key="'menus_' + index"
          :class="['list', routePath === item.path ? 'active' : '']"
          @click="goPage(item)"
        >
          <el-popover placement="bottom-start" trigger="hover" popper-class="listTwoDiv">
            <div slot="reference">{{ item.meta.title }}</div>
            <div v-if="item.children && item.children.length > 1" class="twoNavDiv public_scrollbar">
              <template v-for="(items, indexs) in item.children">
                <p
                  v-if="!items.hidden && (!items.children || items.children.length < 1)"
                  :key="'menus_two_' + indexs"
                  :class="['listTwo', routeName === items.path ? 'active' : '']"
                  @click="goPage(item, items)"
                >
                  <svg-icon :icon-class="items.meta.icon" />
                  <span class="vam">{{ items.meta.title }}</span>
                </p>
                <template v-if="!items.hidden && items.children">
                  <template v-for="(newItem, newIndex) in items.children">
                    <p
                      v-if="!newItem.hidden"
                      :key="'newItem_two_' + newIndex"
                      :class="['listTwo', routeName === newItem.path ? 'active' : '']"
                      @click="goPagenew(item, items, newItem)"
                    >
                      <svg-icon :icon-class="newItem.meta.icon" />
                      <span class="vam">{{ newItem.meta.title }}</span>
                    </p>
                  </template>
                </template>
              </template>
            </div>
          </el-popover>
        </li>
      </template>
    </ul>
    <button v-if="smallScreen" class="scroll-button right" @click="scrollMenu('right')">
      <img style="width: 30px" src="./R.png">
    </button>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  props: {},
  data() {
    return {
      alertRef: null,
      alertTime: null
    }
  },
  computed: {
    ...mapGetters(['menus']),
    routeName() {
      return this.$route.name
    },
    routePath() {
      if (this.$route.matched[0].path) {
        return this.$route.matched[0].path
      } else {
        return this.$route.matched[1].path
      }
    },
    smallScreen() {
      return window.innerWidth < 1480 && this.menus.length >= 8
    }
  },

  methods: {
    scrollMenu(direction) {
      const { menuContainer } = this.$refs
      const scrollAmount = 500 // 每次滚动的像素数
      if (direction === 'left') {
        menuContainer.scrollBy({ left: -scrollAmount, behavior: 'smooth' })
      } else if (direction === 'right') {
        menuContainer.scrollBy({ left: scrollAmount, behavior: 'smooth' })
      }
    },
    goPagenew(item, items, itemThree) {
      const itemsPath = items.path.indexOf('/') === 0 ? items.path.slice(1, 0) : items.path
      const itemThreePath = itemThree.path.indexOf('/') === 0 ? itemThree.path.slice(1, 0) : itemThree.path
      this.$router.push(`${item.path}/${itemsPath}/${itemThreePath}`)
    },
    // 页面跳转
    goPage(item, items) {
      if (item.enabled === 0) {
        this.$confirm('', '提示', {
          showConfirmButton: false,
          showCancelButton: false,
          customClass: 'messageOffLine',
          type: 'warn'
        })
        return
      }
      if (items) {
        if (items.path === 'stationchange' || items.path === 'qualitycontrol') {
          this.$confirm('', '提示', {
            showConfirmButton: false,
            showCancelButton: false,
            customClass: 'messageOffLine',
            type: 'warn'
          })
          return
        }

        if (items.path.indexOf('/') === 0) {
          this.$router.push(`${item.path}${items.path}`)
        } else {
          this.$router.push(`${item.path}/${items.path}`)
        }
      } else if (item.children) {
        if (item.children[0].path.indexOf('/') === 0) {
          this.$router.push(`${item.path}${item.children[0].path}`)
        } else {
          this.$router.push(`${item.path}/${item.children[0].path}`)
        }
      } else {
        this.$router.push(`${item.path}`)
      }
    }
  }
}
</script>

<style>
.listTwoDiv {
  padding: 0 !important;
  border-radius: 0.3rem;
  overflow: hidden;
  transform: translateY(-12px);
  width: 10rem !important;
  height: auto !important;
}
.listTwo .svg-icon {
  margin: 0 0.6rem;
  width: 1rem;
  height: 1rem;
  vertical-align: middle;
}
.listTwo .vam {
  vertical-align: middle;
}
.listTwo:hover .svg-icon,
.twoNavDiv .listTwo.active .svg-icon {
  filter: contrast(0) brightness(2);
}
.twoNavDiv {
  max-height: 50vh;
  overflow: auto;
  font-size: 0.75rem;
}
.twoNavDiv .listTwo {
  line-height: 1.5rem;
  padding: 0.5rem;
  cursor: pointer;
}
.twoNavDiv .listTwo:hover,
.twoNavDiv .listTwo.active {
  background: linear-gradient(133deg, #00b2dd 0%, #0a86c8 100%);
  color: #fff;
}
.messageOffLine {
  width: 25rem;
}
.messageOffLine .el-message-box__content {
  width: 100%;
  height: 10rem;
  background-image: url('~@/assets/404_images/offLine.png');
  background-position: center bottom;
  background-repeat: no-repeat;
  margin-top: 3rem;
}
</style>
<style lang="scss" scoped>
.navDiv {
  flex: 1;
  overflow: hidden;
  padding: 0 40px;
  position: relative;
  display: flex;
  align-items: center;
  //overflow: hidden;
  .scroll-button {
    position: absolute;
    top: 50%;
    transform: translateY(-48%);
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    z-index: 1;
    color: #ffffff;
    &.left {
      left: 0;
    }
    &.right {
      right: 5px;
    }
  }
  .navOh {
    width: 55rem;
    overflow-x: auto;
    overflow-y: hidden;
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    @media screen and (max-width: 1480px) {
      width: 50vw;
      flex: 1;
      display: flex;
      overflow-x: hidden;
      scroll-behavior: smooth;
    }
  }
  .list {
    white-space: nowrap;
    float: left;
    line-height: 50px;
    padding: 0 0.3vw;
    font-size: 0.8rem;
    cursor: pointer;
    &:hover,
    &.active {
      background-color: rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
