<template>
  <div :class="classObj" class="app-wrapper">
    <div :class="{ hasTagsView: needTagsView }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar />
        <dataHeader />
      </div>
      <app-main />
    </div>
  </div>
</template>

<script>
import { AppMain, Navbar } from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState, mapGetters } from 'vuex'
import dataHeader from '@/components/dataBoard/dataHeader/index.vue' // 返回按钮

export default {
  name: 'Layout',
  components: {
    AppMain,
    dataHeader,
    Navbar
  },
  mixins: [ResizeMixin],
  data() {
    return {
      userInfo: {},
      fromType: '',
      followupType: false
    }
  },
  computed: {
    ...mapState({
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      showSettings: (state) => state.settings.showSettings,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader
    }),
    ...mapGetters(['bigFont']),
    classObj() {
      return {
        applicationGlobalBigFont: this.bigFont,
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    getUserInfo() {
      this.userInfo = JSON.parse(sessionStorage.getItem('patientData'))
      if (this.userInfo !== null) {
        this.userInfo.regId = this.userInfo.regId ? this.userInfo.regId : this.userInfo.recordId
        this.userInfo.name = this.userInfo.name ? this.userInfo.name : this.userInfo.patientName
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';
@import '~@/styles/variables.scss';

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}
::v-deep .followupPop {
  .el-dialog__header {
    padding: 0;
    line-height: 2.5rem;
    padding: 0 1rem;
    background-color: #f6f6f6;
    line-height: 40px;
    height: 40px;
    font-size: 0.65rem !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  }
}
</style>
