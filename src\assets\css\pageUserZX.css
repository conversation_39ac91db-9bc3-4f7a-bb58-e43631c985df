body .el-select,
body .el-select .el-select__wrapper {
  height: 1.6rem;
  line-height: 1.6rem;
  font-size: 0.7rem;
  min-height: 1.6rem;
}
body .el-select .el-tooltip__trigger.el-select__wrapper {
  height: auto;
}
body .el-select.w10{
  padding: 0 ;
}
body .el-select.w10 .el-select__wrapper {
  padding: 0 0.6rem;
}
.el-popper.is-light{
  padding: 0;
}
.el-popper.is-light .userPopList {
  line-height: 1.6rem;
  text-align: center;
  font-size: 0.8rem;
}
.el-popper.is-light .userPopList:hover{
    background: #d7f0ee;
    font-weight: bold;
}
body .el-textareat{
  font-size: 0.7rem;
}

.maxHeightArea .el-textarea__inner{
  max-height: 8vh;
}
.uploadListDiv.uploadDiv{
  width: 200px;
  border-radius: 0.1rem;
}
.uploadListDiv.uploadDiv .imgList{
  float: left;
  margin-right: 1rem;
  border-radius: 0.1rem;
  position: relative;
}
.uploadDiv .imgList.btnDiv{
  background: var(--uploadBtnBJ);
  border: 0.05rem solid var(--uploadBtnBorder);
}

.uploadDiv .imgList.btnDiv::after{
  content: '';
  background-color: var(--uploadBtnColor);
  width: 50%;
  height: 0.2rem;
  border-radius: 0.5rem;
  position: absolute;
  left: 25%;
  top: calc(50% - 0.1rem);
}
.uploadDiv .imgList.btnDiv::before{
  content: '';
  background-color: var(--uploadBtnColor);
  height: 50%;
  width: 0.2rem;
  border-radius: 0.5rem;
  position: absolute;
  left: calc(50% - 0.1rem);
  top: 25%;
}
.uploadListDiv.uploadDiv .el-upload,.uploadListDiv.uploadDiv .el-upload .el-upload-dragger{
  border: 0 none;
  border-radius: 0.1rem;
}
.uploadDiv .uploadIconDiv{
  display: none;
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: var(--uploadBtnBJColor);
  left: 0;
  top: 0;
  line-height: 5rem;
}
.uploadDiv .uploadIconDiv .iconfont{
  font-size: 1rem;
  color: #fff;
}
.uploadDiv .imgList:hover .uploadIconDiv{
  display: inline-block;
}
.uploadDiv{
  width: 5rem;
  height: 5rem;
}
.uploadDiv .el-upload{
  width: 100%;
  height: 100%;
}
.uploadDiv .el-upload .el-upload-dragger{
  padding: 0;
  height: 100%;
  position: relative;
}
.uploadDiv .el-upload .el-upload-dragger .btn{
  background-color: var(--noColor);
  padding: 0;
}
body .el-checkbox{
  /* LiSuwan 2024.10.19 注释 */
  /* height: 1.6rem; */
  font-size: 0.7rem;
  --el-checkbox-font-size: 0.7rem;
  --el-checkbox-input-height: 0.7rem;
  --el-checkbox-input-width: 0.7rem;
  --el-text-color-regular: var(--colorSecond);
  --el-checkbox-checked-text-color: var(--color);
}
body .el-checkbox .el-checkbox__label{
  padding-left: 0.15rem;
}
body .el-input,
body .el-input .el-input__wrapper {
  height: 1.6rem;
  line-height: 1.6rem;
  font-size: 0.7rem;
  border-radius: .15rem;
  width: 100%;
}
.el-input-number__increase,.el-input-number__decrease{
  height: 1.6rem;
    line-height: 1.6rem;
}
body .el-input .el-input__wrapper {
  padding: 0 .75vw;
}
.el-input__inner::placeholder{
  color: var(--colorPlaceholder);
  font-size: 0.7rem;
}
body .el-input .el-input__wrapper .iconfont{
  font-size: 1.2rem;
  color: var(--colorActive);
}

/*!* 数字输入 *!*/
/*body .el-input-number .el-input-number__decrease{*/
/*  margin-top: 0.05rem;*/
/*}*/
/*body .el-input-number .el-input-number__increase {*/
/*  margin-top: 0.05rem;*/
/*  margin-right: 1rem;*/
/*}*/
.centerDiv{
  padding: 0.3rem 0.8rem;
  height: 100%;
}
.centerDiv > .searchDiv {
  margin-bottom: 1rem;
}
/*.centerDiv > .treeDiv .titles{*/
/*  padding: 1rem 1vw 0.5rem;*/
/*  height: 3.5rem;*/
/*}*/
/*.centerDiv>.treeDiv .treeInfoDiv {*/
/*  height: calc(100% - 3.75rem);*/
/*}*/
/*.centerDiv > .treeDiv {*/
/*  height: calc(100% - 3.75rem);*/
/*  width: 15vw;*/
/*  overflow: hidden;*/
/*}*/
/*.centerDiv> .tableInfoDiv{*/
/*  width: 100%;*/
/*  height: calc(100% - 3.75rem);*/
/*}*/
/*.centerDiv>.treeDiv + .tableInfoDiv{*/
/*  width: calc(100% - 16vw);*/
/*}*/
/*.centerDiv .tableInfoDiv .searchDiv {*/
/*  margin-bottom: 1rem;*/
/*}*/
/*.centerDiv>.tableInfoDiv .searchDiv+.tabelDivOh {*/
/*  height: calc(100% - 8.9rem);*/
/*}*/
/*.centerDiv>.tableInfoDiv .tabelDivOh {*/
/*  height: calc(100% - 2.6rem);*/
/*}*/
/* 按钮列表 */
.btnListDiv .list{
  cursor: pointer;
  padding: 0 1rem;
  line-height: 1.2rem;
  display: inline-block;
  background-color: var(--btnListBJColor);
  color: var(--colorSecond);
  font-size: 0.7rem;
  margin-right: 1.5rem;
  border-radius: 0.3rem;
}
.btnListDiv .list.active{
  color: var(--colorActive);font-weight: 600;
}
/* 搜索 */
.searchDiv {
  background-color: var(--bjColor);
  padding: 0.6rem 0.75rem .6rem;
  box-shadow: 0 0.1rem 0.4rem 0 var(--searchBoxColor);
  border-radius: 0.5rem;
}
.searchList{
  font-size: .7rem;
  color: var(--colorSecond);
  width: 20%;
  margin-bottom: 0.5rem;
}
.searchList.row2{
  width: 40%;
}
.searchList.w10{
  width: 100%;
}
.searchList .txt{
  line-height: 1.6rem;
  font-weight: 400;
  font-size: .8rem;
}
.searchList .val{
  padding:0 1rem 0 0 !important;
}

.searchList.line .txt {
  line-height: 1.6rem;
  font-weight: 400;
  width: 4rem;
  display: inline-block;
  vertical-align: middle;
}

.searchList.line .val {
  padding: 0 1rem 0 0 !important;
  display: inline-block;
  width: calc(100% - 7rem);
  vertical-align: middle;
}
.searchList.line .val.el-date-editor{
  padding: 0 !important;
}
.btn *{
  cursor: pointer;
}
.searchList .btn {
  padding: 0 1vw;
  line-height: 1.6rem;
  height: 1.6rem;
  font-size: 0.7rem;
  font-weight: 500;
}
.iconTime{
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border-radius: 1rem;
  background-color: var(--colorBJError);
  position: relative;
}
.iconTimeBlue.iconTime{
  background-color: var(--colorBJBlue);
}
.iconTimeBlue.iconTime::after{
  border-left: 0.1rem solid var(--colorBlue);
  border-bottom: 0.1rem solid var(--colorBlue);
}
.iconTime::after{
  content: '';
  display: inline-block;
  left: calc(50% + 0.1rem);
  top: calc(50% - 0.1rem);
  width: 0.2rem;
  height: 0.25rem;
  border-left: 0.1rem solid var(--colorError);
  border-bottom: 0.1rem solid var(--colorError);
  transform: translate(-50%,-50%);
  position: absolute;
}
.loginForm {
  font-weight: 400;
  font-size: 1rem;
  color: var(--colorSecond);
}
.loginForm::after{
  clear: both;
  content: '';
  display: block;
  position: relative;
}
.loginForm .formItem{
  float: left;width: 100%;
}
.loginForm .formItem.w5{
  width: 50%;
}
.loginForm .formItem.w3 {
  width: 33.33%;
}
.loginForm .formItem .txt {
  float: left;
  width: 100%;
  margin-bottom: .5rem;
}
.loginForm .formItem .inputVal {
  float: left;
  width: 100%;
}
/* table 表格 */
.tabelDiv{
  width: 100%;
  border-radius: .1rem .1rem 0 0;
}
.tabelDiv .el-table__expand-icon{
  font-size: 0.6rem;
  line-height: 1rem;
  height: 1rem;
}
.tabelDiv .el-table__expand-icon>.el-icon{
  font-size: 0.6rem;
  line-height: 1rem;
  height: 1rem;
}
.el-table--fit .el-table__inner-wrapper:before{
  display: none;
}
.tabelListDiv.el-table .el-table__cell{
  padding: 0;
}
.el-table .cell{
  font-weight: 500;
  font-size: 0.7rem;
  color: var(--colorSecond);
}
.el-table.tabelCenter .el-table__header .is-group{
  background: var(--tableTitleBJColor);
}
.el-table.tabelCenter .el-table__header .is-group th{
  font-size: .8rem;
  color: var(--colorSecond);
  font-weight: 400;
  padding: 0;
  background-color: transparent;
  text-align: center;
}
.el-table.tabelCenter .el-table__header .is-group tr {
background-color: transparent;
}
.el-table.tabelCenter .el-table__header th {
  background: var(--tableTitleBJColor);
  font-size: .8rem;
  color: var(--colorSecond);
  font-weight: 400;
  padding: 0;
}
.el-table.tabelCenter .el-table__header .cell{
  font-weight: 700;
  font-size: 0.75rem;
  line-height: 1.5;
  padding: .55rem 0.6rem;
}

.el-table.tabelCenter .el-table__body-wrapper .cell {
  line-height: 1.5;
}
.el-table.tabelCenter .el-table__body-wrapper tr:nth-child(even){
  background-color: var(--tableEvenTrBJColor);
}
.tabelDiv .cell .btn{
  border-radius: 1rem;
  padding: 0 .6rem;
  font-size: 0.7rem;
}

.trCP.tabelDiv .el-table__body-wrapper .el-table__row{
  cursor: pointer;
}
.canClickTable .el-table__row.current-row{
  background: var(--btnBJLinear) !important;
}
.canClickTable .el-table__row.current-row>td.el-table__cell{
  background-color: transparent !important;
}
.canClickTable .el-table__row.current-row>td.el-table__cell .cell{
  color: var(--btnLinear);
}
.typeLabel{
  height: 1.2rem;
  line-height: 1.2rem;
  background: var(--colorBJError);
  color: var(--colorError);
  border-radius: .2rem;
  display: inline-block;
  font-size: 0.6rem;
  padding: 0 .8rem;
}
.typeLabel1{
  background: var(--colorGreenBJActive);
  color: var(--colorGreenActive);
}

.filterPop{
  background-color: red;
  min-height: 10rem;
  width: 10vw;
}
.filterParDiv{
  position: relative;
  line-height: 1rem;
}
.filterParDiv .val{
  line-height: 1rem;
  padding: 0 0.5rem;
}
.filterParDiv .listDiv{
  position: absolute;
  left: 0;
  top: 1rem;
  width: 10vw;
  background-color: #fff;
  border: 0.05rem solid blue;
  display: inline-block;
  width: 100%;
  line-height: 1rem;
  z-index: 99;
}
.filterParDiv .listDiv label{
  width: 5vh;
  display: block;
  padding: 0 0.5rem;
}
.filterParDiv{
  width: 40%;
}
.filterParValDiv{
  width: 59%;
}
/* switch 切换*/
.el-switch {
  font-weight: 400;
  font-size: 0.9rem;
  color: var(--colorThird);
  line-height: 1rem;
  height: 1.6rem;
}
.el-switch.is-checked .el-switch__core {
  background-color: var(--colorActive);
}
.el-switch__label *{
  font-size: 0.7rem;
}
.el-switch__core{
  height: 1rem;
  min-width: 1.6rem;
  border-radius: 0.5rem;
}
.el-switch__core .el-switch__action{
  height: .8rem;
  width: .8rem;
}
.el-switch.is-checked .el-switch__core .el-switch__action{
  left: calc(100% - 0.85rem);
}
.labelSwitch{
  font-weight: 500;
  font-size: 0.7rem;
  line-height: 1.5rem;
  display: inline-block;
  background: var(--switchBJColor);
  border-radius: 1rem;
  overflow: hidden;
  position: relative;
  font-weight: 400;
  font-size: 0.7rem;
  color: var(--colorSecond);
}
.labelSwitch label.cp{
  padding: 0 1rem;
  display: inline-block;
  background: var(--noColor);
  color: var(--colorSecond);
}
.labelSwitch label.cp.active{
  background: var(--switchActiveBJColor);
  color: var(--switchActiveColor);
  font-weight: 600;
}

.text-btn{
  color: var(--colorActive);
  font-weight: 500;
  font-size: 0.7rem;
  margin-right: 0.5rem;
}
.text-btn.noClick{
  color: var(--colorNo);
  cursor: text;
}
.text-btn.red{
  color: var(--colorError);
}
/* radio 单选*/
body .el-radio-group{
  line-height: 1.6rem;
  min-height: 1.6rem;
  vertical-align: middle;
}
.labelRadio{
  position: relative;
  overflow: hidden;
}
.labelRadio .radioList{
  border-radius: 0.2rem;
  border: 0.05rem solid var(--radioBorderColor);
  font-weight: 400;
  font-size: 0.7rem;
  color: var(--colorSecond);
  padding: 0.1rem 0.7rem;
  margin-right: 0.75rem;
  float: left;
  cursor: pointer;
  background-color: var(--bjColor);
}
.labelRadio .radioList.active{
  border: 0.05rem solid var(--radioActiveColor);
  color: var(--radioActiveColor);
  font-weight: 500;
}
.el-radio{
  margin-right: 0.5rem;
  /* LiSuwan 2024.10.19 注释 */
  /* height: 1.6rem; */
}
.el-radio .el-radio__input .el-radio__inner{
  width: 0.7rem;
  height: 0.7rem;
}
.el-radio .el-radio__input .el-radio__inner:after{
  width: 0.25rem;
  height: 0.25rem;
}
.el-radio .el-radio__label {
  font-size: 0.7rem;
}
/* echarts */
.optionList{
  padding: 0 2.1rem;
  i{
    display: inline-block;
    width: 0.4rem;
    height: 0.4rem;
    border-radius: 1rem;
    margin-right: 0.5vw;
  }
  .txt{
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 0.6rem;
    color: var(--optionColor);
    line-height: .85rem;
  }
  .number{
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 0.8rem;
    color: var(--optionNumberColor);
    margin-left: 1vw;
  }
}
/* 日历 */
.el-calendar-table thead{
  display: none;
}
.el-calendar-table tr,.el-calendar-table,.el-calendar__body,.el-calendar-table tr td{
  border: 0 none !important;
}
.el-calendar__body{
  padding-bottom: 0.75rem;
  border-bottom: 0.1rem dashed var(--lineBorderColor) !important;
}
.el-calendar-table .el-calendar-day{
  height: 3rem;
  line-height: 3rem;
  text-align: center;
  padding: 0;
  font-size: 0.8rem;
  border: 0 none;
}

.el-calendar-table .is-today .el-calendar-day{
  color: var(--todayColor);
}
.el-calendar-table .is-today .el-calendar-day span{
  background-color: var(--colorActive);
  border-radius: 1rem;
  width: 1.3rem;
  line-height: 1.3rem;
  text-align: center;
  display: inline-block;
}
.el-calendar-table td.is-selected{
  background-color: var(--colorBJActive);
  color: var(--todayColor);
}
.el-calendar-table .el-calendar-day:hover{
  background-color: var(--colorBJActive);
  color: var(--todayColor);
  opacity: 0.8;
}
.el-calendar-table .el-calendar-day{
  position: relative;
}
.el-calendar-table .el-calendar-day .yuQiAndSuiFan{
  line-height: 1;
  position: absolute;
  bottom: .25rem;
  width: 100%;
}
.el-calendar-table .el-calendar-day .yuQiAndSuiFan label{
  display: inline-block;
  width: 0.3rem;
  height: 0.3rem;
  border-radius: 0.3rem;
  background-color: var(--colorError);
  margin: 0 0.15rem;
}
.el-calendar-table .el-calendar-day .yuQiAndSuiFan label.sfCss{
  background-color: var(--colorWarn);
}

/* tree树格式 第一个是角色与权限 */
/*.treeDiv{*/
/*  background: var(--bjColor);*/
/*  border-radius: .5rem;*/
/*  border: 0.05rem solid var(--lineBorderColor);*/
/*  height: calc(100% - 3.25rem);*/
/*  width: 15vw;*/
/*  float: left;*/
/*  box-shadow: 0 0 .4rem 0 var(--searchBoxColor);*/
/*  overflow: hidden;*/
/*}*/
/*.treeDiv .title{*/
/*  line-height: 2.2rem;*/
/*  font-weight: 400;*/
/*  font-size: .9rem;*/
/*  color: var(--colorSecond);*/
/*  background-color: var(--treeTitleBJColor);*/
/*  padding-left: 1rem;*/
/*}*/
/*.treeDiv .treeInfoDiv{*/
/*  height: calc(100% - 3.2rem);*/
/*  overflow: auto;*/
/*  text-indent: 2rem;*/
/*  line-height: 1.6rem;*/
/*  font-weight: 400;*/
/*}*/
/*.treeDiv .treeInfoDiv.el-tree{*/
/*  text-indent: 0rem;*/
/*}*/
/*.treeDiv .treeInfoDiv .lists.active,*/
/*.treeDiv .treeInfoDiv .lists:hover{*/
/*  background-color: var(--treeActiveBJColor);*/
/*  color: var(--color);*/
/*  font-weight: 600;*/
/*  position: relative;*/
/*}*/
/*.treeDiv .treeInfoDiv .lists.active::after{*/
/*  content: '';*/
/*  display: inline-block;*/
/*  position: absolute;*/
/*  left: 0;*/
/*  width: .15rem;*/
/*  background: var(--colorActive);*/
/*  height: 1.8rem;*/
/*  top: 50%;*/
/*  transform: translateY(-50%);*/
/*  border-radius: 0.1rem;*/
/*}*/
/*.treeDiv .treeInfoDiv .lists .iconfont{*/
/*  font-size: 1.1rem;*/
/*  font-weight: normal;*/
/*  text-indent: 0;*/
/*  margin: 0 1rem 0 0;*/
/*  display: none;*/
/*}*/
/*.treeDiv .treeInfoDiv .lists:hover .iconfont{*/
/*  display: inline-block;*/
/*}*/

/*.el-tree-node__content{*/
/*  line-height: 1.6rem;*/
/*  height: 1.6rem;*/
/*  font-size: 0.8rem;;*/
/*}*/
/*.el-tree-node__content>.el-tree-node__expand-icon{*/
/*  padding: 0.3rem 0.3vw;*/
/*  font-size: 0.8rem;*/
/*  width: 1rem;*/
/*  height: 1rem;*/
/*  margin-left: 0.5vw;*/
/*}*/
/*.el-tree-node__content>label.el-checkbox{*/
/*  margin-right: 0.4rem;*/
/*}*/
.el-checkbox__inner{
  width: 0.7rem;
  height: 0.7rem;
}
.el-checkbox__inner:after{
  height: 0.35rem;
  width:0.15rem;
  left: 0.25rem;
  top: 0.1rem;
}
.el-checkbox__input.is-checked .el-checkbox__inner{
  background-color: var(--colorActive);
  border-color: var(--colorActive);
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner{
  background-color: var(--colorActive);
  border-color: var(--colorActive);
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner:before{
  height: .1rem;
  top: .25rem;
}

.el-tree-node.is-current.is-focusable>.el-tree-node__content,
.el-tree-node.is-focusable .el-tree-node.is-current.is-focusable>.el-tree-node__content {
  background-color: var(--treeTitleActiveBJColor);
  font-weight: 600;
}


.el-tree-node.is-expanded.is-current.is-focusable > .el-tree-node__content,
.el-tree-node.is-expanded.is-focusable .el-tree-node.is-current.is-focusable >.el-tree-node__content{
  background-color: var(--treeTitleActiveBJColor);
  font-weight: 600;
}

/* 分页 */
.el-pagination{
  text-align: right;
}
/*.el-pagination{*/
/*  --el-pagination-font-size:0.8rem;*/
/*  --el-pagination-button-width: 1.6rem;*/
/*  --el-pagination-button-height: 1.6rem;*/
/*  --el-pagination-font-size-small: .6rem;*/
/*  --el-pagination-button-width-small: 1.2rem;*/
/*  --el-pagination-button-height-small: 1.2rem;*/
/*  --el-pagination-item-gap: 0.4rem;*/
/*  background-color: var(--bjColor);*/
/*  padding: 0.5rem 0;*/
/*  text-align: right;*/
/*  justify-content: flex-end;*/
/*}*/
/*body .el-pagination .el-select,*/
/*body .el-pagination .el-select .el-select__wrapper{*/
/*  height: 1.2rem;*/
/*  line-height: 1.2rem;*/
/*  font-size: 0.6rem;*/
/*  color: var(--colorThird);*/
/*}*/
/*body .el-pagination .el-input,*/
/*body .el-pagination .el-input .el-input__wrapper {*/
/*  height: 1.5rem;*/
/*  line-height: 1.5rem;*/
/*  font-size: 0.6rem;*/
/*  color: var(--colorThird);*/
/*  padding: 0 0.2rem;*/
/*}*/
/*body .el-pagination .el-pagination__goto{*/
/*  font-size: 0.7rem;*/
/*}*/

/*body .el-pagination .el-pagination__sizes,*/
/*body .el-pagination .el-pagination__total {*/
/*  color: var(--colorThird);*/
/*}*/
/*body .el-pagination .el-select__placeholder{*/
/*  color: var(--colorThird);*/
/*}*/

/*body .el-pagination button.is-disabled,*/
/*body .el-pagination button:disabled,*/
/*body .el-pagination button,*/
/*body .el-pagination button{*/
/*  width: 1.6rem;*/
/*  height: 1.6rem;*/
/*  background: #F3F3F3;*/
/*  border-radius: .15rem;*/
/*  border: 0.05rem solid #DCDFE6;*/
/*}*/


/*body .el-pagination button,body .el-pagination li{*/
/*  background-color: var(--noColor);*/
/*}*/

/*body .el-pagination li {*/
/*  height: 1.6rem;*/
/*  width: 1.6rem;*/
/*  padding: 0;*/
/*  min-width: 1.6rem;*/
/*  background-color: transparent;*/
/*  margin: 0 0.2rem;*/
/*  font-size: .6rem;*/
/*  border-radius: .2rem;*/
/*  border: 0.05rem solid #DCDFE6;*/
/*}*/
/*body .el-pagination li:hover{*/
/*  border: 0.05rem solid #3FE1D2;*/
/*}*/
/*body .el-pagination li.is-active {*/
/*  background: var(--paginationActiveBJColor);*/
/*  color: var(--paginationActiveColor);*/
/*  font-weight: 100;*/
/*}*/

/* 时间 */
body .el-date-editor{
  --el-date-editor-monthrange-width: calc(100% - 1rem);
  --el-date-editor-daterange-width: calc(100% - 1rem);
  --el-date-editor-datetimerange-width: calc(100% - 1rem);
  line-height: 1.6rem;
  height: 1.6rem;
  --el-input-height: 1.6rem;
}

body .el-date-editor.w10{
  width: 100%;
}
body .el-date-editor .el-range-input{
  width: 33%;
}
body .el-date-editor .el-range__icon{
  font-size: 0.8rem;
  width: 2rem;
}
body .el-date-editor .el-range-separator{
  font-size: 0.7rem;
  color: var(--colorThird);
  font-weight: 100;
}
body .el-date-editor .el-range-input{
  font-size: 0.8rem;
  line-height: 1.6rem;
  height: 1.6rem;
}
body .el-date-editor .el-range__close-icon{
  font-size: 0.8rem;
}

.pageNavDiv{
  padding-bottom: .75rem;
}
.pageNavDiv .list{
  font-weight: 400;
  font-size: .8rem;
  color: var(--btnBJColor);
  background-color: var(--colorBJActive);
  padding: 0 .75rem;
  line-height: 1.8rem;
  border-radius: 1rem;
  display: inline-block;
  margin-right: .75rem;
  cursor: pointer;
}
.pageNavDiv .list:hover {
  color: var(--btnColor);
  background-color: var(--btnBJColor);
  opacity: 0.8;
}
.pageNavDiv .list.active{
  color: var(--btnColor);
  background-color: var(--btnBJColor);
}
.formList .list{
  width: 50%;
  padding-right: 1rem;
  margin-top: .5rem;
  margin-bottom: .5rem;
}
.formList .list.w10 {
  width: 100%;
  padding-right: 1rem;
  padding-left: 1rem;
}
.formList .list.w3 {
  width: 33.33%;
  padding-right: 1rem;
  padding-left: 0;
}
.formList .list.w6{
  width: 66.67%;
  padding-right: 1rem;
}
.formList .list.w6:nth-child(even) {
  padding-right: 1rem;
  padding-left: 0;
}
.formList .list.w3:nth-child(even) {
  padding-right: 1rem;
  padding-left: 0;
}
.formList .list .txt {
  font-size: 0.7rem;
  color: var(--colorSecond);
  line-height: 1.5rem;
}
.formList .list:nth-child(even) {
  padding-left: 1rem;
  padding-right: 0;
}

.formList.line .txt {
  line-height: 1.6rem;
  font-weight: 400;
  width: 5rem;
  display: inline-block;
  vertical-align: middle;
}

.formList.line .val {
  padding: 0 1rem 0 0 !important;
  display: inline-block;
  width: calc(100% - 5rem);
  vertical-align: middle;
}
/* el-dialog 弹窗*/
.el-dialog{
  padding: 0;
  border-radius: .5rem;
  overflow: hidden;
}
.popCenter{
  padding: 1rem 0;
}
.w6{
  width: 66.67%;
}
.w3{
  width: 33.33%;
}
.titleLineGreen{
  padding-left: 0.9rem;
  font-weight: 500;
  font-size: .8rem;
  color: var(--crumbActiveColor);
  position: relative;
}

.titleLineGreen::before{
  content: '';
  display: inline-block;
  background-color: var(--lineGreenColor);
  width: 0.15rem;
  height: .8rem;
  position: absolute;
  left: 0;
  top: calc(50% - 0.4rem);
  border-radius: 0.05rem;
}



.cunYiPopHeader{
  background-color: var(--treeTitleBJColor);
  font-size: 0.8rem;
  color: var(--color);
  font-weight: 400;
  padding: 0 1rem;
}
.cunYiPopHeader .icon+.vam {
  margin-left: 0.5rem;
}
.el-dialog__footer{
  padding: .5rem 1.5rem 1rem;
}
.el-dialog__footer .btn{
  margin-left: 1rem;
  line-height: 1.6rem;
  height: 1.6rem;
  border-radius: .15rem;
}
/* form表单 */
/* 必填项红色* */
.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label-wrap>.el-form-item__label:before,
.el-form-item.is-required:not(.is-no-asterisk).asterisk-left>.el-form-item__label:before {
  display: none;
}
.el-form-item.is-required.asterisk-left>.el-form-item__label-wrap::before,
.el-form-item.is-required.asterisk-left::before {
  content: '*';
  color: var(--el-color-danger);
  position: absolute;
  left: 0;
  line-height: 1.75rem;
  font-size: 0.8rem;
}
.el-form-item.asterisk-left .el-form-item__label{
  position: relative;
  padding-right: 1rem;
  line-height: 1.6rem;
  min-height: 1.6rem;
  height: 1.6rem;
}

.el-form-item.asterisk-left:not(.hideMH) .el-form-item__label::after {
  content: ':';
  position: absolute;
  right:  0.7rem;
  line-height: 1.6rem;
  font-size: 0.8rem;
}
.el-form-item.asterisk-left{
  padding-left: .5rem;
  position: relative;
}
.el-overlay .el-dialog.is-draggable .el-dialog__header > svg{
  display: none;
}
.el-overlay .el-dialog.is-draggable .el-dialog__header label.vam {
  font-weight: 500;
  font-size: .9rem;
  color: var(--colorActive);
}
.el-overlay .el-dialog.is-draggable .el-form-item__label,.el-form-item__label{
  font-size: 0.7rem;
  /* text-align: justify;
  text-align-last: justify; */
  display: block;
}
.el-overlay .el-dialog.is-draggable .el-form-item__content,.el-form-item__content{
  padding-right: 4rem;
  line-height: 1.6rem;
  min-height: 1.7rem;
}
.el-overlay .el-dialog .el-dialog__header {
  padding: 0 0 0 1.55rem;
  line-height: 3.1rem;
  font-size: .9rem;
  background-color: var(--dialogTitleColor);
}

.el-overlay .el-dialog .el-dialog__header .icon {
  margin-right: 0.5vw;
}

.el-overlay .el-dialog .el-dialog__header .vam {
  line-height: 1rem;
  font-size: 1rem;
  display: inline-block;
}

.el-overlay .el-dialog .el-dialog__headerbtn {
  line-height: 1.3rem;
  height: 1.3rem;
  width: 1.3rem;
  font-size: 1.3rem;
  right: .75rem;
  top: 0.8rem;
}

.el-overlay .el-dialog .el-dialog__headerbtn .el-icon {
  font-weight: bold;
}

.el-overlay .el-dialog .el-dialog__body {
  padding: 1vw 2vw;
}

.el-overlay .el-dialog.body0 .el-dialog__body {
  padding: 0vw;
}

.el-overlay .el-input__wrapper {
  width: 100%;
  padding-right: 0;
}
.titleIco{
  border: var(--icoBorder);
  padding: 0.45rem;
  margin-left: 1rem;
  border-radius: 0.3rem;
}
.tableTopDiv{
  margin-bottom: 1.2rem;
}

/* tabs */

body .el-tabs__nav-wrap:after{
  display: none;
}


