@mixin elDialogCommon {
  .el-dialog__header {
    position: relative;
    span {
      font-size: 0.83vw !important;
    }
    .dialog-title {
      font-size: 0.83vw !important;
      img {
        width: 1vw !important;
        height: 1vw !important;
      }
      span {
        font-size: 0.83vw !important;
      }
    }
    i {
      font-size: 1vw !important;
    }
    .el-dialog__headerbtn {
      font-size: 0.82vw !important;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .el-dialog__body {
    .el-form-item {
      .el-form-item__label {
        &::before {
          font-size: 0.83vw !important;
        }
        span {
          font-size: 0.83vw !important;
        }
      }
      .el-form-item__content {
        .el-input__inner {
          font-size: 0.83vw !important;
        }
      }
    }
    .el-button {
      font-size: 0.83vw !important;
    }
    .info {
      p {
        font-size: 0.83vw !important;
      }
      .messbox {
        .label {
          span {
            font-size: 0.83vw !important;
          }
        }
      }
      .el-button {
        font-size: 0.83vw !important;
      }
      .el-input__inner {
        font-size: 0.83vw !important;
      }
    }
    .checkboxMainContainer {
      .el-checkbox {
        margin-right: 1.5vw !important;
        margin-bottom: 0.5vw;
      }
      .el-checkbox__label {
        font-size: 0.82vw !important;
      }
      .titleBox {
        font-size: 0.82vw !important;
        img {
          width: 1vw !important;
          height: 1vw !important;
        }
      }
    }
    .el-tree-node__content {
      margin-bottom: 0.3vw !important;
    }
    .el-tree-node__label,
    .tree-span {
      font-size: 0.83vw !important;
    }
    .el-descriptions-item__label {
      font-size: 0.83vw !important;
    }
    .el-descriptions-item__content {
      font-size: 0.83vw !important;
    }
  }
  .el-dialog__footer {
    .dialog-footer {
      .read {
        img {
          width: 1.05vw !important;
        }
        span {
          font-size: 0.83vw !important;
        }
      }
      .btn {
        .el-button {
          font-size: 0.83vw !important;
        }
      }
    }
    .footerButtonBox {
      .el-button {
        min-width: 5vw;
        font-size: 0.82vw !important;
      }
    }
  }
}
// 大字模式
.applicationGlobalBigFont {
  .sidebar-container {
    .topTitle {
      font-size: 1.2rem !important;
    }
    .subTitle {
      font-size: 0.62vw !important;
      display: none;
    }
    .el-submenu {
      .el-submenu__title,
      .el-menu-item {
        height: 2vw;
        display: flex;
        align-items: center;
        svg {
          width: 0.82vw !important;
          height: 0.82vw !important;
        }
        span {
          font-size: 0.83vw !important;
        }
      }
    }
  }
  .navbar {
    .avatar-container {
      span {
        font-size: 1vw !important;
      }
    }
  }
  .el-dialog {
    @include elDialogCommon;
  }
  .wiTreeInputContainer {
    .vue-treeselect__menu {
      width: 150%;
    }
    .vue-treeselect__single-value,
    .vue-treeselect__input,
    .vue-treeselect__label {
      font-size: 0.82vw !important;
    }
    .vue-treeselect__label {
      height: 1.668vw;
    }
  }
  .treatmentContainer {
    .left {
      max-width: 20vw;
      .el-input__inner {
        height: 2vw;
        font-size: 1vw !important;
      }
      .el-input__icon {
        font-size: 1vw;
        line-height: 2vw;
      }
      li {
        font-size: 1vw;
        padding: 0.42vw 0;
        display: flex;
        justify-content: flex-start;

        .type {
          flex-shrink: 0;
          width: 0.5vw;
          height: 0.5vw;
        }

        span {
          font-size: 0.73vw !important;
        }
      }
    }
    .rightContentContainer {
      .top {
        font-size: 1vw;
      }
      .hint {
        p {
          font-size: 1vw;
        }
        span {
          font-size: 1vw;
        }
        .legend {
          .type {
            width: 0.5vw;
            height: 0.5vw;
          }
          span {
            font-size: 1vw;
          }
        }
      }
      .queueBox {
        .userItemBox {
          font-size: 1.45vw;

          .underway {
            font-size: 0.82vw;
          }
          .doctorBox {
            font-size: 1vw;
          }
        }
      }
    }
  }
  .outpatient {
    .left {
      & > p {
        i {
          font-size: 0.83vw !important;
        }
        font-size: 0.83vw !important;
      }
      .user {
        font-size: 0.83vw !important;
        margin-bottom: 0.3vw;
      }
    }
    .right {
      .menu-item {
        font-size: 0.83vw !important;
        svg {
          width: 1vw !important;
          height: 1vw !important;
        }
      }
      .historyTaking {
        .el-textarea__inner {
          font-size: 0.83vw;
        }
        .el-input__count {
          font-size: 0.53vw;
        }
      }
      .sectionTitle {
        .el-button {
          width: 4vw;
          font-size: 0.83vw;
        }
      }
    }
  }
  .el-empty {
    .el-empty__image {
      width: 8vw;
    }
    .el-empty__description {
      p {
        font-size: 0.83vw !important;
      }
    }
  }
  .appCustomCapsuleTab {
    li {
      font-size: 0.83vw !important;
      height: 2vw;
      line-height: 2vw;
      border-radius: 2vw;
      min-width: 6vw;
      padding: 0 1vw;
    }
  }

  .phoneShow {
    .leftPhoneShow {
      p {
        font-size: 0.83vw !important;
      }
    }
    .rightPhoneShow {
      .steps-label {
        font-size: 0.83vw !important;
      }
      .steps-arrows {
        .num {
          font-size: 0.83vw !important;
        }
      }
    }
  }

  .equiptTop {
    .equipTab {
      .equipItem {
        font-size: 0.83vw !important;
        height: 2vw !important;
        line-height: 2vw !important;
        border-radius: 2vw !important;
        width: 7vw !important;
        padding: 0 1vw !important;
      }
    }
  }
  .diagnoseContainer {
    .el-button {
      font-size: 0.83vw !important;
    }
    .mainText {
      font-size: 0.83vw !important;
    }
    .el-table__body {
      .el-input__inner {
        font-size: 0.83vw !important;
        height: 2.1vw !important;
        line-height: 2.1vw !important;
      }
      .el-input-number__increase,
      .el-input-number__decrease {
        font-size: 0.62vw !important;
        height: calc(2.1vw - 2px) !important;
        line-height: calc(2.1vw - 2px) !important;
      }
      .el-button {
        font-size: 0.83vw !important;
      }
    }
  }
  .telereferenceBox {
    .topDoctorBox {
      .el-checkbox {
        .el-checkbox__label {
          font-size: 0.83vw !important;
        }
      }
      .moreBtnBox {
        width: 3vw !important;
        font-size: 0.83vw !important;
      }
      .el-button {
        font-size: 0.83vw !important;
      }
    }
    .titleBox {
      font-size: 0.83vw !important;
    }
    .suggestionInput {
      .el-textarea__inner {
        font-size: 0.83vw !important;
      }
    }
    .prescriptionBtnBox {
      .el-button {
        font-size: 0.83vw !important;
        height: 2vw !important;
        line-height: 2vw !important;
        padding: 0 1vw !important;
      }
    }

    .el-input__inner {
      font-size: 0.83vw !important;
      height: 2.1vw !important;
      line-height: 2.1vw !important;
    }
    .el-input-number__increase,
    .el-input-number__decrease {
      font-size: 0.62vw !important;
      height: calc(2.1vw - 2px) !important;
      line-height: calc(2.1vw - 2px) !important;
    }
  }
  .tabContainer {
    .describe {
      font-size: 0.83vw !important;
    }
    .x-box {
      .label {
        min-width: 2.6vw !important;
        font-size: 0.83vw !important;
      }
      .box {
        .el-input {
          width: 10vw !important;

          .el-input-group__append {
            font-size: 0.83vw !important;
          }
        }
        .el-input__inner {
          height: 2vw !important;
          line-height: 2vw !important;
          font-size: 0.83vw !important;
        }
        .el-radio__label {
          font-size: 0.83vw !important;
        }
        .item {
          margin-bottom: 0.5vw;
          .span {
            width: 7.5vw !important;
            font-size: 0.83vw !important;
          }
        }
      }
    }
    .right-title,
    .his-title {
      font-size: 0.83vw !important;
      img {
        width: 1vw !important;
        height: 1vw !important;
      }
    }
    .el-timeline {
      .el-timeline-item__timestamp {
        font-size: 0.83vw !important;
        width: 4.8vw !important;
      }
      .el-timeline-item__content {
        .el-button.el-button--danger.el-button--mini.is-plain.is-circle {
          i {
            font-size: 0.83vw !important;
          }
        }
        .el-descriptions-item__label {
          font-size: 0.83vw !important;
        }
        .el-descriptions-item__content {
          font-size: 0.83vw !important;
        }

        .timeline-table {
          table td,
          table th,
          p.p {
            font-size: 0.82vw;
          }
        }
      }
    }
    .main {
      .left {
        .img-box {
          width: 8.85vw !important;
          .img-title {
            font-size: 0.83vw !important;
            line-height: 1vw !important;
          }
          img {
            width: 8.85vw !important;
          }
        }
      }
    }
    .text {
      p {
        font-size: 0.83vw !important;
      }
    }
    .card {
      .item-box {
        margin-bottom: 0.5vw !important;
        .item {
          i.labelBox,
          span {
            font-size: 0.83vw !important;
          }
          .el-input {
            font-size: 0.83vw !important;
          }
        }
      }
      .from-item-box {
        .item {
          i,
          span {
            font-size: 0.83vw !important;
          }
        }
      }
      .cont-box {
        .cont-label {
          font-size: 0.83vw !important;
        }
        i.other {
          font-size: 0.83vw !important;
        }
      }

      .el-input {
        width: 8vw !important;

        .el-input-group__append {
          font-size: 0.83vw !important;
        }
      }
      .el-input__inner {
        height: 2vw !important;
        line-height: 2vw !important;
        font-size: 0.83vw !important;
      }
    }
    .headerTr {
      th {
        font-size: 0.83vw !important;
        height: 2.6vw !important;
        line-height: 2.6vw !important;
      }
    }
    .bodyTr {
      td {
        font-size: 0.83vw !important;
        height: 2.6vw !important;
        line-height: 2.6vw !important;

        .el-input {
          .el-input-group__append {
            font-size: 0.83vw !important;
          }
        }
        .el-input__inner {
          height: 2vw !important;
          line-height: 2vw !important;
          font-size: 0.83vw !important;
        }
      }
    }
  }
  .Arteriosclerosis {
    .left {
      td,
      th {
        width: 8.85vw !important;
        font-size: 0.83vw !important;
        padding: 0 1vw !important;
        height: 2.1vw !important;
        .el-input__inner {
          font-size: 0.83vw !important;
          height: 1.3vw !important;
          line-height: 1.3vw !important;
        }
      }
      .input-main {
        .title {
          height: 2vw !important;
          line-height: 2vw !important;
          font-size: 0.83vw !important;
        }
        .el-textarea__inner {
          font-size: 0.83vw !important;
        }
      }
    }
  }
  .applicationLogFixedDrawer {
    .toggleVisibleBox {
      .logToggleTxt {
        font-size: 0.83vw !important;
        width: 0.83vw !important;
      }
    }
    .titleBox {
      font-size: 0.83vw !important;
      img {
        width: 0.93vw !important;
        height: 0.93vw !important;
      }
      .nameBox {
        font-size: 0.83vw !important;
      }
    }
    .searchBox {
      margin-top: 1vw !important;
      .searchLabel {
        width: 4vw !important;
        font-size: 0.62vw !important;
      }
      .el-input__inner {
        font-size: 0.83vw !important;
        height: 2vw !important;
        line-height: 2vw !important;
      }
      .searchBtn {
        width: 3.6vw !important;
        height: 2vw !important;
        border-radius: 0.25vw !important;
        line-height: 2vw !important;
        font-size: 0.83vw !important;
        margin-left: 1vw !important;
        margin-top: 0 !important;
      }
    }
    .timelineBox {
      .dateLine {
        .dateTxt {
          font-size: 0.83vw !important;
        }
        .toggleBox {
          span {
            font-size: 0.83vw !important;
          }
          i {
            font-size: 0.83vw !important;
          }
        }
      }
      .timeTextItemBox {
        .timeTextBox {
          font-size: 0.83vw !important;
        }
        .timelineContentBox {
          font-size: 0.83vw !important;
        }
      }
    }
  }
  .bodyCheckContainer {
    .left {
      width: 230px !important;
      flex-shrink: 0;
      .el-input__inner {
        height: 2vw !important;
        line-height: 2vw !important;
        font-size: 0.82vw !important;
      }
      li {
        font-size: 1vw !important;
        margin-bottom: 0.5vw;
      }
    }
    .right {
      .card {
        font-size: 0.82vw;
      }
      .table-box {
      }
    }
  }

  .healthExaminationConteiner {
    .title {
      font-size: 1vw !important;
    }
  }

  .cstepContainer {
    .nameBox {
      font-size: 0.93vw !important;
    }
  }

  .questionItemBox {
    -webkit-column-break-inside: avoid;
    .qTitle {
      font-size: 0.93vw !important;
      margin-bottom: 0.93vw !important;
    }
    .qRadioGroup {
      .el-radio {
        .el-radio__label {
          font-size: 0.93vw !important;
        }
      }
    }
    .qCkBox {
      .el-checkbox {
        .el-checkbox__label {
          font-size: 0.93vw !important;
        }
      }
    }
    .inputAllBox {
      .qInput {
        .el-input__inner {
          font-size: 0.93vw !important;
        }
      }
    }
    .photoQuestionBox {
      .qPhotoButton {
        &.el-button {
          font-size: 0.93vw !important;
        }
      }
    }
  }

  .followupsContainer {
    .left {
      width: 230px !important;
      flex-shrink: 0;
      .el-input__inner {
        height: 2vw !important;
        line-height: 2vw !important;
        font-size: 0.82vw !important;
      }
      .el-tree-node__content {
        margin-bottom: 0.5vw !important;
      }
      .tree-span {
        font-size: 1vw !important;
      }
    }
    .right {
      .card {
        span {
          font-size: 0.82vw;
        }
      }
    }
  }

  .applicationCustomPageTableVw {
    th.el-table__cell {
      font-size: 1vw !important;
      height: 2vw !important;
      line-height: 2.6vw !important;
      padding-top: 0 !important;
      padding-bottom: 0 !important;
    }
    td.el-table__cell {
      font-size: 1vw !important;
    }
    .el-table__cell {
      padding: 0.41666vw 0 !important;
    }
    .el-table__body-wrapper {
      height: calc(100% - 2vw) !important;
    }
    .el-tag,
    .el-button {
      font-size: 1vw !important;
    }
  }

  // text 类型按钮 加粗
  .el-button.el-button--text {
    font-size: 0.82vw !important;
  }

  .topBasicInfoContainer {
    .leftBasicInfo {
      .infoLine {
        .infoItemBox {
          .labelBox {
            font-size: 0.82vw !important;
          }
          .valueBox {
            font-size: 0.92vw !important;
            .editIcon {
              font-size: 0.72vw !important;
            }
          }
        }
      }
    }

    .badEventABarCode {
      .barCodeBox {
        .bottomCodeText {
          font-size: 0.82vw !important;
        }
      }

      .adverse {
        font-size: 0.83vw !important;
      }
    }

    .rightButtonBox {
      .btnStyle {
        font-size: 0.83vw !important;
      }
    }
  }

  // 客制化tab标签页
  .applicationCustomTabs {
    .el-tabs__nav {
      &.is-top {
        .el-tabs__item {
          font-size: 1vw;
        }
      }
    }
  }
  .el-descriptions {
    .el-descriptions-item__label {
      font-size: 0.83vw;
    }
    .el-descriptions-item__content {
      font-size: 0.83vw;
    }
  }

  .el-pagination {
    .el-pagination__jump,
    .el-pagination__total {
      font-size: 0.82vw !important;
    }
    .el-input__inner {
      font-size: 0.82vw !important;
      height: 32px !important;
    }
  }
}

.bigFontStyleDialog {
  .el-dialog {
    @include elDialogCommon;
  }
}
