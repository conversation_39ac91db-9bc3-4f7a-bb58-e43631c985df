<?xml version="1.0" encoding="UTF-8"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>亮度</title>
    <g id="CSP项目" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="质控管理-详情(图片影像)-影像质控(列表式)" transform="translate(-15.000000, -844.000000)">
            <g id="工具" transform="translate(13.000000, 85.000000)">
                <g id="亮度" transform="translate(2.000000, 759.000000)">
                    <rect id="矩形" x="0" y="0" width="40" height="40"></rect>
                    <circle id="椭圆形" fill="#FFFFFF" cx="21" cy="20" r="10"></circle>
                    <rect id="矩形" fill="#FFFFFF" x="20" y="1" width="2" height="6" rx="1"></rect>
                    <rect id="矩形" fill="#FFFFFF" x="20" y="32" width="2" height="6" rx="1"></rect>
                    <rect id="矩形" fill="#FFFFFF" transform="translate(29.000000, 6.000000) rotate(30.000000) translate(-29.000000, -6.000000) " x="28" y="3" width="2" height="6" rx="1"></rect>
                    <rect id="矩形" fill="#FFFFFF" transform="translate(34.787780, 11.787780) rotate(60.000000) translate(-34.787780, -11.787780) " x="33.78778" y="8.78777998" width="2" height="6" rx="1"></rect>
                    <rect id="矩形" fill="#FFFFFF" transform="translate(36.934642, 19.799980) rotate(90.000000) translate(-36.934642, -19.799980) " x="35.9346424" y="16.7999798" width="2" height="6" rx="1"></rect>
                    <rect id="矩形" fill="#FFFFFF" transform="translate(13.000000, 6.000000) scale(-1, 1) rotate(30.000000) translate(-13.000000, -6.000000) " x="12" y="3" width="2" height="6" rx="1"></rect>
                    <rect id="矩形" fill="#FFFFFF" transform="translate(7.787780, 11.787780) scale(-1, 1) rotate(60.000000) translate(-7.787780, -11.787780) " x="6.78777998" y="8.78777998" width="2" height="6" rx="1"></rect>
                    <rect id="矩形" fill="#FFFFFF" transform="translate(14.000000, 33.000000) scale(-1, -1) rotate(30.000000) translate(-14.000000, -33.000000) " x="13" y="30" width="2" height="6" rx="1"></rect>
                    <rect id="矩形" fill="#FFFFFF" transform="translate(7.787780, 27.787780) scale(-1, -1) rotate(60.000000) translate(-7.787780, -27.787780) " x="6.78777998" y="24.78778" width="2" height="6" rx="1"></rect>
                    <rect id="矩形" fill="#FFFFFF" transform="translate(29.000000, 33.000000) scale(1, -1) rotate(30.000000) translate(-29.000000, -33.000000) " x="28" y="30" width="2" height="6" rx="1"></rect>
                    <rect id="矩形" fill="#FFFFFF" transform="translate(34.787780, 27.787780) scale(1, -1) rotate(60.000000) translate(-34.787780, -27.787780) " x="33.78778" y="24.78778" width="2" height="6" rx="1"></rect>
                    <rect id="矩形" fill="#FFFFFF" transform="translate(5.934642, 19.799980) scale(-1, 1) rotate(90.000000) translate(-5.934642, -19.799980) " x="4.93464244" y="16.7999798" width="2" height="6" rx="1"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>