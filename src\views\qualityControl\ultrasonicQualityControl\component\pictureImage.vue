<template>
  <div class="picture-image">
    <div class="display-section">
      <div class="section-title">报告图片</div>
      <div class="section-content">
        <div v-for="(url, index) in form.attachmentPhotoUrl" :key="'photo-' + index" class="photo-item">
          <img :src="url" alt="报告图片" class="report-image" @click="handlePreview(url, 'image')">
          <div class="photo-number">{{ String(index + 1).padStart(2, '0') }}</div>
        </div>
      </div>
    </div>

    <div class="display-section">
      <div class="section-title">视频影像</div>
      <div class="section-content video-content">
        <div v-for="(url, index) in form.attachmentVideoUrl" :key="index" class="video-item">
          <div class="video-wrapper" @click="handlePreview(url, 'video')">
            <video class="video-player">
              <source :src="url">
            </video>
            <img src="@/assets/cspImg/video-play.png" alt="播放视频" class="video-play-icon">
          </div>
          <div class="video-number">{{ String(index + 1).padStart(2, '0') }}</div>
        </div>
      </div>
    </div>

    <!-- 预览弹窗 -->
    <ProDialog
      :visible.sync="previewVisible"
      :title="previewData.type === 'image' ? '图片预览' : '视频播放'"
      width="70%"
      center
      custom-class="preview-dialog"
    >
      <div class="preview-content">
        <!-- 图片预览 -->
        <div v-if="previewData.type === 'image'" class="image-preview">
          <img :src="previewData.url" alt="预览图片" class="preview-image">
        </div>

        <!-- 视频播放 -->
        <div v-else-if="previewData.type === 'video'" class="video-preview">
          <video :src="previewData.url" class="preview-video" controls autoplay />
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="closePreview">关闭</el-button>
      </span>
    </ProDialog>
  </div>
</template>

<script>
import { getFullUrl } from '@/utils/cspUtils'
import ProDialog from '@/components/ProDialog/index.vue'

export default {
  name: 'PictureImage',
  components: {
    ProDialog
  },
  props: {
    detail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      form: {
        attachmentPhotoUrl: [],
        attachmentVideoUrl: []
      },
      previewVisible: false,
      previewData: {
        url: '',
        type: ''
      }
    }
  },
  methods: {
    initData(data) {
      this.form.attachmentPhotoUrl = (data.attachmentPhotoUrl ? data.attachmentPhotoUrl.split(',') : []).map((item) =>
        getFullUrl(item))

      this.form.attachmentVideoUrl = (data.attachmentVideoUrl ? data.attachmentVideoUrl.split(',') : []).map((item) =>
        getFullUrl(item))
    },

    // 预览图片和视频
    handlePreview(url, type) {
      this.previewData = {
        url,
        type
      }
      this.previewVisible = true
    },

    // 关闭预览弹窗
    closePreview() {
      this.previewVisible = false
      this.previewData = {
        url: '',
        type: ''
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.picture-image {
  padding: 20px 0;
}

.display-section {
  margin-bottom: 30px;
  display: flex;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  width: 100px;
  flex-shrink: 0;
}

.section-content {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.photo-item {
  position: relative;
  width: 200px;
  text-align: center;
}

.report-image {
  width: 100%;
  height: 200px;
  border-radius: 4px;
  cursor: pointer;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
  object-fit: cover;

  &:hover {
    transform: scale(1.02);
  }
}

.photo-number {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  text-align: center;
}

.video-content {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.video-item {
  position: relative;
  width: 240px;
  text-align: center;
}

.video-wrapper {
  position: relative;
  width: 100%;
  height: 180px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  cursor: pointer;

  &:hover {
    .video-play-icon {
      transform: translate(-50%, -50%) scale(1.1);
    }
  }
}

.video-player {
  width: 100%;
  height: 100%;
  border-radius: 4px;
  object-fit: cover;
  display: block;
}

.video-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  pointer-events: none;
  transition: transform 0.3s ease;
  z-index: 10;
  opacity: 0.9;

  // 添加一个半透明背景让图标更突出
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.3));
}

.video-number {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  text-align: center;
}

.preview-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  max-height: 65vh;
  overflow: hidden;
}

.image-preview {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.video-preview {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.preview-video {
  width: 100%;
  max-height: 65vh;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background: #000;
}
</style>
