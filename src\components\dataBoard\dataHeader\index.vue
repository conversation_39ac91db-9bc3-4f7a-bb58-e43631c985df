<template>
  <div class="header public_v2">
    <div class="left_text fontSize_16">
      <div v-if="crumbData.length > 1 && crumbData[0].meta.title">
        <svg-icon v-if="showIcon" class="icon_undo" icon-class="undo" @click="$emit('clickText')" />
        <i v-else class="el-icon-arrow-left cp" @click="goBack(-1)" />
      </div>

      <span v-for="(itemCrumb, index) in crumbData" :key="'crumbList_' + index">
        <label v-if="index > 0" class="crumbLine">{{ itemCrumb.meta.title ? '/' : '' }}</label>
        <span :key="'crumbList_' + index" :class="['crumbTxt', index < crumbData.length - 1 ? '' : '']">
          {{ itemCrumb.meta.title }}
        </span>
      </span>
    </div>

    <div class="right_info fontSize_16">
      <div>
        组织机构: <span>{{ departName }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'

export default {
  components: {},
  props: {
    leftText: {
      type: String,
      default: '首页'
    },
    showIcon: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      crumbData: [],
      hideRight: null
    }
  },
  computed: {
    ...mapGetters(['menus', 'departName']),
    replaceMenus() {
      return this.$store.getters.replaceMenus || null
    }
  },
  watch: {
    $route(to, from) {
      this.getRouteList(this.$route.path, from, to)
    }
  },
  created() {
    this.hideRight = this.$route.path === '/home' || this.$route.path === '/'

    this.getRouteList(this.$route.path)
  },
  mounted() {},

  methods: {
    goPagePath(path) {
      this.$router.replace(path)
    },
    goBack(n) {
      this.$router.go(n)
    },
    getRouteList(path, from, to) {
      this.crumbData = this.replaceMenus || this.$route.matched
    },

    onJumpPath() {
      const self = this
      self.$router.push({
        name: 'Home'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 40px;
  background: #ffffff;
  padding: 0 0.9375vw 0 1.6667vw;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid rgba(159, 161, 174, 0.4);
  .left_text {
    height: 100%;
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    // font-size: 0.75rem;
    color: #2a2d33;
    display: flex;
    align-items: center;
    .crumbLine {
      padding: 0 0.25rem;
    }
    .crumbTxt {
      font-weight: bold;
    }
    .el-icon-arrow-left {
      font-size: 0.8rem;
      margin-right: 0.4rem;
      font-weight: 600;
    }
    .cp.crumbTxt {
      position: relative;
      font-weight: 100;
      display: inline-block;
      &:hover::after {
        display: inline-block;
      }
      &::after {
        content: '';
        display: none;
        height: 1px;
        width: 100%;
        background-color: #222;
        position: absolute;
        left: 0;
        bottom: 0;
      }
    }
    .icon_undo {
      width: 1rem;
      height: 1rem;
      margin-right: 0.5rem;
      cursor: pointer;
    }
  }

  .right_info {
    height: 100%;
    display: flex;

    > div {
      display: flex;
      align-items: center;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      // font-size: 0.75rem;
      color: #646771;
      margin-left: 1.5625vw;

      span {
        color: #2a2d33;
        margin-left: 0.5rem;
      }
    }
  }
}
</style>
