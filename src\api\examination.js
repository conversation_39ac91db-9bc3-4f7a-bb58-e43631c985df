import request from '@/utils/request'

// 获取检验列表
export const getInspectionTestingList = (data) => {
  return request({
    url: '/cspapi/backend/examine/inspect/page',
    method: 'post',
    data
  })
}

// 检验检查详情查询
export const getInspectionTestingDetail = (data) => {
  return request({
    url: '/cspapi/backend/examine/inspect/detail',
    method: 'post',
    data
  })
}

// 查询检验检查历史记录
export const getInspectionTestingHistoryList = (data) => {
  return request({
    url: '/cspapi/backend/examine/inspect/list/history',
    method: 'post',
    data
  })
}

// 新增检验检查
export const addInspectionTesting = (data) => {
  return request({
    url: '/cspapi/backend/examine/inspect/detail/patientId',
    method: 'post',
    data
  })
}

// 查询添加项目列表
export const getAddProjectList = (data) => {
  return request({
    url: '/cspapi/backend/examine/inspect/item/list',
    method: 'post',
    data
  })
}

// 查询项目数据
export const getProjectData = (data) => {
  return request({
    url: '/cspapi/backend/examine/inspect/item/detail',
    method: 'post',
    data
  })
}

// 项目移除
export const projectRemove = (data) => {
  return request({
    url: '/cspapi/backend/examine/inspect/item/remove',
    method: 'post',
    data
  })
}

// 项目添加
export const projectAdd = (data) => {
  return request({
    url: '/cspapi/backend/examine/inspect/item/add',
    method: 'post',
    data
  })
}

// 保存检验项目数据
export const saveInspectionProject = (data) => {
  return request({
    url: '/cspapi/backend/examine/inspect/item/detail/save',
    method: 'post',
    data
  })
}

// 取消检查
export const cancelCheck = (data) => {
  return request({
    url: '/cspapi/backend/examine/inspect/cancel',
    method: 'post',
    data
  })
}

// 完成检查
export const completeCheck = (data) => {
  return request({
    url: '/cspapi/backend/examine/inspect/complete',
    method: 'post',
    data
  })
}
