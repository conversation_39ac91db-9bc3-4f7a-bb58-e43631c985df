<template>
  <el-select
    v-if="request.static"
    v-model="localValue"
    clearable
    :disabled="disabled"
    placeholder="请搜索名称"
    filterable
    class="w10"
    @change="changeSelectData"
  >
    <el-option
      v-for="(item, index) in optionList"
      :key="index"
      :label="item[optionItem.label]"
      :value="item[optionItem.value]"
    >
      {{ item[optionItem.label] }}
    </el-option>
  </el-select>
  <el-select
    v-else
    v-model="localValue"
    clearable
    :disabled="disabled"
    placeholder="请搜索名称"
    filterable
    remote
    :remote-method="searchData"
    class="w10"
    @change="changeSelectData"
  >
    <el-option
      v-for="(item, index) in optionList"
      :key="index"
      :label="item[optionItem.label]"
      :value="item[optionItem.value]"
    >
      {{ item[optionItem.label] }}
    </el-option>
  </el-select>
</template>

<script>
import request from '@/utils/request'

export default {
  name: 'ProSelect',
  props: {
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    request: {
      type: Object,
      default: () => {
        return {
          url: '',
          static: false,
          params: {}
        }
      }
    },
    optionItem: {
      type: Object,
      default: () => {
        return {
          label: 'name',
          value: 'id'
        }
      }
    }
  },
  data() {
    return {
      currentLabel: '',
      optionList: [],
      localValue: this.value
    }
  },
  watch: {
    value(newValue) {
      this.localValue = newValue
    },
    localValue(newValue) {
      console.log('newValue', newValue)
      this.$emit('input', newValue)
    },
    request: {
      handler(nVal, oVal) {
        if (!this.deepEqual(nVal, oVal)) {
          console.log('request', nVal)
          this.searchData()
        }
      },
      deep: true
    }
  },
  created() {
    this.searchData()
  },
  methods: {
    deepEqual(obj1, obj2) {
      if (obj1 === obj2) return true // 简单类型和引用相等情况

      if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {
        return false // 处理非对象或 null 的情况
      }

      const keys1 = Object.keys(obj1)
      const keys2 = Object.keys(obj2)

      if (keys1.length !== keys2.length) {
        return false // 如果属性数量不同，返回 false
      }

      for (const key of keys1) {
        if (!keys2.includes(key) || !this.deepEqual(obj1[key], obj2[key])) {
          return false // 如果 key 不存在或值不同，返回 false
        }
      }
      return true // 如果所有键值对都相同，返回 true
    },
    // 选择
    changeSelectData(val) {
      const { value, label } = this.optionItem
      const findItem = this.optionList.find((v) => Number(v[value]) === Number(val))
      if (findItem) {
        this.currentLabel = findItem[label]
        console.log(this.currentLabel)
      }
      this.$emit('change', val)
    },
    async getList(search = '') {
      const { url, params } = this.request
      const domain = {
        pageSize: 99999,
        pageNo: 1,
        ...params,
        keyword: search
      }
      return request({
        url,
        method: 'get',
        params: domain
      })
    },
    // 远程搜索医生
    async searchData(search) {
      this.pageLoading = true
      const res = await this.getList(search)
      this.optionList = res.data.list ? res.data.list : res.data
      this.pageLoading = false
    }
  }
}
</script>

<style scoped lang="scss"></style>
