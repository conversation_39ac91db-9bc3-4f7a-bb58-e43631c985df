<template>
  <div class="applicationNoDataBox" :class="{ noDataType }">
    <div v-if="!imgUrl" class="noDataImgBox">
      <img v-if="noDataType === 'normal'" :src="require('@/assets/common_images/noDataImg.png')" alt="" />
      <img v-if="noDataType === 'dark'" :src="require('@/assets/common_images/noDataImgDark.png')" alt="" />
      <img v-if="noDataType === 'auth'" :src="require('@/assets/common_images/notAuthorized.png')" alt="" />
    </div>
    <div v-else class="noDataImgBox">
      <img :src="imgUrl" alt="" />
    </div>
    <div class="noDataText" :class="`${noDataType}Type`">{{ infoText }}</div>
  </div>
</template>

<script>
export default {
  name: 'NoData',
  props: {
    noDataType: {
      type: String,
      default: 'normal'
    },
    infoText: {
      type: String,
      default: '暂无数据'
    },
    imgUrl: {
      type: String,
      default: ''
    }
  }
}
</script>
<style lang="scss" scoped>
.applicationNoDataBox {
  padding: 30px 0;
}
.noDataImgBox {
  line-height: 0;
}
.darkType {
  color: #3d86a1;
}
</style>
