import request from '@/utils/request'

// 人群筛查列表
export const getMassScreeningList = (data) => {
  return request({
    url: '/cspapi/backend/app/crowd/screening/page',
    method: 'post',
    data
  })
}

// 人群筛查详情
export const getMassScreeningDetail = (data) => {
  return request({
    url: '/cspapi/backend/app/crowd/screening/wx/detail',
    method: 'post',
    data
  })
}

// 人群筛查疾病数量
export const getMassScreeningDisease = (data) => {
  return request({
    url: '/cspapi/backend/app/crowd/screening/disease',
    method: 'post',
    data
  })
}

// 删除人群筛查
export const deleteMassScreening = (data) => {
  return request({
    url: '/cspapi/backend/app/crowd/screening/remove',
    method: 'post',
    data
  })
}
