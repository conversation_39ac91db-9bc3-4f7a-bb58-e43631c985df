<template>
  <div ref="bloodPressureEchart" style="width: 100%; height: 400px" />
</template>

<script>
import * as echarts from 'echarts'
import dayjs from 'dayjs'

export default {
  name: 'BloodPressureEchart',
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    tableData: {
      handler() {
        this.initChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  },
  methods: {
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.bloodPressureEchart)
      }

      const validData = this.tableData.filter((item) => item.measureTime && item.sp && item.dp)
      if (validData.length === 0) {
        return
      }

      const seriesData = [
        {
          name: '收缩压',
          type: 'line',
          data: validData
            .map((item) => {
              const isoTime = dayjs(item.measureTime).toISOString()
              return [isoTime, item.sp]
            })
            .sort((a, b) => new Date(a[0]) - new Date(b[0])),
          symbol: 'circle',
          symbolSize: 8
        },
        {
          name: '舒张压',
          type: 'line',
          data: validData
            .map((item) => {
              const isoTime = dayjs(item.measureTime).toISOString()
              return [isoTime, item.dp]
            })
            .sort((a, b) => new Date(a[0]) - new Date(b[0])),
          symbol: 'circle',
          symbolSize: 8
        }
      ]

      const option = {
        tooltip: {
          trigger: 'axis',
          formatter(params) {
            let result = `${dayjs(params[0].value[0]).format('YYYY-MM-DD HH:mm')}<br/>`
            params.forEach((item) => {
              result += `${item.marker} ${item.seriesName}: ${item.value[1]}<br/>`
            })
            return result
          }
        },
        legend: {
          data: ['收缩压', '舒张压'],
          bottom: 0
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'time',
          boundaryGap: false,
          axisLabel: {
            formatter: (value) => dayjs(value).format('YYYY-MM-DD HH:mm')
          }
        },
        yAxis: {
          type: 'value',
          name: '血压值'
        },
        series: seriesData
      }

      this.chart.setOption(option, true)
      this.chart.resize()
    }
  }
}
</script>
